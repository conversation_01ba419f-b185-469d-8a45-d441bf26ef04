# OpenRouter Integration Deployment Guide

## 🚨 URGENT: Functions Need Redeployment

Your AI functions are currently returning 500 errors because they've been updated to use OpenRouter but haven't been redeployed yet. The functions are still running the old OpenAI code but trying to use the OpenRouter API key.

## ✅ Functions Updated

I have successfully updated all your AI functions to use OpenRouter instead of OpenAI:

### 1. AI Tutor Function (`supabase/functions/ai-tutor/index.ts`)
- ✅ Changed API endpoint from OpenAI to OpenRouter
- ✅ Updated API key reference from `OPENAI_API_KEY` to `OPENROUTER_API_KEY`
- ✅ Updated model format to `openai/gpt-4o-mini`
- ✅ Added required OpenRouter headers (`HTTP-Referer`, `X-Title`)

### 2. AI Notes Generator (`supabase/functions/generate-ai-notes/index.ts`)
- ✅ Changed API endpoint from OpenAI to OpenRouter
- ✅ Updated API key reference from `OPENAI_API_KEY` to `OPENROUTER_API_KEY`
- ✅ Added model mapping function for OpenRouter format
- ✅ Updated all model references in logging

### 3. Quiz Generator (`supabase/functions/generate-quiz-from-pdf/index.ts`)
- ✅ Changed API endpoint from OpenAI to OpenRouter
- ✅ Updated API key reference from `OPENAI_API_KEY` to `OPENROUTER_API_KEY`
- ✅ Updated model format to `openai/gpt-4o-mini`
- ✅ Added required OpenRouter headers

## 🔑 API Key Status

Your OpenRouter API key is already configured in Supabase:
- ✅ `OPENROUTER_API_KEY` is set in your project secrets
- ✅ Last updated: 2025-06-24 at 10:59:22 UTC

## 🚀 Deployment Required

To activate the OpenRouter integration, you need to redeploy the functions. Here are your options:

### Option 1: Install Supabase CLI (Recommended)

```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Link to your project
supabase link --project-ref umavpljptvamtmaygztq

# Deploy all functions
supabase functions deploy
```

### Option 2: Manual Deployment via Supabase Dashboard (RECOMMENDED - FASTEST)

**This is the quickest way to fix the 500 errors you're seeing:**

1. Go to [Supabase Dashboard](https://supabase.com/dashboard/project/umavpljptvamtmaygztq/functions)
2. Navigate to "Edge Functions" in the sidebar
3. For each function, follow these steps:

#### Update AI Tutor Function:
- Click on "ai-tutor" function
- Click "Edit Function" or the pencil icon
- Replace ALL the code with the content from `supabase/functions/ai-tutor/index.ts` in your project
- Click "Deploy" or "Save"

#### Update Generate AI Notes Function:
- Click on "generate-ai-notes" function
- Click "Edit Function" or the pencil icon
- Replace ALL the code with the content from `supabase/functions/generate-ai-notes/index.ts` in your project
- Click "Deploy" or "Save"

#### Update Quiz Generator Function:
- Click on "generate-quiz-from-pdf" function
- Click "Edit Function" or the pencil icon
- Replace ALL the code with the content from `supabase/functions/generate-quiz-from-pdf/index.ts` in your project
- Click "Deploy" or "Save"

### Option 3: Use the Deploy Script

I can create a deployment script for you that uses the Supabase Management API.

## 🧪 Testing After Deployment

Once deployed, test the functions:

### Test AI Tutor
```bash
curl -X POST 'https://umavpljptvamtmaygztq.supabase.co/functions/v1/ai-tutor' \
  -H 'Authorization: Bearer YOUR_SUPABASE_ANON_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "messages": [{"role": "user", "content": "What is photosynthesis?"}],
    "subject": "science",
    "temperature": 0.7
  }'
```

### Test AI Notes Generator
```bash
curl -X POST 'https://umavpljptvamtmaygztq.supabase.co/functions/v1/generate-ai-notes' \
  -H 'Authorization: Bearer YOUR_SUPABASE_ANON_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "subject": "Mathematics",
    "topic": "Algebra Basics",
    "noteLength": "medium"
  }'
```

## 🔄 Key Changes Made

### API Endpoint Change
```typescript
// Before (OpenAI)
const response = await fetch('https://api.openai.com/v1/chat/completions', {
  headers: {
    'Authorization': `Bearer ${openAIApiKey}`,
    'Content-Type': 'application/json',
  }
})

// After (OpenRouter)
const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
  headers: {
    'Authorization': `Bearer ${openRouterApiKey}`,
    'Content-Type': 'application/json',
    'HTTP-Referer': 'https://study-fam.com',
    'X-Title': 'Study Fam AI Functions',
  }
})
```

### Model Format Change
```typescript
// Before
model: 'gpt-4o-mini'

// After
model: 'openai/gpt-4o-mini'
```

## 💰 Benefits of OpenRouter

1. **Cost Optimization**: Access to multiple AI providers with competitive pricing
2. **Reliability**: Automatic failover between providers
3. **Flexibility**: Easy switching between different models
4. **Monitoring**: Built-in usage analytics and rate limiting

## 🔧 Next Steps

1. Choose a deployment method above
2. Deploy the updated functions
3. Test the functions to ensure they work correctly
4. Monitor usage in the OpenRouter dashboard

Would you like me to help you with any of these deployment options?
