-- Fix infinite recursion in RLS policies
-- Drop problematic policies that cause infinite recursion

-- Drop all existing study_group_members policies
DROP POLICY IF EXISTS "Members can view group membership" ON public.study_group_members;
DROP POLICY IF EXISTS "Users can view members of groups they belong to" ON public.study_group_members;
DROP POLICY IF EXISTS "Users can join study groups" ON public.study_group_members;
DROP POLICY IF EXISTS "Users can join groups" ON public.study_group_members;
DROP POLICY IF EXISTS "Users can leave groups" ON public.study_group_members;

-- Drop duplicate study_groups policies
DROP POLICY IF EXISTS "Public study groups are viewable by everyone" ON public.study_groups;
DROP POLICY IF EXISTS "Users can create study groups" ON public.study_groups;
DROP POLICY IF EXISTS "Group owners can update their groups" ON public.study_groups;
DROP POLICY IF EXISTS "Everyone can view public groups" ON public.study_groups;
DROP POLICY IF EXISTS "Users can create groups" ON public.study_groups;
DROP POLICY IF EXISTS "Creators can update their groups" ON public.study_groups;
DROP POLICY IF EXISTS "Creators can delete their groups" ON public.study_groups;
DROP POLICY IF EXISTS "Group creators can delete their groups" ON public.study_groups;
DROP POLICY IF EXISTS "Public groups are viewable by everyone" ON public.study_groups;
DROP POLICY IF EXISTS "Private groups are viewable by members" ON public.study_groups;
DROP POLICY IF EXISTS "Group creators can update their groups" ON public.study_groups;

-- Create new, non-recursive policies for study_groups
CREATE POLICY "Public groups viewable by all" ON public.study_groups
    FOR SELECT USING (privacy = 'public'::group_privacy);

CREATE POLICY "Creators can view their groups" ON public.study_groups
    FOR SELECT USING (auth.uid() = creator_id);

CREATE POLICY "Users can create groups" ON public.study_groups
    FOR INSERT WITH CHECK (auth.uid() = creator_id);

CREATE POLICY "Creators can update groups" ON public.study_groups
    FOR UPDATE USING (auth.uid() = creator_id);

CREATE POLICY "Creators can delete groups" ON public.study_groups
    FOR DELETE USING (auth.uid() = creator_id);

-- Create new, non-recursive policies for study_group_members
-- Allow users to view their own memberships
CREATE POLICY "Users can view own memberships" ON public.study_group_members
    FOR SELECT USING (auth.uid() = user_id);

-- Allow users to view memberships in public groups
CREATE POLICY "View memberships in public groups" ON public.study_group_members
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.study_groups sg
            WHERE sg.id = group_id AND sg.privacy = 'public'::group_privacy
        )
    );

-- Allow group creators to view all memberships in their groups
CREATE POLICY "Creators can view group memberships" ON public.study_group_members
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.study_groups sg
            WHERE sg.id = group_id AND sg.creator_id = auth.uid()
        )
    );

-- Allow users to join groups (insert their own membership)
CREATE POLICY "Users can join groups" ON public.study_group_members
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Allow users to leave groups (delete their own membership)
CREATE POLICY "Users can leave groups" ON public.study_group_members
    FOR DELETE USING (auth.uid() = user_id);

-- Allow group creators to manage memberships (remove members)
CREATE POLICY "Creators can manage memberships" ON public.study_group_members
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.study_groups sg
            WHERE sg.id = group_id AND sg.creator_id = auth.uid()
        )
    );
