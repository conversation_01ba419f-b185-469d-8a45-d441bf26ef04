-- Create table for tracking WooCommerce orders
CREATE TABLE IF NOT EXISTS woocommerce_orders (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    order_id INTEGER UNIQUE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    status VARCHAR(50) NOT NULL,
    total DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    order_key VARCHAR(255),
    payment_method VARCHAR(100),
    created_at TIMESTAMPTZ NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_woocommerce_orders_order_id ON woocommerce_orders(order_id);
CREATE INDEX IF NOT EXISTS idx_woocommerce_orders_user_id ON woocommerce_orders(user_id);
CREATE INDEX IF NOT EXISTS idx_woocommerce_orders_status ON woocommerce_orders(status);

-- Add RLS policies
ALTER TABLE woocommerce_orders ENABLE ROW LEVEL SECURITY;

-- Users can only see their own orders
CREATE POLICY "Users can view own orders" ON woocommerce_orders
    FOR SELECT USING (auth.uid() = user_id);

-- Service role can manage all orders (for webhooks)
CREATE POLICY "Service role can manage orders" ON woocommerce_orders
    FOR ALL USING (auth.role() = 'service_role');

-- Add columns to user_subscriptions table for WooCommerce integration
ALTER TABLE user_subscriptions 
ADD COLUMN IF NOT EXISTS woocommerce_order_id INTEGER,
ADD COLUMN IF NOT EXISTS woocommerce_order_key VARCHAR(255);

-- Create index for WooCommerce order lookups
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_wc_order ON user_subscriptions(woocommerce_order_id);
