-- Clean up existing policies and recreate subscription system

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Anyone can view active subscription plans" ON public.subscription_plans;
DROP POLICY IF EXISTS "Users can view their own subscriptions" ON public.user_subscriptions;
DROP POLICY IF EXISTS "Users can insert their own subscriptions" ON public.user_subscriptions;
DROP POLICY IF EXISTS "Users can update their own subscriptions" ON public.user_subscriptions;
DROP POLICY IF EXISTS "Users can view their own transactions" ON public.payment_transactions;
DROP POLICY IF EXISTS "Users can insert their own transactions" ON public.payment_transactions;

-- Update the default plan to be daily with $0.10 price
UPDATE public.subscription_plans 
SET 
    price_cents = 10, 
    interval_type = 'daily',
    trial_period_minutes = 1,
    description = 'Daily access to all StudyFam features for $0.10',
    name = 'StudyFam Daily'
WHERE is_active = true;

-- RLS Policies for subscription_plans (publicly readable)
CREATE POLICY "subscription_plans_select_policy" ON public.subscription_plans
    FOR SELECT USING (is_active = true);

-- RLS Policies for user_subscriptions
CREATE POLICY "user_subscriptions_select_policy" ON public.user_subscriptions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "user_subscriptions_insert_policy" ON public.user_subscriptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "user_subscriptions_update_policy" ON public.user_subscriptions
    FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for payment_transactions
CREATE POLICY "payment_transactions_select_policy" ON public.payment_transactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "payment_transactions_insert_policy" ON public.payment_transactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);