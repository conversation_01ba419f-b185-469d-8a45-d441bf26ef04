
-- Add file sharing support to messages table (already exists but let's ensure it has the right columns)
-- The messages table already has file_url, file_name, file_size columns

-- Create a function to handle document sharing
CREATE OR REPLACE FUNCTION public.share_document_to_users(
  p_sender_id UUID,
  p_recipient_ids UUID[],
  p_document_url TEXT,
  p_document_name TEXT,
  p_document_title TEXT,
  p_share_message TEXT DEFAULT NULL
)
RETURNS UUID[]
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  recipient_id UUID;
  conversation_id UUID;
  message_id UUID;
  result_ids UUID[] := '{}';
  share_content TEXT;
BEGIN
  -- Build the share message content
  share_content := COALESCE(p_share_message, '') || 
    CASE WHEN p_share_message IS NOT NULL THEN E'\n\n' ELSE '' END ||
    '📎 Shared document: ' || p_document_title;
  
  -- Loop through each recipient
  FOREACH recipient_id IN ARRAY p_recipient_ids
  LOOP
    -- Get or create conversation between sender and recipient
    SELECT get_or_create_direct_conversation(p_sender_id, recipient_id) INTO conversation_id;
    
    -- Send the document message
    SELECT send_message(
      conversation_id,
      share_content,
      'file',
      p_document_url,
      p_document_name,
      NULL, -- file_size
      NULL, -- reply_to_id
      p_sender_id
    ) INTO message_id;
    
    result_ids := result_ids || message_id;
  END LOOP;
  
  RETURN result_ids;
END;
$$;

-- Create a function to share documents to study groups
CREATE OR REPLACE FUNCTION public.share_document_to_study_group(
  p_sender_id UUID,
  p_group_id UUID,
  p_document_url TEXT,
  p_document_name TEXT,
  p_document_title TEXT,
  p_share_message TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  post_id UUID;
  share_content TEXT;
BEGIN
  -- Verify user is a member of the group
  IF NOT EXISTS (
    SELECT 1 FROM study_group_members 
    WHERE group_id = p_group_id AND user_id = p_sender_id
  ) THEN
    RAISE EXCEPTION 'User is not a member of this study group';
  END IF;
  
  -- Build the share content
  share_content := COALESCE(p_share_message, 'Shared a document: ' || p_document_title);
  
  -- Create study group post
  INSERT INTO study_group_posts (
    group_id,
    author_id,
    title,
    content,
    post_type,
    file_url,
    file_name
  ) VALUES (
    p_group_id,
    p_sender_id,
    p_document_title,
    share_content,
    'shared_document',
    p_document_url,
    p_document_name
  ) RETURNING id INTO post_id;
  
  RETURN post_id;
END;
$$;
