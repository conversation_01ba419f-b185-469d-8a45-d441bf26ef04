-- Complete RLS policies for all study group related tables

-- Study Group Posts Policies
CREATE POLICY "Users can view posts in public groups" ON public.study_group_posts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.study_groups sg
            WHERE sg.id = group_id AND sg.privacy = 'public'::group_privacy
        )
    );

CREATE POLICY "Group members can view posts in private groups" ON public.study_group_posts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.study_group_members sgm
            JOIN public.study_groups sg ON sg.id = sgm.group_id
            WHERE sgm.group_id = group_id 
            AND sgm.user_id = auth.uid()
            AND sg.privacy = 'private'::group_privacy
        )
    );

CREATE POLICY "Group members can create posts" ON public.study_group_posts
    FOR INSERT WITH CHECK (
        auth.uid() = author_id AND
        EXISTS (
            SELECT 1 FROM public.study_group_members sgm
            WHERE sgm.group_id = group_id AND sgm.user_id = auth.uid()
        )
    );

CREATE POLICY "Authors can update their posts" ON public.study_group_posts
    FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Authors and group creators can delete posts" ON public.study_group_posts
    FOR DELETE USING (
        auth.uid() = author_id OR
        EXISTS (
            SELECT 1 FROM public.study_groups sg
            WHERE sg.id = group_id AND sg.creator_id = auth.uid()
        )
    );

-- Study Group Comments Policies
CREATE POLICY "Users can view comments on posts they can see" ON public.study_group_comments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.study_group_posts sgp
            JOIN public.study_groups sg ON sg.id = sgp.group_id
            WHERE sgp.id = post_id 
            AND (
                sg.privacy = 'public'::group_privacy OR
                EXISTS (
                    SELECT 1 FROM public.study_group_members sgm
                    WHERE sgm.group_id = sg.id AND sgm.user_id = auth.uid()
                )
            )
        )
    );

CREATE POLICY "Group members can create comments" ON public.study_group_comments
    FOR INSERT WITH CHECK (
        auth.uid() = author_id AND
        EXISTS (
            SELECT 1 FROM public.study_group_posts sgp
            JOIN public.study_group_members sgm ON sgm.group_id = sgp.group_id
            WHERE sgp.id = post_id AND sgm.user_id = auth.uid()
        )
    );

CREATE POLICY "Authors can update their comments" ON public.study_group_comments
    FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Authors and group creators can delete comments" ON public.study_group_comments
    FOR DELETE USING (
        auth.uid() = author_id OR
        EXISTS (
            SELECT 1 FROM public.study_group_posts sgp
            JOIN public.study_groups sg ON sg.id = sgp.group_id
            WHERE sgp.id = post_id AND sg.creator_id = auth.uid()
        )
    );

-- Study Group Comment Replies Policies
CREATE POLICY "Users can view replies on comments they can see" ON public.study_group_comment_replies
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.study_group_comments sgc
            JOIN public.study_group_posts sgp ON sgp.id = sgc.post_id
            JOIN public.study_groups sg ON sg.id = sgp.group_id
            WHERE sgc.id = comment_id
            AND (
                sg.privacy = 'public'::group_privacy OR
                EXISTS (
                    SELECT 1 FROM public.study_group_members sgm
                    WHERE sgm.group_id = sg.id AND sgm.user_id = auth.uid()
                )
            )
        )
    );

CREATE POLICY "Group members can create replies" ON public.study_group_comment_replies
    FOR INSERT WITH CHECK (
        auth.uid() = author_id AND
        EXISTS (
            SELECT 1 FROM public.study_group_comments sgc
            JOIN public.study_group_posts sgp ON sgp.id = sgc.post_id
            JOIN public.study_group_members sgm ON sgm.group_id = sgp.group_id
            WHERE sgc.id = comment_id AND sgm.user_id = auth.uid()
        )
    );

CREATE POLICY "Authors can update their replies" ON public.study_group_comment_replies
    FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Authors and group creators can delete replies" ON public.study_group_comment_replies
    FOR DELETE USING (
        auth.uid() = author_id OR
        EXISTS (
            SELECT 1 FROM public.study_group_comments sgc
            JOIN public.study_group_posts sgp ON sgp.id = sgc.post_id
            JOIN public.study_groups sg ON sg.id = sgp.group_id
            WHERE sgc.id = comment_id AND sg.creator_id = auth.uid()
        )
    );

-- Study Group Comment Likes Policies
CREATE POLICY "Users can view comment likes" ON public.study_group_comment_likes
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.study_group_comments sgc
            JOIN public.study_group_posts sgp ON sgp.id = sgc.post_id
            JOIN public.study_groups sg ON sg.id = sgp.group_id
            WHERE sgc.id = comment_id
            AND (
                sg.privacy = 'public'::group_privacy OR
                EXISTS (
                    SELECT 1 FROM public.study_group_members sgm
                    WHERE sgm.group_id = sg.id AND sgm.user_id = auth.uid()
                )
            )
        )
    );

CREATE POLICY "Group members can like comments" ON public.study_group_comment_likes
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        EXISTS (
            SELECT 1 FROM public.study_group_comments sgc
            JOIN public.study_group_posts sgp ON sgp.id = sgc.post_id
            JOIN public.study_group_members sgm ON sgm.group_id = sgp.group_id
            WHERE sgc.id = comment_id AND sgm.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can unlike their comment likes" ON public.study_group_comment_likes
    FOR DELETE USING (auth.uid() = user_id);
