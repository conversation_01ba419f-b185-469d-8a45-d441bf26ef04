
-- Create storage buckets for study groups
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  ('group-covers', 'group-covers', true, 5242880, ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp']),
  ('group-files', 'group-files', true, 52428800, ARRAY['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'])
ON CONFLICT (id) DO NOTHING;

-- Create storage policies for group-covers bucket
CREATE POLICY "Authenticated users can upload group covers"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'group-covers' 
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

CREATE POLICY "Anyone can view group covers"
ON storage.objects FOR SELECT
USING (bucket_id = 'group-covers');

CREATE POLICY "Users can update their own group covers"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'group-covers' 
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

CREATE POLICY "Users can delete their own group covers"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'group-covers' 
  AND auth.role() = 'authenticated'
  AND (storage.foldername(name))[1] = auth.uid()::text
);

-- Create storage policies for group-files bucket
CREATE POLICY "Authenticated users can upload group files"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'group-files' 
  AND auth.role() = 'authenticated'
);

CREATE POLICY "Anyone can view group files"
ON storage.objects FOR SELECT
USING (bucket_id = 'group-files');

CREATE POLICY "Users can update their own group files"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'group-files' 
  AND auth.role() = 'authenticated'
);

CREATE POLICY "Users can delete their own group files"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'group-files' 
  AND auth.role() = 'authenticated'
);
