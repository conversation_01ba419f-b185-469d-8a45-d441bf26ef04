-- Messaging System Functions

-- Function to get or create a direct conversation between two users
CREATE OR REPLACE FUNCTION get_or_create_direct_conversation(
    user1_id UUID,
    user2_id UUID
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    conversation_id UUID;
    existing_conversation_id UUID;
BEGIN
    -- Validate inputs
    IF user1_id IS NULL OR user2_id IS NULL THEN
        RAISE EXCEPTION 'Both user IDs must be provided';
    END IF;
    
    IF user1_id = user2_id THEN
        RAISE EXCEPTION 'Cannot create conversation with yourself';
    END IF;
    
    -- Check if conversation already exists between these users
    SELECT c.id INTO existing_conversation_id
    FROM public.conversations c
    WHERE c.type = 'direct'
    AND EXISTS (
        SELECT 1 FROM public.conversation_participants cp1
        WHERE cp1.conversation_id = c.id AND cp1.user_id = user1_id
    )
    AND EXISTS (
        SELECT 1 FROM public.conversation_participants cp2
        WHERE cp2.conversation_id = c.id AND cp2.user_id = user2_id
    )
    AND (
        SELECT COUNT(*) FROM public.conversation_participants cp
        WHERE cp.conversation_id = c.id
    ) = 2;
    
    -- If conversation exists, return it
    IF existing_conversation_id IS NOT NULL THEN
        RETURN existing_conversation_id;
    END IF;
    
    -- Create new conversation
    INSERT INTO public.conversations (type, created_by)
    VALUES ('direct', user1_id)
    RETURNING id INTO conversation_id;
    
    -- Add both users as participants
    INSERT INTO public.conversation_participants (conversation_id, user_id, role)
    VALUES 
        (conversation_id, user1_id, 'member'),
        (conversation_id, user2_id, 'member');
    
    RETURN conversation_id;
END;
$$;

-- Function to get user's conversations with latest message info
CREATE OR REPLACE FUNCTION get_user_conversations_v2(p_user_id UUID)
RETURNS TABLE (
    conversation_id UUID,
    conversation_type TEXT,
    conversation_name TEXT,
    conversation_avatar TEXT,
    other_user_id UUID,
    other_user_name TEXT,
    other_user_avatar TEXT,
    other_user_email TEXT,
    latest_message_id UUID,
    latest_message_content TEXT,
    latest_message_type TEXT,
    latest_message_sender_id UUID,
    latest_message_sender_name TEXT,
    latest_message_time TIMESTAMP WITH TIME ZONE,
    unread_count BIGINT,
    participant_count INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id as conversation_id,
        c.type as conversation_type,
        CASE 
            WHEN c.type = 'group' THEN c.name
            ELSE (
                SELECT p.full_name
                FROM public.profiles p
                INNER JOIN public.conversation_participants cp ON cp.user_id = p.id
                WHERE cp.conversation_id = c.id AND cp.user_id != p_user_id
                LIMIT 1
            )
        END as conversation_name,
        c.avatar_url as conversation_avatar,
        CASE 
            WHEN c.type = 'direct' THEN (
                SELECT p.id
                FROM public.profiles p
                INNER JOIN public.conversation_participants cp ON cp.user_id = p.id
                WHERE cp.conversation_id = c.id AND cp.user_id != p_user_id
                LIMIT 1
            )
            ELSE NULL
        END as other_user_id,
        CASE 
            WHEN c.type = 'direct' THEN (
                SELECT p.full_name
                FROM public.profiles p
                INNER JOIN public.conversation_participants cp ON cp.user_id = p.id
                WHERE cp.conversation_id = c.id AND cp.user_id != p_user_id
                LIMIT 1
            )
            ELSE NULL
        END as other_user_name,
        CASE 
            WHEN c.type = 'direct' THEN (
                SELECT p.avatar_url
                FROM public.profiles p
                INNER JOIN public.conversation_participants cp ON cp.user_id = p.id
                WHERE cp.conversation_id = c.id AND cp.user_id != p_user_id
                LIMIT 1
            )
            ELSE c.avatar_url
        END as other_user_avatar,
        CASE 
            WHEN c.type = 'direct' THEN (
                SELECT p.email
                FROM public.profiles p
                INNER JOIN public.conversation_participants cp ON cp.user_id = p.id
                WHERE cp.conversation_id = c.id AND cp.user_id != p_user_id
                LIMIT 1
            )
            ELSE NULL
        END as other_user_email,
        latest_msg.id as latest_message_id,
        latest_msg.content as latest_message_content,
        latest_msg.message_type as latest_message_type,
        latest_msg.sender_id as latest_message_sender_id,
        sender_profile.full_name as latest_message_sender_name,
        latest_msg.created_at as latest_message_time,
        COALESCE(
            (
                SELECT COUNT(*)
                FROM public.messages m
                WHERE m.conversation_id = c.id
                AND m.created_at > COALESCE(user_participant.last_read_at, '1970-01-01'::timestamp)
                AND m.sender_id != p_user_id
            ), 0
        ) as unread_count,
        (
            SELECT COUNT(*)::INTEGER
            FROM public.conversation_participants cp
            WHERE cp.conversation_id = c.id
        ) as participant_count
    FROM public.conversations c
    INNER JOIN public.conversation_participants user_participant 
        ON user_participant.conversation_id = c.id 
        AND user_participant.user_id = p_user_id
    LEFT JOIN LATERAL (
        SELECT m.id, m.content, m.message_type, m.sender_id, m.created_at
        FROM public.messages m
        WHERE m.conversation_id = c.id
        ORDER BY m.created_at DESC
        LIMIT 1
    ) latest_msg ON true
    LEFT JOIN public.profiles sender_profile ON sender_profile.id = latest_msg.sender_id
    ORDER BY COALESCE(latest_msg.created_at, c.created_at) DESC;
END;
$$;

-- Function to mark messages as read
CREATE OR REPLACE FUNCTION mark_conversation_as_read(
    p_conversation_id UUID,
    p_user_id UUID DEFAULT auth.uid()
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Validate user is participant
    IF NOT EXISTS (
        SELECT 1 FROM public.conversation_participants
        WHERE conversation_id = p_conversation_id AND user_id = p_user_id
    ) THEN
        RAISE EXCEPTION 'User is not a participant in this conversation';
    END IF;
    
    -- Update last_read_at timestamp
    UPDATE public.conversation_participants
    SET last_read_at = NOW()
    WHERE conversation_id = p_conversation_id AND user_id = p_user_id;
END;
$$;

-- Function to send a message
CREATE OR REPLACE FUNCTION send_message(
    p_conversation_id UUID,
    p_content TEXT DEFAULT NULL,
    p_message_type TEXT DEFAULT 'text',
    p_file_url TEXT DEFAULT NULL,
    p_file_name TEXT DEFAULT NULL,
    p_file_size INTEGER DEFAULT NULL,
    p_reply_to_id UUID DEFAULT NULL,
    p_sender_id UUID DEFAULT auth.uid()
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    message_id UUID;
BEGIN
    -- Validate user is participant
    IF NOT EXISTS (
        SELECT 1 FROM public.conversation_participants
        WHERE conversation_id = p_conversation_id AND user_id = p_sender_id
    ) THEN
        RAISE EXCEPTION 'User is not a participant in this conversation';
    END IF;
    
    -- Validate message content
    IF (p_content IS NULL OR p_content = '') AND (p_file_url IS NULL OR p_file_url = '') THEN
        RAISE EXCEPTION 'Message must have content or file';
    END IF;
    
    -- Insert message
    INSERT INTO public.messages (
        conversation_id,
        sender_id,
        content,
        message_type,
        file_url,
        file_name,
        file_size,
        reply_to_id
    )
    VALUES (
        p_conversation_id,
        p_sender_id,
        p_content,
        p_message_type,
        p_file_url,
        p_file_name,
        p_file_size,
        p_reply_to_id
    )
    RETURNING id INTO message_id;
    
    RETURN message_id;
END;
$$;

-- Function to get messages for a conversation
CREATE OR REPLACE FUNCTION get_conversation_messages(
    p_conversation_id UUID,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0,
    p_user_id UUID DEFAULT auth.uid()
)
RETURNS TABLE (
    id UUID,
    conversation_id UUID,
    sender_id UUID,
    sender_name TEXT,
    sender_avatar TEXT,
    content TEXT,
    message_type TEXT,
    file_url TEXT,
    file_name TEXT,
    file_size INTEGER,
    reply_to_id UUID,
    reply_to_content TEXT,
    reply_to_sender_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    is_edited BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Validate user is participant
    IF NOT EXISTS (
        SELECT 1 FROM public.conversation_participants
        WHERE conversation_id = p_conversation_id AND user_id = p_user_id
    ) THEN
        RAISE EXCEPTION 'User is not a participant in this conversation';
    END IF;
    
    RETURN QUERY
    SELECT 
        m.id,
        m.conversation_id,
        m.sender_id,
        sender.full_name as sender_name,
        sender.avatar_url as sender_avatar,
        m.content,
        m.message_type,
        m.file_url,
        m.file_name,
        m.file_size,
        m.reply_to_id,
        reply_msg.content as reply_to_content,
        reply_sender.full_name as reply_to_sender_name,
        m.created_at,
        m.updated_at,
        m.is_edited
    FROM public.messages m
    INNER JOIN public.profiles sender ON sender.id = m.sender_id
    LEFT JOIN public.messages reply_msg ON reply_msg.id = m.reply_to_id
    LEFT JOIN public.profiles reply_sender ON reply_sender.id = reply_msg.sender_id
    WHERE m.conversation_id = p_conversation_id
    ORDER BY m.created_at ASC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$;
