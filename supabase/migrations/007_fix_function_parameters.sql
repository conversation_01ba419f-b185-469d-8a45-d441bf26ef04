-- Fix function parameter names to match the calling code

-- Function to get user friends
CREATE OR REPLACE FUNCTION public.get_user_friends(p_user_id UUID)
RETURNS TABLE (
    id UUID,
    full_name TEXT,
    avatar_url TEXT,
    is_online BOOLEAN,
    last_seen TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.full_name,
        p.avatar_url,
        p.is_online,
        p.last_seen
    FROM public.profiles p
    INNER JOIN public.friendships f ON (
        (f.requester_id = p_user_id AND f.addressee_id = p.id) OR
        (f.addressee_id = p_user_id AND f.requester_id = p.id)
    )
    WHERE f.status = 'accepted';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user conversations with latest message
CREATE OR REPLACE FUNCTION public.get_user_conversations(p_user_id UUID)
RETURNS TABLE (
    conversation_id UUID,
    conversation_type TEXT,
    conversation_name TEXT,
    other_user_id UUID,
    other_user_name TEXT,
    other_user_avatar TEXT,
    latest_message TEXT,
    latest_message_time TIMESTAMP WITH TIME ZONE,
    unread_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id as conversation_id,
        c.type as conversation_type,
        CASE
            WHEN c.type = 'group' THEN c.name
            ELSE (
                SELECT p.full_name
                FROM public.profiles p
                INNER JOIN public.conversation_participants cp ON cp.user_id = p.id
                WHERE cp.conversation_id = c.id AND cp.user_id != p_user_id
                LIMIT 1
            )
        END as conversation_name,
        CASE
            WHEN c.type = 'direct' THEN (
                SELECT p.id
                FROM public.profiles p
                INNER JOIN public.conversation_participants cp ON cp.user_id = p.id
                WHERE cp.conversation_id = c.id AND cp.user_id != p_user_id
                LIMIT 1
            )
            ELSE NULL
        END as other_user_id,
        CASE
            WHEN c.type = 'direct' THEN (
                SELECT p.full_name
                FROM public.profiles p
                INNER JOIN public.conversation_participants cp ON cp.user_id = p.id
                WHERE cp.conversation_id = c.id AND cp.user_id != p_user_id
                LIMIT 1
            )
            ELSE NULL
        END as other_user_name,
        CASE 
            WHEN c.type = 'direct' THEN (
                SELECT p.avatar_url 
                FROM public.profiles p
                INNER JOIN public.conversation_participants cp ON cp.user_id = p.id
                WHERE cp.conversation_id = c.id AND cp.user_id != p_user_id
                LIMIT 1
            )
            ELSE c.avatar_url
        END as other_user_avatar,
        (
            SELECT m.content 
            FROM public.messages m 
            WHERE m.conversation_id = c.id 
            ORDER BY m.created_at DESC 
            LIMIT 1
        ) as latest_message,
        (
            SELECT m.created_at 
            FROM public.messages m 
            WHERE m.conversation_id = c.id 
            ORDER BY m.created_at DESC 
            LIMIT 1
        ) as latest_message_time,
        0::BIGINT as unread_count -- TODO: Implement read receipts
    FROM public.conversations c
    INNER JOIN public.conversation_participants cp ON cp.conversation_id = c.id
    WHERE cp.user_id = p_user_id
    ORDER BY latest_message_time DESC NULLS LAST;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
