-- Create AI tutor logs table for analytics
CREATE TABLE IF NOT EXISTS ai_tutor_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    subject TEXT NOT NULL,
    message_count INTEGER NOT NULL DEFAULT 1,
    response_length INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS ai_tutor_logs_user_id_idx ON ai_tutor_logs(user_id);
CREATE INDEX IF NOT EXISTS ai_tutor_logs_created_at_idx ON ai_tutor_logs(created_at);
CREATE INDEX IF NOT EXISTS ai_tutor_logs_subject_idx ON ai_tutor_logs(subject);

-- Enable RLS
ALTER TABLE ai_tutor_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own AI tutor logs" ON ai_tutor_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own AI tutor logs" ON ai_tutor_logs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Grant permissions
GRANT ALL ON ai_tutor_logs TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;
