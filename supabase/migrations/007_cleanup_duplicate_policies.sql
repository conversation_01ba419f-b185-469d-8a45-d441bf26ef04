-- Clean up duplicate RLS policies for messaging tables

-- Drop all existing policies for conversations table
DROP POLICY IF EXISTS "Users can view conversations they participate in" ON public.conversations;
DROP POLICY IF EXISTS "Users can create conversations" ON public.conversations;
DROP POLICY IF EXISTS "Conversation creators can update conversations" ON public.conversations;
DROP POLICY IF EXISTS "Conversation creators can delete conversations" ON public.conversations;
DROP POLICY IF EXISTS "Conversation admins can update conversations" ON public.conversations;

-- Drop all existing policies for messages table
DROP POLICY IF EXISTS "Users can view messages in their conversations" ON public.messages;
DROP POLICY IF EXISTS "Users can send messages to their conversations" ON public.messages;
DROP POLICY IF EXISTS "Users can update their own messages" ON public.messages;
DROP POLICY IF EXISTS "Users can delete their own messages" ON public.messages;

-- Drop all existing policies for conversation_participants table
DROP POLICY IF EXISTS "Users can view participants of their conversations" ON public.conversation_participants;
DROP POLICY IF EXISTS "Users can join conversations" ON public.conversation_participants;
DROP POLICY IF EXISTS "select_own_participants" ON public.conversation_participants;
DROP POLICY IF EXISTS "insert_own_participants" ON public.conversation_participants;
DROP POLICY IF EXISTS "update_own_participants" ON public.conversation_participants;
DROP POLICY IF EXISTS "delete_own_participants" ON public.conversation_participants;

-- Create clean, non-conflicting policies for conversations
CREATE POLICY "conversations_select_policy" ON public.conversations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.conversation_participants 
            WHERE conversation_id = id AND user_id = auth.uid()
        )
    );

CREATE POLICY "conversations_insert_policy" ON public.conversations
    FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "conversations_update_policy" ON public.conversations
    FOR UPDATE USING (auth.uid() = created_by);

CREATE POLICY "conversations_delete_policy" ON public.conversations
    FOR DELETE USING (auth.uid() = created_by);

-- Create clean policies for messages
CREATE POLICY "messages_select_policy" ON public.messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.conversation_participants 
            WHERE conversation_id = messages.conversation_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "messages_insert_policy" ON public.messages
    FOR INSERT WITH CHECK (
        auth.uid() = sender_id AND
        EXISTS (
            SELECT 1 FROM public.conversation_participants 
            WHERE conversation_id = messages.conversation_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "messages_update_policy" ON public.messages
    FOR UPDATE USING (auth.uid() = sender_id);

CREATE POLICY "messages_delete_policy" ON public.messages
    FOR DELETE USING (auth.uid() = sender_id);

-- Create clean policies for conversation_participants
CREATE POLICY "participants_select_policy" ON public.conversation_participants
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.conversation_participants cp2
            WHERE cp2.conversation_id = conversation_id AND cp2.user_id = auth.uid()
        )
    );

CREATE POLICY "participants_insert_policy" ON public.conversation_participants
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "participants_update_policy" ON public.conversation_participants
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "participants_delete_policy" ON public.conversation_participants
    FOR DELETE USING (auth.uid() = user_id);
