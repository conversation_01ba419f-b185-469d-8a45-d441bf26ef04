
-- Drop all existing policies for study_groups and study_group_members (with <PERSON>SCAD<PERSON> to handle dependencies)
DO $$ 
BEGIN
    -- Drop study_groups policies
    DROP POLICY IF EXISTS "Allow authenticated users to view public groups" ON public.study_groups;
    DROP POLICY IF EXISTS "Allow users to view their own groups" ON public.study_groups;
    DROP POLICY IF EXISTS "Allow authenticated users to create groups" ON public.study_groups;
    DROP POLICY IF EXISTS "Allow creators to update their groups" ON public.study_groups;
    DROP POLICY IF EXISTS "Allow creators to delete their groups" ON public.study_groups;
    DROP POLICY IF EXISTS "Public groups viewable by all" ON public.study_groups;
    DROP POLICY IF EXISTS "Creators can view their groups" ON public.study_groups;
    DROP POLICY IF EXISTS "Users can create groups" ON public.study_groups;
    DROP POLICY IF EXISTS "Creators can update groups" ON public.study_groups;
    DROP POLICY IF EXISTS "Creators can delete groups" ON public.study_groups;
    
    -- Drop study_group_members policies
    DROP POLICY IF EXISTS "Allow users to view their own memberships" ON public.study_group_members;
    DROP POLICY IF EXISTS "Allow authenticated users to join groups" ON public.study_group_members;
    DROP POLICY IF EXISTS "Allow users to leave groups" ON public.study_group_members;
    DROP POLICY IF EXISTS "Users can view own memberships" ON public.study_group_members;
    DROP POLICY IF EXISTS "View memberships in public groups" ON public.study_group_members;
    DROP POLICY IF EXISTS "Creators can view group memberships" ON public.study_group_members;
    DROP POLICY IF EXISTS "Users can join groups" ON public.study_group_members;
    DROP POLICY IF EXISTS "Users can leave groups" ON public.study_group_members;
    DROP POLICY IF EXISTS "Creators can manage memberships" ON public.study_group_members;
END $$;

-- Create security definer functions to prevent infinite recursion
CREATE OR REPLACE FUNCTION public.is_group_creator(group_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.study_groups 
    WHERE id = group_id AND creator_id = auth.uid()
  );
$$;

CREATE OR REPLACE FUNCTION public.is_group_member(group_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.study_group_members 
    WHERE group_id = group_id AND user_id = auth.uid()
  );
$$;

-- Create new, simplified RLS policies for study_groups
CREATE POLICY "study_groups_select_public" ON public.study_groups
    FOR SELECT USING (
        auth.role() = 'authenticated' AND privacy = 'public'
    );

CREATE POLICY "study_groups_select_own" ON public.study_groups
    FOR SELECT USING (
        auth.role() = 'authenticated' AND creator_id = auth.uid()
    );

CREATE POLICY "study_groups_insert" ON public.study_groups
    FOR INSERT WITH CHECK (
        auth.role() = 'authenticated' AND creator_id = auth.uid()
    );

CREATE POLICY "study_groups_update" ON public.study_groups
    FOR UPDATE USING (
        auth.role() = 'authenticated' AND creator_id = auth.uid()
    );

CREATE POLICY "study_groups_delete" ON public.study_groups
    FOR DELETE USING (
        auth.role() = 'authenticated' AND creator_id = auth.uid()
    );

-- Create new, simplified RLS policies for study_group_members
CREATE POLICY "study_group_members_select" ON public.study_group_members
    FOR SELECT USING (
        auth.role() = 'authenticated' AND user_id = auth.uid()
    );

CREATE POLICY "study_group_members_insert" ON public.study_group_members
    FOR INSERT WITH CHECK (
        auth.role() = 'authenticated' AND user_id = auth.uid()
    );

CREATE POLICY "study_group_members_delete" ON public.study_group_members
    FOR DELETE USING (
        auth.role() = 'authenticated' AND user_id = auth.uid()
    );

-- Ensure the trigger for member count updates exists and works correctly
DROP TRIGGER IF EXISTS study_group_member_count_trigger ON public.study_group_members;

CREATE TRIGGER study_group_member_count_trigger
    AFTER INSERT OR DELETE ON public.study_group_members
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_study_group_member_count();
