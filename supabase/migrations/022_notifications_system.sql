-- Create comprehensive notifications system
-- This migration creates tables and functions for managing notifications

-- Create notifications table
CREATE TABLE public.notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    type TEXT NOT NULL CHECK (type IN (
        'friend_request', 
        'friend_accepted', 
        'message', 
        'study_group_invite',
        'study_group_post',
        'note_shared',
        'system'
    )),
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}',
    read BOOLEAN DEFAULT FALSE,
    action_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX idx_notifications_read ON public.notifications(read);
CREATE INDEX idx_notifications_type ON public.notifications(type);
CREATE INDEX idx_notifications_created_at ON public.notifications(created_at DESC);
CREATE INDEX idx_notifications_user_unread ON public.notifications(user_id, read) WHERE read = FALSE;

-- Enable RLS
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own notifications"
    ON public.notifications FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications"
    ON public.notifications FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "System can create notifications"
    ON public.notifications FOR INSERT
    WITH CHECK (true);

-- Function to create a notification
CREATE OR REPLACE FUNCTION create_notification(
    p_user_id UUID,
    p_type TEXT,
    p_title TEXT,
    p_message TEXT,
    p_data JSONB DEFAULT '{}',
    p_action_url TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO public.notifications (
        user_id,
        type,
        title,
        message,
        data,
        action_url
    )
    VALUES (
        p_user_id,
        p_type,
        p_title,
        p_message,
        p_data,
        p_action_url
    )
    RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$;

-- Function to mark notification as read
CREATE OR REPLACE FUNCTION mark_notification_read(
    p_notification_id UUID,
    p_user_id UUID DEFAULT auth.uid()
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE public.notifications
    SET 
        read = TRUE,
        updated_at = NOW()
    WHERE id = p_notification_id AND user_id = p_user_id;
END;
$$;

-- Function to mark all notifications as read for a user
CREATE OR REPLACE FUNCTION mark_all_notifications_read(
    p_user_id UUID DEFAULT auth.uid()
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE public.notifications
    SET 
        read = TRUE,
        updated_at = NOW()
    WHERE user_id = p_user_id AND read = FALSE;
END;
$$;

-- Function to get user notifications with pagination
CREATE OR REPLACE FUNCTION get_user_notifications(
    p_user_id UUID DEFAULT auth.uid(),
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0,
    p_unread_only BOOLEAN DEFAULT FALSE
)
RETURNS TABLE (
    id UUID,
    type TEXT,
    title TEXT,
    message TEXT,
    data JSONB,
    read BOOLEAN,
    action_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        n.id,
        n.type,
        n.title,
        n.message,
        n.data,
        n.read,
        n.action_url,
        n.created_at,
        n.updated_at
    FROM public.notifications n
    WHERE n.user_id = p_user_id
    AND (NOT p_unread_only OR n.read = FALSE)
    ORDER BY n.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$;

-- Function to get unread notification count
CREATE OR REPLACE FUNCTION get_unread_notification_count(
    p_user_id UUID DEFAULT auth.uid()
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    unread_count INTEGER;
BEGIN
    SELECT COUNT(*)::INTEGER INTO unread_count
    FROM public.notifications
    WHERE user_id = p_user_id AND read = FALSE;
    
    RETURN COALESCE(unread_count, 0);
END;
$$;

-- Trigger to automatically create notifications for friend requests
CREATE OR REPLACE FUNCTION notify_friend_request()
RETURNS TRIGGER AS $$
DECLARE
    requester_name TEXT;
BEGIN
    -- Get requester's name
    SELECT full_name INTO requester_name
    FROM public.profiles
    WHERE id = NEW.requester_id;
    
    -- Create notification for the addressee
    PERFORM create_notification(
        NEW.addressee_id,
        'friend_request',
        'New Friend Request',
        requester_name || ' sent you a friend request',
        jsonb_build_object('requester_id', NEW.requester_id, 'friendship_id', NEW.id),
        '/profile?tab=Requests'
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for friend requests
DROP TRIGGER IF EXISTS trigger_notify_friend_request ON public.friendships;
CREATE TRIGGER trigger_notify_friend_request
    AFTER INSERT ON public.friendships
    FOR EACH ROW
    WHEN (NEW.status = 'pending')
    EXECUTE FUNCTION notify_friend_request();

-- Trigger to notify when friend request is accepted
CREATE OR REPLACE FUNCTION notify_friend_accepted()
RETURNS TRIGGER AS $$
DECLARE
    accepter_name TEXT;
BEGIN
    -- Only notify when status changes from pending to accepted
    IF OLD.status = 'pending' AND NEW.status = 'accepted' THEN
        -- Get accepter's name
        SELECT full_name INTO accepter_name
        FROM public.profiles
        WHERE id = NEW.addressee_id;
        
        -- Create notification for the requester
        PERFORM create_notification(
            NEW.requester_id,
            'friend_accepted',
            'Friend Request Accepted',
            accepter_name || ' accepted your friend request',
            jsonb_build_object('friend_id', NEW.addressee_id, 'friendship_id', NEW.id),
            '/profile?tab=Friends'
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for friend acceptance
DROP TRIGGER IF EXISTS trigger_notify_friend_accepted ON public.friendships;
CREATE TRIGGER trigger_notify_friend_accepted
    AFTER UPDATE ON public.friendships
    FOR EACH ROW
    EXECUTE FUNCTION notify_friend_accepted();

-- Trigger to notify about new messages
CREATE OR REPLACE FUNCTION notify_new_message()
RETURNS TRIGGER AS $$
DECLARE
    sender_name TEXT;
    recipient_id UUID;
    conversation_name TEXT;
BEGIN
    -- Get sender's name
    SELECT full_name INTO sender_name
    FROM public.profiles
    WHERE id = NEW.sender_id;
    
    -- Get recipient(s) from conversation participants (excluding sender)
    FOR recipient_id IN 
        SELECT cp.user_id
        FROM public.conversation_participants cp
        WHERE cp.conversation_id = NEW.conversation_id
        AND cp.user_id != NEW.sender_id
    LOOP
        -- Get conversation name for notification
        SELECT 
            CASE 
                WHEN c.type = 'group' THEN c.name
                ELSE sender_name
            END INTO conversation_name
        FROM public.conversations c
        WHERE c.id = NEW.conversation_id;
        
        -- Create notification
        PERFORM create_notification(
            recipient_id,
            'message',
            'New Message',
            'New message from ' || sender_name,
            jsonb_build_object(
                'conversation_id', NEW.conversation_id,
                'message_id', NEW.id,
                'sender_id', NEW.sender_id
            ),
            '/profile?tab=Inbox'
        );
    END LOOP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for new messages
DROP TRIGGER IF EXISTS trigger_notify_new_message ON public.messages;
CREATE TRIGGER trigger_notify_new_message
    AFTER INSERT ON public.messages
    FOR EACH ROW
    EXECUTE FUNCTION notify_new_message();
