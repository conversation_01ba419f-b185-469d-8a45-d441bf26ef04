-- Create AI Notes related tables

-- Folders table for organizing notes
CREATE TABLE IF NOT EXISTS note_folders (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  parent_folder_id UUID REFERENCES note_folders(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, name, parent_folder_id)
);

-- AI generated notes table
CREATE TABLE IF NOT EXISTS ai_notes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  folder_id UUID REFERENCES note_folders(id) ON DELETE SET NULL,
  title TEXT NOT NULL,
  subject TEXT NOT NULL,
  topic TEXT NOT NULL,
  original_content TEXT NOT NULL, -- AI generated content
  edited_content TEXT, -- User edited content
  content_format TEXT DEFAULT 'markdown' CHECK (content_format IN ('markdown', 'html', 'plain')),
  generation_prompt TEXT, -- Store the prompt used for generation
  ai_model TEXT DEFAULT 'gpt-3.5-turbo',
  word_count INTEGER,
  is_favorite BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Notes generation history for analytics
CREATE TABLE IF NOT EXISTS ai_notes_generation_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  note_id UUID REFERENCES ai_notes(id) ON DELETE SET NULL,
  subject TEXT NOT NULL,
  topic TEXT NOT NULL,
  prompt_used TEXT NOT NULL,
  ai_model TEXT NOT NULL,
  tokens_used INTEGER,
  generation_time_ms INTEGER,
  success BOOLEAN DEFAULT TRUE,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User preferences for AI Notes
CREATE TABLE IF NOT EXISTS ai_notes_preferences (
  user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  default_ai_model TEXT DEFAULT 'gpt-3.5-turbo',
  preferred_note_format TEXT DEFAULT 'markdown',
  auto_save_enabled BOOLEAN DEFAULT TRUE,
  default_note_length TEXT DEFAULT 'medium' CHECK (default_note_length IN ('short', 'medium', 'long', 'detailed')),
  include_examples BOOLEAN DEFAULT TRUE,
  include_key_points BOOLEAN DEFAULT TRUE,
  include_summary BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_note_folders_user_id ON note_folders(user_id);
CREATE INDEX IF NOT EXISTS idx_note_folders_parent ON note_folders(parent_folder_id);
CREATE INDEX IF NOT EXISTS idx_ai_notes_user_id ON ai_notes(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_notes_folder_id ON ai_notes(folder_id);
CREATE INDEX IF NOT EXISTS idx_ai_notes_subject ON ai_notes(subject);
CREATE INDEX IF NOT EXISTS idx_ai_notes_created_at ON ai_notes(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ai_notes_last_accessed ON ai_notes(last_accessed_at DESC);
CREATE INDEX IF NOT EXISTS idx_ai_notes_generation_log_user_id ON ai_notes_generation_log(user_id);

-- Enable RLS (Row Level Security)
ALTER TABLE note_folders ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_notes_generation_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_notes_preferences ENABLE ROW LEVEL SECURITY;

-- RLS Policies for note_folders
CREATE POLICY "Users can view their own folders" ON note_folders
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own folders" ON note_folders
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own folders" ON note_folders
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own folders" ON note_folders
  FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for ai_notes
CREATE POLICY "Users can view their own notes" ON ai_notes
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own notes" ON ai_notes
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own notes" ON ai_notes
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own notes" ON ai_notes
  FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for ai_notes_generation_log
CREATE POLICY "Users can view their own generation log" ON ai_notes_generation_log
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own generation log" ON ai_notes_generation_log
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for ai_notes_preferences
CREATE POLICY "Users can view their own preferences" ON ai_notes_preferences
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own preferences" ON ai_notes_preferences
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own preferences" ON ai_notes_preferences
  FOR UPDATE USING (auth.uid() = user_id);

-- Functions to update timestamps
CREATE OR REPLACE FUNCTION update_note_folders_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_ai_notes_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_ai_notes_preferences_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER update_note_folders_updated_at
  BEFORE UPDATE ON note_folders
  FOR EACH ROW
  EXECUTE FUNCTION update_note_folders_updated_at();

CREATE TRIGGER update_ai_notes_updated_at
  BEFORE UPDATE ON ai_notes
  FOR EACH ROW
  EXECUTE FUNCTION update_ai_notes_updated_at();

CREATE TRIGGER update_ai_notes_preferences_updated_at
  BEFORE UPDATE ON ai_notes_preferences
  FOR EACH ROW
  EXECUTE FUNCTION update_ai_notes_preferences_updated_at();
