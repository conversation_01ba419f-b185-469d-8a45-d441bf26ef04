-- Create subscription system with Paystack integration

-- Create subscription plans table
CREATE TABLE IF NOT EXISTS public.subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price_cents INTEGER NOT NULL DEFAULT 0,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    interval_type VARCHAR(20) NOT NULL DEFAULT 'daily', -- daily, monthly, yearly
    trial_period_minutes INTEGER DEFAULT 1, -- Trial period in minutes (1 minute for testing)
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user subscriptions table
CREATE TABLE IF NOT EXISTS public.user_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    plan_id UUID REFERENCES public.subscription_plans(id),
    paystack_customer_code VARCHAR(100),
    paystack_subscription_code VARCHAR(100),
    paystack_email_token VARCHAR(100),
    status VARCHAR(20) DEFAULT 'inactive', -- inactive, active, cancelled, past_due, trialing
    trial_ends_at TIMESTAMP WITH TIME ZONE,
    current_period_start TIMESTAMP WITH TIME ZONE,
    current_period_end TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create payment transactions table
CREATE TABLE IF NOT EXISTS public.payment_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    subscription_id UUID REFERENCES public.user_subscriptions(id),
    paystack_reference VARCHAR(100) UNIQUE,
    paystack_transaction_id VARCHAR(100),
    amount_cents INTEGER NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    status VARCHAR(20) NOT NULL, -- pending, success, failed, cancelled
    transaction_type VARCHAR(20) NOT NULL, -- subscription, one_time
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_transactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for subscription_plans (publicly readable)
CREATE POLICY "Anyone can view active subscription plans" ON public.subscription_plans
    FOR SELECT USING (is_active = true);

-- RLS Policies for user_subscriptions
CREATE POLICY "Users can view their own subscriptions" ON public.user_subscriptions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own subscriptions" ON public.user_subscriptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own subscriptions" ON public.user_subscriptions
    FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for payment_transactions
CREATE POLICY "Users can view their own transactions" ON public.payment_transactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own transactions" ON public.payment_transactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Insert default subscription plan ($0.10 daily)
INSERT INTO public.subscription_plans (name, description, price_cents, currency, interval_type, trial_period_minutes, is_active)
VALUES ('StudyFam Daily', 'Daily access to all StudyFam features', 10, 'USD', 'daily', 1, true)
ON CONFLICT DO NOTHING;

-- Triggers for updated_at
CREATE OR REPLACE FUNCTION public.handle_subscription_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER handle_subscription_plans_updated_at
    BEFORE UPDATE ON public.subscription_plans
    FOR EACH ROW EXECUTE FUNCTION public.handle_subscription_updated_at();

CREATE TRIGGER handle_user_subscriptions_updated_at
    BEFORE UPDATE ON public.user_subscriptions
    FOR EACH ROW EXECUTE FUNCTION public.handle_subscription_updated_at();

CREATE TRIGGER handle_payment_transactions_updated_at
    BEFORE UPDATE ON public.payment_transactions
    FOR EACH ROW EXECUTE FUNCTION public.handle_subscription_updated_at();

-- Function to check if user has active subscription
CREATE OR REPLACE FUNCTION public.user_has_active_subscription(p_user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.user_subscriptions
        WHERE user_id = p_user_id
        AND status IN ('active', 'trialing')
        AND (trial_ends_at IS NULL OR trial_ends_at > NOW())
        AND (current_period_end IS NULL OR current_period_end > NOW())
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user subscription status
CREATE OR REPLACE FUNCTION public.get_user_subscription_status(p_user_id UUID)
RETURNS TABLE (
    subscription_id UUID,
    plan_name VARCHAR,
    status VARCHAR,
    is_trial BOOLEAN,
    trial_ends_at TIMESTAMP WITH TIME ZONE,
    current_period_end TIMESTAMP WITH TIME ZONE,
    minutes_remaining INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        us.id as subscription_id,
        sp.name as plan_name,
        us.status,
        (us.trial_ends_at IS NOT NULL AND us.trial_ends_at > NOW()) as is_trial,
        us.trial_ends_at,
        us.current_period_end,
        CASE 
            WHEN us.trial_ends_at IS NOT NULL AND us.trial_ends_at > NOW() THEN
                EXTRACT(EPOCH FROM us.trial_ends_at - NOW())::INTEGER / 60
            WHEN us.current_period_end IS NOT NULL AND us.current_period_end > NOW() THEN
                EXTRACT(EPOCH FROM us.current_period_end - NOW())::INTEGER / 60
            ELSE 0
        END as minutes_remaining
    FROM public.user_subscriptions us
    JOIN public.subscription_plans sp ON us.plan_id = sp.id
    WHERE us.user_id = p_user_id
    AND us.status IN ('active', 'trialing')
    ORDER BY us.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to start free trial for new users
CREATE OR REPLACE FUNCTION public.start_free_trial(p_user_id UUID, p_plan_id UUID)
RETURNS UUID AS $$
DECLARE
    v_subscription_id UUID;
BEGIN
    -- Check if user already has a subscription
    IF EXISTS (
        SELECT 1 FROM public.user_subscriptions 
        WHERE user_id = p_user_id AND status IN ('active', 'trialing')
    ) THEN
        RAISE EXCEPTION 'User already has an active subscription';
    END IF;
    
    -- Create trial subscription
    INSERT INTO public.user_subscriptions (
        user_id, 
        plan_id, 
        status, 
        trial_ends_at,
        current_period_start,
        created_at,
        updated_at
    ) VALUES (
        p_user_id,
        p_plan_id,
        'trialing',
        NOW() + INTERVAL '1 minute',
        NOW(),
        NOW(),
        NOW()
    ) RETURNING id INTO v_subscription_id;
    
    RETURN v_subscription_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update handle_new_user function to start trial automatically
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    default_plan_id UUID;
BEGIN
    -- Create user profile
    INSERT INTO public.profiles (id, email, full_name, country, course, institute)
    VALUES (
        NEW.id,
        NEW.email,
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'country',
        NEW.raw_user_meta_data->>'course',
        NEW.raw_user_meta_data->>'institute'
    );
    
    -- Get the default subscription plan
    SELECT id INTO default_plan_id 
    FROM public.subscription_plans 
    WHERE interval_type = 'daily' AND is_active = true 
    LIMIT 1;
    
    -- Start 1-minute free trial if plan exists
    IF default_plan_id IS NOT NULL THEN
        PERFORM public.start_free_trial(NEW.id, default_plan_id);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;