-- Enhanced presence tracking with automatic last_seen updates

-- Function to automatically update last_seen when user goes offline
CREATE OR REPLACE FUNCTION public.update_last_seen_on_offline()
RETURNS TRIGGER AS $$
BEGIN
    -- If user is going from online to offline, update last_seen
    IF OLD.is_online = true AND NEW.is_online = false THEN
        NEW.last_seen = NOW();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic last_seen updates
DROP TRIGGER IF EXISTS trigger_update_last_seen_on_offline ON public.profiles;
CREATE TRIGGER trigger_update_last_seen_on_offline
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION public.update_last_seen_on_offline();

-- Function to clean up stale online statuses (users who haven't been active for 5+ minutes)
CREATE OR REPLACE FUNCTION public.cleanup_stale_online_status()
RETURNS void AS $$
BEGIN
    UPDATE public.profiles 
    SET 
        is_online = false,
        last_seen = NOW()
    WHERE 
        is_online = true 
        AND last_seen < NOW() - INTERVAL '5 minutes';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get enhanced user presence info
CREATE OR REPLACE FUNCTION public.get_user_presence(p_user_id UUID)
RETURNS TABLE (
    id UUID,
    full_name TEXT,
    avatar_url TEXT,
    is_online BOOLEAN,
    last_seen TIMESTAMP WITH TIME ZONE,
    status_text TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.full_name,
        p.avatar_url,
        p.is_online,
        p.last_seen,
        CASE 
            WHEN p.is_online THEN 'Online'
            WHEN p.last_seen > NOW() - INTERVAL '5 minutes' THEN 'Just left'
            WHEN p.last_seen > NOW() - INTERVAL '1 hour' THEN 
                EXTRACT(EPOCH FROM (NOW() - p.last_seen))::INTEGER / 60 || 'm ago'
            WHEN p.last_seen > NOW() - INTERVAL '1 day' THEN 
                EXTRACT(EPOCH FROM (NOW() - p.last_seen))::INTEGER / 3600 || 'h ago'
            WHEN p.last_seen > NOW() - INTERVAL '7 days' THEN 
                EXTRACT(EPOCH FROM (NOW() - p.last_seen))::INTEGER / 86400 || 'd ago'
            ELSE 'Long time ago'
        END as status_text
    FROM public.profiles p
    WHERE p.id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the existing get_user_friends function to include better presence info
CREATE OR REPLACE FUNCTION public.get_user_friends(p_user_id UUID)
RETURNS TABLE (
    id UUID,
    full_name TEXT,
    avatar_url TEXT,
    is_online BOOLEAN,
    last_seen TIMESTAMP WITH TIME ZONE,
    status_text TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.full_name,
        p.avatar_url,
        p.is_online,
        p.last_seen,
        CASE 
            WHEN p.is_online THEN 'Online'
            WHEN p.last_seen > NOW() - INTERVAL '5 minutes' THEN 'Just left'
            WHEN p.last_seen > NOW() - INTERVAL '1 hour' THEN 
                EXTRACT(EPOCH FROM (NOW() - p.last_seen))::INTEGER / 60 || 'm ago'
            WHEN p.last_seen > NOW() - INTERVAL '1 day' THEN 
                EXTRACT(EPOCH FROM (NOW() - p.last_seen))::INTEGER / 3600 || 'h ago'
            WHEN p.last_seen > NOW() - INTERVAL '7 days' THEN 
                EXTRACT(EPOCH FROM (NOW() - p.last_seen))::INTEGER / 86400 || 'd ago'
            ELSE 'Long time ago'
        END as status_text
    FROM public.profiles p
    INNER JOIN public.friendships f ON (
        (f.requester_id = p_user_id AND f.addressee_id = p.id) OR
        (f.addressee_id = p_user_id AND f.requester_id = p.id)
    )
    WHERE f.status = 'accepted'
    ORDER BY p.is_online DESC, p.last_seen DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
