-- Add timetable notification triggers and functions
-- This migration adds automatic notification creation for timetable events

-- Function to create timetable notifications
CREATE OR REPLACE FUNCTION create_timetable_notification(
    p_user_id UUID,
    p_session_id UUID,
    p_notification_type TEXT,
    p_session_title TEXT,
    p_subject_name TEXT,
    p_start_time TEXT,
    p_end_time TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    notification_id UUID;
    notification_title TEXT;
    notification_message TEXT;
BEGIN
    -- Generate notification title and message based on type
    CASE p_notification_type
        WHEN 'upcoming' THEN
            notification_title := '📚 Upcoming Study Session';
            notification_message := p_subject_name || ': ' || p_session_title || ' starts in 15 minutes';
        WHEN 'starting' THEN
            notification_title := '🚀 Study Session Starting';
            notification_message := p_subject_name || ': ' || p_session_title || ' is starting now!';
        WHEN 'overdue' THEN
            notification_title := '⏰ Study Session Overdue';
            notification_message := p_subject_name || ': ' || p_session_title || ' session has ended. Mark it as complete?';
        ELSE
            notification_title := '📅 Timetable Update';
            notification_message := p_subject_name || ': ' || p_session_title;
    END CASE;

    -- Create the notification
    INSERT INTO public.notifications (
        user_id,
        type,
        title,
        message,
        data,
        action_url
    )
    VALUES (
        p_user_id,
        'system',
        notification_title,
        notification_message,
        jsonb_build_object(
            'session_id', p_session_id,
            'notification_type', 'timetable',
            'session_type', p_notification_type,
            'start_time', p_start_time,
            'end_time', p_end_time,
            'subject_name', p_subject_name,
            'session_title', p_session_title
        ),
        '/reading-timetable'
    )
    RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$;

-- Function to notify about session completion
CREATE OR REPLACE FUNCTION notify_session_completion()
RETURNS TRIGGER AS $$
DECLARE
    session_record RECORD;
    subject_name TEXT;
BEGIN
    -- Only notify when status changes to 'completed'
    IF NEW.status = 'completed' AND (OLD.status IS NULL OR OLD.status != 'completed') THEN
        -- Get session details
        SELECT rs.*, rsub.name as subject_name
        INTO session_record
        FROM reading_sessions rs
        LEFT JOIN reading_subjects rsub ON rs.subject_id = rsub.id
        WHERE rs.id = NEW.session_id;
        
        IF session_record.id IS NOT NULL THEN
            -- Create completion notification
            PERFORM create_notification(
                NEW.user_id,
                'system',
                '✅ Study Session Completed',
                'Great job! You completed: ' || COALESCE(session_record.subject_name, 'Study Session') || ' - ' || session_record.title,
                jsonb_build_object(
                    'session_id', NEW.session_id,
                    'notification_type', 'timetable',
                    'session_type', 'completed',
                    'completion_percentage', NEW.completion_percentage
                ),
                '/reading-timetable'
            );
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for session completion notifications
DROP TRIGGER IF EXISTS trigger_notify_session_completion ON public.reading_progress;
CREATE TRIGGER trigger_notify_session_completion
    AFTER INSERT OR UPDATE ON public.reading_progress
    FOR EACH ROW
    EXECUTE FUNCTION notify_session_completion();

-- Function to notify about new session creation
CREATE OR REPLACE FUNCTION notify_session_creation()
RETURNS TRIGGER AS $$
DECLARE
    subject_name TEXT;
BEGIN
    -- Get subject name
    SELECT name INTO subject_name
    FROM reading_subjects
    WHERE id = NEW.subject_id;
    
    -- Create notification for new session
    PERFORM create_notification(
        NEW.user_id,
        'system',
        '📅 New Study Session Added',
        'New session scheduled: ' || COALESCE(subject_name, 'Study Session') || ' - ' || NEW.title || ' at ' || NEW.start_time,
        jsonb_build_object(
            'session_id', NEW.id,
            'notification_type', 'timetable',
            'session_type', 'created',
            'start_time', NEW.start_time,
            'end_time', NEW.end_time,
            'day_of_week', NEW.day_of_week
        ),
        '/reading-timetable'
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for new session notifications
DROP TRIGGER IF EXISTS trigger_notify_session_creation ON public.reading_sessions;
CREATE TRIGGER trigger_notify_session_creation
    AFTER INSERT ON public.reading_sessions
    FOR EACH ROW
    WHEN (NEW.is_active = true)
    EXECUTE FUNCTION notify_session_creation();

-- Function to notify about session updates
CREATE OR REPLACE FUNCTION notify_session_update()
RETURNS TRIGGER AS $$
DECLARE
    subject_name TEXT;
    change_details TEXT := '';
BEGIN
    -- Only notify for significant changes
    IF OLD.title != NEW.title OR 
       OLD.start_time != NEW.start_time OR 
       OLD.end_time != NEW.end_time OR
       OLD.day_of_week != NEW.day_of_week OR
       (OLD.is_active != NEW.is_active AND NEW.is_active = false) THEN
        
        -- Get subject name
        SELECT name INTO subject_name
        FROM reading_subjects
        WHERE id = NEW.subject_id;
        
        -- Build change details
        IF OLD.title != NEW.title THEN
            change_details := change_details || 'Title changed. ';
        END IF;
        
        IF OLD.start_time != NEW.start_time THEN
            change_details := change_details || 'Start time changed to ' || NEW.start_time || '. ';
        END IF;
        
        IF OLD.end_time != NEW.end_time THEN
            change_details := change_details || 'End time changed to ' || COALESCE(NEW.end_time, 'not set') || '. ';
        END IF;
        
        IF OLD.day_of_week != NEW.day_of_week THEN
            change_details := change_details || 'Day changed. ';
        END IF;
        
        IF OLD.is_active != NEW.is_active AND NEW.is_active = false THEN
            change_details := 'Session deactivated. ';
        END IF;
        
        -- Create notification for session update
        PERFORM create_notification(
            NEW.user_id,
            'system',
            CASE 
                WHEN NEW.is_active = false THEN '🚫 Study Session Deactivated'
                ELSE '📝 Study Session Updated'
            END,
            COALESCE(subject_name, 'Study Session') || ' - ' || NEW.title || ': ' || change_details,
            jsonb_build_object(
                'session_id', NEW.id,
                'notification_type', 'timetable',
                'session_type', CASE WHEN NEW.is_active = false THEN 'deactivated' ELSE 'updated' END,
                'changes', change_details,
                'start_time', NEW.start_time,
                'end_time', NEW.end_time
            ),
            '/reading-timetable'
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for session update notifications
DROP TRIGGER IF EXISTS trigger_notify_session_update ON public.reading_sessions;
CREATE TRIGGER trigger_notify_session_update
    AFTER UPDATE ON public.reading_sessions
    FOR EACH ROW
    EXECUTE FUNCTION notify_session_update();

-- Function to clean up old timetable notifications (older than 30 days)
CREATE OR REPLACE FUNCTION cleanup_old_timetable_notifications()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    DELETE FROM public.notifications
    WHERE type = 'system'
    AND data->>'notification_type' = 'timetable'
    AND created_at < NOW() - INTERVAL '30 days';
    
    -- Log cleanup
    RAISE NOTICE 'Cleaned up old timetable notifications';
END;
$$;

-- Create a scheduled job to clean up old notifications (if pg_cron is available)
-- This would typically be set up separately in production
-- SELECT cron.schedule('cleanup-timetable-notifications', '0 2 * * *', 'SELECT cleanup_old_timetable_notifications();');
