-- Add read receipts functionality for WhatsApp-like unread message counts

-- Create message_read_receipts table to track which messages have been read by which users
CREATE TABLE public.message_read_receipts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    message_id UUID REFERENCES public.messages(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(message_id, user_id)
);

-- Enable RLS on message_read_receipts
ALTER TABLE public.message_read_receipts ENABLE ROW LEVEL SECURITY;

-- RLS policies for message_read_receipts
CREATE POLICY "Users can view read receipts for their conversations" ON public.message_read_receipts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.messages m
            INNER JOIN public.conversation_participants cp ON cp.conversation_id = m.conversation_id
            WHERE m.id = message_id AND cp.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can mark messages as read" ON public.message_read_receipts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own read receipts" ON public.message_read_receipts
    FOR UPDATE USING (auth.uid() = user_id);

-- Function to mark messages as read
CREATE OR REPLACE FUNCTION public.mark_messages_as_read(p_conversation_id UUID, p_user_id UUID)
RETURNS VOID AS $$
BEGIN
    -- Insert read receipts for all unread messages in the conversation
    INSERT INTO public.message_read_receipts (message_id, user_id)
    SELECT m.id, p_user_id
    FROM public.messages m
    WHERE m.conversation_id = p_conversation_id
    AND m.sender_id != p_user_id  -- Don't mark own messages as read
    AND NOT EXISTS (
        SELECT 1 FROM public.message_read_receipts mrr
        WHERE mrr.message_id = m.id AND mrr.user_id = p_user_id
    )
    ON CONFLICT (message_id, user_id) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the get_user_conversations function to calculate real unread counts
CREATE OR REPLACE FUNCTION public.get_user_conversations(p_user_id UUID)
RETURNS TABLE (
    conversation_id UUID,
    conversation_type TEXT,
    conversation_name TEXT,
    other_user_id UUID,
    other_user_name TEXT,
    other_user_avatar TEXT,
    latest_message TEXT,
    latest_message_time TIMESTAMP WITH TIME ZONE,
    unread_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id as conversation_id,
        c.type as conversation_type,
        CASE
            WHEN c.type = 'group' THEN c.name
            ELSE (
                SELECT p.full_name
                FROM public.profiles p
                INNER JOIN public.conversation_participants cp ON cp.user_id = p.id
                WHERE cp.conversation_id = c.id AND cp.user_id != p_user_id
                LIMIT 1
            )
        END as conversation_name,
        (
            SELECT cp.user_id
            FROM public.conversation_participants cp
            WHERE cp.conversation_id = c.id AND cp.user_id != p_user_id
            LIMIT 1
        ) as other_user_id,
        CASE
            WHEN c.type = 'group' THEN c.name
            ELSE (
                SELECT p.full_name
                FROM public.profiles p
                INNER JOIN public.conversation_participants cp ON cp.user_id = p.id
                WHERE cp.conversation_id = c.id AND cp.user_id != p_user_id
                LIMIT 1
            )
        END as other_user_name,
        CASE
            WHEN c.type = 'group' THEN c.avatar_url
            ELSE (
                SELECT p.avatar_url
                FROM public.profiles p
                INNER JOIN public.conversation_participants cp ON cp.user_id = p.id
                WHERE cp.conversation_id = c.id AND cp.user_id != p_user_id
                LIMIT 1
            )
        END as other_user_avatar,
        (
            SELECT m.content 
            FROM public.messages m 
            WHERE m.conversation_id = c.id 
            ORDER BY m.created_at DESC 
            LIMIT 1
        ) as latest_message,
        (
            SELECT m.created_at 
            FROM public.messages m 
            WHERE m.conversation_id = c.id 
            ORDER BY m.created_at DESC 
            LIMIT 1
        ) as latest_message_time,
        (
            SELECT COUNT(*)
            FROM public.messages m
            WHERE m.conversation_id = c.id
            AND m.sender_id != p_user_id  -- Don't count own messages
            AND NOT EXISTS (
                SELECT 1 FROM public.message_read_receipts mrr
                WHERE mrr.message_id = m.id AND mrr.user_id = p_user_id
            )
        ) as unread_count
    FROM public.conversations c
    INNER JOIN public.conversation_participants cp ON cp.conversation_id = c.id
    WHERE cp.user_id = p_user_id
    ORDER BY latest_message_time DESC NULLS LAST;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_message_read_receipts_message_user ON public.message_read_receipts(message_id, user_id);
CREATE INDEX IF NOT EXISTS idx_message_read_receipts_user_id ON public.message_read_receipts(user_id);
CREATE INDEX IF NOT EXISTS idx_messages_conversation_sender ON public.messages(conversation_id, sender_id);
