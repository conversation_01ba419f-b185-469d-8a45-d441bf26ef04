
-- Create quizzes table for storing quiz metadata
CREATE TABLE public.quizzes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    subject TEXT,
    difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard', 'mixed')) DEFAULT 'medium',
    time_limit INTEGER, -- in minutes, null means no limit
    randomize_questions BOOLEAN DEFAULT false,
    show_results_immediately BOOLEAN DEFAULT true,
    is_public BOOLEAN DEFAULT false,
    created_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create quiz_questions table for storing individual questions
CREATE TABLE public.quiz_questions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    quiz_id UUID REFERENCES public.quizzes(id) ON DELETE CASCADE NOT NULL,
    question_text TEXT NOT NULL,
    question_type TEXT CHECK (question_type IN ('multiple_choice', 'true_false', 'short_answer')) NOT NULL,
    correct_answer TEXT NOT NULL,
    options JSONB, -- for multiple choice options
    points INTEGER DEFAULT 1,
    explanation TEXT,
    order_index INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create quiz_attempts table for tracking user quiz attempts
CREATE TABLE public.quiz_attempts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    quiz_id UUID REFERENCES public.quizzes(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    score INTEGER DEFAULT 0,
    total_questions INTEGER NOT NULL,
    time_taken INTEGER, -- in seconds
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    answers JSONB -- store user answers
);

-- Update past_papers table to add more metadata
ALTER TABLE public.past_papers ADD COLUMN IF NOT EXISTS exam_type TEXT;
ALTER TABLE public.past_papers ADD COLUMN IF NOT EXISTS has_answers BOOLEAN DEFAULT false;
ALTER TABLE public.past_papers ADD COLUMN IF NOT EXISTS has_marking_scheme BOOLEAN DEFAULT false;
ALTER TABLE public.past_papers ADD COLUMN IF NOT EXISTS tags TEXT[];

-- Enable RLS on new tables
ALTER TABLE public.quizzes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quiz_questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quiz_attempts ENABLE ROW LEVEL SECURITY;

-- RLS policies for quizzes
CREATE POLICY "Users can view public quizzes and their own quizzes" 
    ON public.quizzes FOR SELECT 
    USING (is_public = true OR created_by = auth.uid());

CREATE POLICY "Users can create their own quizzes" 
    ON public.quizzes FOR INSERT 
    WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can update their own quizzes" 
    ON public.quizzes FOR UPDATE 
    USING (created_by = auth.uid());

CREATE POLICY "Users can delete their own quizzes" 
    ON public.quizzes FOR DELETE 
    USING (created_by = auth.uid());

-- RLS policies for quiz_questions
CREATE POLICY "Users can view questions for accessible quizzes" 
    ON public.quiz_questions FOR SELECT 
    USING (
        EXISTS (
            SELECT 1 FROM public.quizzes q 
            WHERE q.id = quiz_id 
            AND (q.is_public = true OR q.created_by = auth.uid())
        )
    );

CREATE POLICY "Users can manage questions for their own quizzes" 
    ON public.quiz_questions FOR ALL 
    USING (
        EXISTS (
            SELECT 1 FROM public.quizzes q 
            WHERE q.id = quiz_id 
            AND q.created_by = auth.uid()
        )
    );

-- RLS policies for quiz_attempts
CREATE POLICY "Users can view their own quiz attempts" 
    ON public.quiz_attempts FOR SELECT 
    USING (user_id = auth.uid());

CREATE POLICY "Users can create their own quiz attempts" 
    ON public.quiz_attempts FOR INSERT 
    WITH CHECK (user_id = auth.uid());

-- RLS policies for past_papers (update existing)
DROP POLICY IF EXISTS "Enable read access for all users" ON public.past_papers;
CREATE POLICY "Users can view public past papers and their own uploads" 
    ON public.past_papers FOR SELECT 
    USING (is_public = true OR user_id = auth.uid());

CREATE POLICY "Users can upload past papers" 
    ON public.past_papers FOR INSERT 
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own past papers" 
    ON public.past_papers FOR UPDATE 
    USING (user_id = auth.uid());

-- Functions for quiz operations
CREATE OR REPLACE FUNCTION public.get_user_quizzes(p_user_id UUID)
RETURNS TABLE(
    id UUID,
    title TEXT,
    description TEXT,
    subject TEXT,
    difficulty TEXT,
    question_count BIGINT,
    attempt_count BIGINT,
    is_public BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        q.id,
        q.title,
        q.description,
        q.subject,
        q.difficulty,
        COUNT(qq.id) as question_count,
        COUNT(qa.id) as attempt_count,
        q.is_public,
        q.created_at
    FROM public.quizzes q
    LEFT JOIN public.quiz_questions qq ON q.id = qq.quiz_id
    LEFT JOIN public.quiz_attempts qa ON q.id = qa.quiz_id
    WHERE q.created_by = p_user_id
    GROUP BY q.id, q.title, q.description, q.subject, q.difficulty, q.is_public, q.created_at
    ORDER BY q.created_at DESC;
END;
$$;

CREATE OR REPLACE FUNCTION public.get_public_quizzes(p_limit INTEGER DEFAULT 20)
RETURNS TABLE(
    id UUID,
    title TEXT,
    description TEXT,
    subject TEXT,
    difficulty TEXT,
    question_count BIGINT,
    attempt_count BIGINT,
    creator_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        q.id,
        q.title,
        q.description,
        q.subject,
        q.difficulty,
        COUNT(qq.id) as question_count,
        COUNT(qa.id) as attempt_count,
        p.full_name as creator_name,
        q.created_at
    FROM public.quizzes q
    LEFT JOIN public.quiz_questions qq ON q.id = qq.quiz_id
    LEFT JOIN public.quiz_attempts qa ON q.id = qa.quiz_id
    LEFT JOIN public.profiles p ON q.created_by = p.id
    WHERE q.is_public = true
    GROUP BY q.id, q.title, q.description, q.subject, q.difficulty, p.full_name, q.created_at
    ORDER BY q.created_at DESC
    LIMIT p_limit;
END;
$$;

CREATE OR REPLACE FUNCTION public.get_quiz_with_questions(p_quiz_id UUID)
RETURNS TABLE(
    quiz_id UUID,
    quiz_title TEXT,
    quiz_description TEXT,
    quiz_subject TEXT,
    quiz_difficulty TEXT,
    time_limit INTEGER,
    randomize_questions BOOLEAN,
    show_results_immediately BOOLEAN,
    question_id UUID,
    question_text TEXT,
    question_type TEXT,
    correct_answer TEXT,
    options JSONB,
    points INTEGER,
    explanation TEXT,
    order_index INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        q.id as quiz_id,
        q.title as quiz_title,
        q.description as quiz_description,
        q.subject as quiz_subject,
        q.difficulty as quiz_difficulty,
        q.time_limit,
        q.randomize_questions,
        q.show_results_immediately,
        qq.id as question_id,
        qq.question_text,
        qq.question_type,
        qq.correct_answer,
        qq.options,
        qq.points,
        qq.explanation,
        qq.order_index
    FROM public.quizzes q
    LEFT JOIN public.quiz_questions qq ON q.id = qq.quiz_id
    WHERE q.id = p_quiz_id
    AND (q.is_public = true OR q.created_by = auth.uid())
    ORDER BY qq.order_index;
END;
$$;

-- Function to submit quiz attempt
CREATE OR REPLACE FUNCTION public.submit_quiz_attempt(
    p_quiz_id UUID,
    p_answers JSONB,
    p_time_taken INTEGER
)
RETURNS TABLE(
    attempt_id UUID,
    score INTEGER,
    total_questions INTEGER,
    percentage NUMERIC
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_attempt_id UUID;
    v_score INTEGER := 0;
    v_total_questions INTEGER;
    v_question RECORD;
    v_user_answer TEXT;
BEGIN
    -- Get total questions count
    SELECT COUNT(*) INTO v_total_questions
    FROM public.quiz_questions
    WHERE quiz_id = p_quiz_id;
    
    -- Calculate score
    FOR v_question IN 
        SELECT id, correct_answer 
        FROM public.quiz_questions 
        WHERE quiz_id = p_quiz_id
    LOOP
        v_user_answer := p_answers->>(v_question.id::TEXT);
        IF v_user_answer = v_question.correct_answer THEN
            v_score := v_score + 1;
        END IF;
    END LOOP;
    
    -- Insert attempt record
    INSERT INTO public.quiz_attempts (
        quiz_id, user_id, score, total_questions, time_taken, answers
    ) VALUES (
        p_quiz_id, auth.uid(), v_score, v_total_questions, p_time_taken, p_answers
    ) RETURNING id INTO v_attempt_id;
    
    RETURN QUERY
    SELECT 
        v_attempt_id,
        v_score,
        v_total_questions,
        ROUND((v_score::NUMERIC / v_total_questions::NUMERIC) * 100, 2) as percentage;
END;
$$;

-- Create updated_at trigger for quizzes
CREATE OR REPLACE FUNCTION public.handle_quiz_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER quiz_updated_at_trigger
    BEFORE UPDATE ON public.quizzes
    FOR EACH ROW EXECUTE FUNCTION public.handle_quiz_updated_at();
