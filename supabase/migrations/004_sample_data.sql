-- Insert sample countries, courses, and institutes for dropdowns
CREATE TABLE IF NOT EXISTS public.countries (
    id SERIAL PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    code TEXT UNIQUE NOT NULL
);

CREATE TABLE IF NOT EXISTS public.courses (
    id SERIAL PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    category TEXT
);

CREATE TABLE IF NOT EXISTS public.institutes (
    id SERIAL PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    country TEXT,
    type TEXT
);

-- Insert sample countries
INSERT INTO public.countries (name, code) VALUES
('United States', 'US'),
('United Kingdom', 'UK'),
('Canada', 'CA'),
('Australia', 'AU'),
('Germany', 'DE'),
('France', 'FR'),
('Japan', 'JP'),
('South Korea', 'KR'),
('Singapore', 'SG'),
('Netherlands', 'NL'),
('Sweden', 'SE'),
('Switzerland', 'CH'),
('India', 'IN'),
('China', 'CN'),
('Brazil', 'BR'),
('South Africa', 'ZA'),
('Kenya', 'KE'),
('Nigeria', 'NG'),
('Egypt', 'EG'),
('Mexico', 'MX')
ON CONFLICT (name) DO NOTHING;

-- Insert sample courses
INSERT INTO public.courses (name, category) VALUES
('Computer Science', 'Technology'),
('Software Engineering', 'Technology'),
('Data Science', 'Technology'),
('Artificial Intelligence', 'Technology'),
('Cybersecurity', 'Technology'),
('Information Technology', 'Technology'),
('Business Administration', 'Business'),
('Marketing', 'Business'),
('Finance', 'Business'),
('Economics', 'Business'),
('Accounting', 'Business'),
('International Business', 'Business'),
('Medicine', 'Health Sciences'),
('Nursing', 'Health Sciences'),
('Pharmacy', 'Health Sciences'),
('Dentistry', 'Health Sciences'),
('Psychology', 'Health Sciences'),
('Public Health', 'Health Sciences'),
('Mechanical Engineering', 'Engineering'),
('Electrical Engineering', 'Engineering'),
('Civil Engineering', 'Engineering'),
('Chemical Engineering', 'Engineering'),
('Biomedical Engineering', 'Engineering'),
('Environmental Engineering', 'Engineering'),
('Mathematics', 'Sciences'),
('Physics', 'Sciences'),
('Chemistry', 'Sciences'),
('Biology', 'Sciences'),
('Environmental Science', 'Sciences'),
('Geology', 'Sciences'),
('English Literature', 'Arts & Humanities'),
('History', 'Arts & Humanities'),
('Philosophy', 'Arts & Humanities'),
('Art History', 'Arts & Humanities'),
('Music', 'Arts & Humanities'),
('Theater Arts', 'Arts & Humanities'),
('Law', 'Law'),
('International Law', 'Law'),
('Criminal Justice', 'Law'),
('Education', 'Education'),
('Early Childhood Education', 'Education'),
('Special Education', 'Education')
ON CONFLICT (name) DO NOTHING;

-- Insert sample institutes
INSERT INTO public.institutes (name, country, type) VALUES
('Harvard University', 'United States', 'University'),
('Stanford University', 'United States', 'University'),
('MIT', 'United States', 'University'),
('University of California, Berkeley', 'United States', 'University'),
('Yale University', 'United States', 'University'),
('Princeton University', 'United States', 'University'),
('Oxford University', 'United Kingdom', 'University'),
('Cambridge University', 'United Kingdom', 'University'),
('Imperial College London', 'United Kingdom', 'University'),
('University College London', 'United Kingdom', 'University'),
('University of Toronto', 'Canada', 'University'),
('McGill University', 'Canada', 'University'),
('University of British Columbia', 'Canada', 'University'),
('University of Melbourne', 'Australia', 'University'),
('Australian National University', 'Australia', 'University'),
('University of Sydney', 'Australia', 'University'),
('Technical University of Munich', 'Germany', 'University'),
('University of Heidelberg', 'Germany', 'University'),
('Sorbonne University', 'France', 'University'),
('École Polytechnique', 'France', 'University'),
('University of Tokyo', 'Japan', 'University'),
('Kyoto University', 'Japan', 'University'),
('Seoul National University', 'South Korea', 'University'),
('KAIST', 'South Korea', 'University'),
('National University of Singapore', 'Singapore', 'University'),
('Nanyang Technological University', 'Singapore', 'University'),
('University of Amsterdam', 'Netherlands', 'University'),
('Delft University of Technology', 'Netherlands', 'University'),
('KTH Royal Institute of Technology', 'Sweden', 'University'),
('ETH Zurich', 'Switzerland', 'University'),
('Indian Institute of Technology Delhi', 'India', 'University'),
('Indian Institute of Science', 'India', 'University'),
('Tsinghua University', 'China', 'University'),
('Peking University', 'China', 'University'),
('University of São Paulo', 'Brazil', 'University'),
('University of Cape Town', 'South Africa', 'University'),
('University of Nairobi', 'Kenya', 'University'),
('University of Lagos', 'Nigeria', 'University'),
('Cairo University', 'Egypt', 'University'),
('National Autonomous University of Mexico', 'Mexico', 'University')
ON CONFLICT (name) DO NOTHING;

-- Create functions to get dropdown data
CREATE OR REPLACE FUNCTION public.get_countries()
RETURNS TABLE (name TEXT, code TEXT) AS $$
BEGIN
    RETURN QUERY SELECT c.name, c.code FROM public.countries c ORDER BY c.name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.get_courses()
RETURNS TABLE (name TEXT, category TEXT) AS $$
BEGIN
    RETURN QUERY SELECT c.name, c.category FROM public.courses c ORDER BY c.category, c.name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.get_institutes()
RETURNS TABLE (name TEXT, country TEXT, type TEXT) AS $$
BEGIN
    RETURN QUERY SELECT i.name, i.country, i.type FROM public.institutes i ORDER BY i.country, i.name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Enable RLS for new tables
ALTER TABLE public.countries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.institutes ENABLE ROW LEVEL SECURITY;

-- Create policies for dropdown data (read-only for everyone)
CREATE POLICY "Everyone can view countries" ON public.countries FOR SELECT USING (true);
CREATE POLICY "Everyone can view courses" ON public.courses FOR SELECT USING (true);
CREATE POLICY "Everyone can view institutes" ON public.institutes FOR SELECT USING (true);
