-- Profiles policies
CREATE POLICY "Public profiles are viewable by everyone" ON public.profiles
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

-- Friendships policies
CREATE POLICY "Users can view their own friendships" ON public.friendships
    FOR SELECT USING (auth.uid() = requester_id OR auth.uid() = addressee_id);

CREATE POLICY "Users can create friendship requests" ON public.friendships
    FOR INSERT WITH CHECK (auth.uid() = requester_id);

CREATE POLICY "Users can update friendships they're involved in" ON public.friendships
    FOR UPDATE USING (auth.uid() = requester_id OR auth.uid() = addressee_id);

-- Conversations policies
CREATE POLICY "Users can view conversations they participate in" ON public.conversations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.conversation_participants 
            WHERE conversation_id = id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create conversations" ON public.conversations
    FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Conversation admins can update conversations" ON public.conversations
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.conversation_participants 
            WHERE conversation_id = id AND user_id = auth.uid() AND role = 'admin'
        )
    );

-- Conversation participants policies
CREATE POLICY "Users can view participants of their conversations" ON public.conversation_participants
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.conversation_participants cp2
            WHERE cp2.conversation_id = conversation_id AND cp2.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can join conversations" ON public.conversation_participants
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Messages policies
CREATE POLICY "Users can view messages in their conversations" ON public.messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.conversation_participants 
            WHERE conversation_id = messages.conversation_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can send messages to their conversations" ON public.messages
    FOR INSERT WITH CHECK (
        auth.uid() = sender_id AND
        EXISTS (
            SELECT 1 FROM public.conversation_participants 
            WHERE conversation_id = messages.conversation_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own messages" ON public.messages
    FOR UPDATE USING (auth.uid() = sender_id);

-- Study groups policies
CREATE POLICY "Public study groups are viewable by everyone" ON public.study_groups
    FOR SELECT USING (is_public = true OR auth.uid() = owner_id OR 
        EXISTS (
            SELECT 1 FROM public.study_group_members 
            WHERE group_id = id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create study groups" ON public.study_groups
    FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Group owners can update their groups" ON public.study_groups
    FOR UPDATE USING (auth.uid() = owner_id);

-- Study group members policies
CREATE POLICY "Users can view members of groups they belong to" ON public.study_group_members
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.study_groups sg
            WHERE sg.id = group_id AND (sg.is_public = true OR sg.owner_id = auth.uid())
        ) OR
        EXISTS (
            SELECT 1 FROM public.study_group_members sgm2
            WHERE sgm2.group_id = group_id AND sgm2.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can join study groups" ON public.study_group_members
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Notes policies
CREATE POLICY "Users can view public notes and their own notes" ON public.notes
    FOR SELECT USING (is_public = true OR auth.uid() = owner_id);

CREATE POLICY "Users can create their own notes" ON public.notes
    FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Users can update their own notes" ON public.notes
    FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "Users can delete their own notes" ON public.notes
    FOR DELETE USING (auth.uid() = owner_id);

-- Past papers policies
CREATE POLICY "Everyone can view past papers" ON public.past_papers
    FOR SELECT USING (true);

CREATE POLICY "Authenticated users can upload past papers" ON public.past_papers
    FOR INSERT WITH CHECK (auth.uid() = uploaded_by);

-- Revision plans policies
CREATE POLICY "Users can view their own revision plans" ON public.revision_plans
    FOR SELECT USING (auth.uid() = owner_id);

CREATE POLICY "Users can create their own revision plans" ON public.revision_plans
    FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Users can update their own revision plans" ON public.revision_plans
    FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "Users can delete their own revision plans" ON public.revision_plans
    FOR DELETE USING (auth.uid() = owner_id);

-- Revision tasks policies
CREATE POLICY "Users can view tasks of their own plans" ON public.revision_tasks
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.revision_plans 
            WHERE id = plan_id AND owner_id = auth.uid()
        )
    );

CREATE POLICY "Users can create tasks for their own plans" ON public.revision_tasks
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.revision_plans 
            WHERE id = plan_id AND owner_id = auth.uid()
        )
    );

CREATE POLICY "Users can update tasks of their own plans" ON public.revision_tasks
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.revision_plans 
            WHERE id = plan_id AND owner_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete tasks of their own plans" ON public.revision_tasks
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.revision_plans 
            WHERE id = plan_id AND owner_id = auth.uid()
        )
    );
