import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface QuizRequest {
  content: string;
  numQuestions?: number;
  difficulty?: 'easy' | 'medium' | 'hard';
  questionTypes?: ('multiple-choice' | 'true-false' | 'short-answer')[];
  subject?: string;
  includeAnswers?: boolean;
}

interface QuizQuestion {
  id: string;
  type: 'multiple-choice' | 'true-false' | 'short-answer';
  question: string;
  options?: string[];
  correct_answer: string;
  explanation?: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

interface QuizResponse {
  success: boolean;
  quiz?: {
    title: string;
    questions: QuizQuestion[];
    metadata: {
      totalQuestions: number;
      difficulty: string;
      subject: string;
      generatedAt: string;
      estimatedTime: number;
    };
  };
  error?: string;
}

// Helper function to get OpenRouter model name
function getOpenRouterModel(model: string): string {
  const modelMap: { [key: string]: string } = {
    'gpt-4': 'openai/gpt-4',
    'gpt-4-turbo': 'openai/gpt-4-turbo-preview',
    'gpt-3.5-turbo': 'openai/gpt-3.5-turbo-0125',
    'claude-3-sonnet': 'anthropic/claude-3-sonnet',
    'claude-3-haiku': 'anthropic/claude-3-haiku'
  };

  // Use a reliable default model that's available on OpenRouter
  return modelMap[model] || 'openai/gpt-3.5-turbo-0125';
}

// Helper function to generate quiz prompt
function generateQuizPrompt(request: QuizRequest): string {
  const { content, numQuestions = 10, difficulty = 'medium', questionTypes = ['multiple-choice'], subject = 'General' } = request;
  
  const questionTypeInstructions = questionTypes.map(type => {
    switch (type) {
      case 'multiple-choice':
        return 'Multiple choice questions with 4 options (A, B, C, D)';
      case 'true-false':
        return 'True/False questions';
      case 'short-answer':
        return 'Short answer questions requiring 1-2 sentence responses';
      default:
        return 'Multiple choice questions';
    }
  }).join(', ');

  return `You are an expert quiz generator. Create a comprehensive quiz based on the following content.

CONTENT TO ANALYZE:
${content}

QUIZ REQUIREMENTS:
- Generate exactly ${numQuestions} questions
- Difficulty level: ${difficulty}
- Question types: ${questionTypeInstructions}
- Subject area: ${subject}
- Include clear explanations for correct answers
- Ensure questions test understanding, not just memorization

RESPONSE FORMAT (JSON):
{
  "title": "Quiz Title Based on Content",
  "questions": [
    {
      "id": "q1",
      "type": "multiple-choice",
      "question": "Question text here?",
      "options": ["A) Option 1", "B) Option 2", "C) Option 3", "D) Option 4"],
      "correct_answer": "A",
      "explanation": "Explanation of why this is correct",
      "difficulty": "medium"
    }
  ]
}

IMPORTANT:
- For multiple-choice: options array with A), B), C), D) format, correct_answer as letter only
- For true-false: options as ["True", "False"], correct_answer as "True" or "False"
- For short-answer: no options array, correct_answer as the expected answer
- Make questions challenging but fair
- Vary difficulty within the specified level
- Ensure all questions are directly related to the provided content

Generate the quiz now:`;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { content, numQuestions, difficulty, questionTypes, subject, includeAnswers = true }: QuizRequest = await req.json()

    if (!content || content.trim().length < 50) {
      return Response.json(
        { success: false, error: 'Content must be at least 50 characters long' },
        { status: 400, headers: corsHeaders }
      )
    }

    // Get OpenRouter API key
    const openRouterApiKey = Deno.env.get('OPENROUTER_API_KEY')
    if (!openRouterApiKey) {
      console.error('OpenRouter API key not found in environment variables')
      return Response.json(
        { success: false, error: 'OpenRouter API key not configured. Please set OPENROUTER_API_KEY environment variable.' },
        { status: 500, headers: corsHeaders }
      )
    }

    console.log('OpenRouter API key found, length:', openRouterApiKey.length)

    // Get user preferences (optional)
    const authHeader = req.headers.get('Authorization')
    let preferences = null
    
    if (authHeader) {
      try {
        const supabaseClient = createClient(
          Deno.env.get('SUPABASE_URL') ?? '',
          Deno.env.get('SUPABASE_ANON_KEY') ?? '',
          { global: { headers: { Authorization: authHeader } } }
        )
        
        const { data: { user } } = await supabaseClient.auth.getUser()
        if (user) {
          const { data } = await supabaseClient
            .from('ai_preferences')
            .select('*')
            .eq('user_id', user.id)
            .single()
          preferences = data
        }
      } catch (error) {
        console.log('Could not fetch user preferences:', error)
      }
    }

    // Generate quiz prompt
    const prompt = generateQuizPrompt({
      content,
      numQuestions,
      difficulty,
      questionTypes,
      subject
    })

    console.log('Generating quiz with OpenRouter...')
    console.log('Request details:', { numQuestions, difficulty, subject, contentLength: content.length })

    // Call OpenRouter API
    try {
      const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${openRouterApiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://study-fam.com',
          'X-Title': 'Study Fam Quiz Generator',
        },
        body: JSON.stringify({
          model: 'meta-llama/llama-3.1-8b-instruct:free',
          messages: [
            {
              role: 'system',
              content: 'You are an expert educational quiz generator. Create high-quality, engaging quiz questions that test comprehension and critical thinking. Always respond with valid JSON format.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: Math.min(4000, (numQuestions || 10) * 200),
          temperature: 0.7,
        }),
      })

      if (!openRouterResponse.ok) {
        const errorText = await openRouterResponse.text()
        console.error('OpenRouter API error:', openRouterResponse.status, errorText)
        return Response.json(
          { success: false, error: `OpenRouter API error (${openRouterResponse.status}): ${errorText}` },
          { status: 500, headers: corsHeaders }
        )
      }

    const openRouterData = await openRouterResponse.json()
    
    if (!openRouterData.choices || !openRouterData.choices[0]) {
      console.error('Invalid OpenRouter response:', openRouterData)
      return Response.json(
        { success: false, error: 'Invalid response from AI service' },
        { status: 500, headers: corsHeaders }
      )
    }

    const aiResponse = openRouterData.choices[0].message.content

      // Parse AI response
      let quizData: any
      try {
        // Clean the response to extract JSON
        const jsonMatch = aiResponse.match(/\{[\s\S]*\}/)
        if (!jsonMatch) {
          throw new Error('No JSON found in response')
        }

        quizData = JSON.parse(jsonMatch[0])
      } catch (error) {
        console.error('Failed to parse AI response:', error, 'Response:', aiResponse)
        return Response.json(
          { success: false, error: 'Failed to parse quiz data. Please try again.' },
          { status: 500, headers: corsHeaders }
        )
      }

    // Validate and format quiz data
    if (!quizData.questions || !Array.isArray(quizData.questions)) {
      return Response.json(
        { success: false, error: 'Invalid quiz format generated' },
        { status: 500, headers: corsHeaders }
      )
    }

    // Calculate estimated time (2 minutes per question on average)
    const estimatedTime = quizData.questions.length * 2

    const response: QuizResponse = {
      success: true,
      quiz: {
        title: quizData.title || `${subject} Quiz`,
        questions: quizData.questions,
        metadata: {
          totalQuestions: quizData.questions.length,
          difficulty: difficulty || 'medium',
          subject: subject || 'General',
          generatedAt: new Date().toISOString(),
          estimatedTime
        }
      }
    }

      return Response.json(response, { headers: corsHeaders })

    } catch (openRouterError) {
      console.error('OpenRouter API call failed:', openRouterError)
      return Response.json(
        { success: false, error: `OpenRouter API call failed: ${openRouterError.message}` },
        { status: 500, headers: corsHeaders }
      )
    }

  } catch (error) {
    console.error('Quiz generation error:', error)
    return Response.json(
      { success: false, error: `An unexpected error occurred: ${error.message}` },
      { status: 500, headers: corsHeaders }
    )
  }
})
