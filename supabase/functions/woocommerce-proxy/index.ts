import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const url = new URL(req.url)
    const targetUrl = url.searchParams.get('url')
    
    if (!targetUrl) {
      return new Response('Missing url parameter', { 
        status: 400, 
        headers: corsHeaders 
      })
    }

    // Only allow studyfam.co.ke URLs for security
    if (!targetUrl.includes('studyfam.co.ke')) {
      return new Response('Unauthorized domain', { 
        status: 403, 
        headers: corsHeaders 
      })
    }

    console.log('Proxying request to:', targetUrl)

    // Fetch the WooCommerce page
    const response = await fetch(targetUrl, {
      method: req.method,
      headers: {
        'User-Agent': 'StudyFam-Proxy/1.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
      },
    })

    if (!response.ok) {
      return new Response(`Proxy error: ${response.status}`, { 
        status: response.status, 
        headers: corsHeaders 
      })
    }

    let html = await response.text()

    // Inject our success detection script
    const injectionScript = `
    <script>
      (function() {
        console.log('StudyFam payment monitor loaded');
        
        function checkForSuccess() {
          const url = window.location.href;
          const pathname = window.location.pathname;
          
          if (pathname.includes('/checkout/order-received/') || 
              pathname.includes('/order-received/') ||
              url.includes('order-received') ||
              url.includes('thank-you') ||
              document.querySelector('.woocommerce-order-received') ||
              document.querySelector('.woocommerce-thankyou-order-received')) {
            
            const orderIdMatch = url.match(/order-received\\/(\\d+)/);
            const orderKeyMatch = url.match(/key=([^&]+)/);
            
            // Send message to parent window
            if (window.parent && window.parent !== window) {
              window.parent.postMessage({
                type: 'payment_success',
                url: url,
                order_id: orderIdMatch ? orderIdMatch[1] : null,
                order_key: orderKeyMatch ? orderKeyMatch[1] : null,
                timestamp: new Date().toISOString(),
              }, '*');
              
              console.log('Payment success message sent to parent');
            }
            
            return true;
          }
          
          return false;
        }
        
        // Check immediately
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', checkForSuccess);
        } else {
          checkForSuccess();
        }
        
        // Check periodically
        setInterval(checkForSuccess, 3000);
        
        // Monitor for page changes
        let lastUrl = window.location.href;
        const observer = new MutationObserver(() => {
          const currentUrl = window.location.href;
          if (currentUrl !== lastUrl) {
            lastUrl = currentUrl;
            setTimeout(checkForSuccess, 1000);
          }
        });
        
        if (document.body) {
          observer.observe(document.body, {
            childList: true,
            subtree: true,
          });
        }
        
        // Also monitor for URL changes via popstate
        window.addEventListener('popstate', () => {
          setTimeout(checkForSuccess, 1000);
        });
        
      })();
    </script>
    `

    // Inject the script before closing body tag
    html = html.replace('</body>', injectionScript + '</body>')

    // Return the modified HTML without X-Frame-Options
    return new Response(html, {
      status: 200,
      headers: {
        ...corsHeaders,
        'Content-Type': 'text/html; charset=utf-8',
        // Explicitly remove X-Frame-Options to allow iframe embedding
        'X-Frame-Options': 'ALLOWALL',
        'Content-Security-Policy': "frame-ancestors 'self' http://localhost:* https://*.supabase.co https://*.vercel.app;",
      },
    })

  } catch (error) {
    console.error('Proxy error:', error)
    return new Response(`Proxy error: ${error.message}`, {
      status: 500,
      headers: corsHeaders,
    })
  }
})
