
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

interface ChatRequest {
  messages: ChatMessage[];
  subject: string;
  temperature?: number;
}

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'No authorization header' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Create Supabase client to verify user
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: authHeader },
        },
      }
    )

    // Verify the user is authenticated
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser()
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse the request body
    const { messages, subject, temperature = 0.7 }: ChatRequest = await req.json()

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return new Response(
        JSON.stringify({ error: 'Messages array is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get OpenRouter API key from environment
    const openRouterApiKey = Deno.env.get('OPENROUTER_API_KEY')
    if (!openRouterApiKey) {
      return new Response(
        JSON.stringify({ error: 'OpenRouter API key not configured' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Create system message based on subject
    const systemMessage = createSystemMessage(subject)
    const openRouterMessages = [
      { role: 'system', content: systemMessage },
      ...messages
    ]

    // Call OpenRouter API
    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openRouterApiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://study-fam.com',
        'X-Title': 'Study Fam AI Tutor',
      },
      body: JSON.stringify({
        model: 'openai/gpt-4o-mini',
        messages: openRouterMessages,
        temperature: temperature,
        max_tokens: 2000,
      }),
    })

    if (!openRouterResponse.ok) {
      const errorData = await openRouterResponse.json()
      console.error('OpenRouter API error:', errorData)
      return new Response(
        JSON.stringify({ error: 'Failed to get response from AI' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    const data = await openRouterResponse.json()
    const assistantMessage = data.choices[0]?.message?.content

    if (!assistantMessage) {
      return new Response(
        JSON.stringify({ error: 'No response from AI' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Log the interaction for analytics
    try {
      await supabaseClient
        .from('ai_tutor_logs')
        .insert({
          user_id: user.id,
          subject: subject,
          message_count: messages.length,
          response_length: assistantMessage.length,
          created_at: new Date().toISOString()
        })
      console.log('Interaction logged successfully')
    } catch (logError) {
      console.warn('Failed to log interaction:', logError)
    }

    // Return the response
    return new Response(
      JSON.stringify({
        message: assistantMessage,
        usage: data.usage
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Function error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

function createSystemMessage(subject: string): string {
  const basePrompt = `You are an expert AI tutor designed to help students learn effectively. You should:

1. Provide clear, step-by-step explanations
2. Use examples and analogies to make concepts easier to understand
3. Encourage critical thinking by asking follow-up questions
4. Break down complex problems into manageable parts
5. Adapt your teaching style to the student's level of understanding
6. Be patient, encouraging, and supportive

Always aim to help students learn and understand concepts rather than just providing answers.`

  const subjectSpecificPrompts = {
    'mathematics': `${basePrompt}

As a mathematics tutor, focus on:
- Breaking down problems step-by-step
- Explaining the reasoning behind each step
- Providing multiple solution methods when applicable
- Using visual descriptions when helpful
- Connecting concepts to real-world applications`,

    'science': `${basePrompt}

As a science tutor, focus on:
- Explaining scientific concepts with clear examples
- Using analogies to make abstract concepts concrete
- Connecting theory to practical applications
- Encouraging scientific thinking and inquiry
- Breaking down complex processes into steps`,

    'english': `${basePrompt}

As an English tutor, focus on:
- Helping with reading comprehension and analysis
- Improving writing skills and structure
- Explaining grammar and language rules
- Encouraging creative expression
- Analyzing literature and texts`,

    'history': `${basePrompt}

As a history tutor, focus on:
- Providing historical context and background
- Explaining cause and effect relationships
- Connecting past events to present situations
- Encouraging critical analysis of sources
- Making history engaging and relatable`,

    'computer-science': `${basePrompt}

As a computer science tutor, focus on:
- Breaking down programming concepts clearly
- Providing code examples with explanations
- Explaining algorithms and data structures
- Helping with debugging and problem-solving
- Connecting theory to practical applications`,

    'art': `${basePrompt}

As an art tutor, focus on:
- Explaining artistic techniques and principles
- Discussing art history and movements
- Encouraging creative expression
- Providing constructive feedback
- Connecting art to culture and society`,

    'music': `${basePrompt}

As a music tutor, focus on:
- Explaining music theory concepts
- Discussing different musical styles and genres
- Helping with composition and performance
- Providing practice suggestions
- Connecting music to cultural contexts`
  }

  return subjectSpecificPrompts[subject.toLowerCase()] || `${basePrompt}

Focus on the subject of ${subject} and provide comprehensive, educational assistance.`
}
