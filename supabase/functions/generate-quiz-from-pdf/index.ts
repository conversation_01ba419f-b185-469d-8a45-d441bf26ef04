
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface QuizGenerationRequest {
  pdfContent: string;
  numQuestions?: number;
  difficulty?: 'easy' | 'medium' | 'hard';
  questionTypes?: ('multiple-choice' | 'true-false' | 'short-answer')[];
  subject?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'No authorization header' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Create Supabase client to verify user
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: authHeader },
        },
      }
    )

    // Verify the user is authenticated
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser()
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse the request body
    const { 
      pdfContent, 
      numQuestions = 10, 
      difficulty = 'medium',
      questionTypes = ['multiple-choice'],
      subject = 'general'
    }: QuizGenerationRequest = await req.json()

    if (!pdfContent || pdfContent.trim().length === 0) {
      return new Response(
        JSON.stringify({ error: 'PDF content is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get OpenRouter API key from environment
    const openRouterApiKey = Deno.env.get('OPENROUTER_API_KEY')
    if (!openRouterApiKey) {
      return new Response(
        JSON.stringify({ error: 'OpenRouter API key not configured' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Create prompt for quiz generation
    const systemPrompt = `You are an expert quiz generator. Generate ${numQuestions} ${difficulty} level quiz questions based on the provided content.

Question types to include: ${questionTypes.join(', ')}.

For multiple-choice questions, provide 4 options (A, B, C, D).
For true-false questions, the answer should be either "True" or "False".
For short-answer questions, provide a concise correct answer.

Always include an explanation for each answer.

Return the response as a JSON object with this structure:
{
  "title": "Generated Quiz Title",
  "questions": [
    {
      "question": "Question text",
      "type": "multiple-choice|true-false|short-answer",
      "options": ["A", "B", "C", "D"] (only for multiple-choice),
      "correctAnswer": "Correct answer",
      "explanation": "Why this is the correct answer"
    }
  ]
}

Focus on key concepts, important facts, and understanding rather than trivial details.`

    const userPrompt = `Generate a quiz from this content:\n\n${pdfContent.substring(0, 12000)}`

    // Call OpenRouter API
    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openRouterApiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://study-fam.com',
        'X-Title': 'Study Fam Quiz Generator',
      },
      body: JSON.stringify({
        model: 'openai/gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.7,
        max_tokens: 3000,
      }),
    })

    if (!openRouterResponse.ok) {
      const errorData = await openRouterResponse.json()
      console.error('OpenRouter API error:', errorData)
      return new Response(
        JSON.stringify({ error: 'Failed to generate quiz questions' }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    const data = await openRouterResponse.json()
    const generatedContent = data.choices[0]?.message?.content

    if (!generatedContent) {
      return new Response(
        JSON.stringify({ error: 'No quiz generated' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse the JSON response
    let quizData;
    try {
      quizData = JSON.parse(generatedContent);
    } catch (parseError) {
      console.error('Failed to parse quiz JSON:', parseError);
      return new Response(
        JSON.stringify({ error: 'Failed to parse generated quiz' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Log the generation for analytics
    try {
      await supabaseClient
        .from('ai_tutor_logs')
        .insert({
          user_id: user.id,
          subject: 'quiz-generation',
          message_count: 1,
          response_length: generatedContent.length,
          created_at: new Date().toISOString()
        })
      console.log('Quiz generation logged successfully')
    } catch (logError) {
      console.warn('Failed to log quiz generation:', logError)
    }

    // Return the generated quiz
    return new Response(
      JSON.stringify({
        quiz: quizData,
        usage: data.usage
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Function error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
