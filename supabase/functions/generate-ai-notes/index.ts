import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface GenerateNotesRequest {
  subject: string;
  topic: string;
  noteLength?: 'short' | 'medium' | 'long' | 'detailed';
  includeExamples?: boolean;
  includeKeyPoints?: boolean;
  includeSummary?: boolean;
  customInstructions?: string;
}

interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  usage: {
    total_tokens: number;
  };
}

// Helper function to convert model names to OpenRouter format
function getOpenRouterModel(model: string): string {
  // Use the free model that works reliably on OpenRouter
  return 'meta-llama/llama-3.1-8b-instruct:free';
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'No authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Verify the user
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid authorization' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const { subject, topic, noteLength = 'medium', includeExamples = true, includeKeyPoints = true, includeSummary = true, customInstructions }: GenerateNotesRequest = await req.json()

    // Validate input
    if (!subject || !topic) {
      return new Response(
        JSON.stringify({ error: 'Subject and topic are required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get user preferences
    const { data: preferences } = await supabase
      .from('ai_notes_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single()

    // Build the prompt based on preferences and inputs
    const lengthInstructions = {
      short: 'Create concise notes (300-500 words)',
      medium: 'Create comprehensive notes (500-800 words)',
      long: 'Create detailed notes (800-1200 words)',
      detailed: 'Create extensive notes (1200+ words)'
    }

    let prompt = `Create high-quality study notes for the subject "${subject}" on the topic "${topic}".

${lengthInstructions[noteLength]}.

Structure the notes with:
- Clear headings and subheadings
- Well-organized content
- Important concepts highlighted`

    if (includeKeyPoints) {
      prompt += '\n- Key points section with bullet points'
    }

    if (includeExamples) {
      prompt += '\n- Relevant examples and illustrations'
    }

    if (includeSummary) {
      prompt += '\n- Summary section at the end'
    }

    if (customInstructions) {
      prompt += `\n\nAdditional instructions: ${customInstructions}`
    }

    prompt += `

Format the response in clean Markdown format with proper headings (##, ###), bullet points, and emphasis where appropriate.
Make the content educational, accurate, and suitable for students studying this topic.`

    const startTime = Date.now()

    // Call OpenRouter API
    const openRouterApiKey = Deno.env.get('OPENROUTER_API_KEY')
    if (!openRouterApiKey) {
      throw new Error('OpenRouter API key not configured')
    }

    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openRouterApiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://study-fam.com',
        'X-Title': 'Study Fam AI Notes Generator',
      },
      body: JSON.stringify({
        model: getOpenRouterModel(preferences?.default_ai_model || 'gpt-3.5-turbo'),
        messages: [
          {
            role: 'system',
            content: 'You are an expert educational content creator. Create well-structured, accurate, and engaging study notes that help students learn effectively.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: noteLength === 'detailed' ? 2000 : noteLength === 'long' ? 1500 : noteLength === 'medium' ? 1000 : 600,
        temperature: 0.7,
      }),
    })

    if (!openRouterResponse.ok) {
      const errorData = await openRouterResponse.text()
      console.error('OpenRouter API error:', openRouterResponse.status, errorData)
      throw new Error(`OpenRouter API error (${openRouterResponse.status}): ${errorData}`)
    }

    const aiResult: OpenAIResponse = await openRouterResponse.json()
    const generatedContent = aiResult.choices[0]?.message?.content

    if (!generatedContent) {
      throw new Error('No content generated by AI')
    }

    const endTime = Date.now()
    const generationTime = endTime - startTime

    // Log the generation
    await supabase
      .from('ai_notes_generation_log')
      .insert([
        {
          user_id: user.id,
          subject,
          topic,
          prompt_used: prompt,
          ai_model: getOpenRouterModel(preferences?.default_ai_model || 'gpt-3.5-turbo'),
          tokens_used: aiResult.usage?.total_tokens || 0,
          generation_time_ms: generationTime,
          success: true
        }
      ])

    return new Response(
      JSON.stringify({
        success: true,
        content: generatedContent,
        metadata: {
          subject,
          topic,
          wordCount: generatedContent.split(' ').length,
          tokensUsed: aiResult.usage?.total_tokens || 0,
          generationTime: generationTime,
          model: getOpenRouterModel(preferences?.default_ai_model || 'gpt-3.5-turbo')
        }
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error generating AI notes:', error)
    
    // Log the error
    try {
      const supabaseUrl = Deno.env.get('SUPABASE_URL')!
      const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
      const supabase = createClient(supabaseUrl, supabaseKey)
      
      const authHeader = req.headers.get('Authorization')
      if (authHeader) {
        const token = authHeader.replace('Bearer ', '')
        const { data: { user } } = await supabase.auth.getUser(token)
        
        if (user) {
          await supabase
            .from('ai_notes_generation_log')
            .insert([
              {
                user_id: user.id,
                subject: 'unknown',
                topic: 'unknown',
                prompt_used: 'error',
                ai_model: 'unknown',
                tokens_used: 0,
                generation_time_ms: 0,
                success: false,
                error_message: error.message
              }
            ])
        }
      }
    } catch (logError) {
      console.error('Error logging generation failure:', logError)
    }

    return new Response(
      JSON.stringify({ 
        error: 'Failed to generate notes',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
