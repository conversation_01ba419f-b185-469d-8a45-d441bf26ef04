import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-wc-webhook-signature',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Verify webhook signature (optional but recommended)
    const signature = req.headers.get('x-wc-webhook-signature')
    const webhookSecret = Deno.env.get('WOOCOMMERCE_WEBHOOK_SECRET')
    
    if (webhookSecret && signature) {
      // Verify the webhook signature here if needed
      console.log('Webhook signature verification:', signature)
    }

    const webhookData = await req.json()
    console.log('WooCommerce webhook received:', webhookData)

    // Handle different webhook events
    const eventType = req.headers.get('x-wc-webhook-topic') || 'unknown'
    
    switch (eventType) {
      case 'order.completed':
      case 'order.processing':
        await handleOrderCompleted(supabaseClient, webhookData)
        break
      
      case 'order.cancelled':
      case 'order.refunded':
        await handleOrderCancelled(supabaseClient, webhookData)
        break
      
      default:
        console.log('Unhandled webhook event:', eventType)
    }

    return new Response(JSON.stringify({ success: true }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })

  } catch (error) {
    console.error('Webhook error:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    })
  }
})

async function handleOrderCompleted(supabase: any, orderData: any) {
  console.log('Processing completed order:', orderData.id)
  
  // Extract StudyFam user ID from order meta data
  const userIdMeta = orderData.meta_data?.find((meta: any) => meta.key === 'studyfam_user_id')
  const userId = userIdMeta?.value
  
  if (!userId) {
    console.error('No StudyFam user ID found in order meta data')
    return
  }

  try {
    // Get the active subscription plan
    const { data: plan } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('is_active', true)
      .single()

    if (!plan) {
      throw new Error('No active subscription plan found')
    }

    // Calculate subscription period (1 month from now)
    const now = new Date()
    const periodEnd = new Date(now)
    periodEnd.setMonth(periodEnd.getMonth() + 1)

    // Create or update user subscription
    const { error: subscriptionError } = await supabase
      .from('user_subscriptions')
      .upsert({
        user_id: userId,
        plan_id: plan.id,
        status: 'active',
        current_period_start: now.toISOString(),
        current_period_end: periodEnd.toISOString(),
        payment_provider: 'woocommerce',
        payment_reference: `wc_order_${orderData.id}`,
        last_payment_date: now.toISOString(),
        is_trial: false,
        woocommerce_order_id: orderData.id,
        woocommerce_order_key: orderData.order_key,
      }, {
        onConflict: 'user_id',
      })

    if (subscriptionError) {
      throw subscriptionError
    }

    console.log('✅ Subscription activated for user:', userId)

    // Store order record
    const { error: orderError } = await supabase
      .from('woocommerce_orders')
      .upsert({
        order_id: orderData.id,
        user_id: userId,
        status: orderData.status,
        total: parseFloat(orderData.total),
        currency: orderData.currency,
        order_key: orderData.order_key,
        payment_method: orderData.payment_method,
        created_at: orderData.date_created,
        updated_at: new Date().toISOString(),
      }, {
        onConflict: 'order_id',
      })

    if (orderError) {
      console.error('Error storing order record:', orderError)
    }

  } catch (error) {
    console.error('Error activating subscription:', error)
    throw error
  }
}

async function handleOrderCancelled(supabase: any, orderData: any) {
  console.log('Processing cancelled/refunded order:', orderData.id)
  
  // Find and cancel the subscription
  const { error } = await supabase
    .from('user_subscriptions')
    .update({
      status: 'cancelled',
      cancelled_at: new Date().toISOString(),
    })
    .eq('woocommerce_order_id', orderData.id)

  if (error) {
    console.error('Error cancelling subscription:', error)
  } else {
    console.log('✅ Subscription cancelled for order:', orderData.id)
  }

  // Update order record
  await supabase
    .from('woocommerce_orders')
    .update({
      status: orderData.status,
      updated_at: new Date().toISOString(),
    })
    .eq('order_id', orderData.id)
}
