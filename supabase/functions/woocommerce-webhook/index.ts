import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const body = await req.json()
    console.log('WooCommerce webhook received:', body)

    // Verify webhook signature (optional but recommended)
    const signature = req.headers.get('x-wc-webhook-signature')
    const webhookSecret = Deno.env.get('WOOCOMMERCE_WEBHOOK_SECRET')
    
    if (webhookSecret && signature) {
      // Verify signature here if needed
      // const expectedSignature = await crypto.subtle.digest('SHA-256', new TextEncoder().encode(JSON.stringify(body) + webhookSecret))
    }

    // Handle different webhook events
    const event = req.headers.get('x-wc-webhook-topic') || 'unknown'
    
    switch (event) {
      case 'order.completed':
      case 'order.processing':
        await handleOrderComplete(supabase, body)
        break
      case 'order.refunded':
        await handleOrderRefund(supabase, body)
        break
      default:
        console.log('Unhandled webhook event:', event)
    }

    return new Response(JSON.stringify({ success: true }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })

  } catch (error) {
    console.error('Webhook error:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400,
    })
  }
})

async function handleOrderComplete(supabase: any, orderData: any) {
  console.log('Processing completed order:', orderData.id)

  try {
    // Extract user information from order metadata or billing info
    const userEmail = orderData.billing?.email
    const userId = orderData.meta_data?.find((meta: any) => meta.key === 'user_id')?.value

    if (!userEmail && !userId) {
      console.log('No user information found in order')
      return
    }

    // Find user by email if userId not provided
    let user = null
    if (userId) {
      const { data } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()
      user = data
    } else if (userEmail) {
      const { data } = await supabase
        .from('profiles')
        .select('*')
        .eq('email', userEmail)
        .single()
      user = data
    }

    if (!user) {
      console.log('User not found for order:', orderData.id)
      return
    }

    // Record the transaction
    const { error: transactionError } = await supabase
      .from('payment_transactions')
      .insert({
        user_id: user.id,
        woocommerce_order_id: orderData.id,
        amount_cents: Math.round(parseFloat(orderData.total) * 100),
        currency: orderData.currency,
        status: 'success',
        transaction_type: 'subscription',
        metadata: {
          payment_gateway: 'woocommerce',
          order_data: orderData,
        },
      })

    if (transactionError) {
      console.error('Error recording transaction:', transactionError)
    }

    // Get or create subscription plan
    const { data: plans } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('is_active', true)
      .limit(1)
      .single()

    if (!plans) {
      console.error('No active subscription plan found')
      return
    }

    // Create or update user subscription
    const subscriptionEndDate = new Date()
    subscriptionEndDate.setMonth(subscriptionEndDate.getMonth() + 1) // 1 month subscription

    const { error: subscriptionError } = await supabase
      .from('user_subscriptions')
      .upsert({
        user_id: user.id,
        plan_id: plans.id,
        status: 'active',
        current_period_start: new Date().toISOString(),
        current_period_end: subscriptionEndDate.toISOString(),
        woocommerce_order_id: orderData.id,
        trial_ends_at: null, // Clear any trial
      }, {
        onConflict: 'user_id',
      })

    if (subscriptionError) {
      console.error('Error updating subscription:', subscriptionError)
      return
    }

    // Send welcome notification
    await supabase
      .from('notifications')
      .insert({
        user_id: user.id,
        title: 'Welcome to StudyFam Premium! 🎉',
        message: 'Your subscription is now active. Enjoy unlimited access to all premium features!',
        type: 'subscription',
        is_read: false,
      })

    console.log('Successfully activated subscription for user:', user.id)

  } catch (error) {
    console.error('Error processing order completion:', error)
  }
}

async function handleOrderRefund(supabase: any, orderData: any) {
  console.log('Processing refunded order:', orderData.id)

  try {
    // Find the user subscription associated with this order
    const { data: subscription } = await supabase
      .from('user_subscriptions')
      .select('*')
      .eq('woocommerce_order_id', orderData.id)
      .single()

    if (subscription) {
      // Cancel the subscription
      const { error } = await supabase
        .from('user_subscriptions')
        .update({
          status: 'cancelled',
          cancelled_at: new Date().toISOString(),
        })
        .eq('id', subscription.id)

      if (error) {
        console.error('Error cancelling subscription:', error)
        return
      }

      // Send cancellation notification
      await supabase
        .from('notifications')
        .insert({
          user_id: subscription.user_id,
          title: 'Subscription Cancelled',
          message: 'Your subscription has been cancelled due to a refund.',
          type: 'subscription',
          is_read: false,
        })

      console.log('Successfully cancelled subscription for order:', orderData.id)
    }

  } catch (error) {
    console.error('Error processing order refund:', error)
  }
}
