// Debug script to test avatar upload functionality
// Run this in the browser console on the profile page

async function debugAvatarUpload() {
  console.log('🔍 Starting avatar upload debug...');
  
  // Check if user is authenticated
  const { data: { user }, error: authError } = await window.supabase.auth.getUser();
  if (authError || !user) {
    console.error('❌ Authentication failed:', authError);
    return;
  }
  console.log('✅ User authenticated:', user.id);
  
  // Check storage bucket access
  try {
    const { data: buckets, error: bucketsError } = await window.supabase.storage.listBuckets();
    if (bucketsError) {
      console.error('❌ Failed to list buckets:', bucketsError);
      return;
    }
    
    const avatarsBucket = buckets.find(b => b.name === 'avatars');
    if (!avatarsBucket) {
      console.error('❌ Avatars bucket not found');
      return;
    }
    console.log('✅ Avatars bucket found:', avatarsBucket);
  } catch (error) {
    console.error('❌ Storage access failed:', error);
    return;
  }
  
  // Test file listing in user's folder
  try {
    const { data: files, error: listError } = await window.supabase.storage
      .from('avatars')
      .list(user.id);
    
    if (listError) {
      console.warn('⚠️ Failed to list user files:', listError);
    } else {
      console.log('✅ User folder accessible, files:', files);
    }
  } catch (error) {
    console.warn('⚠️ List operation failed:', error);
  }
  
  // Test upload with a small dummy file
  try {
    const dummyFile = new File(['test'], 'test-avatar.txt', { type: 'text/plain' });
    const fileName = `${user.id}/test-${Date.now()}.txt`;
    
    const { data: uploadData, error: uploadError } = await window.supabase.storage
      .from('avatars')
      .upload(fileName, dummyFile);
    
    if (uploadError) {
      console.error('❌ Test upload failed:', uploadError);
    } else {
      console.log('✅ Test upload successful:', uploadData);
      
      // Clean up test file
      await window.supabase.storage.from('avatars').remove([fileName]);
      console.log('🧹 Test file cleaned up');
    }
  } catch (error) {
    console.error('❌ Test upload exception:', error);
  }
  
  console.log('🏁 Debug complete');
}

// Make it available globally
window.debugAvatarUpload = debugAvatarUpload;

console.log('🛠️ Debug script loaded. Run debugAvatarUpload() to test avatar upload functionality.');
