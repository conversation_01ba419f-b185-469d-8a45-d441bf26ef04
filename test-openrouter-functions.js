#!/usr/bin/env node

/**
 * Test script for OpenRouter-enabled Supabase Edge Functions
 * Run this after deploying the updated functions to verify they work correctly
 */

const SUPABASE_URL = 'https://umavpljptvamtmaygztq.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVtYXZwbGpwdHZhbXRtYXlnenRxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyMzg1NjEsImV4cCI6MjA2NTgxNDU2MX0.f16xzL7S0VLtiYyovLl4mOxWFW4ys-KVJQgF-VkgLU4';

async function testFunction(functionName, payload) {
  console.log(`\n🧪 Testing ${functionName}...`);
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/${functionName}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload)
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log(`✅ ${functionName} - SUCCESS`);
      console.log(`Response preview: ${JSON.stringify(data).substring(0, 200)}...`);
      return true;
    } else {
      console.log(`❌ ${functionName} - FAILED`);
      console.log(`Error: ${JSON.stringify(data)}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${functionName} - ERROR`);
    console.log(`Error: ${error.message}`);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Testing OpenRouter Integration in Supabase Edge Functions');
  console.log('=' .repeat(60));

  const tests = [
    {
      name: 'ai-tutor',
      payload: {
        messages: [
          {
            role: 'user',
            content: 'Explain what 2+2 equals in simple terms'
          }
        ],
        subject: 'mathematics',
        temperature: 0.7
      }
    },
    {
      name: 'generate-ai-notes',
      payload: {
        subject: 'Mathematics',
        topic: 'Basic Addition',
        noteLength: 'short',
        includeExamples: true
      }
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    const success = await testFunction(test.name, test.payload);
    if (success) passedTests++;
    
    // Wait a bit between tests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('\n' + '=' .repeat(60));
  console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! OpenRouter integration is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Check the deployment and API key configuration.');
  }
}

// Run the tests
runTests().catch(console.error);
