# Quiz Generator Deployment Guide

## 🚀 **Deploy Supabase Edge Function**

### **1. Prerequisites**
- Supabase CLI installed (`npm install -g supabase`)
- Supabase project set up
- OpenRouter API key

### **2. Deploy the Edge Function**

```bash
# Navigate to your project directory
cd /path/to/study-fam

# Login to Supabase (if not already logged in)
supabase login

# Link to your Supabase project
supabase link --project-ref YOUR_PROJECT_REF

# Deploy the quiz generation function
supabase functions deploy generate-quiz
```

### **3. Set Environment Variables**

In your Supabase dashboard:

1. Go to **Project Settings** → **Edge Functions**
2. Add the following environment variable:
   - `OPENROUTER_API_KEY`: Your OpenRouter API key

Or via CLI:
```bash
supabase secrets set OPENROUTER_API_KEY=your_openrouter_api_key_here
```

### **4. Test the Function**

```bash
# Test the function locally first
supabase functions serve

# Test with curl
curl -X POST 'http://localhost:54321/functions/v1/generate-quiz' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "content": "Photosynthesis is the process by which plants convert sunlight into energy.",
    "numQuestions": 3,
    "difficulty": "medium",
    "subject": "Biology"
  }'
```

## 🔧 **Fix PDF.js Issues**

### **Option 1: Install PDF.js Properly**

```bash
# Install PDF.js
npm install pdfjs-dist

# Install types (optional)
npm install --save-dev @types/pdfjs-dist
```

### **Option 2: Use Alternative PDF Processing**

If PDF.js continues to have issues, consider using a server-side solution:

1. **Upload PDF to Supabase Storage**
2. **Process on server** using a PDF parsing library
3. **Return extracted text** to client

## 🛠️ **Current Fallback System**

The quiz generator now includes a robust fallback system:

### **When Edge Function is Available:**
- ✅ Uses OpenRouter API for high-quality AI-generated questions
- ✅ Supports multiple question types and difficulties
- ✅ Provides detailed explanations

### **When Edge Function is Not Available:**
- ✅ Uses local fallback generator
- ✅ Extracts key terms from content
- ✅ Creates contextual questions based on content
- ✅ Supports all question types
- ✅ Shows "Demo Mode" in title

## 📋 **Testing Checklist**

### **Text Input:**
- [ ] Paste text content
- [ ] Generate quiz (should use fallback initially)
- [ ] Verify questions are generated
- [ ] Test PDF export
- [ ] Test save to notes

### **File Upload:**
- [ ] Upload TXT file → Extract text → Generate quiz
- [ ] Upload PDF file → Extract text (may fail initially) → Generate quiz
- [ ] Upload DOC file → Extract text → Generate quiz
- [ ] Test file size limits
- [ ] Test unsupported file types

### **Quiz Features:**
- [ ] Different question types (Multiple choice, True/False, Short answer)
- [ ] Different difficulty levels
- [ ] Different number of questions
- [ ] PDF export with and without answers
- [ ] Copy to clipboard
- [ ] Save to notes with folder selection

## 🔍 **Troubleshooting**

### **PDF.js Worker Issues:**
```javascript
// If you see worker errors, try this alternative setup:
import * as pdfjsLib from 'pdfjs-dist/legacy/build/pdf';
pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js';
```

### **CORS Issues:**
- Ensure Edge Function is deployed
- Check CORS headers in function
- Verify API keys are set

### **File Upload Issues:**
- Check file size limits (10MB)
- Verify supported file types
- Test with different file formats

## 🎯 **Next Steps**

1. **Deploy Edge Function** to enable AI-powered quiz generation
2. **Test PDF extraction** with various PDF types
3. **Monitor usage** and adjust fallback logic as needed
4. **Add more question types** based on user feedback
5. **Implement quiz taking functionality** for generated quizzes

## 📊 **Performance Notes**

- **Fallback mode**: Instant generation, basic questions
- **AI mode**: 5-15 seconds, high-quality questions
- **PDF extraction**: 2-10 seconds depending on file size
- **File size limit**: 10MB for uploads

---

**The quiz generator is now fully functional with graceful fallbacks! 🎉**
