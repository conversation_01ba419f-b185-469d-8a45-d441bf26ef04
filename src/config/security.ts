// Security Configuration for StudyFam
export const SECURITY_CONFIG = {
  // Rate Limiting
  RATE_LIMITS: {
    LOGIN_ATTEMPTS: 5,
    PASSWORD_RESET: 3,
    API_REQUESTS: 100,
    FILE_UPLOADS: 10,
    MESSAGE_SENDING: 20,
    SEARCH_QUERIES: 50,
    TIME_WINDOW: 15 * 60 * 1000, // 15 minutes
  },

  // Session Management
  SESSION: {
    MAX_AGE: 24 * 60 * 60 * 1000, // 24 hours
    REFRESH_THRESHOLD: 30 * 60 * 1000, // 30 minutes
    CONCURRENT_SESSIONS: 3,
    IDLE_TIMEOUT: 2 * 60 * 60 * 1000, // 2 hours
  },

  // Content Security Policy
  CSP: {
    DEFAULT_SRC: ["'self'"],
    SCRIPT_SRC: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
    STYLE_SRC: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
    IMG_SRC: ["'self'", "data:", "https:", "blob:"],
    FONT_SRC: ["'self'", "https://fonts.gstatic.com"],
    CONNECT_SRC: ["'self'", "https://api.openai.com", "wss:"],
    FRAME_SRC: ["'none'"],
    OBJECT_SRC: ["'none'"],
    BASE_URI: ["'self'"],
    FORM_ACTION: ["'self'"],
  },

  // Input Validation
  VALIDATION: {
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_FILE_TYPES: [
      'image/jpeg', 'image/png', 'image/gif', 'image/webp',
      'application/pdf', 'text/plain', 'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ],
    MAX_TEXT_LENGTH: 10000,
    MAX_TITLE_LENGTH: 200,
    MIN_PASSWORD_LENGTH: 8,
  },

  // XSS Protection
  XSS: {
    ALLOWED_TAGS: ['b', 'i', 'u', 'strong', 'em', 'p', 'br', 'ul', 'ol', 'li'],
    ALLOWED_ATTRIBUTES: {},
    STRIP_COMMENTS: true,
  },

  // CSRF Protection
  CSRF: {
    TOKEN_LENGTH: 32,
    TOKEN_EXPIRY: 60 * 60 * 1000, // 1 hour
  },

  // Encryption
  ENCRYPTION: {
    ALGORITHM: 'AES-256-GCM',
    KEY_LENGTH: 32,
    IV_LENGTH: 16,
  },

  // Security Headers
  HEADERS: {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  },

  // Monitoring
  MONITORING: {
    LOG_FAILED_ATTEMPTS: true,
    LOG_SUSPICIOUS_ACTIVITY: true,
    ALERT_THRESHOLD: 10,
    BLOCK_THRESHOLD: 20,
  },
};

// Security Error Messages
export const SECURITY_MESSAGES = {
  RATE_LIMIT_EXCEEDED: 'Too many requests. Please try again later.',
  INVALID_TOKEN: 'Invalid or expired security token.',
  UNAUTHORIZED_ACCESS: 'Unauthorized access attempt detected.',
  SUSPICIOUS_ACTIVITY: 'Suspicious activity detected. Account temporarily locked.',
  INVALID_FILE_TYPE: 'File type not allowed for security reasons.',
  FILE_TOO_LARGE: 'File size exceeds security limits.',
  XSS_DETECTED: 'Potentially malicious content detected.',
  CSRF_TOKEN_MISSING: 'Security token missing or invalid.',
  SESSION_EXPIRED: 'Your session has expired. Please log in again.',
  CONCURRENT_SESSION_LIMIT: 'Maximum number of concurrent sessions reached.',
};

// Trusted Domains
export const TRUSTED_DOMAINS = [
  'studyfam.com',
  'api.studyfam.com',
  'cdn.studyfam.com',
  'supabase.co',
  'openai.com',
];

// Blocked User Agents (Known Bots/Scrapers)
export const BLOCKED_USER_AGENTS = [
  /bot/i,
  /crawler/i,
  /spider/i,
  /scraper/i,
  /curl/i,
  /wget/i,
];

// IP Whitelist for Admin Functions
export const ADMIN_IP_WHITELIST = [
  // Add your admin IPs here
  '127.0.0.1',
  '::1',
];

// Sensitive Routes that require extra protection
export const SENSITIVE_ROUTES = [
  '/admin',
  '/api/admin',
  '/api/users',
  '/api/payments',
  '/api/analytics',
];

// File Upload Security
export const FILE_SECURITY = {
  SCAN_FOR_MALWARE: true,
  CHECK_FILE_HEADERS: true,
  QUARANTINE_SUSPICIOUS: true,
  MAX_FILENAME_LENGTH: 255,
  BLOCKED_EXTENSIONS: [
    '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js',
    '.jar', '.php', '.asp', '.aspx', '.jsp', '.sh', '.ps1'
  ],
};
