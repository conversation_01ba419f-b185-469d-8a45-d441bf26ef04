
import jsPDF from 'jspdf';

export interface GeneratedQuiz {
  title: string;
  questions: {
    question: string;
    options?: string[];
    correctAnswer: string;
    type: 'multiple-choice' | 'true-false' | 'short-answer';
    explanation?: string;
  }[];
}

export interface NotePDFOptions {
  title: string;
  content: string;
  author?: string;
  subject?: string;
  keywords?: string[];
  includeMetadata?: boolean;
}

export const generatePDFFromQuiz = (quiz: GeneratedQuiz): Blob => {
  const doc = new jsPDF();
  let yPosition = 20;

  // Title
  doc.setFontSize(20);
  doc.text(quiz.title, 20, yPosition);
  yPosition += 20;

  // Questions
  quiz.questions.forEach((question, index) => {
    // Check if we need a new page
    if (yPosition > 250) {
      doc.addPage();
      yPosition = 20;
    }

    // Question number and text
    doc.setFontSize(14);
    doc.text(`${index + 1}. ${question.question}`, 20, yPosition);
    yPosition += 10;

    // Options for multiple choice
    if (question.type === 'multiple-choice' && question.options) {
      doc.setFontSize(12);
      question.options.forEach((option, optionIndex) => {
        const letter = String.fromCharCode(65 + optionIndex); // A, B, C, D
        doc.text(`   ${letter}. ${option}`, 25, yPosition);
        yPosition += 8;
      });
    }

    // Answer
    doc.setFontSize(10);
    doc.text(`Answer: ${question.correctAnswer}`, 25, yPosition);
    yPosition += 8;

    // Explanation if available
    if (question.explanation) {
      doc.text(`Explanation: ${question.explanation}`, 25, yPosition);
      yPosition += 8;
    }

    yPosition += 5; // Space between questions
  });

  return doc.output('blob');
};

export const downloadPDF = (blob: Blob, filename: string) => {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export const generateQuizPDF = (quiz: GeneratedQuiz, filename?: string): void => {
  const pdfBlob = generatePDFFromQuiz(quiz);
  const fileName = filename || `${quiz.title.replace(/\s+/g, '_')}_quiz.pdf`;
  downloadPDF(pdfBlob, fileName);
};

// Helper function to strip HTML tags from content
const stripHtmlTags = (html: string): string => {
  return html.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').replace(/&amp;/g, '&').replace(/&lt;/g, '<').replace(/&gt;/g, '>');
};

// Generate PDF from note content
export const generateNotePDF = (options: NotePDFOptions): Blob => {
  const doc = new jsPDF();
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = 20;
  const maxWidth = pageWidth - 2 * margin;
  let yPosition = margin;

  // Title
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(18);
  const titleLines = doc.splitTextToSize(options.title, maxWidth);
  titleLines.forEach((line: string) => {
    if (yPosition > pageHeight - margin) {
      doc.addPage();
      yPosition = margin;
    }
    doc.text(line, margin, yPosition);
    yPosition += 10;
  });
  yPosition += 10;

  // Metadata section
  if (options.includeMetadata) {
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(10);

    if (options.author) {
      doc.text(`Author: ${options.author}`, margin, yPosition);
      yPosition += 6;
    }

    if (options.subject) {
      doc.text(`Subject: ${options.subject}`, margin, yPosition);
      yPosition += 6;
    }

    if (options.keywords && options.keywords.length > 0) {
      const keywordsText = `Keywords: ${options.keywords.join(', ')}`;
      const keywordLines = doc.splitTextToSize(keywordsText, maxWidth);
      keywordLines.forEach((line: string) => {
        if (yPosition > pageHeight - margin) {
          doc.addPage();
          yPosition = margin;
        }
        doc.text(line, margin, yPosition);
        yPosition += 6;
      });
    }

    doc.text(`Generated: ${new Date().toLocaleDateString()}`, margin, yPosition);
    yPosition += 15;
  }

  // Content
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(12);

  // Strip HTML tags and split content into paragraphs
  const cleanContent = stripHtmlTags(options.content);
  const paragraphs = cleanContent.split('\n').filter(p => p.trim());

  paragraphs.forEach((paragraph) => {
    if (paragraph.trim()) {
      const lines = doc.splitTextToSize(paragraph.trim(), maxWidth);
      lines.forEach((line: string) => {
        if (yPosition > pageHeight - margin) {
          doc.addPage();
          yPosition = margin;
        }
        doc.text(line, margin, yPosition);
        yPosition += 7;
      });
      yPosition += 5; // Space between paragraphs
    }
  });

  return doc.output('blob');
};

// Generate and download PDF from note content
export const generateAndDownloadNotePDF = (options: NotePDFOptions, filename?: string): void => {
  const pdfBlob = generateNotePDF(options);
  const fileName = filename || `${options.title.replace(/[^a-zA-Z0-9]/g, '_')}_notes.pdf`;
  downloadPDF(pdfBlob, fileName);
};
