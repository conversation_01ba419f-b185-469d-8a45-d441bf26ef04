import DOMPurify from 'dompurify';
import { SECURITY_CONFIG, SECURITY_MESSAGES, BLOCKED_USER_AGENTS } from '@/config/security';

// Rate Limiting Store
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Failed Attempts Store
const failedAttemptsStore = new Map<string, { count: number; lastAttempt: number; blocked: boolean }>();

// CSRF Token Store
const csrfTokenStore = new Map<string, { token: string; expiry: number }>();

/**
 * Rate Limiting Implementation
 */
export class RateLimiter {
  static check(key: string, limit: number, windowMs: number = SECURITY_CONFIG.RATE_LIMITS.TIME_WINDOW): boolean {
    const now = Date.now();
    const record = rateLimitStore.get(key);

    if (!record || now > record.resetTime) {
      rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
      return true;
    }

    if (record.count >= limit) {
      return false;
    }

    record.count++;
    return true;
  }

  static getRemainingAttempts(key: string, limit: number): number {
    const record = rateLimitStore.get(key);
    if (!record) return limit;
    return Math.max(0, limit - record.count);
  }

  static reset(key: string): void {
    rateLimitStore.delete(key);
  }
}

/**
 * Input Sanitization and Validation
 */
export class InputSanitizer {
  static sanitizeHTML(input: string): string {
    return DOMPurify.sanitize(input, {
      ALLOWED_TAGS: SECURITY_CONFIG.XSS.ALLOWED_TAGS,
      ALLOWED_ATTR: SECURITY_CONFIG.XSS.ALLOWED_ATTRIBUTES,
      STRIP_COMMENTS: SECURITY_CONFIG.XSS.STRIP_COMMENTS,
    });
  }

  static sanitizeText(input: string): string {
    return input
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocols
      .replace(/on\w+=/gi, '') // Remove event handlers
      .trim();
  }

  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
  }

  static validatePassword(password: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (password.length < SECURITY_CONFIG.VALIDATION.MIN_PASSWORD_LENGTH) {
      errors.push(`Password must be at least ${SECURITY_CONFIG.VALIDATION.MIN_PASSWORD_LENGTH} characters long`);
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return { valid: errors.length === 0, errors };
  }

  static validateFileType(file: File): boolean {
    return SECURITY_CONFIG.VALIDATION.ALLOWED_FILE_TYPES.includes(file.type);
  }

  static validateFileSize(file: File): boolean {
    return file.size <= SECURITY_CONFIG.VALIDATION.MAX_FILE_SIZE;
  }
}

/**
 * CSRF Protection
 */
export class CSRFProtection {
  static generateToken(sessionId: string): string {
    const token = crypto.randomUUID();
    const expiry = Date.now() + SECURITY_CONFIG.CSRF.TOKEN_EXPIRY;
    csrfTokenStore.set(sessionId, { token, expiry });
    return token;
  }

  static validateToken(sessionId: string, token: string): boolean {
    const record = csrfTokenStore.get(sessionId);
    if (!record) return false;
    
    if (Date.now() > record.expiry) {
      csrfTokenStore.delete(sessionId);
      return false;
    }
    
    return record.token === token;
  }

  static refreshToken(sessionId: string): string {
    csrfTokenStore.delete(sessionId);
    return this.generateToken(sessionId);
  }
}

/**
 * Security Headers Management
 */
export class SecurityHeaders {
  static getHeaders(): Record<string, string> {
    return SECURITY_CONFIG.HEADERS;
  }

  static getCSPHeader(): string {
    const csp = SECURITY_CONFIG.CSP;
    return Object.entries(csp)
      .map(([directive, sources]) => {
        const kebabDirective = directive.replace(/_/g, '-').toLowerCase();
        return `${kebabDirective} ${sources.join(' ')}`;
      })
      .join('; ');
  }
}

/**
 * Failed Attempts Tracking
 */
export class FailedAttemptsTracker {
  static recordFailedAttempt(identifier: string): void {
    const now = Date.now();
    const record = failedAttemptsStore.get(identifier) || { count: 0, lastAttempt: 0, blocked: false };
    
    record.count++;
    record.lastAttempt = now;
    
    if (record.count >= SECURITY_CONFIG.MONITORING.BLOCK_THRESHOLD) {
      record.blocked = true;
    }
    
    failedAttemptsStore.set(identifier, record);
  }

  static isBlocked(identifier: string): boolean {
    const record = failedAttemptsStore.get(identifier);
    if (!record) return false;
    
    // Auto-unblock after 24 hours
    if (record.blocked && Date.now() - record.lastAttempt > 24 * 60 * 60 * 1000) {
      failedAttemptsStore.delete(identifier);
      return false;
    }
    
    return record.blocked;
  }

  static reset(identifier: string): void {
    failedAttemptsStore.delete(identifier);
  }

  static getFailedAttempts(identifier: string): number {
    const record = failedAttemptsStore.get(identifier);
    return record ? record.count : 0;
  }
}

/**
 * User Agent Validation
 */
export class UserAgentValidator {
  static isBlocked(userAgent: string): boolean {
    return BLOCKED_USER_AGENTS.some(pattern => pattern.test(userAgent));
  }

  static isSuspicious(userAgent: string): boolean {
    const suspicious = [
      /headless/i,
      /phantom/i,
      /selenium/i,
      /automation/i,
    ];
    
    return suspicious.some(pattern => pattern.test(userAgent));
  }
}

/**
 * Content Security Scanner
 */
export class ContentSecurityScanner {
  static scanForMaliciousContent(content: string): { safe: boolean; threats: string[] } {
    const threats: string[] = [];
    
    // Check for script injections
    if (/<script/i.test(content)) {
      threats.push('Script injection detected');
    }
    
    // Check for SQL injection patterns
    const sqlPatterns = [
      /union\s+select/i,
      /drop\s+table/i,
      /insert\s+into/i,
      /delete\s+from/i,
    ];
    
    if (sqlPatterns.some(pattern => pattern.test(content))) {
      threats.push('SQL injection pattern detected');
    }
    
    // Check for XSS patterns
    const xssPatterns = [
      /javascript:/i,
      /on\w+\s*=/i,
      /eval\s*\(/i,
      /expression\s*\(/i,
    ];
    
    if (xssPatterns.some(pattern => pattern.test(content))) {
      threats.push('XSS pattern detected');
    }
    
    return { safe: threats.length === 0, threats };
  }
}

/**
 * Session Security Manager
 */
export class SessionSecurity {
  static validateSession(sessionData: any): boolean {
    if (!sessionData || !sessionData.createdAt) return false;
    
    const now = Date.now();
    const sessionAge = now - new Date(sessionData.createdAt).getTime();
    
    return sessionAge <= SECURITY_CONFIG.SESSION.MAX_AGE;
  }

  static shouldRefreshSession(sessionData: any): boolean {
    if (!sessionData || !sessionData.lastActivity) return true;
    
    const now = Date.now();
    const timeSinceActivity = now - new Date(sessionData.lastActivity).getTime();
    
    return timeSinceActivity >= SECURITY_CONFIG.SESSION.REFRESH_THRESHOLD;
  }

  static isSessionIdle(sessionData: any): boolean {
    if (!sessionData || !sessionData.lastActivity) return true;
    
    const now = Date.now();
    const timeSinceActivity = now - new Date(sessionData.lastActivity).getTime();
    
    return timeSinceActivity >= SECURITY_CONFIG.SESSION.IDLE_TIMEOUT;
  }
}

/**
 * Encryption Utilities
 */
export class EncryptionUtils {
  static async hashPassword(password: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  static generateSecureToken(length: number = 32): string {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }
}
