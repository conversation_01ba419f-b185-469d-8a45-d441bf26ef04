export interface FileTypeInfo {
  type: string;
  category: 'document' | 'image' | 'video' | 'audio' | 'archive' | 'other';
  icon: string;
  color: string;
  canPreview: boolean;
  mimeTypes: string[];
}

export const FILE_TYPES: Record<string, FileTypeInfo> = {
  pdf: {
    type: 'PDF',
    category: 'document',
    icon: 'FileText',
    color: 'text-red-500',
    canPreview: true,
    mimeTypes: ['application/pdf']
  },
  doc: {
    type: 'Word',
    category: 'document',
    icon: 'FileText',
    color: 'text-blue-500',
    canPreview: false,
    mimeTypes: ['application/msword']
  },
  docx: {
    type: 'Word',
    category: 'document',
    icon: 'FileText',
    color: 'text-blue-500',
    canPreview: false,
    mimeTypes: ['application/vnd.openxmlformats-officedocument.wordprocessingml.document']
  },
  ppt: {
    type: 'PowerPoint',
    category: 'document',
    icon: 'FileText',
    color: 'text-orange-500',
    canPreview: false,
    mimeTypes: ['application/vnd.ms-powerpoint']
  },
  pptx: {
    type: 'PowerPoint',
    category: 'document',
    icon: 'FileText',
    color: 'text-orange-500',
    canPreview: false,
    mimeTypes: ['application/vnd.openxmlformats-officedocument.presentationml.presentation']
  },
  xls: {
    type: 'Excel',
    category: 'document',
    icon: 'FileText',
    color: 'text-green-500',
    canPreview: false,
    mimeTypes: ['application/vnd.ms-excel']
  },
  xlsx: {
    type: 'Excel',
    category: 'document',
    icon: 'FileText',
    color: 'text-green-500',
    canPreview: false,
    mimeTypes: ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']
  },
  txt: {
    type: 'Text',
    category: 'document',
    icon: 'FileText',
    color: 'text-gray-500',
    canPreview: true,
    mimeTypes: ['text/plain']
  },
  md: {
    type: 'Markdown',
    category: 'document',
    icon: 'FileText',
    color: 'text-gray-600',
    canPreview: true,
    mimeTypes: ['text/markdown']
  },
  jpg: {
    type: 'JPEG',
    category: 'image',
    icon: 'Image',
    color: 'text-purple-500',
    canPreview: true,
    mimeTypes: ['image/jpeg']
  },
  jpeg: {
    type: 'JPEG',
    category: 'image',
    icon: 'Image',
    color: 'text-purple-500',
    canPreview: true,
    mimeTypes: ['image/jpeg']
  },
  png: {
    type: 'PNG',
    category: 'image',
    icon: 'Image',
    color: 'text-purple-500',
    canPreview: true,
    mimeTypes: ['image/png']
  },
  gif: {
    type: 'GIF',
    category: 'image',
    icon: 'Image',
    color: 'text-purple-500',
    canPreview: true,
    mimeTypes: ['image/gif']
  },
  webp: {
    type: 'WebP',
    category: 'image',
    icon: 'Image',
    color: 'text-purple-500',
    canPreview: true,
    mimeTypes: ['image/webp']
  },
  mp4: {
    type: 'MP4',
    category: 'video',
    icon: 'Video',
    color: 'text-red-500',
    canPreview: true,
    mimeTypes: ['video/mp4']
  },
  webm: {
    type: 'WebM',
    category: 'video',
    icon: 'Video',
    color: 'text-red-500',
    canPreview: true,
    mimeTypes: ['video/webm']
  },
  mp3: {
    type: 'MP3',
    category: 'audio',
    icon: 'Music',
    color: 'text-green-500',
    canPreview: true,
    mimeTypes: ['audio/mpeg']
  },
  wav: {
    type: 'WAV',
    category: 'audio',
    icon: 'Music',
    color: 'text-green-500',
    canPreview: true,
    mimeTypes: ['audio/wav']
  },
  zip: {
    type: 'ZIP',
    category: 'archive',
    icon: 'Archive',
    color: 'text-yellow-500',
    canPreview: false,
    mimeTypes: ['application/zip']
  }
};

export const getFileTypeFromExtension = (filename: string): FileTypeInfo | null => {
  const extension = filename.split('.').pop()?.toLowerCase();
  if (!extension) return null;
  return FILE_TYPES[extension] || null;
};

export const getFileTypeFromMimeType = (mimeType: string): FileTypeInfo | null => {
  for (const [key, info] of Object.entries(FILE_TYPES)) {
    if (info.mimeTypes.includes(mimeType)) {
      return info;
    }
  }
  return null;
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const isImageFile = (filename: string): boolean => {
  const fileType = getFileTypeFromExtension(filename);
  return fileType?.category === 'image';
};

export const isVideoFile = (filename: string): boolean => {
  const fileType = getFileTypeFromExtension(filename);
  return fileType?.category === 'video';
};

export const isAudioFile = (filename: string): boolean => {
  const fileType = getFileTypeFromExtension(filename);
  return fileType?.category === 'audio';
};

export const isDocumentFile = (filename: string): boolean => {
  const fileType = getFileTypeFromExtension(filename);
  return fileType?.category === 'document';
};

export const canPreviewFile = (filename: string): boolean => {
  const fileType = getFileTypeFromExtension(filename);
  return fileType?.canPreview || false;
};
