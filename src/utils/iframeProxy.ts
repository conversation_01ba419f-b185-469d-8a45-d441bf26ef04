// Utility to help bypass iframe restrictions

export const createProxyUrl = (originalUrl: string): string => {
  // Option 1: Use a CORS proxy service (for development/testing)
  const corsProxies = [
    'https://cors-anywhere.herokuapp.com/',
    'https://api.allorigins.win/raw?url=',
    'https://corsproxy.io/?',
  ];
  
  // For production, you'd want to set up your own proxy
  // For now, we'll try the original URL first
  return originalUrl;
};

export const injectIframeScript = (iframeDocument: Document) => {
  // Inject script to communicate with parent window
  const script = iframeDocument.createElement('script');
  script.textContent = `
    (function() {
      // Monitor for WooCommerce success pages
      function checkForSuccess() {
        const url = window.location.href;
        const pathname = window.location.pathname;
        
        if (pathname.includes('/checkout/order-received/') || 
            pathname.includes('/order-received/') ||
            url.includes('order-received') ||
            url.includes('thank-you') ||
            document.querySelector('.woocommerce-order-received') ||
            document.querySelector('.woocommerce-thankyou-order-received')) {
          
          // Extract order information
          const orderIdMatch = url.match(/order-received\\/(\\d+)/);
          const orderKeyMatch = url.match(/key=([^&]+)/);
          
          // Send message to parent
          if (window.parent && window.parent !== window) {
            window.parent.postMessage({
              type: 'payment_success',
              url: url,
              order_id: orderIdMatch ? orderIdMatch[1] : null,
              order_key: orderKeyMatch ? orderKeyMatch[1] : null,
            }, '*');
          }
          
          return true;
        }
        
        return false;
      }
      
      // Check immediately and periodically
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', checkForSuccess);
      } else {
        checkForSuccess();
      }
      
      // Also check periodically
      setInterval(checkForSuccess, 3000);
      
      // Monitor for page changes
      let lastUrl = window.location.href;
      const observer = new MutationObserver(() => {
        const currentUrl = window.location.href;
        if (currentUrl !== lastUrl) {
          lastUrl = currentUrl;
          setTimeout(checkForSuccess, 1000);
        }
      });
      
      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });
    })();
  `;
  
  // Try to inject the script
  try {
    if (iframeDocument.head) {
      iframeDocument.head.appendChild(script);
    } else if (iframeDocument.body) {
      iframeDocument.body.appendChild(script);
    }
  } catch (error) {
    console.log('Could not inject script due to CORS:', error);
  }
};

export const handleIframeLoad = (iframe: HTMLIFrameElement) => {
  try {
    const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;
    if (iframeDocument) {
      injectIframeScript(iframeDocument);
    }
  } catch (error) {
    console.log('Cross-origin iframe, cannot inject script:', error);
  }
};
