// Simple PDF text extraction without complex dependencies
// We'll use a basic approach that works for most PDFs

export interface TextExtractionResult {
  success: boolean;
  text?: string;
  error?: string;
  metadata?: {
    fileType: string;
    fileName: string;
    fileSize: number;
    pageCount?: number;
    wordCount?: number;
  };
}

/**
 * Extract text from PDF files using improved parsing
 */
export async function extractTextFromPDF(file: File): Promise<TextExtractionResult> {
  console.log('Starting PDF text extraction for:', file.name);

  try {
    const arrayBuffer = await file.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);
    const pdfText = new TextDecoder('utf-8').decode(uint8Array);

    // Extract text from PDF streams and text objects
    let extractedText = '';

    // Method 1: Look for text in parentheses (Tj commands)
    const tjMatches = pdfText.match(/\(([^)]+)\)\s*Tj/g);
    if (tjMatches) {
      tjMatches.forEach(match => {
        const text = match.match(/\(([^)]+)\)/);
        if (text && text[1]) {
          extractedText += text[1] + ' ';
        }
      });
    }

    // Method 2: Look for text in square brackets (TJ commands)
    const tjArrayMatches = pdfText.match(/\[([^\]]+)\]\s*TJ/g);
    if (tjArrayMatches) {
      tjArrayMatches.forEach(match => {
        const content = match.match(/\[([^\]]+)\]/);
        if (content && content[1]) {
          // Extract text from array format
          const textParts = content[1].match(/\(([^)]+)\)/g);
          if (textParts) {
            textParts.forEach(part => {
              const text = part.match(/\(([^)]+)\)/);
              if (text && text[1]) {
                extractedText += text[1] + ' ';
              }
            });
          }
        }
      });
    }

    // Method 3: Look for BT...ET blocks (text blocks)
    const textBlocks = pdfText.match(/BT\s+.*?ET/gs);
    if (textBlocks) {
      textBlocks.forEach(block => {
        // Extract text from within parentheses in text blocks
        const textMatches = block.match(/\(([^)]+)\)/g);
        if (textMatches) {
          textMatches.forEach(match => {
            const text = match.match(/\(([^)]+)\)/);
            if (text && text[1]) {
              extractedText += text[1] + ' ';
            }
          });
        }
      });
    }

    // Method 4: Look for stream content
    const streamMatches = pdfText.match(/stream\s+(.*?)\s+endstream/gs);
    if (streamMatches) {
      streamMatches.forEach(stream => {
        const content = stream.replace(/^stream\s+/, '').replace(/\s+endstream$/, '');
        // Look for readable text patterns in streams
        const readableText = content.match(/[a-zA-Z][a-zA-Z0-9\s.,!?;:'"()-]{3,}/g);
        if (readableText) {
          readableText.forEach(text => {
            if (!text.match(/^(obj|endobj|stream|endstream|xref|trailer|BT|ET|Tf|Tj|TJ)$/)) {
              extractedText += text + ' ';
            }
          });
        }
      });
    }

    // Clean and process the extracted text
    let cleanText = extractedText
      .replace(/\\n/g, ' ') // Replace literal \n with spaces
      .replace(/\\r/g, ' ') // Replace literal \r with spaces
      .replace(/\\t/g, ' ') // Replace literal \t with spaces
      .replace(/\\\(/g, '(') // Unescape parentheses
      .replace(/\\\)/g, ')') // Unescape parentheses
      .replace(/\\\\/g, '\\') // Unescape backslashes
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    // If we got meaningful text, return it
    if (cleanText.length > 50) {
      const wordCount = cleanText.split(/\s+/).filter(word => word.length > 0).length;

      console.log(`Successfully extracted ${wordCount} words from PDF`);

      return {
        success: true,
        text: cleanText,
        metadata: {
          fileType: 'PDF',
          fileName: file.name,
          fileSize: file.size,
          wordCount
        }
      };
    }

    // Fallback: Try to extract any readable ASCII text
    let fallbackText = '';
    let currentWord = '';

    for (let i = 0; i < uint8Array.length; i++) {
      const char = String.fromCharCode(uint8Array[i]);

      // Look for readable characters
      if ((uint8Array[i] >= 32 && uint8Array[i] <= 126) || uint8Array[i] === 10 || uint8Array[i] === 13) {
        if (char.match(/[a-zA-Z0-9.,!?;:'"()\-\s]/)) {
          currentWord += char;
        } else {
          if (currentWord.trim().length > 2) {
            fallbackText += currentWord + ' ';
          }
          currentWord = '';
        }
      } else {
        if (currentWord.trim().length > 2) {
          fallbackText += currentWord + ' ';
        }
        currentWord = '';
      }
    }

    // Clean fallback text
    cleanText = fallbackText
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s.,!?;:'"()\-]/g, ' ')
      .trim();

    // Filter out PDF commands and metadata
    const words = cleanText.split(/\s+/);
    const filteredWords = words.filter(word => {
      return !word.match(/^(PDF|obj|endobj|stream|endstream|xref|trailer|BT|ET|Tf|Tj|TJ|Type|Page|Parent|Resources|MediaBox|Contents|Length|Filter|FlateDecode)$/i) &&
             word.length > 1 &&
             !word.match(/^[0-9.]+$/) &&
             !word.match(/^[\/\\<>{}%\[\]]+$/);
    });

    cleanText = filteredWords.join(' ');

    if (cleanText.length > 50) {
      const wordCount = filteredWords.length;

      console.log(`Extracted ${wordCount} words from PDF using fallback method`);

      return {
        success: true,
        text: cleanText,
        metadata: {
          fileType: 'PDF',
          fileName: file.name,
          fileSize: file.size,
          wordCount
        }
      };
    }

    throw new Error('No readable text found in PDF');
  } catch (error) {
    console.error('PDF extraction error:', error);

    return {
      success: false,
      error: 'Failed to extract text from PDF. This PDF may be image-based or encrypted. Please try converting to TXT format or use a PDF with selectable text.',
      metadata: {
        fileType: 'PDF',
        fileName: file.name,
        fileSize: file.size
      }
    };
  }
}



/**
 * Main function to extract text from PDF files only
 */
export async function extractTextFromFile(file: File): Promise<TextExtractionResult> {
  const fileName = file.name.toLowerCase();
  const fileSize = file.size;

  // Check file size (limit to 10MB)
  if (fileSize > 10 * 1024 * 1024) {
    return {
      success: false,
      error: 'File size too large. Please use PDF files smaller than 10MB.',
      metadata: {
        fileType: 'PDF',
        fileName: file.name,
        fileSize
      }
    };
  }

  // Only accept PDF files
  if (!fileName.endsWith('.pdf')) {
    return {
      success: false,
      error: 'Only PDF files are supported. Please upload a PDF file.',
      metadata: {
        fileType: 'Unsupported',
        fileName: file.name,
        fileSize
      }
    };
  }

  return extractTextFromPDF(file);
}

/**
 * Validate and clean extracted text for quiz generation
 */
export function validateTextForQuiz(text: string): { isValid: boolean; error?: string; cleanText?: string } {
  if (!text || text.trim().length === 0) {
    return { isValid: false, error: 'No text content found' };
  }
  
  const cleanText = text.trim();
  
  if (cleanText.length < 100) {
    return { isValid: false, error: 'Text content is too short. Please provide at least 100 characters for meaningful quiz generation.' };
  }
  
  if (cleanText.length > 50000) {
    return { 
      isValid: true, 
      cleanText: cleanText.substring(0, 50000) + '...',
      error: 'Text was truncated to 50,000 characters for processing.'
    };
  }
  
  return { isValid: true, cleanText };
}
