import { supabase } from '@/integrations/supabase/client';

/**
 * Clear invalid authentication session and redirect to login
 */
export const clearInvalidSession = async () => {
  try {
    // Clear all auth-related localStorage items
    const authKeys = Object.keys(localStorage).filter(key =>
      key.startsWith('supabase.auth') ||
      key.includes('auth') ||
      key.includes('session')
    );

    authKeys.forEach(key => {
      localStorage.removeItem(key);
    });

    // Clear our custom auth state
    localStorage.removeItem('studyfam_auth_state');
    localStorage.removeItem('studyfam_user_id');

    // Sign out from Supabase (this will also clear the session)
    await supabase.auth.signOut();

    console.log('Invalid session cleared');
  } catch (error) {
    console.error('Error clearing invalid session:', error);

    // Still clear localStorage even if signOut fails
    localStorage.removeItem('studyfam_auth_state');
    localStorage.removeItem('studyfam_user_id');
  }
};

/**
 * Check if the current session is valid
 */
export const validateSession = async () => {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Session validation error:', error);
      await clearInvalidSession();
      return false;
    }
    
    if (!session) {
      return false;
    }
    
    // Check if token is expired
    const now = Math.floor(Date.now() / 1000);
    if (session.expires_at && session.expires_at < now) {
      console.log('Session expired, clearing...');
      await clearInvalidSession();
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error validating session:', error);
    await clearInvalidSession();
    return false;
  }
};

/**
 * Handle authentication errors gracefully
 */
export const handleAuthError = async (error: any) => {
  console.error('Authentication error:', error);
  
  // Check for specific error types that indicate invalid session
  if (
    error?.message?.includes('Invalid Refresh Token') ||
    error?.message?.includes('Refresh Token Not Found') ||
    error?.message?.includes('JWT expired') ||
    error?.status === 400
  ) {
    console.log('Invalid session detected, clearing...');
    await clearInvalidSession();
    
    // Redirect to login page
    if (window.location.pathname !== '/login' && window.location.pathname !== '/') {
      window.location.href = '/login';
    }
  }
};

/**
 * Initialize authentication with error handling
 */
export const initializeAuth = async () => {
  try {
    console.log('🔐 Initializing auth...');

    // Validate current session
    const isValid = await validateSession();

    if (!isValid) {
      console.log('ℹ️ No valid session found');

      // Clear localStorage if no session
      localStorage.removeItem('studyfam_auth_state');
      localStorage.removeItem('studyfam_user_id');

      return null;
    }

    // Get current user
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error) {
      await handleAuthError(error);
      return null;
    }

    if (user) {
      console.log('✅ Valid session found for user:', user.id);

      // Update localStorage with current auth state
      localStorage.setItem('studyfam_auth_state', 'authenticated');
      localStorage.setItem('studyfam_user_id', user.id);
    }

    return user;
  } catch (error) {
    await handleAuthError(error);
    return null;
  }
};
