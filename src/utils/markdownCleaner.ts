// Utility function to convert markdown to formatted HTML for display
export const cleanMarkdownForDisplay = (content: string): string => {
  return content
    // Remove existing HTML tags first
    .replace(/<[^>]*>/g, '')
    // Convert headers to styled text
    .replace(/^#{1}\s+(.+)$/gm, '<div style="font-size: 1.5em; font-weight: bold; margin: 1em 0 0.5em 0; color: #2563eb;">$1</div>')
    .replace(/^#{2}\s+(.+)$/gm, '<div style="font-size: 1.3em; font-weight: bold; margin: 0.8em 0 0.4em 0; color: #3b82f6;">$1</div>')
    .replace(/^#{3}\s+(.+)$/gm, '<div style="font-size: 1.1em; font-weight: bold; margin: 0.6em 0 0.3em 0; color: #6366f1;">$1</div>')
    .replace(/^#{4,6}\s+(.+)$/gm, '<div style="font-weight: bold; margin: 0.5em 0 0.2em 0; color: #8b5cf6;">$1</div>')
    // Convert bold markdown
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/__(.*?)__/g, '<strong>$1</strong>')
    // Convert italic markdown
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/_(.*?)_/g, '<em>$1</em>')
    // Remove strikethrough but keep content
    .replace(/~~(.*?)~~/g, '<del>$1</del>')
    // Convert code blocks to styled text
    .replace(/```[\s\S]*?```/g, '<div style="background: #f3f4f6; padding: 0.5em; border-radius: 4px; font-family: monospace; margin: 0.5em 0;">$&</div>')
    // Convert inline code
    .replace(/`([^`]+)`/g, '<code style="background: #f3f4f6; padding: 0.2em 0.4em; border-radius: 3px; font-family: monospace;">$1</code>')
    // Convert links to just the text (since we can't navigate)
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '<span style="color: #2563eb; text-decoration: underline;">$1</span>')
    // Convert unordered lists to bullet points
    .replace(/^[\s]*[-*+]\s+(.+)$/gm, '<div style="margin: 0.2em 0; padding-left: 1em;">• $1</div>')
    // Convert ordered lists to numbered points
    .replace(/^[\s]*(\d+)\.\s+(.+)$/gm, '<div style="margin: 0.2em 0; padding-left: 1em;">$1. $2</div>')
    // Convert blockquotes to styled text
    .replace(/^>\s+(.+)$/gm, '<div style="border-left: 4px solid #d1d5db; padding-left: 1em; margin: 0.5em 0; font-style: italic; color: #6b7280;">"$1"</div>')
    // Convert line breaks to HTML breaks
    .replace(/\n/g, '<br>')
    // Clean up extra spaces
    .replace(/&nbsp;/g, ' ')
    .replace(/[ \t]+/g, ' ')
    .trim();
};

// Utility function for plain text display (PDFs, etc.)
export const cleanMarkdownForPlainText = (content: string): string => {
  return content
    // Remove HTML tags first
    .replace(/<[^>]*>/g, '')
    // Convert headers to text with underlines
    .replace(/^#{1}\s+(.+)$/gm, '\n$1\n' + '='.repeat(50) + '\n')
    .replace(/^#{2}\s+(.+)$/gm, '\n$1\n' + '-'.repeat(30) + '\n')
    .replace(/^#{3,6}\s+(.+)$/gm, '\n• $1\n')
    // Remove bold/italic markdown but keep content
    .replace(/\*\*(.*?)\*\*/g, '$1')
    .replace(/__(.*?)__/g, '$1')
    .replace(/\*(.*?)\*/g, '$1')
    .replace(/_(.*?)_/g, '$1')
    // Remove other formatting
    .replace(/~~(.*?)~~/g, '$1')
    .replace(/```[\s\S]*?```/g, '')
    .replace(/`([^`]+)`/g, '$1')
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
    // Convert lists to plain text
    .replace(/^[\s]*[-*+]\s+(.+)$/gm, '  • $1')
    .replace(/^[\s]*(\d+)\.\s+(.+)$/gm, '  $1. $2')
    .replace(/^>\s+(.+)$/gm, '    "$1"')
    // Clean up spaces and line breaks
    .replace(/&nbsp;/g, ' ')
    .replace(/[ \t]+/g, ' ')
    .replace(/\n{3,}/g, '\n\n')
    .trim();
};
