// PDF export utility using jsPDF for reliable PDF generation
import jsPDF from 'jspdf';
import { cleanMarkdownForPlainText } from './markdownCleaner';

export interface PDFExportOptions {
  title: string;
  subject: string;
  topic: string;
  content: string;
  author?: string;
  includeMetadata?: boolean;
}



export const exportToPDF = async (options: PDFExportOptions): Promise<void> => {
  const { title, subject, topic, content, author = 'StudyFam User', includeMetadata = true } = options;

  try {
    // Create new jsPDF instance
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();
    const margin = 20;
    const maxWidth = pageWidth - 2 * margin;
    let yPosition = margin;

    // Add title
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(20);
    const titleLines = doc.splitTextToSize(title, maxWidth);
    titleLines.forEach((line: string) => {
      if (yPosition > pageHeight - margin) {
        doc.addPage();
        yPosition = margin;
      }
      doc.text(line, margin, yPosition);
      yPosition += 12;
    });
    yPosition += 10;

    // Add subtitle
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(14);
    doc.text(`${subject} - ${topic}`, margin, yPosition);
    yPosition += 20;

    // Add metadata if requested
    if (includeMetadata) {
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(10);
      const currentDate = new Date().toLocaleDateString();
      doc.text(`Author: ${author}`, margin, yPosition);
      yPosition += 8;
      doc.text(`Generated: ${currentDate}`, margin, yPosition);
      yPosition += 8;
      doc.text(`Word Count: ${content.split(' ').length} words`, margin, yPosition);
      yPosition += 15;
    }

    // Add content
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(12);

    // Clean markdown and HTML formatting from content
    const cleanContent = cleanMarkdownForPlainText(content);
    const paragraphs = cleanContent.split('\n').filter(p => p.trim());

    paragraphs.forEach((paragraph) => {
      if (paragraph.trim()) {
        const lines = doc.splitTextToSize(paragraph.trim(), maxWidth);
        lines.forEach((line: string) => {
          if (yPosition > pageHeight - margin) {
            doc.addPage();
            yPosition = margin;
          }
          doc.text(line, margin, yPosition);
          yPosition += 7;
        });
        yPosition += 5; // Space between paragraphs
      }
    });

    // Save the PDF
    const fileName = `${subject}-${topic}-notes.pdf`.replace(/[^a-zA-Z0-9-]/g, '_');
    doc.save(fileName);
    
  } catch (error) {
    console.error('PDF generation error:', error);
    throw new Error('Failed to generate PDF');
  }
};

// Alternative method using jsPDF (now the main method)
export const exportToPDFAdvanced = async (options: PDFExportOptions): Promise<void> => {
  // This is now the same as the main export function
  return exportToPDF(options);
};

// Utility to download content as a text file (fallback)
export const downloadAsText = (options: PDFExportOptions): void => {
  const { title, subject, topic, content, author = 'StudyFam User' } = options;
  const currentDate = new Date().toLocaleDateString();

  // Clean markdown formatting from content
  const cleanContent = cleanMarkdownForPlainText(content);

  const textContent = `
${title}
${subject} - ${topic}

Author: ${author}
Generated: ${currentDate}
Word Count: ${cleanContent.split(' ').length} words

---

${cleanContent}

---
Generated by StudyFam AI Notes
Visit studyfam.app for more study tools
  `.trim();

  const blob = new Blob([textContent], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = `${subject}-${topic}-notes.txt`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
};

// Quiz-specific PDF export options
export interface QuizPDFOptions {
  title: string;
  subject: string;
  difficulty: string;
  questions: Array<{
    id: string;
    type: string;
    question: string;
    options?: string[];
    correct_answer: string;
    explanation?: string;
  }>;
  includeAnswers?: boolean;
  includeExplanations?: boolean;
  estimatedTime?: number;
  author?: string;
}

// Export quiz as PDF
export const exportQuizToPDF = async (options: QuizPDFOptions): Promise<void> => {
  const {
    title,
    subject,
    difficulty,
    questions,
    includeAnswers = false,
    includeExplanations = false,
    estimatedTime,
    author = 'StudyFam Quiz Generator'
  } = options;

  try {
    // Create new jsPDF instance
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();
    const margin = 20;
    const maxWidth = pageWidth - (margin * 2);
    let yPosition = margin;

    // Helper function to add new page if needed
    const checkPageBreak = (requiredSpace: number = 20) => {
      if (yPosition + requiredSpace > pageHeight - margin) {
        doc.addPage();
        yPosition = margin;
      }
    };

    // Title and metadata
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(18);
    doc.text(title, margin, yPosition);
    yPosition += 15;

    doc.setFont('helvetica', 'normal');
    doc.setFontSize(12);
    doc.text(`Subject: ${subject}`, margin, yPosition);
    yPosition += 8;
    doc.text(`Difficulty: ${difficulty}`, margin, yPosition);
    yPosition += 8;
    doc.text(`Total Questions: ${questions.length}`, margin, yPosition);
    yPosition += 8;

    if (estimatedTime) {
      doc.text(`Estimated Time: ${estimatedTime} minutes`, margin, yPosition);
      yPosition += 8;
    }

    doc.text(`Generated: ${new Date().toLocaleDateString()}`, margin, yPosition);
    yPosition += 8;
    doc.text(`Author: ${author}`, margin, yPosition);
    yPosition += 20;

    // Add separator line
    doc.setLineWidth(0.5);
    doc.line(margin, yPosition, pageWidth - margin, yPosition);
    yPosition += 15;

    // Questions
    questions.forEach((question, index) => {
      checkPageBreak(40);

      // Question number and text
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(14);
      doc.text(`Question ${index + 1}:`, margin, yPosition);
      yPosition += 10;

      doc.setFont('helvetica', 'normal');
      doc.setFontSize(12);
      const questionLines = doc.splitTextToSize(question.question, maxWidth);
      questionLines.forEach((line: string) => {
        checkPageBreak();
        doc.text(line, margin, yPosition);
        yPosition += 7;
      });
      yPosition += 5;

      // Options (for multiple choice and true/false)
      if (question.options && question.options.length > 0) {
        question.options.forEach((option) => {
          checkPageBreak();
          const optionLines = doc.splitTextToSize(option, maxWidth - 10);
          optionLines.forEach((line: string, lineIndex: number) => {
            checkPageBreak();
            doc.text(line, margin + (lineIndex === 0 ? 0 : 10), yPosition);
            yPosition += 7;
          });
        });
        yPosition += 5;
      }

      // Answer space for short answer questions
      if (question.type === 'short-answer' && !includeAnswers) {
        checkPageBreak(20);
        doc.text('Answer:', margin, yPosition);
        yPosition += 10;
        // Add lines for writing
        for (let i = 0; i < 3; i++) {
          checkPageBreak();
          doc.setLineWidth(0.1);
          doc.line(margin, yPosition, pageWidth - margin, yPosition);
          yPosition += 8;
        }
        yPosition += 5;
      }

      // Correct answer (if included)
      if (includeAnswers) {
        checkPageBreak();
        doc.setFont('helvetica', 'bold');
        doc.text(`Correct Answer: ${question.correct_answer}`, margin, yPosition);
        yPosition += 10;
      }

      // Explanation (if included)
      if (includeExplanations && question.explanation) {
        checkPageBreak();
        doc.setFont('helvetica', 'italic');
        doc.setFontSize(10);
        const explanationLines = doc.splitTextToSize(`Explanation: ${question.explanation}`, maxWidth);
        explanationLines.forEach((line: string) => {
          checkPageBreak();
          doc.text(line, margin, yPosition);
          yPosition += 6;
        });
        yPosition += 5;
      }

      // Add space between questions
      yPosition += 10;

      // Add separator line between questions
      if (index < questions.length - 1) {
        checkPageBreak();
        doc.setLineWidth(0.2);
        doc.line(margin, yPosition, pageWidth - margin, yPosition);
        yPosition += 15;
      }
    });

    // Save the PDF
    const fileName = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_quiz.pdf`;
    doc.save(fileName);

  } catch (error) {
    console.error('Quiz PDF export error:', error);
    throw new Error('Failed to export quiz as PDF');
  }
};
