// This script should be injected into the WooCommerce checkout page
// to detect successful payments and communicate with the parent window

(function() {
  'use strict';

  // Function to send message to parent window
  function sendMessageToParent(type, data = {}) {
    if (window.parent && window.parent !== window) {
      window.parent.postMessage({
        type: type,
        ...data,
        timestamp: new Date().toISOString(),
      }, '*');
    }
  }

  // Check if we're on the order received (success) page
  function checkForOrderSuccess() {
    const url = window.location.href;
    const pathname = window.location.pathname;
    
    // Check various success indicators
    if (pathname.includes('/checkout/order-received/') || 
        pathname.includes('/order-received/') ||
        url.includes('order-received') ||
        url.includes('thank-you') ||
        document.querySelector('.woocommerce-order-received') ||
        document.querySelector('.woocommerce-thankyou-order-received')) {
      
      // Extract order information
      const orderIdMatch = url.match(/order-received\/(\d+)/);
      const orderKeyMatch = url.match(/key=([^&]+)/);
      
      const orderData = {
        order_id: orderIdMatch ? orderIdMatch[1] : null,
        order_key: orderKeyMatch ? orderKeyMatch[1] : null,
        url: url,
      };

      sendMessageToParent('woocommerce_payment_success', orderData);
      return true;
    }
    
    return false;
  }

  // Check for payment failure
  function checkForPaymentFailure() {
    const url = window.location.href;
    const pathname = window.location.pathname;
    
    if (pathname.includes('/checkout/') && 
        (url.includes('payment-failed') || 
         url.includes('cancelled') ||
         document.querySelector('.woocommerce-error'))) {
      
      sendMessageToParent('woocommerce_payment_failed', {
        url: url,
        error: document.querySelector('.woocommerce-error')?.textContent || 'Payment failed',
      });
      return true;
    }
    
    return false;
  }

  // Monitor for checkout form submission
  function monitorCheckoutForm() {
    const checkoutForm = document.querySelector('form.checkout');
    if (checkoutForm) {
      checkoutForm.addEventListener('submit', function() {
        sendMessageToParent('woocommerce_checkout_submitted');
      });
    }
  }

  // Monitor for page changes (for single-page checkout flows)
  function monitorPageChanges() {
    let lastUrl = window.location.href;
    
    const observer = new MutationObserver(function() {
      const currentUrl = window.location.href;
      if (currentUrl !== lastUrl) {
        lastUrl = currentUrl;
        setTimeout(() => {
          checkForOrderSuccess() || checkForPaymentFailure();
        }, 1000);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }

  // Initialize when DOM is ready
  function initialize() {
    // Check immediately if we're already on a success/failure page
    if (checkForOrderSuccess() || checkForPaymentFailure()) {
      return;
    }

    // Monitor checkout form
    monitorCheckoutForm();
    
    // Monitor for page changes
    monitorPageChanges();
    
    // Send ready message
    sendMessageToParent('woocommerce_iframe_ready');
  }

  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
  } else {
    initialize();
  }

  // Also check periodically in case we miss events
  setInterval(() => {
    checkForOrderSuccess() || checkForPaymentFailure();
  }, 5000);

})();
