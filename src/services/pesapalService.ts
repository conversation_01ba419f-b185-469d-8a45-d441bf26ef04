import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

// Pesapal API configuration
const PESAPAL_CONFIG = {
  // Use sandbox for testing, change to production when ready
  baseUrl: 'https://cybqa.pesapal.com/pesapalv3/api', // Sandbox
  // baseUrl: 'https://pay.pesapal.com/v3/api', // Production
  consumerKey: process.env.VITE_PESAPAL_CONSUMER_KEY || '',
  consumerSecret: process.env.VITE_PESAPAL_CONSUMER_SECRET || '',
};

export interface PesapalAuthResponse {
  token: string;
  expiryDate: string;
  error?: string;
  message?: string;
}

export interface PesapalOrderRequest {
  id: string;
  currency: string;
  amount: number;
  description: string;
  callback_url: string;
  notification_id: string;
  billing_address: {
    email_address: string;
    phone_number?: string;
    country_code?: string;
    first_name?: string;
    middle_name?: string;
    last_name?: string;
    line_1?: string;
    line_2?: string;
    city?: string;
    state?: string;
    postal_code?: string;
    zip_code?: string;
  };
}

export interface PesapalOrderResponse {
  order_tracking_id: string;
  merchant_reference: string;
  redirect_url: string;
  error?: string;
  message?: string;
}

export interface PesapalTransactionStatus {
  payment_method: string;
  amount: number;
  created_date: string;
  confirmation_code: string;
  payment_status_description: string;
  description: string;
  message: string;
  payment_account: string;
  call_back_url: string;
  status_code: number;
  merchant_reference: string;
  account_number: string;
  status: string;
}

class PesapalService {
  private baseUrl: string;
  private consumerKey: string;
  private consumerSecret: string;
  private authToken: string | null = null;
  private tokenExpiry: Date | null = null;

  constructor() {
    this.baseUrl = PESAPAL_CONFIG.baseUrl;
    this.consumerKey = PESAPAL_CONFIG.consumerKey;
    this.consumerSecret = PESAPAL_CONFIG.consumerSecret;
  }

  private async authenticate(): Promise<string> {
    // Check if we have a valid token
    if (this.authToken && this.tokenExpiry && new Date() < this.tokenExpiry) {
      return this.authToken;
    }

    try {
      const response = await fetch(`${this.baseUrl}/Auth/RequestToken`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          consumer_key: this.consumerKey,
          consumer_secret: this.consumerSecret,
        }),
      });

      const data: PesapalAuthResponse = await response.json();

      if (!response.ok || data.error) {
        throw new Error(data.message || 'Authentication failed');
      }

      this.authToken = data.token;
      this.tokenExpiry = new Date(data.expiryDate);
      
      return this.authToken;
    } catch (error) {
      console.error('Pesapal authentication error:', error);
      throw new Error('Failed to authenticate with Pesapal');
    }
  }

  private async makeAuthenticatedRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    const token = await this.authenticate();
    
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Pesapal API error: ${response.status}`);
    }

    return response.json();
  }

  async registerIPN(url: string, ipn_type: string = 'GET'): Promise<any> {
    try {
      return await this.makeAuthenticatedRequest('/URLSetup/RegisterIPN', {
        method: 'POST',
        body: JSON.stringify({
          url,
          ipn_type,
          ipn_notification_type: 'GET',
        }),
      });
    } catch (error) {
      console.error('Error registering IPN:', error);
      throw error;
    }
  }

  async submitOrder(orderData: PesapalOrderRequest): Promise<PesapalOrderResponse> {
    try {
      const response = await this.makeAuthenticatedRequest('/Transactions/SubmitOrderRequest', {
        method: 'POST',
        body: JSON.stringify(orderData),
      });

      if (response.error) {
        throw new Error(response.message || 'Failed to submit order');
      }

      return response;
    } catch (error) {
      console.error('Error submitting order:', error);
      toast.error('Failed to initialize payment');
      throw error;
    }
  }

  async getTransactionStatus(orderTrackingId: string): Promise<PesapalTransactionStatus> {
    try {
      return await this.makeAuthenticatedRequest(`/Transactions/GetTransactionStatus?orderTrackingId=${orderTrackingId}`);
    } catch (error) {
      console.error('Error getting transaction status:', error);
      throw error;
    }
  }

  async initializePayment(
    userId: string,
    userEmail: string,
    amount: number,
    orderId: string,
    description: string = 'StudyFam Subscription'
  ): Promise<PesapalOrderResponse> {
    const reference = `studyfam_${Date.now()}_${userId}`;
    const callbackUrl = `${window.location.origin}/payment/pesapal-callback`;
    
    // Register IPN if not already done
    try {
      await this.registerIPN(`${window.location.origin}/api/pesapal-ipn`);
    } catch (error) {
      console.warn('IPN registration failed:', error);
    }

    const orderData: PesapalOrderRequest = {
      id: reference,
      currency: 'KES', // Kenyan Shilling
      amount: amount,
      description: description,
      callback_url: callbackUrl,
      notification_id: reference,
      billing_address: {
        email_address: userEmail,
        first_name: 'StudyFam',
        last_name: 'User',
        country_code: 'KE',
      },
    };

    return this.submitOrder(orderData);
  }

  async verifyPayment(orderTrackingId: string): Promise<boolean> {
    try {
      const status = await this.getTransactionStatus(orderTrackingId);
      return status.status === 'COMPLETED' || status.payment_status_description === 'COMPLETED';
    } catch (error) {
      console.error('Payment verification error:', error);
      return false;
    }
  }

  // Store payment transaction in database
  async recordTransaction(
    userId: string,
    orderTrackingId: string,
    merchantReference: string,
    amount: number,
    status: string,
    woocommerceOrderId?: number
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('payment_transactions')
        .insert({
          user_id: userId,
          pesapal_tracking_id: orderTrackingId,
          merchant_reference: merchantReference,
          amount_cents: amount * 100, // Convert to cents
          currency: 'KES',
          status: status,
          transaction_type: 'subscription',
          woocommerce_order_id: woocommerceOrderId,
          metadata: {
            payment_gateway: 'pesapal',
            order_tracking_id: orderTrackingId,
          },
        });

      if (error) {
        console.error('Error recording transaction:', error);
        throw error;
      }
    } catch (error) {
      console.error('Failed to record transaction:', error);
      throw error;
    }
  }
}

export const pesapalService = new PesapalService();
