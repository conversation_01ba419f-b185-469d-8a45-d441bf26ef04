import { supabase } from '@/integrations/supabase/client';

export interface GenerateNotesRequest {
  subject: string;
  topic: string;
  noteLength?: 'short' | 'medium' | 'long' | 'detailed';
  includeExamples?: boolean;
  includeKeyPoints?: boolean;
  includeSummary?: boolean;
  customInstructions?: string;
}

export interface GenerateNotesResponse {
  success: boolean;
  content?: string;
  metadata?: {
    subject: string;
    topic: string;
    wordCount: number;
    tokensUsed: number;
    generationTime: number;
    model: string;
  };
  error?: string;
}

export interface AINote {
  id: string;
  user_id: string;
  folder_id?: string;
  title: string;
  subject: string;
  topic: string;
  original_content: string;
  edited_content?: string;
  content_format: 'markdown' | 'html' | 'plain';
  generation_prompt?: string;
  ai_model: string;
  word_count?: number;
  is_favorite: boolean;
  created_at: string;
  updated_at: string;
  last_accessed_at: string;
}

export interface NoteFolder {
  id: string;
  user_id: string;
  name: string;
  parent_folder_id?: string;
  created_at: string;
  updated_at: string;
}

export interface AINotesPreferences {
  user_id: string;
  default_ai_model: string;
  preferred_note_format: string;
  auto_save_enabled: boolean;
  default_note_length: 'short' | 'medium' | 'long' | 'detailed';
  include_examples: boolean;
  include_key_points: boolean;
  include_summary: boolean;
  created_at: string;
  updated_at: string;
}

// Generate AI Notes with fallback
export const generateAINotes = async (request: GenerateNotesRequest): Promise<GenerateNotesResponse> => {
  try {
    console.log('Attempting to generate AI notes with request:', request);

    // Try Edge Function first
    const { data, error } = await supabase.functions.invoke('generate-ai-notes', {
      body: request
    });

    console.log('Edge Function response:', { data, error });

    if (error) {
      console.info('AI Edge Function error, using fallback generator:', error);
      const fallbackResult = generateFallbackNotes(request);
      // Add demo mode indicator
      if (fallbackResult.success && fallbackResult.content) {
        fallbackResult.content = `> **Demo Mode**: AI service unavailable, showing sample content\n\n${fallbackResult.content}`;
      }
      return fallbackResult;
    }

    if (data && data.success) {
      console.log('AI Edge Function successful!');
      return data;
    } else {
      console.info('AI Edge Function returned unsuccessful response, using fallback');
      return generateFallbackNotes(request);
    }
  } catch (error) {
    console.info('AI Edge Function not deployed or failed, using fallback generator:', error);
    const fallbackResult = generateFallbackNotes(request);
    // Add demo mode indicator
    if (fallbackResult.success && fallbackResult.content) {
      fallbackResult.content = `> **Demo Mode**: AI service unavailable, showing sample content\n\n${fallbackResult.content}`;
    }
    return fallbackResult;
  }
};

// Fallback note generation when Edge Function is not available
const generateFallbackNotes = (request: GenerateNotesRequest): GenerateNotesResponse => {
  const { subject, topic, noteLength = 'medium', includeExamples = true, includeKeyPoints = true, includeSummary = true } = request;

  const lengthMultiplier = {
    short: 0.5,
    medium: 1,
    long: 1.5,
    detailed: 2
  }[noteLength];

  const baseContent = `# ${topic}

## Introduction
${topic} is an important concept in ${subject} that students need to understand thoroughly. This topic covers fundamental principles and applications that are essential for academic success.

## Key Concepts
- **Definition**: ${topic} refers to the core principles and methodologies within ${subject}
- **Importance**: Understanding this topic is crucial for mastering ${subject}
- **Applications**: This knowledge can be applied in various real-world scenarios

${includeKeyPoints ? `## Key Points
- Fundamental understanding of ${topic} concepts
- Practical applications in ${subject}
- Relationship to other topics in the curriculum
- Problem-solving approaches and methodologies
- Critical thinking skills development` : ''}

## Detailed Explanation
The study of ${topic} in ${subject} involves understanding both theoretical foundations and practical applications. Students should focus on:

1. **Core Principles**: The fundamental concepts that underpin this topic
2. **Methodology**: How to approach problems and questions related to ${topic}
3. **Analysis**: Critical thinking skills needed to evaluate information
4. **Synthesis**: Combining different concepts to form comprehensive understanding

${includeExamples ? `## Examples and Applications
- **Example 1**: Practical application of ${topic} in real-world scenarios
- **Example 2**: Case study demonstrating key principles
- **Example 3**: Problem-solving exercise with step-by-step solution
- **Practice Questions**: Self-assessment opportunities to test understanding` : ''}

## Study Tips
- Review the material regularly to reinforce understanding
- Practice with examples and exercises
- Connect this topic to other areas of ${subject}
- Seek clarification on difficult concepts
- Form study groups to discuss and explore ideas

${includeSummary ? `## Summary
${topic} is a fundamental aspect of ${subject} that requires careful study and practice. By understanding the key concepts, practicing with examples, and connecting ideas to broader themes, students can develop mastery of this important topic. Regular review and active engagement with the material will lead to academic success.` : ''}

## Additional Resources
- Textbook chapters related to ${topic}
- Online resources and educational videos
- Practice exercises and past exam questions
- Study groups and peer discussions
- Office hours with instructors for additional support`;

  const adjustedContent = lengthMultiplier < 1
    ? baseContent.split('\n').slice(0, Math.floor(baseContent.split('\n').length * lengthMultiplier)).join('\n')
    : baseContent + (lengthMultiplier > 1 ? '\n\n## Extended Study\nFor advanced understanding, consider exploring related topics and conducting independent research to deepen your knowledge of ' + topic + ' within the broader context of ' + subject + '.' : '');

  return {
    success: true,
    content: adjustedContent,
    metadata: {
      subject,
      topic,
      wordCount: adjustedContent.split(' ').length,
      tokensUsed: 0,
      generationTime: 500,
      model: 'fallback-generator'
    }
  };
};

// Save AI Note
export const saveAINote = async (noteData: {
  title: string;
  subject: string;
  topic: string;
  original_content: string;
  edited_content?: string;
  folder_id?: string;
  content_format?: 'markdown' | 'html' | 'plain';
  generation_prompt?: string;
  ai_model?: string;
  word_count?: number;
}): Promise<{ success: boolean; note?: AINote; error?: string }> => {
  try {
    const { data, error } = await supabase
      .from('ai_notes')
      .insert([{
        ...noteData,
        content_format: noteData.content_format || 'markdown',
        ai_model: noteData.ai_model || 'gpt-3.5-turbo'
      }])
      .select()
      .single();

    if (error) {
      console.error('Error saving AI note:', error);
      return {
        success: false,
        error: error.message
      };
    }

    return {
      success: true,
      note: data
    };
  } catch (error) {
    console.error('Unexpected error saving AI note:', error);
    return {
      success: false,
      error: 'An unexpected error occurred while saving the note'
    };
  }
};

// Update AI Note
export const updateAINote = async (noteId: string, updates: {
  title?: string;
  edited_content?: string;
  is_favorite?: boolean;
  folder_id?: string;
}): Promise<{ success: boolean; note?: AINote; error?: string }> => {
  try {
    const { data, error } = await supabase
      .from('ai_notes')
      .update({
        ...updates,
        last_accessed_at: new Date().toISOString()
      })
      .eq('id', noteId)
      .select()
      .single();

    if (error) {
      console.error('Error updating AI note:', error);
      return {
        success: false,
        error: error.message
      };
    }

    return {
      success: true,
      note: data
    };
  } catch (error) {
    console.error('Unexpected error updating AI note:', error);
    return {
      success: false,
      error: 'An unexpected error occurred while updating the note'
    };
  }
};

// Get AI Notes
export const getAINotes = async (filters?: {
  subject?: string;
  folder_id?: string;
  is_favorite?: boolean;
  limit?: number;
  offset?: number;
}): Promise<{ success: boolean; notes?: AINote[]; error?: string }> => {
  try {
    let query = supabase
      .from('ai_notes')
      .select('*')
      .order('last_accessed_at', { ascending: false });

    if (filters?.subject) {
      query = query.eq('subject', filters.subject);
    }
    if (filters?.folder_id) {
      query = query.eq('folder_id', filters.folder_id);
    }
    if (filters?.is_favorite !== undefined) {
      query = query.eq('is_favorite', filters.is_favorite);
    }
    if (filters?.limit) {
      query = query.limit(filters.limit);
    }
    if (filters?.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching AI notes:', error);
      return {
        success: false,
        error: error.message
      };
    }

    return {
      success: true,
      notes: data
    };
  } catch (error) {
    console.error('Unexpected error fetching AI notes:', error);
    return {
      success: false,
      error: 'An unexpected error occurred while fetching notes'
    };
  }
};

// Delete AI Note
export const deleteAINote = async (noteId: string): Promise<{ success: boolean; error?: string }> => {
  try {
    const { error } = await supabase
      .from('ai_notes')
      .delete()
      .eq('id', noteId);

    if (error) {
      console.error('Error deleting AI note:', error);
      return {
        success: false,
        error: error.message
      };
    }

    return { success: true };
  } catch (error) {
    console.error('Unexpected error deleting AI note:', error);
    return {
      success: false,
      error: 'An unexpected error occurred while deleting the note'
    };
  }
};

// Folder Management
export const createFolder = async (name: string, parentFolderId?: string): Promise<{ success: boolean; folder?: NoteFolder; error?: string }> => {
  try {
    const { data, error } = await supabase
      .from('note_folders')
      .insert([{
        name,
        parent_folder_id: parentFolderId
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating folder:', error);
      return {
        success: false,
        error: error.message
      };
    }

    return {
      success: true,
      folder: data
    };
  } catch (error) {
    console.error('Unexpected error creating folder:', error);
    return {
      success: false,
      error: 'An unexpected error occurred while creating the folder'
    };
  }
};

export const getFolders = async (): Promise<{ success: boolean; folders?: NoteFolder[]; error?: string }> => {
  try {
    const { data, error } = await supabase
      .from('note_folders')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching folders:', error);
      return {
        success: false,
        error: error.message
      };
    }

    return {
      success: true,
      folders: data
    };
  } catch (error) {
    console.error('Unexpected error fetching folders:', error);
    return {
      success: false,
      error: 'An unexpected error occurred while fetching folders'
    };
  }
};

// User Preferences
export const getUserPreferences = async (): Promise<{ success: boolean; preferences?: AINotesPreferences; error?: string }> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return {
        success: false,
        error: 'User not authenticated'
      };
    }

    const { data, error } = await supabase
      .from('ai_notes_preferences')
      .select('*')
      .eq('user_id', user.id)
      .maybeSingle();

    if (error) {
      console.error('Error fetching user preferences:', error);
      return {
        success: false,
        error: error.message
      };
    }

    return {
      success: true,
      preferences: data
    };
  } catch (error) {
    console.error('Unexpected error fetching user preferences:', error);
    return {
      success: false,
      error: 'An unexpected error occurred while fetching preferences'
    };
  }
};

export const updateUserPreferences = async (preferences: Partial<AINotesPreferences>): Promise<{ success: boolean; error?: string }> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return {
        success: false,
        error: 'User not authenticated'
      };
    }

    const { error } = await supabase
      .from('ai_notes_preferences')
      .upsert([{ ...preferences, user_id: user.id }]);

    if (error) {
      console.error('Error updating user preferences:', error);
      return {
        success: false,
        error: error.message
      };
    }

    return { success: true };
  } catch (error) {
    console.error('Unexpected error updating user preferences:', error);
    return {
      success: false,
      error: 'An unexpected error occurred while updating preferences'
    };
  }
};
