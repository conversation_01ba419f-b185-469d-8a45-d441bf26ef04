import { toast } from 'sonner';

// WooCommerce API configuration
const WOOCOMMERCE_CONFIG = {
  baseUrl: 'https://studyfam.co.ke',
  consumerKey: 'ck_c5c40420db86d92e18e2628dff01d2b5b9c39356',
  consumerSecret: 'cs_187ac0a88c1f66032647235d060acb654195aa2e',
  productId: 'studyfam-subscripton', // Product slug from the URL
};

export interface WooCommerceProduct {
  id: number;
  name: string;
  slug: string;
  price: string;
  regular_price: string;
  sale_price: string;
  description: string;
  short_description: string;
  permalink: string;
  images: Array<{
    id: number;
    src: string;
    alt: string;
  }>;
  attributes: Array<{
    id: number;
    name: string;
    options: string[];
  }>;
}

export interface CheckoutData {
  billing: {
    first_name: string;
    last_name: string;
    email: string;
    phone?: string;
    country?: string;
    address_1?: string;
    city?: string;
  };
  line_items: Array<{
    product_id: number;
    quantity: number;
  }>;
  payment_method: string;
  payment_method_title: string;
  set_paid: boolean;
  meta_data?: Array<{
    key: string;
    value: string;
  }>;
}

export interface WooCommerceOrder {
  id: number;
  status: string;
  total: string;
  currency: string;
  date_created: string;
  payment_method: string;
  payment_method_title: string;
  billing: any;
  line_items: any[];
  meta_data: any[];
}

class WooCommerceService {
  private baseUrl: string;
  private consumerKey: string;
  private consumerSecret: string;

  constructor() {
    this.baseUrl = WOOCOMMERCE_CONFIG.baseUrl;
    this.consumerKey = WOOCOMMERCE_CONFIG.consumerKey;
    this.consumerSecret = WOOCOMMERCE_CONFIG.consumerSecret;
  }

  private getAuthHeader(): string {
    const credentials = btoa(`${this.consumerKey}:${this.consumerSecret}`);
    return `Basic ${credentials}`;
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
    const url = `${this.baseUrl}/wp-json/wc/v3${endpoint}`;
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': this.getAuthHeader(),
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `WooCommerce API error: ${response.status}`);
    }

    return response.json();
  }

  async getProductBySlug(slug: string): Promise<WooCommerceProduct | null> {
    try {
      const products = await this.makeRequest(`/products?slug=${slug}`);
      return products.length > 0 ? products[0] : null;
    } catch (error) {
      console.error('Error fetching product:', error);
      toast.error('Failed to fetch product details');
      return null;
    }
  }

  async getProduct(productId: number): Promise<WooCommerceProduct | null> {
    try {
      return await this.makeRequest(`/products/${productId}`);
    } catch (error) {
      console.error('Error fetching product:', error);
      toast.error('Failed to fetch product details');
      return null;
    }
  }

  async createOrder(orderData: CheckoutData): Promise<WooCommerceOrder | null> {
    try {
      return await this.makeRequest('/orders', {
        method: 'POST',
        body: JSON.stringify(orderData),
      });
    } catch (error) {
      console.error('Error creating order:', error);
      toast.error('Failed to create order');
      return null;
    }
  }

  async getOrder(orderId: number): Promise<WooCommerceOrder | null> {
    try {
      return await this.makeRequest(`/orders/${orderId}`);
    } catch (error) {
      console.error('Error fetching order:', error);
      return null;
    }
  }

  async updateOrderStatus(orderId: number, status: string): Promise<WooCommerceOrder | null> {
    try {
      return await this.makeRequest(`/orders/${orderId}`, {
        method: 'PUT',
        body: JSON.stringify({ status }),
      });
    } catch (error) {
      console.error('Error updating order status:', error);
      return null;
    }
  }

  // Get the checkout URL for the subscription product
  getCheckoutUrl(userId?: string): string {
    const baseCheckoutUrl = `${this.baseUrl}/checkout/`;
    const productUrl = `${this.baseUrl}/product/${WOOCOMMERCE_CONFIG.productId}/`;
    
    // Add user metadata if available
    const params = new URLSearchParams();
    if (userId) {
      params.append('user_id', userId);
    }
    
    // Return product page URL which will have "Add to Cart" -> Checkout flow
    return productUrl + (params.toString() ? `?${params.toString()}` : '');
  }

  // Get direct add to cart URL
  getAddToCartUrl(productId: number, userId?: string): string {
    const params = new URLSearchParams();
    params.append('add-to-cart', productId.toString());
    if (userId) {
      params.append('user_id', userId);
    }
    
    return `${this.baseUrl}/?${params.toString()}`;
  }

  // Get subscription product details
  async getSubscriptionProduct(): Promise<WooCommerceProduct | null> {
    return this.getProductBySlug(WOOCOMMERCE_CONFIG.productId);
  }
}

export const woocommerceService = new WooCommerceService();
