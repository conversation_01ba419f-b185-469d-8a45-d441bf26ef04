import { supabase } from '@/integrations/supabase/client';

export interface SubscriptionStatus {
  isActive: boolean;
  planName: string | null;
  expiresAt: string | null;
  daysRemaining: number;
  isTrial: boolean;
}

export class SubscriptionService {
  // Check if user has active subscription
  async checkActiveSubscription(userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('user_subscriptions')
        .select('current_period_end, status')
        .eq('user_id', userId)
        .eq('status', 'active')
        .single();

      if (error || !data) {
        return false;
      }

      // Check if subscription is still valid
      const now = new Date();
      const expiresAt = new Date(data.current_period_end);
      
      return expiresAt > now;
    } catch (error) {
      console.error('Error checking subscription:', error);
      return false;
    }
  }

  // Get detailed subscription status
  async getSubscriptionStatus(userId: string): Promise<SubscriptionStatus> {
    try {
      const { data, error } = await supabase
        .from('user_subscriptions')
        .select(`
          status,
          current_period_end,
          is_trial,
          subscription_plans (
            name
          )
        `)
        .eq('user_id', userId)
        .eq('status', 'active')
        .single();

      if (error || !data) {
        return {
          isActive: false,
          planName: null,
          expiresAt: null,
          daysRemaining: 0,
          isTrial: false,
        };
      }

      const now = new Date();
      const expiresAt = new Date(data.current_period_end);
      const isActive = expiresAt > now;
      const daysRemaining = Math.max(0, Math.ceil((expiresAt.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));

      return {
        isActive,
        planName: data.subscription_plans?.name || 'Premium Plan',
        expiresAt: data.current_period_end,
        daysRemaining,
        isTrial: data.is_trial || false,
      };
    } catch (error) {
      console.error('Error getting subscription status:', error);
      return {
        isActive: false,
        planName: null,
        expiresAt: null,
        daysRemaining: 0,
        isTrial: false,
      };
    }
  }

  // Start free trial
  async startFreeTrial(userId: string): Promise<void> {
    try {
      // Get the active plan
      const { data: plan } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .single();

      if (!plan) {
        throw new Error('No active subscription plan found');
      }

      // Set trial to end in 1 minute for testing
      const trialEnd = new Date();
      trialEnd.setMinutes(trialEnd.getMinutes() + 1);

      const { error } = await supabase
        .from('user_subscriptions')
        .upsert({
          user_id: userId,
          plan_id: plan.id,
          status: 'active',
          current_period_start: new Date().toISOString(),
          current_period_end: trialEnd.toISOString(),
          is_trial: true,
          trial_started_at: new Date().toISOString(),
        }, {
          onConflict: 'user_id',
        });

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error starting free trial:', error);
      throw error;
    }
  }

  // Cancel subscription
  async cancelSubscription(userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('user_subscriptions')
        .update({
          status: 'cancelled',
          cancelled_at: new Date().toISOString(),
        })
        .eq('user_id', userId);

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      throw error;
    }
  }
}

export const subscriptionService = new SubscriptionService();
