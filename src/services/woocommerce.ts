// Simple WooCommerce integration for StudyFam subscription
export const WOOCOMMERCE_CONFIG = {
  storeUrl: 'https://studyfam.co.ke',
  productSlug: 'studyfam-subscripton',
  consumerKey: 'ck_c5c40420db86d92e18e2628dff01d2b5b9c39356',
  consumerSecret: 'cs_187ac0a88c1f66032647235d060acb654195aa2e',
};

export class WooCommerceService {
  private baseUrl: string;
  private consumerKey: string;
  private consumerSecret: string;

  constructor() {
    this.baseUrl = WOOCOMMERCE_CONFIG.storeUrl;
    this.consumerKey = WOOCOMMERCE_CONFIG.consumerKey;
    this.consumerSecret = WOOCOMMERCE_CONFIG.consumerSecret;
  }

  // Get the product page URL for checkout
  getProductUrl(userId?: string): string {
    const productUrl = `${this.baseUrl}/product/${WOOCOMMERCE_CONFIG.productSlug}/`;
    
    if (userId) {
      const params = new URLSearchParams({ user_id: userId });
      return `${productUrl}?${params.toString()}`;
    }
    
    return productUrl;
  }

  // Get direct add to cart URL (bypasses product page)
  getAddToCartUrl(userId?: string): string {
    const params = new URLSearchParams();
    
    // You'll need to get the actual product ID from WooCommerce admin
    // For now, we'll use the product page approach
    return this.getProductUrl(userId);
  }

  // Check if URL indicates successful order
  isSuccessUrl(url: string): boolean {
    return url.includes('/checkout/order-received/') || 
           url.includes('/order-received/') ||
           url.includes('order-received') ||
           url.includes('thank-you');
  }

  // Extract order ID from success URL
  extractOrderId(url: string): string | null {
    const match = url.match(/order-received\/(\d+)/);
    return match ? match[1] : null;
  }

  // Extract order key from success URL
  extractOrderKey(url: string): string | null {
    const match = url.match(/key=([^&]+)/);
    return match ? match[1] : null;
  }
}

export const woocommerce = new WooCommerceService();
