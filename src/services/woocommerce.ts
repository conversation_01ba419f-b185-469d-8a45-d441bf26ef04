// Enhanced WooCommerce integration with REST API
export const WOOCOMMERCE_CONFIG = {
  storeUrl: 'https://studyfam.co.ke',
  productSlug: 'studyfam-subscripton',
  productId: 123, // You'll need to get this from WooCommerce admin
  consumerKey: 'ck_c5c40420db86d92e18e2628dff01d2b5b9c39356',
  consumerSecret: 'cs_187ac0a88c1f66032647235d060acb654195aa2e',
};

export class WooCommerceService {
  private baseUrl: string;
  private apiUrl: string;
  private consumerKey: string;
  private consumerSecret: string;

  constructor() {
    this.baseUrl = WOOCOMMERCE_CONFIG.storeUrl;
    this.apiUrl = `${this.baseUrl}/wp-json/wc/v3`;
    this.consumerKey = WOOCOMMERCE_CONFIG.consumerKey;
    this.consumerSecret = WOOCOMMERCE_CONFIG.consumerSecret;
  }

  // Make authenticated API request
  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const auth = btoa(`${this.consumerKey}:${this.consumerSecret}`);

    const response = await fetch(`${this.apiUrl}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`WooCommerce API Error: ${response.status} - ${error}`);
    }

    return response.json();
  }

  // Create a new order via API
  async createOrder(customerData: {
    email: string;
    firstName: string;
    lastName: string;
    userId: string;
  }) {
    const orderData = {
      payment_method: 'pesapal', // or whatever payment method you're using
      payment_method_title: 'Pesapal',
      set_paid: false,
      billing: {
        first_name: customerData.firstName,
        last_name: customerData.lastName,
        email: customerData.email,
      },
      shipping: {
        first_name: customerData.firstName,
        last_name: customerData.lastName,
      },
      line_items: [
        {
          product_id: WOOCOMMERCE_CONFIG.productId,
          quantity: 1,
        },
      ],
      meta_data: [
        {
          key: 'studyfam_user_id',
          value: customerData.userId,
        },
        {
          key: 'studyfam_subscription',
          value: 'premium',
        },
      ],
    };

    return this.makeRequest('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });
  }

  // Get order status
  async getOrder(orderId: number) {
    return this.makeRequest(`/orders/${orderId}`);
  }

  // Check if order is paid
  async isOrderPaid(orderId: number): Promise<boolean> {
    try {
      const order = await this.getOrder(orderId);
      return order.status === 'completed' || order.status === 'processing';
    } catch (error) {
      console.error('Error checking order status:', error);
      return false;
    }
  }

  // Get checkout URL for existing order
  getOrderCheckoutUrl(orderId: number, orderKey: string): string {
    return `${this.baseUrl}/checkout/order-pay/${orderId}/?pay_for_order=true&key=${orderKey}`;
  }

  // Get the product page URL for checkout (fallback)
  getProductUrl(userId?: string): string {
    const productUrl = `${this.baseUrl}/product/${WOOCOMMERCE_CONFIG.productSlug}/`;

    if (userId) {
      const params = new URLSearchParams({ user_id: userId });
      return `${productUrl}?${params.toString()}`;
    }

    return productUrl;
  }

  // Check if URL indicates successful order
  isSuccessUrl(url: string): boolean {
    return url.includes('/checkout/order-received/') || 
           url.includes('/order-received/') ||
           url.includes('order-received') ||
           url.includes('thank-you');
  }

  // Extract order ID from success URL
  extractOrderId(url: string): string | null {
    const match = url.match(/order-received\/(\d+)/);
    return match ? match[1] : null;
  }

  // Extract order key from success URL
  extractOrderKey(url: string): string | null {
    const match = url.match(/key=([^&]+)/);
    return match ? match[1] : null;
  }
}

export const woocommerce = new WooCommerceService();
