import { supabase } from '@/integrations/supabase/client';
import { ReadingSession } from '@/hooks/useReadingTimetable';
import { toast } from 'sonner';

export interface TimetableNotification {
  sessionId: string;
  title: string;
  subject: string;
  startTime: string;
  endTime?: string;
  type: 'upcoming' | 'starting' | 'overdue';
  minutesUntil?: number;
}

class TimetableNotificationService {
  private checkInterval: NodeJS.Timeout | null = null;
  private notifiedSessions = new Set<string>();
  private isRunning = false;

  /**
   * Start the notification service
   */
  start() {
    if (this.isRunning) return;
    
    console.log('🔔 Starting timetable notification service...');
    this.isRunning = true;
    
    // Check immediately
    this.checkUpcomingSessions();
    
    // Check every minute
    this.checkInterval = setInterval(() => {
      this.checkUpcomingSessions();
    }, 60000); // 1 minute
  }

  /**
   * Stop the notification service
   */
  stop() {
    if (!this.isRunning) return;
    
    console.log('🔔 Stopping timetable notification service...');
    this.isRunning = false;
    
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
    
    this.notifiedSessions.clear();
  }

  /**
   * Check for upcoming sessions and send notifications
   */
  private async checkUpcomingSessions() {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const sessions = await this.getTodaysSessions();
      const now = new Date();
      const currentTime = this.timeToMinutes(now.getHours(), now.getMinutes());

      for (const session of sessions) {
        const sessionStartTime = this.parseTime(session.start_time);
        const sessionEndTime = session.end_time ? this.parseTime(session.end_time) : null;
        
        if (!sessionStartTime) continue;

        const minutesUntilStart = sessionStartTime - currentTime;
        const sessionKey = `${session.id}-${this.getDateKey(now)}`;

        // Check if session is starting (within 1 minute)
        if (minutesUntilStart <= 1 && minutesUntilStart >= -1 && !this.notifiedSessions.has(`${sessionKey}-starting`)) {
          await this.sendNotification({
            sessionId: session.id,
            title: session.title,
            subject: session.subject?.name || 'Study Session',
            startTime: session.start_time,
            endTime: session.end_time,
            type: 'starting',
            minutesUntil: minutesUntilStart
          });
          this.notifiedSessions.add(`${sessionKey}-starting`);
        }
        
        // Check if session is upcoming (15 minutes before)
        else if (minutesUntilStart <= 15 && minutesUntilStart > 1 && !this.notifiedSessions.has(`${sessionKey}-upcoming`)) {
          await this.sendNotification({
            sessionId: session.id,
            title: session.title,
            subject: session.subject?.name || 'Study Session',
            startTime: session.start_time,
            endTime: session.end_time,
            type: 'upcoming',
            minutesUntil: minutesUntilStart
          });
          this.notifiedSessions.add(`${sessionKey}-upcoming`);
        }
        
        // Check if session is overdue (15 minutes after start time)
        else if (sessionEndTime && currentTime > sessionEndTime + 15 && !this.notifiedSessions.has(`${sessionKey}-overdue`)) {
          await this.sendNotification({
            sessionId: session.id,
            title: session.title,
            subject: session.subject?.name || 'Study Session',
            startTime: session.start_time,
            endTime: session.end_time,
            type: 'overdue',
            minutesUntil: minutesUntilStart
          });
          this.notifiedSessions.add(`${sessionKey}-overdue`);
        }
      }

      // Clean up old notifications (older than today)
      this.cleanupOldNotifications();
    } catch (error) {
      console.error('Error checking upcoming sessions:', error);
    }
  }

  /**
   * Get today's active sessions
   */
  private async getTodaysSessions(): Promise<ReadingSession[]> {
    const today = new Date();
    const dayOfWeek = today.getDay();

    const { data, error } = await supabase
      .from('reading_sessions')
      .select(`
        *,
        subject:reading_subjects(*)
      `)
      .eq('is_active', true)
      .eq('day_of_week', dayOfWeek)
      .order('start_time');

    if (error) {
      console.error('Error fetching today\'s sessions:', error);
      return [];
    }

    return data as ReadingSession[];
  }

  /**
   * Check if timetable notifications are enabled for the user
   */
  private async areTimetableNotificationsEnabled(): Promise<boolean> {
    try {
      // Check localStorage for user's notification preferences
      const timetableNotifications = localStorage.getItem('studyfam_timetable_notifications');

      // Default to true if not set
      if (timetableNotifications === null) return true;

      return timetableNotifications === 'true';
    } catch (error) {
      console.error('Error checking timetable notification settings:', error);
      return true; // Default to enabled
    }
  }

  /**
   * Send notification to user
   */
  private async sendNotification(notification: TimetableNotification) {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Check if timetable notifications are enabled
      const notificationsEnabled = await this.areTimetableNotificationsEnabled();
      if (!notificationsEnabled) {
        console.log('Timetable notifications disabled for user, skipping notification');
        return;
      }

      let title = '';
      let message = '';
      let actionUrl = '/reading-timetable';

      switch (notification.type) {
        case 'upcoming':
          title = '📚 Upcoming Study Session';
          message = `${notification.subject}: ${notification.title} starts in ${notification.minutesUntil} minutes`;
          break;
        case 'starting':
          title = '🚀 Study Session Starting';
          message = `${notification.subject}: ${notification.title} is starting now!`;
          break;
        case 'overdue':
          title = '⏰ Study Session Overdue';
          message = `${notification.subject}: ${notification.title} session has ended. Mark it as complete?`;
          break;
      }

      // Create notification in database
      await supabase.rpc('create_notification', {
        p_user_id: user.id,
        p_type: 'system',
        p_title: title,
        p_message: message,
        p_data: {
          session_id: notification.sessionId,
          notification_type: 'timetable',
          session_type: notification.type,
          start_time: notification.startTime,
          end_time: notification.endTime
        },
        p_action_url: actionUrl
      });

      // Show toast notification
      const toastOptions = {
        duration: notification.type === 'starting' ? 10000 : 5000,
        action: {
          label: notification.type === 'overdue' ? 'Mark Complete' : 'View Timetable',
          onClick: () => {
            if (notification.type === 'overdue') {
              // Handle marking session as complete
              this.markSessionComplete(notification.sessionId);
            } else {
              window.location.href = actionUrl;
            }
          }
        }
      };

      switch (notification.type) {
        case 'upcoming':
          toast.info(message, toastOptions);
          break;
        case 'starting':
          toast.success(message, toastOptions);
          break;
        case 'overdue':
          toast.warning(message, toastOptions);
          break;
      }

      // Send browser push notification if permission granted
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(title, {
          body: message,
          icon: '/favicon.ico',
          tag: `timetable-${notification.sessionId}`,
          data: { url: actionUrl }
        });
      }

      console.log(`🔔 Sent ${notification.type} notification for session:`, notification.title);
    } catch (error) {
      console.error('Error sending notification:', error);
    }
  }

  /**
   * Mark session as complete
   */
  private async markSessionComplete(sessionId: string) {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      await supabase
        .from('reading_progress')
        .upsert({
          user_id: user.id,
          session_id: sessionId,
          session_date: today,
          status: 'completed',
          completion_percentage: 100,
          completed_at: new Date().toISOString(),
        });

      toast.success('Session marked as complete!');
    } catch (error) {
      console.error('Error marking session complete:', error);
      toast.error('Failed to mark session as complete');
    }
  }

  /**
   * Parse time string (HH:MM) to minutes since midnight
   */
  private parseTime(timeStr: string): number | null {
    if (!timeStr) return null;
    
    const [hours, minutes] = timeStr.split(':').map(Number);
    if (isNaN(hours) || isNaN(minutes)) return null;
    
    return this.timeToMinutes(hours, minutes);
  }

  /**
   * Convert hours and minutes to total minutes since midnight
   */
  private timeToMinutes(hours: number, minutes: number): number {
    return hours * 60 + minutes;
  }

  /**
   * Get a unique key for today's date
   */
  private getDateKey(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Clean up old notification tracking
   */
  private cleanupOldNotifications() {
    const today = this.getDateKey(new Date());
    const keysToRemove: string[] = [];

    this.notifiedSessions.forEach(key => {
      if (!key.includes(today)) {
        keysToRemove.push(key);
      }
    });

    keysToRemove.forEach(key => {
      this.notifiedSessions.delete(key);
    });
  }
}

// Export singleton instance
export const timetableNotificationService = new TimetableNotificationService();
