import { supabase } from '@/integrations/supabase/client';

export interface ContactFormData {
  name: string;
  email: string;
  message: string;
}

export interface EmailResponse {
  success: boolean;
  message: string;
}

// Send contact form message via Supabase Edge Function
export const sendContactMessage = async (formData: ContactFormData): Promise<EmailResponse> => {
  try {
    const { data, error } = await supabase.functions.invoke('send-contact-email', {
      body: {
        name: formData.name,
        email: formData.email,
        message: formData.message,
        to: '<EMAIL>',
        subject: `StudyFam Contact Form - Message from ${formData.name}`
      }
    });

    if (error) {
      console.error('Email service error:', error);
      return {
        success: false,
        message: 'Failed to send message. Please try again later.'
      };
    }

    return {
      success: true,
      message: 'Message sent successfully! We\'ll get back to you soon.'
    };
  } catch (error) {
    console.error('Unexpected error:', error);
    return {
      success: false,
      message: 'An unexpected error occurred. Please try again.'
    };
  }
};

// Fallback email service using EmailJS (client-side)
export const sendContactMessageFallback = async (formData: ContactFormData): Promise<EmailResponse> => {
  try {
    // Using a simple fetch to a contact form service
    const response = await fetch('/api/contact', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: formData.name,
        email: formData.email,
        message: formData.message,
        to: '<EMAIL>'
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to send message');
    }

    const result = await response.json();
    
    return {
      success: true,
      message: 'Message sent successfully! We\'ll get back to you soon.'
    };
  } catch (error) {
    console.error('Fallback email error:', error);
    
    // Final fallback - store in database for manual processing
    try {
      const { error: dbError } = await supabase
        .from('contact_messages')
        .insert([
          {
            name: formData.name,
            email: formData.email,
            message: formData.message,
            status: 'pending',
            created_at: new Date().toISOString()
          }
        ]);

      if (dbError) {
        throw dbError;
      }

      return {
        success: true,
        message: 'Message received! We\'ll get back to you within 24 hours.'
      };
    } catch (dbError) {
      console.error('Database fallback error:', dbError);
      return {
        success: false,
        message: 'Unable to send message. Please email us <NAME_EMAIL>'
      };
    }
  }
};

// Main email service with fallback chain
export const sendEmail = async (formData: ContactFormData): Promise<EmailResponse> => {
  // Try primary service first
  const primaryResult = await sendContactMessage(formData);
  
  if (primaryResult.success) {
    return primaryResult;
  }
  
  // If primary fails, try fallback
  console.log('Primary email service failed, trying fallback...');
  return await sendContactMessageFallback(formData);
};
