export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      ai_chat_conversations: {
        Row: {
          created_at: string
          id: string
          title: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          title?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          title?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "ai_chat_conversations_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      ai_chat_messages: {
        Row: {
          content: string
          conversation_id: string
          created_at: string
          id: string
          role: string
        }
        Insert: {
          content: string
          conversation_id: string
          created_at?: string
          id?: string
          role: string
        }
        Update: {
          content?: string
          conversation_id?: string
          created_at?: string
          id?: string
          role?: string
        }
        Relationships: [
          {
            foreignKeyName: "ai_chat_messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "ai_chat_conversations"
            referencedColumns: ["id"]
          },
        ]
      }
      ai_notes: {
        Row: {
          ai_model: string | null
          content_format: string | null
          created_at: string | null
          edited_content: string | null
          folder_id: string | null
          generation_prompt: string | null
          id: string
          is_favorite: boolean | null
          last_accessed_at: string | null
          original_content: string
          subject: string
          title: string
          topic: string
          updated_at: string | null
          user_id: string
          word_count: number | null
        }
        Insert: {
          ai_model?: string | null
          content_format?: string | null
          created_at?: string | null
          edited_content?: string | null
          folder_id?: string | null
          generation_prompt?: string | null
          id?: string
          is_favorite?: boolean | null
          last_accessed_at?: string | null
          original_content: string
          subject: string
          title: string
          topic: string
          updated_at?: string | null
          user_id: string
          word_count?: number | null
        }
        Update: {
          ai_model?: string | null
          content_format?: string | null
          created_at?: string | null
          edited_content?: string | null
          folder_id?: string | null
          generation_prompt?: string | null
          id?: string
          is_favorite?: boolean | null
          last_accessed_at?: string | null
          original_content?: string
          subject?: string
          title?: string
          topic?: string
          updated_at?: string | null
          user_id?: string
          word_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "ai_notes_folder_id_fkey"
            columns: ["folder_id"]
            isOneToOne: false
            referencedRelation: "note_folders"
            referencedColumns: ["id"]
          },
        ]
      }
      ai_notes_generation_log: {
        Row: {
          ai_model: string
          created_at: string | null
          error_message: string | null
          generation_time_ms: number | null
          id: string
          note_id: string | null
          prompt_used: string
          subject: string
          success: boolean | null
          tokens_used: number | null
          topic: string
          user_id: string
        }
        Insert: {
          ai_model: string
          created_at?: string | null
          error_message?: string | null
          generation_time_ms?: number | null
          id?: string
          note_id?: string | null
          prompt_used: string
          subject: string
          success?: boolean | null
          tokens_used?: number | null
          topic: string
          user_id: string
        }
        Update: {
          ai_model?: string
          created_at?: string | null
          error_message?: string | null
          generation_time_ms?: number | null
          id?: string
          note_id?: string | null
          prompt_used?: string
          subject?: string
          success?: boolean | null
          tokens_used?: number | null
          topic?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "ai_notes_generation_log_note_id_fkey"
            columns: ["note_id"]
            isOneToOne: false
            referencedRelation: "ai_notes"
            referencedColumns: ["id"]
          },
        ]
      }
      ai_notes_preferences: {
        Row: {
          auto_save_enabled: boolean | null
          created_at: string | null
          default_ai_model: string | null
          default_note_length: string | null
          include_examples: boolean | null
          include_key_points: boolean | null
          include_summary: boolean | null
          preferred_note_format: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          auto_save_enabled?: boolean | null
          created_at?: string | null
          default_ai_model?: string | null
          default_note_length?: string | null
          include_examples?: boolean | null
          include_key_points?: boolean | null
          include_summary?: boolean | null
          preferred_note_format?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          auto_save_enabled?: boolean | null
          created_at?: string | null
          default_ai_model?: string | null
          default_note_length?: string | null
          include_examples?: boolean | null
          include_key_points?: boolean | null
          include_summary?: boolean | null
          preferred_note_format?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      ai_tutor_logs: {
        Row: {
          created_at: string | null
          id: string
          message_count: number
          response_length: number
          subject: string
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          message_count?: number
          response_length?: number
          subject: string
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          message_count?: number
          response_length?: number
          subject?: string
          user_id?: string | null
        }
        Relationships: []
      }
      conversation_participants: {
        Row: {
          conversation_id: string
          id: string
          joined_at: string | null
          last_read_at: string | null
          role: string | null
          user_id: string
        }
        Insert: {
          conversation_id: string
          id?: string
          joined_at?: string | null
          last_read_at?: string | null
          role?: string | null
          user_id: string
        }
        Update: {
          conversation_id?: string
          id?: string
          joined_at?: string | null
          last_read_at?: string | null
          role?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "conversation_participants_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "conversation_participants_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      conversations: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          created_by: string | null
          description: string | null
          id: string
          last_message_at: string | null
          name: string | null
          type: string
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          last_message_at?: string | null
          name?: string | null
          type?: string
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          last_message_at?: string | null
          name?: string | null
          type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "conversations_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      countries: {
        Row: {
          code: string
          id: number
          name: string
        }
        Insert: {
          code: string
          id?: number
          name: string
        }
        Update: {
          code?: string
          id?: number
          name?: string
        }
        Relationships: []
      }
      courses: {
        Row: {
          category: string | null
          id: number
          name: string
        }
        Insert: {
          category?: string | null
          id?: number
          name: string
        }
        Update: {
          category?: string | null
          id?: number
          name?: string
        }
        Relationships: []
      }
      exams: {
        Row: {
          created_at: string
          description: string | null
          difficulty: Database["public"]["Enums"]["exam_difficulty"] | null
          exam_date: string
          id: string
          is_completed: boolean | null
          subject: string
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          difficulty?: Database["public"]["Enums"]["exam_difficulty"] | null
          exam_date: string
          id?: string
          is_completed?: boolean | null
          subject: string
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          description?: string | null
          difficulty?: Database["public"]["Enums"]["exam_difficulty"] | null
          exam_date?: string
          id?: string
          is_completed?: boolean | null
          subject?: string
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "exams_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      friendships: {
        Row: {
          addressee_id: string
          created_at: string
          id: string
          requester_id: string
          status: string | null
          updated_at: string
        }
        Insert: {
          addressee_id: string
          created_at?: string
          id?: string
          requester_id: string
          status?: string | null
          updated_at?: string
        }
        Update: {
          addressee_id?: string
          created_at?: string
          id?: string
          requester_id?: string
          status?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "friendships_addressee_id_fkey"
            columns: ["addressee_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "friendships_requester_id_fkey"
            columns: ["requester_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      group_invitations: {
        Row: {
          created_at: string | null
          group_id: string | null
          id: string
          invite_token: string | null
          invited_by: string | null
          invited_user_id: string | null
          status: string | null
        }
        Insert: {
          created_at?: string | null
          group_id?: string | null
          id?: string
          invite_token?: string | null
          invited_by?: string | null
          invited_user_id?: string | null
          status?: string | null
        }
        Update: {
          created_at?: string | null
          group_id?: string | null
          id?: string
          invite_token?: string | null
          invited_by?: string | null
          invited_user_id?: string | null
          status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "group_invitations_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "study_groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_invitations_invited_by_fkey"
            columns: ["invited_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_invitations_invited_user_id_fkey"
            columns: ["invited_user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      group_messages: {
        Row: {
          content: string
          created_at: string
          file_url: string | null
          group_id: string
          id: string
          message_type: string | null
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string
          file_url?: string | null
          group_id: string
          id?: string
          message_type?: string | null
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string
          file_url?: string | null
          group_id?: string
          id?: string
          message_type?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "group_messages_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "study_groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_messages_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      institutes: {
        Row: {
          country: string | null
          id: number
          name: string
          type: string | null
        }
        Insert: {
          country?: string | null
          id?: number
          name: string
          type?: string | null
        }
        Update: {
          country?: string | null
          id?: number
          name?: string
          type?: string | null
        }
        Relationships: []
      }
      messages: {
        Row: {
          content: string | null
          conversation_id: string
          created_at: string | null
          file_name: string | null
          file_size: number | null
          file_url: string | null
          id: string
          is_edited: boolean | null
          message_type: string | null
          reply_to_id: string | null
          sender_id: string
          updated_at: string | null
        }
        Insert: {
          content?: string | null
          conversation_id: string
          created_at?: string | null
          file_name?: string | null
          file_size?: number | null
          file_url?: string | null
          id?: string
          is_edited?: boolean | null
          message_type?: string | null
          reply_to_id?: string | null
          sender_id: string
          updated_at?: string | null
        }
        Update: {
          content?: string | null
          conversation_id?: string
          created_at?: string | null
          file_name?: string | null
          file_size?: number | null
          file_url?: string | null
          id?: string
          is_edited?: boolean | null
          message_type?: string | null
          reply_to_id?: string | null
          sender_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_reply_to_id_fkey"
            columns: ["reply_to_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      note_folders: {
        Row: {
          created_at: string | null
          id: string
          name: string
          parent_folder_id: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          name: string
          parent_folder_id?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string
          parent_folder_id?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "note_folders_parent_folder_id_fkey"
            columns: ["parent_folder_id"]
            isOneToOne: false
            referencedRelation: "note_folders"
            referencedColumns: ["id"]
          },
        ]
      }
      notes: {
        Row: {
          content: string | null
          created_at: string
          file_name: string | null
          file_size: number | null
          file_url: string | null
          id: string
          is_public: boolean | null
          note_type: Database["public"]["Enums"]["note_type"] | null
          tags: string[] | null
          title: string
          topic_id: string | null
          unit_id: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          content?: string | null
          created_at?: string
          file_name?: string | null
          file_size?: number | null
          file_url?: string | null
          id?: string
          is_public?: boolean | null
          note_type?: Database["public"]["Enums"]["note_type"] | null
          tags?: string[] | null
          title: string
          topic_id?: string | null
          unit_id?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          content?: string | null
          created_at?: string
          file_name?: string | null
          file_size?: number | null
          file_url?: string | null
          id?: string
          is_public?: boolean | null
          note_type?: Database["public"]["Enums"]["note_type"] | null
          tags?: string[] | null
          title?: string
          topic_id?: string | null
          unit_id?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notes_topic_id_fkey"
            columns: ["topic_id"]
            isOneToOne: false
            referencedRelation: "topics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notes_unit_id_fkey"
            columns: ["unit_id"]
            isOneToOne: false
            referencedRelation: "units"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notes_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      notification_preferences: {
        Row: {
          achievements: boolean | null
          created_at: string | null
          email_notifications: boolean | null
          friend_requests: boolean | null
          id: string
          push_notifications: boolean | null
          reading_reminders: boolean | null
          study_group_invites: boolean | null
          system_updates: boolean | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          achievements?: boolean | null
          created_at?: string | null
          email_notifications?: boolean | null
          friend_requests?: boolean | null
          id?: string
          push_notifications?: boolean | null
          reading_reminders?: boolean | null
          study_group_invites?: boolean | null
          system_updates?: boolean | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          achievements?: boolean | null
          created_at?: string | null
          email_notifications?: boolean | null
          friend_requests?: boolean | null
          id?: string
          push_notifications?: boolean | null
          reading_reminders?: boolean | null
          study_group_invites?: boolean | null
          system_updates?: boolean | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      notifications: {
        Row: {
          action_url: string | null
          created_at: string | null
          data: Json | null
          id: string
          message: string
          read: boolean | null
          sender_id: string | null
          title: string
          type: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          action_url?: string | null
          created_at?: string | null
          data?: Json | null
          id?: string
          message: string
          read?: boolean | null
          sender_id?: string | null
          title: string
          type: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          action_url?: string | null
          created_at?: string | null
          data?: Json | null
          id?: string
          message?: string
          read?: boolean | null
          sender_id?: string | null
          title?: string
          type?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      past_papers: {
        Row: {
          course_code: string | null
          created_at: string
          download_count: number | null
          exam_type: string | null
          file_url: string | null
          has_answers: boolean | null
          has_marking_scheme: boolean | null
          id: string
          is_public: boolean | null
          subject: string
          tags: string[] | null
          title: string
          university: string | null
          user_id: string | null
          year: number | null
        }
        Insert: {
          course_code?: string | null
          created_at?: string
          download_count?: number | null
          exam_type?: string | null
          file_url?: string | null
          has_answers?: boolean | null
          has_marking_scheme?: boolean | null
          id?: string
          is_public?: boolean | null
          subject: string
          tags?: string[] | null
          title: string
          university?: string | null
          user_id?: string | null
          year?: number | null
        }
        Update: {
          course_code?: string | null
          created_at?: string
          download_count?: number | null
          exam_type?: string | null
          file_url?: string | null
          has_answers?: boolean | null
          has_marking_scheme?: boolean | null
          id?: string
          is_public?: boolean | null
          subject?: string
          tags?: string[] | null
          title?: string
          university?: string | null
          user_id?: string | null
          year?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "past_papers_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      payment_transactions: {
        Row: {
          amount_cents: number
          completed_at: string | null
          confirmation_code: string | null
          created_at: string | null
          currency: string
          id: string
          metadata: Json | null
          order_tracking_id: string | null
          payment_account: string | null
          payment_data: Json | null
          payment_method: string | null
          payment_provider: string | null
          payment_reference: string | null
          status: string
          subscription_id: string | null
          transaction_type: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          amount_cents: number
          completed_at?: string | null
          confirmation_code?: string | null
          created_at?: string | null
          currency?: string
          id?: string
          metadata?: Json | null
          order_tracking_id?: string | null
          payment_account?: string | null
          payment_data?: Json | null
          payment_method?: string | null
          payment_provider?: string | null
          payment_reference?: string | null
          status: string
          subscription_id?: string | null
          transaction_type: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          amount_cents?: number
          completed_at?: string | null
          confirmation_code?: string | null
          created_at?: string | null
          currency?: string
          id?: string
          metadata?: Json | null
          order_tracking_id?: string | null
          payment_account?: string | null
          payment_data?: Json | null
          payment_method?: string | null
          payment_provider?: string | null
          payment_reference?: string | null
          status?: string
          subscription_id?: string | null
          transaction_type?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payment_transactions_subscription_id_fkey"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          bio: string | null
          country: string | null
          course: string | null
          courses: string[] | null
          created_at: string
          email: string
          full_name: string | null
          id: string
          institute: string | null
          is_online: boolean | null
          last_seen: string | null
          updated_at: string
        }
        Insert: {
          avatar_url?: string | null
          bio?: string | null
          country?: string | null
          course?: string | null
          courses?: string[] | null
          created_at?: string
          email: string
          full_name?: string | null
          id: string
          institute?: string | null
          is_online?: boolean | null
          last_seen?: string | null
          updated_at?: string
        }
        Update: {
          avatar_url?: string | null
          bio?: string | null
          country?: string | null
          course?: string | null
          courses?: string[] | null
          created_at?: string
          email?: string
          full_name?: string | null
          id?: string
          institute?: string | null
          is_online?: boolean | null
          last_seen?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      push_subscriptions: {
        Row: {
          auth_key: string
          created_at: string | null
          endpoint: string
          id: string
          p256dh_key: string
          updated_at: string | null
          user_agent: string | null
          user_id: string
        }
        Insert: {
          auth_key: string
          created_at?: string | null
          endpoint: string
          id?: string
          p256dh_key: string
          updated_at?: string | null
          user_agent?: string | null
          user_id: string
        }
        Update: {
          auth_key?: string
          created_at?: string | null
          endpoint?: string
          id?: string
          p256dh_key?: string
          updated_at?: string | null
          user_agent?: string | null
          user_id?: string
        }
        Relationships: []
      }
      quiz_attempts: {
        Row: {
          answers: Json | null
          completed_at: string | null
          id: string
          quiz_id: string
          score: number | null
          time_taken: number | null
          total_questions: number
          user_id: string
        }
        Insert: {
          answers?: Json | null
          completed_at?: string | null
          id?: string
          quiz_id: string
          score?: number | null
          time_taken?: number | null
          total_questions: number
          user_id: string
        }
        Update: {
          answers?: Json | null
          completed_at?: string | null
          id?: string
          quiz_id?: string
          score?: number | null
          time_taken?: number | null
          total_questions?: number
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "quiz_attempts_quiz_id_fkey"
            columns: ["quiz_id"]
            isOneToOne: false
            referencedRelation: "quizzes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quiz_attempts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      quiz_questions: {
        Row: {
          correct_answer: string
          created_at: string | null
          explanation: string | null
          id: string
          options: Json | null
          order_index: number
          points: number | null
          question_text: string
          question_type: string
          quiz_id: string
        }
        Insert: {
          correct_answer: string
          created_at?: string | null
          explanation?: string | null
          id?: string
          options?: Json | null
          order_index: number
          points?: number | null
          question_text: string
          question_type: string
          quiz_id: string
        }
        Update: {
          correct_answer?: string
          created_at?: string | null
          explanation?: string | null
          id?: string
          options?: Json | null
          order_index?: number
          points?: number | null
          question_text?: string
          question_type?: string
          quiz_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "quiz_questions_quiz_id_fkey"
            columns: ["quiz_id"]
            isOneToOne: false
            referencedRelation: "quizzes"
            referencedColumns: ["id"]
          },
        ]
      }
      quizzes: {
        Row: {
          created_at: string | null
          created_by: string
          description: string | null
          difficulty: string | null
          id: string
          is_public: boolean | null
          randomize_questions: boolean | null
          show_results_immediately: boolean | null
          subject: string | null
          time_limit: number | null
          title: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          created_by: string
          description?: string | null
          difficulty?: string | null
          id?: string
          is_public?: boolean | null
          randomize_questions?: boolean | null
          show_results_immediately?: boolean | null
          subject?: string | null
          time_limit?: number | null
          title: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string
          description?: string | null
          difficulty?: string | null
          id?: string
          is_public?: boolean | null
          randomize_questions?: boolean | null
          show_results_immediately?: boolean | null
          subject?: string | null
          time_limit?: number | null
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "quizzes_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      reading_progress: {
        Row: {
          completed_at: string | null
          completion_percentage: number | null
          created_at: string | null
          id: string
          notes: string | null
          session_date: string
          session_id: string
          status: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          completed_at?: string | null
          completion_percentage?: number | null
          created_at?: string | null
          id?: string
          notes?: string | null
          session_date: string
          session_id: string
          status?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          completed_at?: string | null
          completion_percentage?: number | null
          created_at?: string | null
          id?: string
          notes?: string | null
          session_date?: string
          session_id?: string
          status?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "reading_progress_session_id_fkey"
            columns: ["session_id"]
            isOneToOne: false
            referencedRelation: "reading_sessions"
            referencedColumns: ["id"]
          },
        ]
      }
      reading_sessions: {
        Row: {
          color: string | null
          created_at: string | null
          day_of_week: number
          description: string | null
          end_time: string
          goals: string | null
          id: string
          is_active: boolean | null
          is_recurring: boolean | null
          linked_file_id: string | null
          notes: string | null
          reading_text: string | null
          start_time: string
          subject_id: string | null
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          color?: string | null
          created_at?: string | null
          day_of_week: number
          description?: string | null
          end_time: string
          goals?: string | null
          id?: string
          is_active?: boolean | null
          is_recurring?: boolean | null
          linked_file_id?: string | null
          notes?: string | null
          reading_text?: string | null
          start_time: string
          subject_id?: string | null
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          color?: string | null
          created_at?: string | null
          day_of_week?: number
          description?: string | null
          end_time?: string
          goals?: string | null
          id?: string
          is_active?: boolean | null
          is_recurring?: boolean | null
          linked_file_id?: string | null
          notes?: string | null
          reading_text?: string | null
          start_time?: string
          subject_id?: string | null
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "reading_sessions_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "reading_subjects"
            referencedColumns: ["id"]
          },
        ]
      }
      reading_streaks: {
        Row: {
          completed_sessions: number | null
          created_at: string | null
          current_streak: number | null
          id: string
          last_completed_date: string | null
          longest_streak: number | null
          subject_id: string | null
          total_sessions: number | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          completed_sessions?: number | null
          created_at?: string | null
          current_streak?: number | null
          id?: string
          last_completed_date?: string | null
          longest_streak?: number | null
          subject_id?: string | null
          total_sessions?: number | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          completed_sessions?: number | null
          created_at?: string | null
          current_streak?: number | null
          id?: string
          last_completed_date?: string | null
          longest_streak?: number | null
          subject_id?: string | null
          total_sessions?: number | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "reading_streaks_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "reading_subjects"
            referencedColumns: ["id"]
          },
        ]
      }
      reading_subjects: {
        Row: {
          color: string | null
          created_at: string | null
          description: string | null
          id: string
          name: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      study_group_comment_likes: {
        Row: {
          comment_id: string | null
          created_at: string | null
          id: string
          user_id: string | null
        }
        Insert: {
          comment_id?: string | null
          created_at?: string | null
          id?: string
          user_id?: string | null
        }
        Update: {
          comment_id?: string | null
          created_at?: string | null
          id?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "study_group_comment_likes_comment_id_fkey"
            columns: ["comment_id"]
            isOneToOne: false
            referencedRelation: "study_group_comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "study_group_comment_likes_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      study_group_comment_replies: {
        Row: {
          author_id: string | null
          comment_id: string | null
          content: string
          created_at: string | null
          id: string
        }
        Insert: {
          author_id?: string | null
          comment_id?: string | null
          content: string
          created_at?: string | null
          id?: string
        }
        Update: {
          author_id?: string | null
          comment_id?: string | null
          content?: string
          created_at?: string | null
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "study_group_comment_replies_author_id_fkey"
            columns: ["author_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "study_group_comment_replies_comment_id_fkey"
            columns: ["comment_id"]
            isOneToOne: false
            referencedRelation: "study_group_comments"
            referencedColumns: ["id"]
          },
        ]
      }
      study_group_comments: {
        Row: {
          author_id: string | null
          content: string
          created_at: string | null
          id: string
          post_id: string | null
          updated_at: string | null
        }
        Insert: {
          author_id?: string | null
          content: string
          created_at?: string | null
          id?: string
          post_id?: string | null
          updated_at?: string | null
        }
        Update: {
          author_id?: string | null
          content?: string
          created_at?: string | null
          id?: string
          post_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "study_group_comments_author_id_fkey"
            columns: ["author_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "study_group_comments_post_id_fkey"
            columns: ["post_id"]
            isOneToOne: false
            referencedRelation: "study_group_posts"
            referencedColumns: ["id"]
          },
        ]
      }
      study_group_invitations: {
        Row: {
          created_at: string | null
          expires_at: string | null
          group_id: string
          id: string
          invited_by_user_id: string
          invited_user_id: string
          message: string | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          expires_at?: string | null
          group_id: string
          id?: string
          invited_by_user_id: string
          invited_user_id: string
          message?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          expires_at?: string | null
          group_id?: string
          id?: string
          invited_by_user_id?: string
          invited_user_id?: string
          message?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "study_group_invitations_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "study_groups"
            referencedColumns: ["id"]
          },
        ]
      }
      study_group_members: {
        Row: {
          group_id: string
          id: string
          joined_at: string
          role: string | null
          user_id: string
        }
        Insert: {
          group_id: string
          id?: string
          joined_at?: string
          role?: string | null
          user_id: string
        }
        Update: {
          group_id?: string
          id?: string
          joined_at?: string
          role?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "study_group_members_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "study_groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "study_group_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      study_group_post_likes: {
        Row: {
          created_at: string | null
          id: string
          post_id: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          post_id?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          post_id?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "study_group_post_likes_post_id_fkey"
            columns: ["post_id"]
            isOneToOne: false
            referencedRelation: "study_group_posts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "study_group_post_likes_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      study_group_posts: {
        Row: {
          author_id: string | null
          content: string | null
          created_at: string | null
          file_name: string | null
          file_url: string | null
          group_id: string | null
          id: string
          post_type: string | null
          title: string | null
          updated_at: string | null
        }
        Insert: {
          author_id?: string | null
          content?: string | null
          created_at?: string | null
          file_name?: string | null
          file_url?: string | null
          group_id?: string | null
          id?: string
          post_type?: string | null
          title?: string | null
          updated_at?: string | null
        }
        Update: {
          author_id?: string | null
          content?: string | null
          created_at?: string | null
          file_name?: string | null
          file_url?: string | null
          group_id?: string | null
          id?: string
          post_type?: string | null
          title?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "study_group_posts_author_id_fkey"
            columns: ["author_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "study_group_posts_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "study_groups"
            referencedColumns: ["id"]
          },
        ]
      }
      study_groups: {
        Row: {
          cover_image: string | null
          cover_image_url: string | null
          created_at: string
          creator_id: string
          description: string | null
          id: string
          member_count: number | null
          name: string
          privacy: Database["public"]["Enums"]["group_privacy"]
          updated_at: string
        }
        Insert: {
          cover_image?: string | null
          cover_image_url?: string | null
          created_at?: string
          creator_id: string
          description?: string | null
          id?: string
          member_count?: number | null
          name: string
          privacy?: Database["public"]["Enums"]["group_privacy"]
          updated_at?: string
        }
        Update: {
          cover_image?: string | null
          cover_image_url?: string | null
          created_at?: string
          creator_id?: string
          description?: string | null
          id?: string
          member_count?: number | null
          name?: string
          privacy?: Database["public"]["Enums"]["group_privacy"]
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "study_groups_creator_id_fkey"
            columns: ["creator_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      study_sessions: {
        Row: {
          created_at: string
          duration_minutes: number | null
          end_time: string | null
          exam_id: string | null
          id: string
          is_completed: boolean | null
          notes: string | null
          session_date: string
          start_time: string | null
          subject: string
          title: string
          user_id: string
        }
        Insert: {
          created_at?: string
          duration_minutes?: number | null
          end_time?: string | null
          exam_id?: string | null
          id?: string
          is_completed?: boolean | null
          notes?: string | null
          session_date: string
          start_time?: string | null
          subject: string
          title: string
          user_id: string
        }
        Update: {
          created_at?: string
          duration_minutes?: number | null
          end_time?: string | null
          exam_id?: string | null
          id?: string
          is_completed?: boolean | null
          notes?: string | null
          session_date?: string
          start_time?: string | null
          subject?: string
          title?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "study_sessions_exam_id_fkey"
            columns: ["exam_id"]
            isOneToOne: false
            referencedRelation: "exams"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "study_sessions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      subscription_plans: {
        Row: {
          created_at: string | null
          currency: string
          description: string | null
          id: string
          interval_count: number | null
          interval_type: string
          is_active: boolean | null
          name: string
          price_cents: number
          trial_period_days: number | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          currency?: string
          description?: string | null
          id?: string
          interval_count?: number | null
          interval_type?: string
          is_active?: boolean | null
          name: string
          price_cents?: number
          trial_period_days?: number | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          currency?: string
          description?: string | null
          id?: string
          interval_count?: number | null
          interval_type?: string
          is_active?: boolean | null
          name?: string
          price_cents?: number
          trial_period_days?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      topics: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          name: string
          owner_id: string
          unit_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          owner_id: string
          unit_id: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          owner_id?: string
          unit_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "topics_owner_id_fkey"
            columns: ["owner_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "topics_unit_id_fkey"
            columns: ["unit_id"]
            isOneToOne: false
            referencedRelation: "units"
            referencedColumns: ["id"]
          },
        ]
      }
      units: {
        Row: {
          color: string | null
          created_at: string | null
          description: string | null
          id: string
          name: string
          owner_id: string
          updated_at: string | null
        }
        Insert: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          owner_id: string
          updated_at?: string | null
        }
        Update: {
          color?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          owner_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "units_owner_id_fkey"
            columns: ["owner_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      uploaded_files: {
        Row: {
          category: string | null
          created_at: string
          file_size: number | null
          file_type: string | null
          file_url: string
          filename: string
          id: string
          is_processed: boolean | null
          user_id: string
        }
        Insert: {
          category?: string | null
          created_at?: string
          file_size?: number | null
          file_type?: string | null
          file_url: string
          filename: string
          id?: string
          is_processed?: boolean | null
          user_id: string
        }
        Update: {
          category?: string | null
          created_at?: string
          file_size?: number | null
          file_type?: string | null
          file_url?: string
          filename?: string
          id?: string
          is_processed?: boolean | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "uploaded_files_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_roles: {
        Row: {
          created_at: string
          id: string
          role: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          role?: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          role?: Database["public"]["Enums"]["app_role"]
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_roles_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_subscriptions: {
        Row: {
          cancelled_at: string | null
          created_at: string | null
          current_period_end: string | null
          current_period_start: string | null
          expires_at: string | null
          id: string
          is_trial: boolean | null
          last_payment_date: string | null
          next_payment_due: string | null
          payment_method: string | null
          payment_provider: string | null
          payment_reference: string | null
          plan_id: string | null
          status: string | null
          trial_ends_at: string | null
          trial_started_at: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          cancelled_at?: string | null
          created_at?: string | null
          current_period_end?: string | null
          current_period_start?: string | null
          expires_at?: string | null
          id?: string
          is_trial?: boolean | null
          last_payment_date?: string | null
          next_payment_due?: string | null
          payment_method?: string | null
          payment_provider?: string | null
          payment_reference?: string | null
          plan_id?: string | null
          status?: string | null
          trial_ends_at?: string | null
          trial_started_at?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          cancelled_at?: string | null
          created_at?: string | null
          current_period_end?: string | null
          current_period_start?: string | null
          expires_at?: string | null
          id?: string
          is_trial?: boolean | null
          last_payment_date?: string | null
          next_payment_due?: string | null
          payment_method?: string | null
          payment_provider?: string | null
          payment_reference?: string | null
          plan_id?: string | null
          status?: string | null
          trial_ends_at?: string | null
          trial_started_at?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_subscriptions_plan_id_fkey"
            columns: ["plan_id"]
            isOneToOne: false
            referencedRelation: "subscription_plans"
            referencedColumns: ["id"]
          },
        ]
      }
      user_trials: {
        Row: {
          completed_at: string | null
          created_at: string | null
          expires_at: string | null
          id: string
          is_active: boolean | null
          started_at: string | null
          user_id: string | null
        }
        Insert: {
          completed_at?: string | null
          created_at?: string | null
          expires_at?: string | null
          id?: string
          is_active?: boolean | null
          started_at?: string | null
          user_id?: string | null
        }
        Update: {
          completed_at?: string | null
          created_at?: string | null
          expires_at?: string | null
          id?: string
          is_active?: boolean | null
          started_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      cleanup_stale_online_status: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_notification: {
        Args:
          | {
              p_user_id: string
              p_type: string
              p_title: string
              p_message: string
              p_data?: Json
              p_action_url?: string
            }
          | {
              p_user_id: string
              p_type: string
              p_title: string
              p_message: string
              p_data?: Json
              p_action_url?: string
              p_sender_id?: string
            }
        Returns: string
      }
      create_timetable_notification: {
        Args:
          | {
              p_user_id: string
              p_session_id: string
              p_notification_type: string
              p_session_title: string
              p_subject_name: string
              p_start_time: string
              p_end_time?: string
            }
          | {
              p_user_id: string
              p_session_id: string
              p_notification_type: string
              p_session_title: string
              p_subject_name: string
              p_start_time: string
              p_end_time?: string
            }
        Returns: string
      }
      decrement_group_member_count: {
        Args: { group_id: string }
        Returns: undefined
      }
      discover_study_groups: {
        Args: { p_user_id: string }
        Returns: {
          id: string
          name: string
          description: string
          cover_image_url: string
          privacy: string
          creator_id: string
          member_count: number
          created_at: string
          creator_name: string
        }[]
      }
      extend_user_subscription: {
        Args: { p_user_id: string; p_days?: number }
        Returns: boolean
      }
      get_complete_subscription_status: {
        Args: { p_user_id: string }
        Returns: {
          has_subscription: boolean
          subscription_id: string
          plan_name: string
          status: string
          expires_at: string
          days_remaining: number
          needs_payment: boolean
          trial_id: string
          trial_active: boolean
          trial_expires_at: string
          trial_seconds_remaining: number
          trial_expired: boolean
        }[]
      }
      get_conversation_messages: {
        Args: {
          p_conversation_id: string
          p_limit?: number
          p_offset?: number
          p_user_id?: string
        }
        Returns: {
          id: string
          conversation_id: string
          sender_id: string
          sender_name: string
          sender_avatar: string
          content: string
          message_type: string
          file_url: string
          file_name: string
          file_size: number
          reply_to_id: string
          reply_to_content: string
          reply_to_sender_name: string
          created_at: string
          updated_at: string
          is_edited: boolean
        }[]
      }
      get_countries: {
        Args: Record<PropertyKey, never>
        Returns: {
          name: string
          code: string
        }[]
      }
      get_courses: {
        Args: Record<PropertyKey, never>
        Returns: {
          name: string
          category: string
        }[]
      }
      get_current_user_role: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_institutes: {
        Args: Record<PropertyKey, never>
        Returns: {
          name: string
          country: string
          type: string
        }[]
      }
      get_or_create_conversation: {
        Args: { p_user1_id: string; p_user2_id: string }
        Returns: string
      }
      get_or_create_direct_conversation: {
        Args: { user1_id: string; user2_id: string }
        Returns: string
      }
      get_post_like_count: {
        Args: { p_post_id: string }
        Returns: number
      }
      get_public_quizzes: {
        Args: { p_limit?: number }
        Returns: {
          id: string
          title: string
          description: string
          subject: string
          difficulty: string
          question_count: number
          attempt_count: number
          creator_name: string
          created_at: string
        }[]
      }
      get_quiz_with_questions: {
        Args: { p_quiz_id: string }
        Returns: {
          quiz_id: string
          quiz_title: string
          quiz_description: string
          quiz_subject: string
          quiz_difficulty: string
          time_limit: number
          randomize_questions: boolean
          show_results_immediately: boolean
          question_id: string
          question_text: string
          question_type: string
          correct_answer: string
          options: Json
          points: number
          explanation: string
          order_index: number
        }[]
      }
      get_recommended_groups: {
        Args: { p_user_id: string }
        Returns: {
          group_id: string
          group_name: string
          description: string
          cover_image_url: string
          member_count: number
          match_score: number
          match_reason: string
        }[]
      }
      get_trial_status: {
        Args: { p_user_id: string }
        Returns: {
          trial_id: string
          is_active: boolean
          started_at: string
          expires_at: string
          time_remaining_seconds: number
          has_expired: boolean
        }[]
      }
      get_unread_notification_count: {
        Args: { p_user_id: string }
        Returns: number
      }
      get_user_conversations: {
        Args: { p_user_id: string }
        Returns: {
          conversation_id: string
          conversation_type: string
          conversation_name: string
          other_user_id: string
          other_user_name: string
          other_user_avatar: string
          latest_message: string
          latest_message_time: string
          unread_count: number
        }[]
      }
      get_user_conversations_v2: {
        Args: { p_user_id: string }
        Returns: {
          conversation_id: string
          conversation_type: string
          conversation_name: string
          conversation_avatar: string
          other_user_id: string
          other_user_name: string
          other_user_avatar: string
          other_user_email: string
          latest_message_id: string
          latest_message_content: string
          latest_message_type: string
          latest_message_sender_id: string
          latest_message_sender_name: string
          latest_message_time: string
          unread_count: number
          participant_count: number
        }[]
      }
      get_user_friends: {
        Args: { p_user_id: string }
        Returns: {
          id: string
          full_name: string
          email: string
          avatar_url: string
          country: string
          course: string
          institute: string
          is_online: boolean
          last_seen: string
          status_text: string
        }[]
      }
      get_user_notifications: {
        Args: {
          p_user_id: string
          p_limit?: number
          p_offset?: number
          p_unread_only?: boolean
        }
        Returns: {
          id: string
          user_id: string
          type: string
          title: string
          message: string
          data: Json
          read: boolean
          action_url: string
          sender_id: string
          created_at: string
          updated_at: string
        }[]
      }
      get_user_presence: {
        Args: { p_user_id: string }
        Returns: {
          id: string
          full_name: string
          avatar_url: string
          is_online: boolean
          last_seen: string
          status_text: string
        }[]
      }
      get_user_quizzes: {
        Args: { p_user_id: string }
        Returns: {
          id: string
          title: string
          description: string
          subject: string
          difficulty: string
          question_count: number
          attempt_count: number
          is_public: boolean
          created_at: string
        }[]
      }
      get_user_study_groups: {
        Args: { p_user_id: string }
        Returns: {
          id: string
          name: string
          description: string
          cover_image_url: string
          privacy: string
          creator_id: string
          member_count: number
          created_at: string
          user_role: string
          is_member: boolean
        }[]
      }
      get_user_subscription_status: {
        Args: { p_user_id: string }
        Returns: {
          subscription_id: string
          plan_name: string
          status: string
          is_trial: boolean
          trial_ends_at: string
          current_period_end: string
          days_remaining: number
        }[]
      }
      get_user_subscription_status_v2: {
        Args: { p_user_id: string }
        Returns: {
          subscription_id: string
          plan_name: string
          status: string
          is_trial: boolean
          trial_ends_at: string
          current_period_end: string
          days_remaining: number
          minutes_remaining: number
          is_expired: boolean
          needs_payment: boolean
        }[]
      }
      get_weekly_reading_schedule: {
        Args: { p_user_id: string; p_week_start: string }
        Returns: {
          session_id: string
          subject_name: string
          subject_color: string
          title: string
          day_of_week: number
          start_time: string
          end_time: string
          notes: string
          goals: string
          session_date: string
          progress_status: string
          completion_percentage: number
        }[]
      }
      increment_group_member_count: {
        Args: { group_id: string }
        Returns: undefined
      }
      is_group_creator: {
        Args: { group_id: string }
        Returns: boolean
      }
      is_group_member: {
        Args: { group_id: string }
        Returns: boolean
      }
      mark_all_notifications_read: {
        Args: { p_user_id: string }
        Returns: number
      }
      mark_conversation_as_read: {
        Args: { p_conversation_id: string; p_user_id?: string }
        Returns: undefined
      }
      mark_messages_as_read: {
        Args: { p_conversation_id: string; p_user_id: string }
        Returns: undefined
      }
      mark_notification_read: {
        Args: { p_notification_id: string; p_user_id: string }
        Returns: boolean
      }
      send_message: {
        Args: {
          p_conversation_id: string
          p_content?: string
          p_message_type?: string
          p_file_url?: string
          p_file_name?: string
          p_file_size?: number
          p_reply_to_id?: string
          p_sender_id?: string
        }
        Returns: string
      }
      share_document_to_study_group: {
        Args: {
          p_sender_id: string
          p_group_id: string
          p_document_url: string
          p_document_name: string
          p_document_title: string
          p_share_message?: string
        }
        Returns: string
      }
      share_document_to_users: {
        Args: {
          p_sender_id: string
          p_recipient_ids: string[]
          p_document_url: string
          p_document_name: string
          p_document_title: string
          p_share_message?: string
        }
        Returns: string[]
      }
      start_free_trial: {
        Args: { p_user_id: string; p_plan_id: string }
        Returns: string
      }
      start_user_trial: {
        Args: { p_user_id: string }
        Returns: string
      }
      submit_quiz_attempt: {
        Args: { p_quiz_id: string; p_answers: Json; p_time_taken: number }
        Returns: {
          attempt_id: string
          score: number
          total_questions: number
          percentage: number
        }[]
      }
      update_reading_streak: {
        Args: {
          p_user_id: string
          p_subject_id: string
          p_session_date: string
        }
        Returns: undefined
      }
      user_has_active_subscription: {
        Args: { p_user_id: string }
        Returns: boolean
      }
      user_liked_post: {
        Args: { p_post_id: string; p_user_id: string }
        Returns: boolean
      }
      user_subscription_expired: {
        Args: { p_user_id: string }
        Returns: boolean
      }
    }
    Enums: {
      app_role: "admin" | "moderator" | "user"
      exam_difficulty: "easy" | "medium" | "hard"
      group_privacy: "public" | "private"
      note_type: "text" | "image" | "audio" | "video"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      app_role: ["admin", "moderator", "user"],
      exam_difficulty: ["easy", "medium", "hard"],
      group_privacy: ["public", "private"],
      note_type: ["text", "image", "audio", "video"],
    },
  },
} as const
