import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { 
  RateLimiter, 
  InputSanitizer, 
  CSRFProtection, 
  FailedAttemptsTracker,
  UserAgentValidator,
  SessionSecurity 
} from '@/utils/security';
import { SECURITY_CONFIG, SECURITY_MESSAGES } from '@/config/security';
import { useUser } from '@/hooks/useAuth';
import { toast } from 'sonner';

interface SecurityContextType {
  // Rate Limiting
  checkRateLimit: (action: string, limit?: number) => boolean;
  getRemainingAttempts: (action: string, limit?: number) => number;
  
  // Input Sanitization
  sanitizeInput: (input: string, type?: 'html' | 'text') => string;
  validateInput: (input: string, type: 'email' | 'password' | 'text') => { valid: boolean; errors?: string[] };
  
  // File Security
  validateFile: (file: File) => { valid: boolean; errors: string[] };
  
  // CSRF Protection
  getCSRFToken: () => string;
  validateCSRFToken: (token: string) => boolean;
  
  // Session Security
  isSessionValid: () => boolean;
  refreshSession: () => void;
  
  // Security Status
  securityStatus: {
    isBlocked: boolean;
    failedAttempts: number;
    sessionValid: boolean;
    lastActivity: Date | null;
  };
  
  // Security Actions
  reportSuspiciousActivity: (activity: string, details?: any) => void;
  blockUser: (reason: string) => void;
  unblockUser: () => void;
}

const SecurityContext = createContext<SecurityContextType | undefined>(undefined);

interface SecurityProviderProps {
  children: ReactNode;
}

export const SecurityProvider: React.FC<SecurityProviderProps> = ({ children }) => {
  const user = useUser();
  const [sessionId] = useState(() => crypto.randomUUID());
  const [securityStatus, setSecurityStatus] = useState({
    isBlocked: false,
    failedAttempts: 0,
    sessionValid: true,
    lastActivity: new Date(),
  });

  // Initialize security checks
  useEffect(() => {
    const initializeSecurity = () => {
      // Check user agent
      const userAgent = navigator.userAgent;
      if (UserAgentValidator.isBlocked(userAgent)) {
        toast.error('Access denied: Unsupported browser detected');
        return;
      }

      if (UserAgentValidator.isSuspicious(userAgent)) {
        reportSuspiciousActivity('Suspicious user agent detected', { userAgent });
      }

      // Check if user is blocked
      const userId = user?.id || 'anonymous';
      const isBlocked = FailedAttemptsTracker.isBlocked(userId);
      if (isBlocked) {
        setSecurityStatus(prev => ({ ...prev, isBlocked: true }));
        toast.error(SECURITY_MESSAGES.SUSPICIOUS_ACTIVITY);
      }

      // Initialize CSRF token
      CSRFProtection.generateToken(sessionId);
    };

    initializeSecurity();
  }, [user, sessionId]);

  // Session activity tracking
  useEffect(() => {
    const updateActivity = () => {
      setSecurityStatus(prev => ({ ...prev, lastActivity: new Date() }));
    };

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    events.forEach(event => {
      document.addEventListener(event, updateActivity, true);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, updateActivity, true);
      });
    };
  }, []);

  // Session validation
  useEffect(() => {
    const validateSession = () => {
      if (user) {
        const sessionData = {
          createdAt: user.created_at,
          lastActivity: securityStatus.lastActivity,
        };

        const isValid = SessionSecurity.validateSession(sessionData);
        const isIdle = SessionSecurity.isSessionIdle(sessionData);

        if (!isValid || isIdle) {
          setSecurityStatus(prev => ({ ...prev, sessionValid: false }));
          toast.warning(SECURITY_MESSAGES.SESSION_EXPIRED);
        }
      }
    };

    const interval = setInterval(validateSession, 60000); // Check every minute
    return () => clearInterval(interval);
  }, [user, securityStatus.lastActivity]);

  const checkRateLimit = (action: string, limit?: number): boolean => {
    const userId = user?.id || 'anonymous';
    const key = `${userId}:${action}`;
    const actionLimit = limit || SECURITY_CONFIG.RATE_LIMITS.API_REQUESTS;
    
    const allowed = RateLimiter.check(key, actionLimit);
    
    if (!allowed) {
      toast.error(SECURITY_MESSAGES.RATE_LIMIT_EXCEEDED);
      reportSuspiciousActivity('Rate limit exceeded', { action, userId });
    }
    
    return allowed;
  };

  const getRemainingAttempts = (action: string, limit?: number): number => {
    const userId = user?.id || 'anonymous';
    const key = `${userId}:${action}`;
    const actionLimit = limit || SECURITY_CONFIG.RATE_LIMITS.API_REQUESTS;
    
    return RateLimiter.getRemainingAttempts(key, actionLimit);
  };

  const sanitizeInput = (input: string, type: 'html' | 'text' = 'text'): string => {
    return type === 'html' 
      ? InputSanitizer.sanitizeHTML(input)
      : InputSanitizer.sanitizeText(input);
  };

  const validateInput = (input: string, type: 'email' | 'password' | 'text') => {
    switch (type) {
      case 'email':
        return { valid: InputSanitizer.validateEmail(input) };
      case 'password':
        return InputSanitizer.validatePassword(input);
      case 'text':
        return { valid: input.length <= SECURITY_CONFIG.VALIDATION.MAX_TEXT_LENGTH };
      default:
        return { valid: false, errors: ['Invalid validation type'] };
    }
  };

  const validateFile = (file: File): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    if (!InputSanitizer.validateFileType(file)) {
      errors.push(SECURITY_MESSAGES.INVALID_FILE_TYPE);
    }
    
    if (!InputSanitizer.validateFileSize(file)) {
      errors.push(SECURITY_MESSAGES.FILE_TOO_LARGE);
    }
    
    return { valid: errors.length === 0, errors };
  };

  const getCSRFToken = (): string => {
    return CSRFProtection.generateToken(sessionId);
  };

  const validateCSRFToken = (token: string): boolean => {
    return CSRFProtection.validateToken(sessionId, token);
  };

  const isSessionValid = (): boolean => {
    return securityStatus.sessionValid;
  };

  const refreshSession = (): void => {
    setSecurityStatus(prev => ({ 
      ...prev, 
      sessionValid: true, 
      lastActivity: new Date() 
    }));
    CSRFProtection.refreshToken(sessionId);
  };

  const reportSuspiciousActivity = (activity: string, details?: any): void => {
    console.warn('🚨 Security Alert:', activity, details);
    
    // Log to monitoring service (implement as needed)
    const logData = {
      timestamp: new Date().toISOString(),
      userId: user?.id || 'anonymous',
      activity,
      details,
      userAgent: navigator.userAgent,
      url: window.location.href,
    };
    
    // Store in localStorage for now (replace with proper logging service)
    const logs = JSON.parse(localStorage.getItem('security_logs') || '[]');
    logs.push(logData);
    localStorage.setItem('security_logs', JSON.stringify(logs.slice(-100))); // Keep last 100 logs
  };

  const blockUser = (reason: string): void => {
    const userId = user?.id || 'anonymous';
    FailedAttemptsTracker.recordFailedAttempt(userId);
    
    setSecurityStatus(prev => ({ 
      ...prev, 
      isBlocked: true,
      failedAttempts: FailedAttemptsTracker.getFailedAttempts(userId)
    }));
    
    reportSuspiciousActivity('User blocked', { reason });
    toast.error(`Account blocked: ${reason}`);
  };

  const unblockUser = (): void => {
    const userId = user?.id || 'anonymous';
    FailedAttemptsTracker.reset(userId);
    
    setSecurityStatus(prev => ({ 
      ...prev, 
      isBlocked: false,
      failedAttempts: 0
    }));
    
    toast.success('Account access restored');
  };

  const contextValue: SecurityContextType = {
    checkRateLimit,
    getRemainingAttempts,
    sanitizeInput,
    validateInput,
    validateFile,
    getCSRFToken,
    validateCSRFToken,
    isSessionValid,
    refreshSession,
    securityStatus,
    reportSuspiciousActivity,
    blockUser,
    unblockUser,
  };

  return (
    <SecurityContext.Provider value={contextValue}>
      {children}
    </SecurityContext.Provider>
  );
};

export const useSecurity = (): SecurityContextType => {
  const context = useContext(SecurityContext);
  if (!context) {
    throw new Error('useSecurity must be used within a SecurityProvider');
  }
  return context;
};
