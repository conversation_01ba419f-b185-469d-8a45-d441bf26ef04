import { useState, useEffect, useCallback } from 'react';
import { offlineStorage, STORES } from '@/utils/offlineStorage';

// Extend ServiceWorkerRegistration to include sync
declare global {
  interface ServiceWorkerRegistration {
    sync: {
      register(tag: string): Promise<void>;
    };
  }
}

export interface OfflineStatus {
  isOnline: boolean;
  isOffline: boolean;
  hasOfflineData: boolean;
  pendingSyncCount: number;
  lastSyncTime: string | null;
}

export interface OfflineCapabilities {
  canSaveNotes: boolean;
  canViewNotes: boolean;
  canSaveTimetable: boolean;
  canViewTimetable: boolean;
  canSendMessages: boolean;
  canViewMessages: boolean;
  canViewPastPapers: boolean;
  canParticipateInGroups: boolean;
}

export const useOffline = () => {
  const [status, setStatus] = useState<OfflineStatus>({
    isOnline: navigator.onLine,
    isOffline: !navigator.onLine,
    hasOfflineData: false,
    pendingSyncCount: 0,
    lastSyncTime: null
  });

  const [capabilities] = useState<OfflineCapabilities>({
    canSaveNotes: true,
    canViewNotes: true,
    canSaveTimetable: true,
    canViewTimetable: true,
    canSendMessages: true,
    canViewMessages: true,
    canViewPastPapers: true,
    canParticipateInGroups: true
  });

  // Update online/offline status
  const updateOnlineStatus = useCallback(() => {
    const isOnline = navigator.onLine;
    setStatus(prev => ({
      ...prev,
      isOnline,
      isOffline: !isOnline
    }));

    // If coming back online, trigger background sync
    if (isOnline && 'serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      navigator.serviceWorker.ready.then(registration => {
        return registration.sync.register('sync-offline-data');
      }).catch(console.error);
    }
  }, []);

  // Check for offline data
  const checkOfflineData = useCallback(async () => {
    try {
      const [notes, timetable, messages, actions] = await Promise.all([
        offlineStorage.getAll(STORES.NOTES),
        offlineStorage.getAll(STORES.TIMETABLE),
        offlineStorage.getAll(STORES.MESSAGES),
        offlineStorage.getUnsynced(STORES.OFFLINE_ACTIONS)
      ]);

      const hasOfflineData = notes.length > 0 || timetable.length > 0 || messages.length > 0;
      const pendingSyncCount = actions.length;

      setStatus(prev => ({
        ...prev,
        hasOfflineData,
        pendingSyncCount
      }));
    } catch (error) {
      console.error('Error checking offline data:', error);
    }
  }, []);

  // Save data for offline use
  const saveForOffline = useCallback(async (type: string, data: any) => {
    try {
      switch (type) {
        case 'note':
          const { OfflineNotes } = await import('@/utils/offlineStorage');
          return await OfflineNotes.save(data);
        
        case 'timetable':
          const { OfflineTimetable } = await import('@/utils/offlineStorage');
          return await OfflineTimetable.save(data);

        case 'timetable_session':
          const { OfflineTimetableSessions } = await import('@/utils/offlineStorage');
          return await OfflineTimetableSessions.save(data);

        case 'message':
          const { OfflineMessages } = await import('@/utils/offlineStorage');
          return await OfflineMessages.save(data);

        case 'discussion':
          const { OfflineDiscussions } = await import('@/utils/offlineStorage');
          return await OfflineDiscussions.save(data);

        default:
          throw new Error(`Unsupported offline data type: ${type}`);
      }
    } catch (error) {
      console.error('Error saving offline data:', error);
      throw error;
    }
  }, []);

  // Get offline data
  const getOfflineData = useCallback(async (type: string, filter?: any) => {
    try {
      switch (type) {
        case 'notes':
          const { OfflineNotes } = await import('@/utils/offlineStorage');
          return await OfflineNotes.getAll();
        
        case 'timetable':
          const { OfflineTimetable } = await import('@/utils/offlineStorage');
          if (filter?.dayOfWeek !== undefined) {
            return await OfflineTimetable.getByDay(filter.dayOfWeek);
          }
          return await OfflineTimetable.getAll();

        case 'timetable_sessions':
          const { OfflineTimetableSessions } = await import('@/utils/offlineStorage');
          if (filter?.date) {
            return await OfflineTimetableSessions.getByDate(filter.date);
          }
          return await OfflineTimetableSessions.getAll();

        case 'groups':
          const { OfflineGroups } = await import('@/utils/offlineStorage');
          return await OfflineGroups.getAll();

        case 'discussions':
          const { OfflineDiscussions } = await import('@/utils/offlineStorage');
          if (filter?.groupId) {
            return await OfflineDiscussions.getByGroup(filter.groupId);
          }
          return await OfflineDiscussions.getAll();

        case 'past_papers':
          const { OfflinePastPapers } = await import('@/utils/offlineStorage');
          if (filter?.subject) {
            return await OfflinePastPapers.getBySubject(filter.subject);
          }
          if (filter?.year) {
            return await OfflinePastPapers.getByYear(filter.year);
          }
          return await OfflinePastPapers.getAll();

        case 'messages':
          const { OfflineMessages } = await import('@/utils/offlineStorage');
          if (filter?.groupId) {
            return await OfflineMessages.getByGroup(filter.groupId);
          }
          return await OfflineMessages.getAll();
        
        default:
          return [];
      }
    } catch (error) {
      console.error('Error getting offline data:', error);
      return [];
    }
  }, []);

  // Clear offline data
  const clearOfflineData = useCallback(async (type?: string) => {
    try {
      if (type) {
        await offlineStorage.clear(type);
      } else {
        // Clear all offline data
        await Promise.all([
          offlineStorage.clear(STORES.NOTES),
          offlineStorage.clear(STORES.TIMETABLE),
          offlineStorage.clear(STORES.MESSAGES),
          offlineStorage.clear(STORES.OFFLINE_ACTIONS)
        ]);
      }
      
      await checkOfflineData();
    } catch (error) {
      console.error('Error clearing offline data:', error);
      throw error;
    }
  }, [checkOfflineData]);

  // Force sync offline data
  const forcSync = useCallback(async () => {
    if (!status.isOnline) {
      throw new Error('Cannot sync while offline');
    }

    try {
      if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
        const registration = await navigator.serviceWorker.ready;
        await registration.sync.register('sync-offline-data');
        
        // Update last sync time
        setStatus(prev => ({
          ...prev,
          lastSyncTime: new Date().toISOString()
        }));
      }
    } catch (error) {
      console.error('Error forcing sync:', error);
      throw error;
    }
  }, [status.isOnline]);

  // Get storage usage
  const getStorageUsage = useCallback(async () => {
    try {
      return await offlineStorage.getStorageInfo();
    } catch (error) {
      console.error('Error getting storage usage:', error);
      return { used: 0, quota: 0 };
    }
  }, []);

  // Setup event listeners
  useEffect(() => {
    // Listen for online/offline events
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    // Listen for service worker messages
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data?.type === 'SYNC_COMPLETE') {
          checkOfflineData();
          setStatus(prev => ({
            ...prev,
            lastSyncTime: new Date().toISOString()
          }));
        }
      });
    }

    // Initial check for offline data
    checkOfflineData();

    // Cleanup
    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, [updateOnlineStatus, checkOfflineData]);

  // Periodic check for offline data (every 30 seconds)
  useEffect(() => {
    const interval = setInterval(checkOfflineData, 30000);
    return () => clearInterval(interval);
  }, [checkOfflineData]);

  return {
    status,
    capabilities,
    saveForOffline,
    getOfflineData,
    clearOfflineData,
    forcSync,
    getStorageUsage,
    checkOfflineData
  };
};

// Hook for specific offline features
export const useOfflineNotes = () => {
  const { status, saveForOffline, getOfflineData } = useOffline();

  const saveNote = useCallback(async (note: any) => {
    if (status.isOffline) {
      return await saveForOffline('note', note);
    }
    // If online, save normally (implement with your existing note saving logic)
    throw new Error('Online note saving not implemented in this hook');
  }, [status.isOffline, saveForOffline]);

  const getNotes = useCallback(async () => {
    return await getOfflineData('notes');
  }, [getOfflineData]);

  return {
    isOffline: status.isOffline,
    saveNote,
    getNotes,
    hasOfflineNotes: status.hasOfflineData
  };
};

export const useOfflineTimetable = () => {
  const { status, saveForOffline, getOfflineData } = useOffline();

  const saveTimetableEntry = useCallback(async (entry: any) => {
    if (status.isOffline) {
      return await saveForOffline('timetable', entry);
    }
    throw new Error('Online timetable saving not implemented in this hook');
  }, [status.isOffline, saveForOffline]);

  const getTimetableEntries = useCallback(async (dayOfWeek?: number) => {
    return await getOfflineData('timetable', dayOfWeek !== undefined ? { dayOfWeek } : undefined);
  }, [getOfflineData]);

  return {
    isOffline: status.isOffline,
    saveTimetableEntry,
    getTimetableEntries,
    hasOfflineTimetable: status.hasOfflineData
  };
};

export const useOfflineMessages = () => {
  const { status, saveForOffline, getOfflineData } = useOffline();

  const sendMessage = useCallback(async (message: any) => {
    if (status.isOffline) {
      return await saveForOffline('message', message);
    }
    throw new Error('Online message sending not implemented in this hook');
  }, [status.isOffline, saveForOffline]);

  const getMessages = useCallback(async (groupId?: string) => {
    return await getOfflineData('messages', groupId ? { groupId } : undefined);
  }, [getOfflineData]);

  return {
    isOffline: status.isOffline,
    sendMessage,
    getMessages,
    hasOfflineMessages: status.hasOfflineData
  };
};

// Hook for offline groups
export const useOfflineGroups = () => {
  const { status, getOfflineData } = useOffline();

  const getGroups = useCallback(async () => {
    return await getOfflineData('groups');
  }, [getOfflineData]);

  const getDiscussions = useCallback(async (groupId?: string) => {
    return await getOfflineData('discussions', groupId ? { groupId } : undefined);
  }, [getOfflineData]);

  return {
    isOffline: status.isOffline,
    getGroups,
    getDiscussions,
    hasOfflineGroups: status.hasOfflineData
  };
};

// Hook for offline timetable sessions
export const useOfflineTimetableSessions = () => {
  const { status, saveForOffline, getOfflineData } = useOffline();

  const saveSession = useCallback(async (session: any) => {
    return await saveForOffline('timetable_session', session);
  }, [saveForOffline]);

  const getSessions = useCallback(async (date?: string) => {
    return await getOfflineData('timetable_sessions', date ? { date } : undefined);
  }, [getOfflineData]);

  return {
    isOffline: status.isOffline,
    saveSession,
    getSessions,
    hasOfflineSessions: status.hasOfflineData
  };
};

// Hook for offline past papers
export const useOfflinePastPapers = () => {
  const { status, getOfflineData } = useOffline();

  const getPapers = useCallback(async (filter?: { subject?: string; year?: number }) => {
    return await getOfflineData('past_papers', filter);
  }, [getOfflineData]);

  return {
    isOffline: status.isOffline,
    getPapers,
    hasOfflinePapers: status.hasOfflineData
  };
};
