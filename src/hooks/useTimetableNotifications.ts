import { useEffect } from 'react';
import { useIsAuthenticated } from '@/hooks/useAuth';
import { timetableNotificationService } from '@/services/timetableNotificationService';

/**
 * Hook to manage timetable notifications
 * Automatically starts/stops the notification service based on authentication state
 */
export const useTimetableNotifications = () => {
  const { isAuthenticated, isLoading } = useIsAuthenticated();

  useEffect(() => {
    // Don't start service while auth state is loading
    if (isLoading) return;

    if (isAuthenticated) {
      // Start the notification service when user is authenticated
      timetableNotificationService.start();
      
      return () => {
        // Stop the service when component unmounts or user logs out
        timetableNotificationService.stop();
      };
    } else {
      // Stop the service if user is not authenticated
      timetableNotificationService.stop();
    }
  }, [isAuthenticated, isLoading]);

  return {
    isRunning: isAuthenticated && !isLoading,
  };
};
