import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// Types
export interface Conversation {
  id: string;
  type: 'direct' | 'group';
  name?: string;
  description?: string;
  avatar_url?: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
  last_message_at: string;
  // Computed fields
  other_user_id?: string;
  other_user_name?: string;
  other_user_avatar?: string;
  other_user_email?: string;
  latest_message?: string;
  latest_message_time?: string;
  unread_count: number;
  is_online?: boolean;
}

export interface Message {
  id: string;
  conversation_id: string;
  sender_id: string;
  content: string;
  message_type: 'text' | 'image' | 'file';
  file_url?: string;
  file_name?: string;
  file_size?: number;
  reply_to_id?: string;
  is_edited: boolean;
  created_at: string;
  updated_at: string;
  // Computed fields
  sender_name?: string;
  sender_avatar?: string;
  sender_email?: string;
  is_own: boolean;
}

// Get user's conversations - SIMPLE VERSION
export const useConversations = () => {
  return useQuery({
    queryKey: ['conversations'],
    queryFn: async (): Promise<Conversation[]> => {
      // Return empty array for now - focus on getting basic messaging working first
      return [];
    },
    staleTime: 30000,
  });
};

// Get messages for a conversation - FIXED VERSION
export const useMessages = (conversationId: string) => {
  return useQuery({
    queryKey: ['messages', conversationId],
    queryFn: async (): Promise<Message[]> => {
      if (!conversationId) return [];

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      try {
        // Simple query without complex joins first
        const { data: messagesData, error } = await supabase
          .from('messages')
          .select('*')
          .eq('conversation_id', conversationId)
          .order('created_at', { ascending: true });

        if (error) {
          console.error('Messages query error:', error);
          return [];
        }



        // Get sender info separately for each message
        const messages: Message[] = [];
        for (const msg of messagesData || []) {
          let senderName = 'Unknown User';
          let senderAvatar = undefined;

          try {
            const { data: profile } = await supabase
              .from('profiles')
              .select('full_name, avatar_url')
              .eq('id', msg.sender_id)
              .single();

            if (profile) {
              senderName = profile.full_name || 'Unknown User';
              senderAvatar = profile.avatar_url;
            }
          } catch (profileError) {
            console.warn('Could not fetch profile for sender:', msg.sender_id);
          }

          const transformedMessage: Message = {
            id: msg.id,
            conversation_id: msg.conversation_id,
            sender_id: msg.sender_id,
            content: msg.content || '',
            message_type: (msg.message_type as 'text' | 'image' | 'file') || 'text',
            file_url: msg.file_url,
            file_name: msg.file_name,
            file_size: msg.file_size,
            reply_to_id: undefined,
            is_edited: msg.is_edited || false,
            created_at: msg.created_at,
            updated_at: msg.updated_at,
            sender_name: senderName,
            sender_avatar: senderAvatar,
            sender_email: undefined,
            is_own: msg.sender_id === user.id,
          };

          messages.push(transformedMessage);
        }

        return messages;
      } catch (error) {
        console.error('Error fetching messages:', error);
        return [];
      }
    },
    enabled: !!conversationId,
    staleTime: 5000,
  });
};

// Send a message - SIMPLE VERSION
export const useSendMessage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      conversationId,
      content,
    }: {
      conversationId: string;
      content: string;
    }) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      try {
        // Simple insert - only required fields
        const { data: messageData, error: messageError } = await supabase
          .from('messages')
          .insert({
            conversation_id: conversationId,
            sender_id: user.id,
            content: content.trim(),
            topic: 'message',
          })
          .select()
          .single();

        if (messageError) {
          console.error('Message insert error:', messageError);
          throw messageError;
        }

        return messageData;
      } catch (error) {
        console.error('Error sending message:', error);
        throw error;
      }
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['messages', variables.conversationId] });
      toast.success('Message sent!');
    },
    onError: (error) => {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message');
    },
  });
};

// Create conversation - USING DATABASE FUNCTION
export const useGetOrCreateConversation = () => {
  return useMutation({
    mutationFn: async ({ otherUserId }: { otherUserId: string }) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      // Use the same database function as sharing
      const { data: conversationId, error } = await supabase
        .rpc('get_or_create_conversation', {
          p_user1_id: user.id,
          p_user2_id: otherUserId
        } as any);

      if (error) {
        console.error('Error creating conversation:', error);
        throw error;
      }

      return { conversationId };
    },
    onSuccess: () => {
      toast.success('Conversation started!');
    },
    onError: (error) => {
      console.error('Failed to start conversation:', error);
      toast.error('Failed to start conversation');
    },
  });
};

// Mark messages as read - simplified version
export const useMarkMessagesAsRead = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ conversationId }: { conversationId: string }) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      // For now, just return success - we'll implement this step by step
      console.log('Marking messages as read for conversation:', conversationId);
      return { success: true };
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      queryClient.invalidateQueries({ queryKey: ['messages', variables.conversationId] });
    },
    onError: (error) => {
      console.error('Failed to mark messages as read:', error);
    },
  });
};

// Real-time message subscription - disabled temporarily to fix responsiveness
export const useMessageSubscription = (conversationId: string) => {
  // Temporarily disabled to avoid potential subscription issues
  console.log('Message subscription disabled for conversation:', conversationId);
  return null;
};


