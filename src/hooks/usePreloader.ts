import { useState, useEffect } from 'react';

interface UsePreloaderOptions {
  minDuration?: number;
  dependencies?: any[];
  autoHide?: boolean;
}

export const usePreloader = ({
  minDuration = 2000,
  dependencies = [],
  autoHide = true
}: UsePreloaderOptions = {}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [progress, setProgress] = useState(0);
  const [startTime] = useState(Date.now());

  useEffect(() => {
    // Simulate loading progress
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          return 100;
        }
        return prev + Math.random() * 15 + 5; // Random increment between 5-20
      });
    }, 100);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (!autoHide) return;

    const checkCompletion = () => {
      const elapsed = Date.now() - startTime;
      const dependenciesReady = dependencies.every(dep => dep !== undefined && dep !== null);
      
      if (progress >= 100 && elapsed >= minDuration && dependenciesReady) {
        setTimeout(() => setIsLoading(false), 500);
      }
    };

    checkCompletion();
  }, [progress, dependencies, minDuration, startTime, autoHide]);

  const hidePreloader = () => {
    setIsLoading(false);
  };

  const showPreloader = () => {
    setIsLoading(true);
    setProgress(0);
  };

  return {
    isLoading,
    progress,
    hidePreloader,
    showPreloader
  };
};
