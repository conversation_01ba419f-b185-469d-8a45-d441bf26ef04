import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// Types
export type ReadingSubject = {
  id: string;
  user_id: string;
  name: string;
  color: string;
  description?: string;
  created_at: string;
  updated_at: string;
};

export type ReadingSession = {
  id: string;
  user_id: string;
  subject_id?: string;
  title: string;
  description?: string;
  day_of_week: number; // 0=Sunday, 6=Saturday
  start_time: string;
  end_time?: string;
  duration_minutes?: number;
  notes?: string;
  goals?: string;
  linked_file_id?: string;
  reading_text?: string;
  is_recurring: boolean;
  is_active: boolean;
  quest_type?: 'daily' | 'weekly' | 'epic' | 'legendary';
  difficulty?: 'novice' | 'adept' | 'expert' | 'master';
  xp_reward?: number;
  color?: string; // Session color
  created_at: string;
  updated_at: string;
  subject?: ReadingSubject;
};

export type ReadingProgress = {
  id: string;
  user_id: string;
  session_id: string;
  session_date: string;
  status: 'pending' | 'completed' | 'skipped' | 'partial';
  completion_percentage: number;
  notes?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
};

export type ReadingStreak = {
  id: string;
  user_id: string;
  subject_id?: string;
  current_streak: number;
  longest_streak: number;
  last_completed_date?: string;
  total_sessions: number;
  completed_sessions: number;
  created_at: string;
  updated_at: string;
};

// Hook for reading subjects
export const useReadingSubjects = () => {
  return useQuery({
    queryKey: ['reading-subjects'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('reading_subjects')
        .select('*')
        .order('name');

      if (error) throw error;
      return data as ReadingSubject[];
    },
  });
};

// Hook for reading sessions
export const useReadingSessions = () => {
  return useQuery({
    queryKey: ['reading-sessions'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('reading_sessions')
        .select(`
          *,
          subject:reading_subjects(*)
        `)
        .eq('is_active', true)
        .order('day_of_week')
        .order('start_time');

      if (error) throw error;
      return data as ReadingSession[];
    },
  });
};

// Hook for reading progress
export const useReadingProgress = () => {
  return useQuery({
    queryKey: ['reading-progress'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('reading_progress')
        .select('*')
        .order('session_date', { ascending: false });

      if (error) throw error;
      return data as ReadingProgress[];
    },
  });
};

// Hook for reading streaks
export const useReadingStreaks = () => {
  return useQuery({
    queryKey: ['reading-streaks'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('reading_streaks')
        .select('*')
        .order('current_streak', { ascending: false });

      if (error) throw error;
      return data as ReadingStreak[];
    },
  });
};

// Main hook that combines all reading timetable data
export const useReadingTimetable = () => {
  const queryClient = useQueryClient();
  
  const { data: subjects, isLoading: subjectsLoading } = useReadingSubjects();
  const { data: sessions, isLoading: sessionsLoading } = useReadingSessions();
  const { data: progress, isLoading: progressLoading } = useReadingProgress();
  const { data: streaks, isLoading: streaksLoading } = useReadingStreaks();

  // Create subject mutation
  const createSubjectMutation = useMutation({
    mutationFn: async (subjectData: Omit<ReadingSubject, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data, error } = await supabase
        .from('reading_subjects')
        .insert({
          ...subjectData,
          user_id: user.id,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reading-subjects'] });
      toast.success('Subject created successfully!');
    },
    onError: (error) => {
      console.error('Error creating subject:', error);
      toast.error('Failed to create subject');
    },
  });

  // Create session mutation
  const createSessionMutation = useMutation({
    mutationFn: async (sessionData: Omit<ReadingSession, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data, error } = await supabase
        .from('reading_sessions')
        .insert({
          ...sessionData,
          user_id: user.id,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reading-sessions'] });
      toast.success('Reading session created successfully!');
    },
    onError: (error) => {
      console.error('Error creating session:', error);
      toast.error('Failed to create reading session');
    },
  });

  // Update session mutation
  const updateSessionMutation = useMutation({
    mutationFn: async ({ id, ...updates }: Partial<ReadingSession> & { id: string }) => {
      const { data, error } = await supabase
        .from('reading_sessions')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reading-sessions'] });
      toast.success('Session updated successfully!');
    },
    onError: (error) => {
      console.error('Error updating session:', error);
      toast.error('Failed to update session');
    },
  });

  // Delete session mutation
  const deleteSessionMutation = useMutation({
    mutationFn: async (sessionId: string) => {
      const { error } = await supabase
        .from('reading_sessions')
        .delete()
        .eq('id', sessionId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reading-sessions'] });
      toast.success('Session deleted successfully!');
    },
    onError: (error) => {
      console.error('Error deleting session:', error);
      toast.error('Failed to delete session');
    },
  });

  // Mark session complete mutation
  const markSessionCompleteMutation = useMutation({
    mutationFn: async ({ sessionId, sessionDate, completionPercentage = 100, notes }: {
      sessionId: string;
      sessionDate: string;
      completionPercentage?: number;
      notes?: string;
    }) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      // Upsert progress record
      const { data, error } = await supabase
        .from('reading_progress')
        .upsert({
          user_id: user.id,
          session_id: sessionId,
          session_date: sessionDate,
          status: completionPercentage >= 100 ? 'completed' : 'partial',
          completion_percentage: completionPercentage,
          notes,
          completed_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) throw error;

      // Update streak if completed
      if (completionPercentage >= 100) {
        const session = sessions?.find(s => s.id === sessionId);
        if (session?.subject_id) {
          await supabase.rpc('update_reading_streak', {
            p_user_id: user.id,
            p_subject_id: session.subject_id,
            p_session_date: sessionDate,
          });
        }
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['reading-progress'] });
      queryClient.invalidateQueries({ queryKey: ['reading-streaks'] });
      toast.success('Progress updated successfully!');
    },
    onError: (error) => {
      console.error('Error updating progress:', error);
      toast.error('Failed to update progress');
    },
  });

  return {
    // Data
    subjects,
    sessions,
    progress,
    streaks,
    
    // Loading states
    isLoading: subjectsLoading || sessionsLoading || progressLoading || streaksLoading,
    
    // Mutations
    createSubject: createSubjectMutation.mutateAsync,
    createSession: createSessionMutation.mutateAsync,
    updateSession: updateSessionMutation.mutateAsync,
    deleteSession: deleteSessionMutation.mutateAsync,
    markSessionComplete: markSessionCompleteMutation.mutateAsync,
    
    // Mutation states
    isCreatingSubject: createSubjectMutation.isPending,
    isCreatingSession: createSessionMutation.isPending,
    isUpdatingSession: updateSessionMutation.isPending,
    isDeletingSession: deleteSessionMutation.isPending,
    isMarkingComplete: markSessionCompleteMutation.isPending,
  };
};
