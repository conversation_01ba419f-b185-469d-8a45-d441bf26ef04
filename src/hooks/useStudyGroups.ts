
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

// Types
export interface StudyGroup {
  id: string;
  name: string;
  description?: string;
  cover_image_url?: string;
  privacy: 'public' | 'private';
  creator_id: string;
  member_count: number;
  created_at: string;
  user_role?: string;
  is_member?: boolean;
  creator_name?: string;
  members?: Array<{
    id: string;
    name: string;
    avatar: string;
    role: string;
    joined_at: string;
  }>;
}

export interface CreateStudyGroupData {
  name: string;
  description?: string;
  privacy: 'public' | 'private';
  cover_image?: File;
}

export interface CreatePostData {
  group_id: string;
  content: string;
  post_type: 'discussion' | 'note';
  title?: string;
  file?: File;
}

// Get user's joined study groups with improved error handling
export const useMyStudyGroups = () => {
  return useQuery({
    queryKey: ['my-study-groups'],
    queryFn: async () => {
      console.log('Fetching my study groups...');
      
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.log('No authenticated user found');
        return [];
      }

      console.log('User authenticated:', user.id);

      // Get groups where user is a member
      const { data: memberData, error: memberError } = await supabase
        .from('study_group_members')
        .select(`
          role,
          joined_at,
          group_id,
          study_groups!inner(
            id,
            name,
            description,
            cover_image_url,
            privacy,
            creator_id,
            member_count,
            created_at
          )
        `)
        .eq('user_id', user.id);

      if (memberError) {
        console.error('Error fetching my groups:', memberError);
        throw memberError;
      }

      console.log('My groups data:', memberData);

      // Transform the data
      const groups = memberData?.map((item: any) => ({
        id: item.study_groups.id,
        name: item.study_groups.name,
        description: item.study_groups.description || '',
        cover_image_url: item.study_groups.cover_image_url,
        privacy: item.study_groups.privacy,
        creator_id: item.study_groups.creator_id,
        member_count: item.study_groups.member_count,
        created_at: item.study_groups.created_at,
        user_role: item.role,
        is_member: true,
        members: [], // Will be populated separately if needed
      })) || [];

      console.log('Transformed my groups:', groups);
      return groups;
    },
  });
};

// Discover public study groups (not joined)
export const useDiscoverStudyGroups = () => {
  return useQuery({
    queryKey: ['discover-study-groups'],
    queryFn: async () => {
      console.log('Fetching discover study groups...');
      
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.log('No authenticated user found');
        return [];
      }

      console.log('User authenticated for discover:', user.id);

      // Get all public groups
      const { data: publicGroups, error: publicError } = await supabase
        .from('study_groups')
        .select(`
          id,
          name,
          description,
          cover_image_url,
          privacy,
          creator_id,
          member_count,
          created_at,
          profiles!creator_id(full_name)
        `)
        .eq('privacy', 'public');

      if (publicError) {
        console.error('Error fetching public groups:', publicError);
        throw publicError;
      }

      // Get user's memberships to filter out joined groups
      const { data: userMemberships, error: membershipError } = await supabase
        .from('study_group_members')
        .select('group_id')
        .eq('user_id', user.id);

      if (membershipError) {
        console.error('Error fetching user memberships:', membershipError);
        throw membershipError;
      }

      const joinedGroupIds = new Set(userMemberships?.map(m => m.group_id) || []);
      
      // Filter out groups user has already joined
      const discoverGroups = publicGroups?.filter(group => !joinedGroupIds.has(group.id)) || [];

      console.log('Discover groups data:', discoverGroups);

      // Transform the data
      const groups = discoverGroups.map((group: any) => ({
        id: group.id,
        name: group.name,
        description: group.description || '',
        cover_image_url: group.cover_image_url,
        privacy: group.privacy,
        creator_id: group.creator_id,
        member_count: group.member_count,
        created_at: group.created_at,
        creator_name: group.profiles?.full_name || 'Unknown',
        is_member: false,
        members: [], // Will be populated separately if needed
      }));

      console.log('Transformed discover groups:', groups);
      return groups;
    },
  });
};

// Get study group details
export const useStudyGroupDetails = (groupId: string) => {
  return useQuery({
    queryKey: ['study-group-details', groupId],
    queryFn: async () => {
      if (!groupId) return null;

      const { data: group, error } = await supabase
        .from('study_groups')
        .select(`
          *,
          profiles!creator_id(full_name)
        `)
        .eq('id', groupId)
        .single();

      if (error) throw error;
      return group;
    },
    enabled: !!groupId,
  });
};

// Check group membership
export const useCheckGroupMembership = (groupId: string) => {
  return useQuery({
    queryKey: ['group-membership', groupId],
    queryFn: async () => {
      if (!groupId) return { isMember: false, isAdmin: false };

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return { isMember: false, isAdmin: false };

      const { data: membership, error } = await supabase
        .from('study_group_members')
        .select('role')
        .eq('group_id', groupId)
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') throw error;

      return {
        isMember: !!membership,
        isAdmin: membership?.role === 'admin',
      };
    },
    enabled: !!groupId,
  });
};

// Get study group posts
export const useStudyGroupPosts = (groupId: string) => {
  return useQuery({
    queryKey: ['study-group-posts', groupId],
    queryFn: async () => {
      if (!groupId) return [];

      const { data: posts, error } = await supabase
        .from('study_group_posts')
        .select(`
          *,
          profiles!author_id(full_name, avatar_url)
        `)
        .eq('group_id', groupId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return posts || [];
    },
    enabled: !!groupId,
  });
};

// Get user friends
export const useGetUserFriends = () => {
  return useQuery({
    queryKey: ['user-friends'],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return [];

      const { data: friends, error } = await supabase
        .from('friendships')
        .select(`
          requester_id,
          addressee_id,
          profiles!requester_id(id, full_name, avatar_url),
          addressee:profiles!addressee_id(id, full_name, avatar_url)
        `)
        .or(`requester_id.eq.${user.id},addressee_id.eq.${user.id}`)
        .eq('status', 'accepted');

      if (error) throw error;

      // Transform to get the other person in the friendship
      const friendsList = friends?.map((friendship: any) => {
        const isRequester = friendship.requester_id === user.id;
        const friend = isRequester ? friendship.addressee : friendship.profiles;
        return {
          id: friend.id,
          full_name: friend.full_name,
          avatar_url: friend.avatar_url,
        };
      }) || [];

      return friendsList;
    },
  });
};

// Create study group with improved error handling
export const useCreateStudyGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (groupData: CreateStudyGroupData) => {
      console.log('Creating study group with data:', groupData);

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('Not authenticated');
      }

      console.log('User authenticated:', user.id);

      let cover_image_url = null;

      // Upload cover image if provided
      if (groupData.cover_image) {
        console.log('Uploading cover image:', groupData.cover_image.name);

        const fileExt = groupData.cover_image.name.split('.').pop();
        const fileName = `group-cover-${Date.now()}.${fileExt}`;
        const filePath = `${user.id}/${fileName}`;

        console.log('Upload path:', filePath);

        const { error: uploadError } = await supabase.storage
          .from('group-covers')
          .upload(filePath, groupData.cover_image);

        if (uploadError) {
          console.error('Upload error:', uploadError);
          throw uploadError;
        }

        const { data: { publicUrl } } = supabase.storage
          .from('group-covers')
          .getPublicUrl(filePath);

        cover_image_url = publicUrl;
        console.log('Cover image uploaded:', cover_image_url);
      }

      // Create the study group
      console.log('Creating group in database...');
      
      const insertData = {
        name: groupData.name,
        description: groupData.description,
        privacy: groupData.privacy,
        cover_image_url,
        creator_id: user.id,
        member_count: 1,
      };
      
      console.log('Insert data:', insertData);

      const { data: group, error: groupError } = await supabase
        .from('study_groups')
        .insert(insertData)
        .select()
        .single();

      if (groupError) {
        console.error('Group creation error:', groupError);
        console.error('Error details:', JSON.stringify(groupError, null, 2));
        throw new Error(`Database error: ${groupError.message || 'Unknown error'}`);
      }

      console.log('Group created:', group);

      // Add creator as admin member
      console.log('Adding creator as admin member...');
      const { error: memberError } = await supabase
        .from('study_group_members')
        .insert({
          group_id: group.id,
          user_id: user.id,
          role: 'admin',
        });

      if (memberError) {
        console.error('Member creation error:', memberError);
        throw memberError;
      }

      console.log('Study group creation completed successfully');
      return group;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['my-study-groups'] });
      queryClient.invalidateQueries({ queryKey: ['discover-study-groups'] });
    },
  });
};

// Create study group post
export const useCreateStudyGroupPost = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (postData: CreatePostData) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      let file_url = null;
      let file_name = null;

      // Upload file if provided
      if (postData.file) {
        const fileExt = postData.file.name.split('.').pop();
        const fileName = `post-${Date.now()}.${fileExt}`;
        const filePath = `${user.id}/${fileName}`;

        const { error: uploadError } = await supabase.storage
          .from('group-files')
          .upload(filePath, postData.file);

        if (uploadError) throw uploadError;

        const { data: { publicUrl } } = supabase.storage
          .from('group-files')
          .getPublicUrl(filePath);

        file_url = publicUrl;
        file_name = postData.file.name;
      }

      const { data: post, error } = await supabase
        .from('study_group_posts')
        .insert({
          group_id: postData.group_id,
          author_id: user.id,
          content: postData.content,
          post_type: postData.post_type,
          title: postData.title,
          file_url,
          file_name,
        })
        .select()
        .single();

      if (error) throw error;
      return post;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['study-group-posts', data.group_id] });
    },
  });
};

// Update study group
export const useUpdateStudyGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (updateData: { id: string; name?: string; description?: string; cover_image?: File }) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      let cover_image_url = undefined;

      // Upload new cover image if provided
      if (updateData.cover_image) {
        const fileExt = updateData.cover_image.name.split('.').pop();
        const fileName = `group-cover-${Date.now()}.${fileExt}`;
        const filePath = `${user.id}/${fileName}`;

        const { error: uploadError } = await supabase.storage
          .from('group-covers')
          .upload(filePath, updateData.cover_image);

        if (uploadError) throw uploadError;

        const { data: { publicUrl } } = supabase.storage
          .from('group-covers')
          .getPublicUrl(filePath);

        cover_image_url = publicUrl;
      }

      const updatePayload: any = {};
      if (updateData.name) updatePayload.name = updateData.name;
      if (updateData.description !== undefined) updatePayload.description = updateData.description;
      if (cover_image_url) updatePayload.cover_image_url = cover_image_url;

      const { data: group, error } = await supabase
        .from('study_groups')
        .update(updatePayload)
        .eq('id', updateData.id)
        .eq('creator_id', user.id)
        .select()
        .single();

      if (error) throw error;
      return group;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['my-study-groups'] });
      queryClient.invalidateQueries({ queryKey: ['study-group-details'] });
    },
  });
};

// Delete study group
export const useDeleteStudyGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (groupId: string) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { error } = await supabase
        .from('study_groups')
        .delete()
        .eq('id', groupId)
        .eq('creator_id', user.id);

      if (error) throw error;
      return { groupId };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['my-study-groups'] });
      queryClient.invalidateQueries({ queryKey: ['discover-study-groups'] });
    },
  });
};

// Send group invitations
export const useSendGroupInvitations = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ group_id, friend_ids }: { group_id: string; friend_ids: string[] }) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const invitations = friend_ids.map(friend_id => ({
        group_id,
        invited_user_id: friend_id,
        invited_by_user_id: user.id,
      }));

      const { data, error } = await supabase
        .from('study_group_invitations')
        .insert(invitations);

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
  });
};

// Remove group member
export const useRemoveGroupMember = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ groupId, memberId }: { groupId: string; memberId: string }) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { error } = await supabase
        .from('study_group_members')
        .delete()
        .eq('group_id', groupId)
        .eq('user_id', memberId);

      if (error) throw error;
      return { groupId, memberId };
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['study-group-details', data.groupId] });
      queryClient.invalidateQueries({ queryKey: ['my-study-groups'] });
    },
  });
};

// Create comment
export const useCreateComment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ postId, content }: { postId: string; content: string }) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data, error } = await supabase
        .from('study_group_comments')
        .insert({
          post_id: postId,
          author_id: user.id,
          content,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['study-group-posts'] });
    },
  });
};

// Like post
export const useLikePost = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (postId: string) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data, error } = await supabase
        .from('study_group_post_likes')
        .insert({
          post_id: postId,
          user_id: user.id,
        });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['study-group-posts'] });
    },
  });
};

// Like comment
export const useLikeComment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (commentId: string) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data, error } = await supabase
        .from('study_group_comment_likes')
        .insert({
          comment_id: commentId,
          user_id: user.id,
        });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['study-group-posts'] });
    },
  });
};

// Create comment reply
export const useCreateCommentReply = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ commentId, content }: { commentId: string; content: string }) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data, error } = await supabase
        .from('study_group_comment_replies')
        .insert({
          comment_id: commentId,
          author_id: user.id,
          content,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['study-group-posts'] });
    },
  });
};

// Leave study group from detail page
export const useLeaveStudyGroupFromDetail = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (groupId: string) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { error } = await supabase
        .from('study_group_members')
        .delete()
        .eq('group_id', groupId)
        .eq('user_id', user.id);

      if (error) throw error;
      return { groupId };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['my-study-groups'] });
      queryClient.invalidateQueries({ queryKey: ['discover-study-groups'] });
      queryClient.invalidateQueries({ queryKey: ['group-membership'] });
    },
  });
};

// Join study group
export const useJoinStudyGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (groupId: string) => {
      console.log('Joining group:', groupId);
      
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data, error } = await supabase
        .from('study_group_members')
        .insert({
          group_id: groupId,
          user_id: user.id,
          role: 'member',
        })
        .select()
        .single();

      if (error) {
        console.error('Join group error:', error);
        throw error;
      }

      console.log('Successfully joined group');
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['my-study-groups'] });
      queryClient.invalidateQueries({ queryKey: ['discover-study-groups'] });
    },
  });
};

// Leave study group
export const useLeaveStudyGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (groupId: string) => {
      console.log('Leaving group:', groupId);
      
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { error } = await supabase
        .from('study_group_members')
        .delete()
        .eq('group_id', groupId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Leave group error:', error);
        throw error;
      }

      console.log('Successfully left group');
      return { groupId };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['my-study-groups'] });
      queryClient.invalidateQueries({ queryKey: ['discover-study-groups'] });
    },
  });
};
