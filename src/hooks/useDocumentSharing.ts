
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export interface ShareDocumentData {
  documentUrl: string;
  documentName: string;
  documentTitle: string;
  shareMessage?: string;
  recipients: {
    friends?: string[];
    studyGroups?: string[];
  };
}

export const useShareDocument = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (shareData: ShareDocumentData) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const results = [];

      // Share to friends
      if (shareData.recipients.friends && shareData.recipients.friends.length > 0) {
        try {
          const { data, error } = await supabase.rpc('share_document_to_users', {
            p_sender_id: user.id,
            p_recipient_ids: shareData.recipients.friends,
            p_document_url: shareData.documentUrl,
            p_document_name: shareData.documentName,
            p_document_title: shareData.documentTitle,
            p_share_message: shareData.shareMessage || null,
          });

          if (error) throw error;
          results.push({ type: 'friends', success: true, count: shareData.recipients.friends.length });
        } catch (error) {
          console.error('Failed to share to friends:', error);
          results.push({ type: 'friends', success: false, error });
        }
      }

      // Share to study groups
      if (shareData.recipients.studyGroups && shareData.recipients.studyGroups.length > 0) {
        for (const groupId of shareData.recipients.studyGroups) {
          try {
            const { data, error } = await supabase.rpc('share_document_to_study_group', {
              p_sender_id: user.id,
              p_group_id: groupId,
              p_document_url: shareData.documentUrl,
              p_document_name: shareData.documentName,
              p_document_title: shareData.documentTitle,
              p_share_message: shareData.shareMessage || null,
            });

            if (error) throw error;
            results.push({ type: 'group', success: true, groupId });
          } catch (error) {
            console.error(`Failed to share to group ${groupId}:`, error);
            results.push({ type: 'group', success: false, groupId, error });
          }
        }
      }

      return results;
    },
    onSuccess: (results) => {
      const successCount = results.filter(r => r.success).length;
      const totalCount = results.length;

      if (successCount === totalCount) {
        toast.success('Document shared successfully!');
      } else if (successCount > 0) {
        toast.warning(`Document shared to ${successCount} of ${totalCount} recipients. Some failed.`);
      } else {
        toast.error('Failed to share document to any recipients.');
      }

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      queryClient.invalidateQueries({ queryKey: ['study-group-posts'] });
      queryClient.invalidateQueries({ queryKey: ['messages'] });
    },
    onError: (error) => {
      console.error('Share error:', error);
      toast.error('Failed to share document');
    },
  });
};

export const useSaveDocumentToNotes = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ documentUrl, documentName, documentTitle }: {
      documentUrl: string;
      documentName: string;
      documentTitle: string;
    }) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data, error } = await supabase
        .from('notes')
        .insert({
          user_id: user.id,
          title: documentTitle,
          content: `Document: ${documentName}`,
          file_url: documentUrl,
          file_name: documentName,
          note_type: 'file',
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast.success('Document saved to notes successfully!');
      queryClient.invalidateQueries({ queryKey: ['notes'] });
    },
    onError: (error) => {
      console.error('Save to notes error:', error);
      toast.error('Failed to save document to notes');
    },
  });
};
