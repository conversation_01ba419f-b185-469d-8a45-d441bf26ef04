
import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export interface Profile {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  institute?: string;
  country?: string;
  courses?: string[];
  course?: string;
  bio?: string;
  is_online?: boolean;
  last_seen?: string;
  created_at: string;
  updated_at: string;
}

export interface UpdateProfileData {
  full_name?: string;
  bio?: string;
  institute?: string;
  country?: string;
  courses?: string[];
  course?: string;
}

// Get current user's profile
export const useProfile = () => {
  return useQuery({
    queryKey: ['profile'],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) throw error;
      return data as Profile;
    },
  });
};

// Get profile by ID
export const useProfileById = (userId: string) => {
  return useQuery({
    queryKey: ['profile', userId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;
      return data as Profile;
    },
    enabled: !!userId,
  });
};

// Get countries
export const useCountries = () => {
  return useQuery({
    queryKey: ['countries'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('countries')
        .select('*')
        .order('name');

      if (error) throw error;
      return data;
    },
  });
};

// Get courses
export const useCourses = () => {
  return useQuery({
    queryKey: ['courses'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('courses')
        .select('*')
        .order('name');

      if (error) throw error;
      return data;
    },
  });
};

// Get institutes
export const useInstitutes = () => {
  return useQuery({
    queryKey: ['institutes'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('institutes')
        .select('*')
        .order('name');

      if (error) throw error;
      return data;
    },
  });
};

// Update profile
export const useUpdateProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (updates: UpdateProfileData) => {
      console.log('🔄 Starting profile update...', updates);

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('❌ User not authenticated');
        throw new Error('Not authenticated');
      }
      console.log('✅ User authenticated:', user.id);

      // Clean the updates object - remove undefined values
      const cleanUpdates = Object.fromEntries(
        Object.entries(updates).filter(([_, value]) => value !== undefined)
      );
      console.log('📝 Clean updates:', cleanUpdates);

      const updateData = {
        ...cleanUpdates,
        updated_at: new Date().toISOString(),
      };

      console.log('💾 Updating profile with data:', updateData);
      const { data, error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', user.id)
        .select()
        .single();

      if (error) {
        console.error('❌ Profile update failed:', error);
        throw error;
      }

      console.log('✅ Profile updated successfully:', data);
      return data;
    },
    onSuccess: (data) => {
      console.log('🎉 Profile update success callback');
      queryClient.invalidateQueries({ queryKey: ['profile'] });
      queryClient.setQueryData(['profile'], data);
      // Don't show toast here - let the calling component handle it
    },
    onError: (error: any) => {
      console.error('❌ Profile update error:', error);
      // Don't show toast here - let the calling component handle it
    },
  });
};

// Upload avatar
export const useUploadAvatar = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (file: File) => {
      console.log('🔄 Starting avatar upload process...');
      console.log('📁 File details:', { name: file.name, size: file.size, type: file.type });

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('❌ User not authenticated');
        throw new Error('Not authenticated');
      }
      console.log('✅ User authenticated:', user.id);

      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        console.error('❌ File too large:', file.size);
        throw new Error('File size must be less than 5MB');
      }

      // Validate file type
      if (!file.type.startsWith('image/')) {
        console.error('❌ Invalid file type:', file.type);
        throw new Error('File must be an image');
      }

      const fileExt = file.name.split('.').pop();
      // Use folder structure: {user_id}/avatar-{timestamp}.{ext}
      const fileName = `${user.id}/avatar-${Date.now()}.${fileExt}`;
      console.log('📝 Generated file path:', fileName);

      // Clean up old avatar files first
      try {
        console.log('🧹 Cleaning up old avatar files...');
        const { data: existingFiles } = await supabase.storage
          .from('avatars')
          .list(user.id);

        if (existingFiles && existingFiles.length > 0) {
          console.log('🗑️ Found existing files to delete:', existingFiles.length);
          const filesToDelete = existingFiles.map(file => `${user.id}/${file.name}`);
          const { error: deleteError } = await supabase.storage
            .from('avatars')
            .remove(filesToDelete);

          if (deleteError) {
            console.warn('⚠️ Delete error (continuing anyway):', deleteError);
          } else {
            console.log('✅ Old files cleaned up successfully');
          }
        } else {
          console.log('ℹ️ No existing files to clean up');
        }
      } catch (cleanupError) {
        console.warn('⚠️ Failed to cleanup old avatar files:', cleanupError);
        // Continue with upload even if cleanup fails
      }

      console.log('⬆️ Starting file upload...');
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(fileName, file);

      if (uploadError) {
        console.error('❌ Upload failed:', uploadError);
        throw uploadError;
      }
      console.log('✅ File uploaded successfully:', uploadData);

      console.log('🔗 Getting public URL...');
      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(fileName);
      console.log('✅ Public URL generated:', publicUrl);

      // Update profile with new avatar URL
      console.log('💾 Updating profile with new avatar URL...');
      const { data, error } = await supabase
        .from('profiles')
        .update({ avatar_url: publicUrl })
        .eq('id', user.id)
        .select()
        .single();

      if (error) {
        console.error('❌ Profile update failed:', error);
        throw error;
      }

      console.log('🎉 Avatar upload completed successfully!');
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['profile'] });
      toast.success('Avatar updated successfully!');
    },
    onError: (error: any) => {
      console.error('Avatar upload error details:', {
        message: error.message,
        error: error,
        stack: error.stack,
        cause: error.cause
      });

      // Provide more specific error messages
      let errorMessage = 'Failed to upload avatar';
      if (error.message) {
        if (error.message.includes('not authenticated')) {
          errorMessage = 'Please sign in to upload an avatar';
        } else if (error.message.includes('size')) {
          errorMessage = 'File size must be less than 5MB';
        } else if (error.message.includes('image')) {
          errorMessage = 'Please select a valid image file';
        } else if (error.message.includes('policy')) {
          errorMessage = 'Permission denied. Please try signing in again.';
        } else {
          errorMessage = error.message;
        }
      }

      toast.error(errorMessage);
    },
  });
};

// Update online status
export const useUpdateOnlineStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (isOnline: boolean) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data, error } = await supabase
        .from('profiles')
        .update({
          is_online: isOnline,
          last_seen: new Date().toISOString(),
        })
        .eq('id', user.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Invalidate profile queries to update online status everywhere
      queryClient.invalidateQueries({ queryKey: ['profile'] });
      queryClient.invalidateQueries({ queryKey: ['friends'] });
    },
  });
};

// Note: Presence tracking is now handled directly in App.tsx to avoid infinite loops
