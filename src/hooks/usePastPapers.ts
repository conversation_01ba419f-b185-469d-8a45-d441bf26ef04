import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface PastPaper {
  id: string;
  title: string;
  subject: string;
  university?: string;
  course_code?: string;
  year?: number;
  exam_type?: string;
  file_url?: string;
  has_answers: boolean;
  has_marking_scheme: boolean;
  tags?: string[];
  download_count: number;
  is_public: boolean;
  user_id?: string;
  created_at: string;
}

export interface CreatePastPaperData {
  title: string;
  subject: string;
  university?: string;
  course_code?: string;
  year?: number;
  exam_type?: string;
  file_url: string; // Now required since we always upload a file
  has_answers?: boolean;
  has_marking_scheme?: boolean;
  tags?: string[];
  is_public?: boolean;
}

// Hook to get all past papers
export const usePastPapers = (filters?: {
  subject?: string;
  year?: string;
  exam_type?: string;
  search?: string;
}) => {
  return useQuery({
    queryKey: ['past-papers', filters],
    queryFn: async () => {
      let query = supabase
        .from('past_papers')
        .select('*')
        .eq('is_public', true)
        .order('created_at', { ascending: false });

      if (filters?.subject && filters.subject !== 'all') {
        query = query.ilike('subject', `%${filters.subject}%`);
      }

      if (filters?.year && filters.year !== 'all') {
        query = query.eq('year', parseInt(filters.year));
      }

      if (filters?.exam_type && filters.exam_type !== 'all') {
        query = query.eq('exam_type', filters.exam_type);
      }

      if (filters?.search) {
        query = query.or(`title.ilike.%${filters.search}%,subject.ilike.%${filters.search}%`);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data as PastPaper[];
    },
  });
};

// Hook to get user's uploaded past papers
export const useUserPastPapers = () => {
  return useQuery({
    queryKey: ['user-past-papers'],
    queryFn: async () => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('Not authenticated');

      const { data, error } = await supabase
        .from('past_papers')
        .select('*')
        .eq('user_id', user.user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as PastPaper[];
    },
  });
};

// Hook to create/upload a past paper
export const useCreatePastPaper = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (paperData: CreatePastPaperData) => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('Not authenticated');

      const { data, error } = await supabase
        .from('past_papers')
        .insert({
          ...paperData,
          user_id: user.user.id,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['past-papers'] });
      queryClient.invalidateQueries({ queryKey: ['user-past-papers'] });
    },
  });
};

// Hook to update past paper
export const useUpdatePastPaper = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, ...updateData }: { id: string } & Partial<CreatePastPaperData>) => {
      const { data, error } = await supabase
        .from('past_papers')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['past-papers'] });
      queryClient.invalidateQueries({ queryKey: ['user-past-papers'] });
    },
  });
};

// Hook to increment download count
export const useIncrementDownloadCount = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (paperId: string) => {
      // Manual increment since RPC function doesn't exist
      const { data: paper, error: fetchError } = await supabase
        .from('past_papers')
        .select('download_count')
        .eq('id', paperId)
        .single();

      if (fetchError) throw fetchError;

      const { error: updateError } = await supabase
        .from('past_papers')
        .update({ download_count: (paper.download_count || 0) + 1 })
        .eq('id', paperId);

      if (updateError) throw updateError;
      
      return paper;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['past-papers'] });
    },
  });
};

// Hook to delete past paper
export const useDeletePastPaper = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (paperId: string) => {
      const { error } = await supabase
        .from('past_papers')
        .delete()
        .eq('id', paperId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['past-papers'] });
      queryClient.invalidateQueries({ queryKey: ['user-past-papers'] });
    },
  });
};
