import { useState } from 'react';

interface DocumentInfo {
  url: string;
  name: string;
  type?: string;
  title?: string;
}

export const useDocumentViewer = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentDocument, setCurrentDocument] = useState<DocumentInfo | null>(null);

  const openDocument = (document: DocumentInfo) => {
    setCurrentDocument(document);
    setIsOpen(true);
  };

  const closeDocument = () => {
    setIsOpen(false);
    setCurrentDocument(null);
  };

  return {
    isOpen,
    currentDocument,
    openDocument,
    closeDocument,
  };
};

export default useDocumentViewer;
