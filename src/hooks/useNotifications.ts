import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useEffect } from 'react';

// Types
export interface Notification {
  id: string;
  type: 'friend_request' | 'friend_accepted' | 'message' | 'study_group_invite' | 'study_group_post' | 'note_shared' | 'system';
  title: string;
  message: string;
  data: Record<string, any>;
  read: boolean;
  action_url?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateNotificationData {
  user_id: string;
  type: Notification['type'];
  title: string;
  message: string;
  data?: Record<string, any>;
  action_url?: string;
}

/**
 * Hook to get user notifications with pagination
 */
export const useNotifications = (limit = 20, offset = 0, unreadOnly = false) => {
  return useQuery({
    queryKey: ['notifications', limit, offset, unreadOnly],
    queryFn: async (): Promise<Notification[]> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data, error } = await supabase.rpc('get_user_notifications', {
        p_user_id: user.id,
        p_limit: limit,
        p_offset: offset,
        p_unread_only: unreadOnly
      });

      if (error) {
        console.error('Error fetching notifications:', error);
        throw error;
      }

      return data || [];
    },
    staleTime: 30000, // 30 seconds
    retry: 2,
  });
};

/**
 * Hook to get unread notification count
 */
export const useUnreadNotificationCount = () => {
  return useQuery({
    queryKey: ['notifications', 'unread-count'],
    queryFn: async (): Promise<number> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return 0;

      const { data, error } = await supabase.rpc('get_unread_notification_count', {
        p_user_id: user.id
      });

      if (error) {
        console.error('Error fetching unread count:', error);
        return 0;
      }

      return data || 0;
    },
    staleTime: 10000, // 10 seconds
    retry: 2,
  });
};

/**
 * Hook to mark a notification as read
 */
export const useMarkNotificationRead = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (notificationId: string) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { error } = await supabase.rpc('mark_notification_read', {
        p_notification_id: notificationId,
        p_user_id: user.id
      });

      if (error) throw error;
    },
    onSuccess: () => {
      // Invalidate notifications queries
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast.success('Notification marked as read');
    },
    onError: (error: any) => {
      console.error('Error marking notification as read:', error);
      toast.error('Failed to mark notification as read');
    },
  });
};

/**
 * Hook to mark all notifications as read
 */
export const useMarkAllNotificationsRead = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { error } = await supabase.rpc('mark_all_notifications_read', {
        p_user_id: user.id
      });

      if (error) throw error;
    },
    onSuccess: () => {
      // Invalidate notifications queries
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast.success('All notifications marked as read');
    },
    onError: (error: any) => {
      console.error('Error marking all notifications as read:', error);
      toast.error('Failed to mark notifications as read');
    },
  });
};

/**
 * Hook to create a notification (admin/system use)
 */
export const useCreateNotification = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateNotificationData) => {
      const { data: result, error } = await supabase.rpc('create_notification', {
        p_user_id: data.user_id,
        p_type: data.type,
        p_title: data.title,
        p_message: data.message,
        p_data: data.data || {},
        p_action_url: data.action_url
      });

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      // Invalidate notifications queries
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
    onError: (error: any) => {
      console.error('Error creating notification:', error);
      toast.error('Failed to create notification');
    },
  });
};

/**
 * Hook to set up real-time notifications
 */
export const useNotificationSubscription = () => {
  const queryClient = useQueryClient();

  useEffect(() => {
    const setupSubscription = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return;

        console.log('🔔 Setting up notifications subscription for user:', user.id);

      const subscription = supabase
        .channel(`notifications:${user.id}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'notifications',
            filter: `user_id=eq.${user.id}`,
          },
          (payload) => {
            console.log('🔔 New notification received:', payload);
            
            // Invalidate notifications queries
            queryClient.invalidateQueries({ queryKey: ['notifications'] });
            
            // Show toast notification
            const notification = payload.new as Notification;
            toast.info(notification.title, {
              description: notification.message,
              action: notification.action_url ? {
                label: 'View',
                onClick: () => window.location.href = notification.action_url!
              } : undefined,
            });
          }
        )
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'notifications',
            filter: `user_id=eq.${user.id}`,
          },
          (payload) => {
            console.log('🔔 Notification updated:', payload);
            
            // Invalidate notifications queries
            queryClient.invalidateQueries({ queryKey: ['notifications'] });
          }
        )
        .subscribe((status) => {
          console.log('🔔 Notifications subscription status:', status);
        });

        return () => {
          console.log('🔔 Cleaning up notifications subscription');
          subscription.unsubscribe();
        };
      } catch (error) {
        console.error('Error setting up notifications subscription:', error);
      }
    };

    setupSubscription();
  }, [queryClient]);
};

/**
 * Hook to request notification permission and set up push notifications
 */
export const usePushNotifications = () => {
  const requestPermission = async () => {
    if (typeof window === 'undefined' || !('Notification' in window)) {
      toast.error('This browser does not support notifications');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission === 'denied') {
      toast.error('Notifications are blocked. Please enable them in your browser settings.');
      return false;
    }

    const permission = await Notification.requestPermission();
    
    if (permission === 'granted') {
      toast.success('Notifications enabled!');
      return true;
    } else {
      toast.error('Notification permission denied');
      return false;
    }
  };

  const sendPushNotification = (title: string, options?: NotificationOptions) => {
    if (typeof window !== 'undefined' && 'Notification' in window && Notification.permission === 'granted') {
      new Notification(title, {
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        ...options,
      });
    }
  };

  return {
    requestPermission,
    sendPushNotification,
    isSupported: typeof window !== 'undefined' && 'Notification' in window,
    permission: typeof window !== 'undefined' && 'Notification' in window ? Notification.permission : 'default',
  };
};

/**
 * Hook to get notification icon based on type
 */
export const useNotificationIcon = (type: Notification['type']) => {
  const iconMap = {
    friend_request: '👥',
    friend_accepted: '✅',
    message: '💬',
    study_group_invite: '📚',
    study_group_post: '📝',
    note_shared: '📄',
    system: '⚙️',
  };

  return iconMap[type] || '🔔';
};

/**
 * Hook to get notification color based on type
 */
export const useNotificationColor = (type: Notification['type']) => {
  const colorMap = {
    friend_request: 'text-blue-600 bg-blue-50',
    friend_accepted: 'text-green-600 bg-green-50',
    message: 'text-purple-600 bg-purple-50',
    study_group_invite: 'text-orange-600 bg-orange-50',
    study_group_post: 'text-indigo-600 bg-indigo-50',
    note_shared: 'text-cyan-600 bg-cyan-50',
    system: 'text-gray-600 bg-gray-50',
  };

  return colorMap[type] || 'text-gray-600 bg-gray-50';
};
