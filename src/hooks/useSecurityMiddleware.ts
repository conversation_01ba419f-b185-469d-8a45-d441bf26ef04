import { useEffect, useCallback } from 'react';
import { useSecurity } from '@/contexts/SecurityContext';
import { SECURITY_CONFIG, SENSITIVE_ROUTES } from '@/config/security';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'sonner';

interface SecurityMiddlewareOptions {
  enableRateLimit?: boolean;
  enableCSRFProtection?: boolean;
  enableSessionValidation?: boolean;
  enableRouteProtection?: boolean;
  customRules?: Array<{
    condition: () => boolean;
    action: () => void;
    message?: string;
  }>;
}

export const useSecurityMiddleware = (options: SecurityMiddlewareOptions = {}) => {
  const {
    enableRateLimit = true,
    enableCSRFProtection = true,
    enableSessionValidation = true,
    enableRouteProtection = true,
    customRules = [],
  } = options;

  const security = useSecurity();
  const location = useLocation();
  const navigate = useNavigate();

  // Route Protection
  useEffect(() => {
    if (!enableRouteProtection) return;

    const currentPath = location.pathname;
    const isSensitiveRoute = SENSITIVE_ROUTES.some(route => 
      currentPath.startsWith(route)
    );

    if (isSensitiveRoute) {
      // Check if user is blocked
      if (security.securityStatus.isBlocked) {
        toast.error('Access denied: Account temporarily blocked');
        navigate('/');
        return;
      }

      // Check session validity
      if (!security.isSessionValid()) {
        toast.error('Session expired. Please log in again.');
        navigate('/login');
        return;
      }

      // Log access to sensitive route
      security.reportSuspiciousActivity('Sensitive route access', { 
        route: currentPath 
      });
    }
  }, [location.pathname, enableRouteProtection, security, navigate]);

  // Session Validation
  useEffect(() => {
    if (!enableSessionValidation) return;

    const validateSession = () => {
      if (!security.isSessionValid()) {
        toast.warning('Your session will expire soon. Please save your work.');
        // Auto-refresh session if user is active
        if (security.securityStatus.lastActivity) {
          const timeSinceActivity = Date.now() - security.securityStatus.lastActivity.getTime();
          if (timeSinceActivity < 5 * 60 * 1000) { // 5 minutes
            security.refreshSession();
          }
        }
      }
    };

    const interval = setInterval(validateSession, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, [enableSessionValidation, security]);

  // Custom Security Rules
  useEffect(() => {
    customRules.forEach(rule => {
      if (rule.condition()) {
        rule.action();
        if (rule.message) {
          toast.error(rule.message);
        }
      }
    });
  }, [customRules]);

  // API Request Wrapper with Security
  const secureApiRequest = useCallback(async (
    url: string,
    options: RequestInit = {},
    action: string = 'api_request'
  ) => {
    // Rate limiting check
    if (enableRateLimit && !security.checkRateLimit(action)) {
      throw new Error('Rate limit exceeded');
    }

    // Add CSRF token if enabled
    const headers = { ...options.headers };
    if (enableCSRFProtection) {
      headers['X-CSRF-Token'] = security.getCSRFToken();
    }

    // Add security headers
    headers['X-Requested-With'] = 'XMLHttpRequest';
    headers['Content-Type'] = headers['Content-Type'] || 'application/json';

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      // Check for security-related response headers
      const securityHeaders = response.headers.get('X-Security-Warning');
      if (securityHeaders) {
        security.reportSuspiciousActivity('Security warning from server', {
          warning: securityHeaders,
          url,
        });
      }

      return response;
    } catch (error) {
      security.reportSuspiciousActivity('API request failed', {
        url,
        error: error.message,
      });
      throw error;
    }
  }, [enableRateLimit, enableCSRFProtection, security]);

  // Form Submission Security
  const secureFormSubmit = useCallback((
    formData: Record<string, any>,
    action: string = 'form_submit'
  ) => {
    // Rate limiting
    if (enableRateLimit && !security.checkRateLimit(action)) {
      return { success: false, error: 'Too many submissions. Please wait.' };
    }

    // Sanitize all form inputs
    const sanitizedData: Record<string, any> = {};
    Object.entries(formData).forEach(([key, value]) => {
      if (typeof value === 'string') {
        sanitizedData[key] = security.sanitizeInput(value, 'text');
      } else {
        sanitizedData[key] = value;
      }
    });

    // Validate inputs
    const validationErrors: string[] = [];
    Object.entries(sanitizedData).forEach(([key, value]) => {
      if (typeof value === 'string' && value.length > SECURITY_CONFIG.VALIDATION.MAX_TEXT_LENGTH) {
        validationErrors.push(`${key} exceeds maximum length`);
      }
    });

    if (validationErrors.length > 0) {
      return { success: false, errors: validationErrors };
    }

    return { success: true, data: sanitizedData };
  }, [enableRateLimit, security]);

  // File Upload Security
  const secureFileUpload = useCallback((
    file: File,
    action: string = 'file_upload'
  ) => {
    // Rate limiting
    if (enableRateLimit && !security.checkRateLimit(action, SECURITY_CONFIG.RATE_LIMITS.FILE_UPLOADS)) {
      return { success: false, error: 'Too many file uploads. Please wait.' };
    }

    // File validation
    const validation = security.validateFile(file);
    if (!validation.valid) {
      return { success: false, errors: validation.errors };
    }

    // Additional security checks
    const securityChecks = [];

    // Check file name for suspicious patterns
    if (/[<>:"/\\|?*]/.test(file.name)) {
      securityChecks.push('Suspicious characters in filename');
    }

    // Check for double extensions
    if ((file.name.match(/\./g) || []).length > 1) {
      securityChecks.push('Multiple file extensions detected');
    }

    if (securityChecks.length > 0) {
      security.reportSuspiciousActivity('Suspicious file upload attempt', {
        filename: file.name,
        checks: securityChecks,
      });
      return { success: false, errors: securityChecks };
    }

    return { success: true };
  }, [enableRateLimit, security]);

  // Content Security Scanner
  const scanContent = useCallback((content: string) => {
    const scanner = security.sanitizeInput(content, 'html');
    
    // Additional content scanning
    const suspiciousPatterns = [
      /<iframe/i,
      /<embed/i,
      /<object/i,
      /javascript:/i,
      /data:text\/html/i,
    ];

    const threats = suspiciousPatterns.filter(pattern => pattern.test(content));
    
    if (threats.length > 0) {
      security.reportSuspiciousActivity('Suspicious content detected', {
        patterns: threats.map(t => t.toString()),
      });
      return { safe: false, sanitized: scanner };
    }

    return { safe: true, sanitized: scanner };
  }, [security]);

  return {
    secureApiRequest,
    secureFormSubmit,
    secureFileUpload,
    scanContent,
    security,
  };
};
