import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useNavigate } from 'react-router-dom';
import { handleAuthError, clearInvalidSession, validateSession } from '@/utils/authUtils';
import { toast } from 'sonner';

export type AuthUser = {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
};

// Get current user
export const useUser = () => {
  return useQuery({
    queryKey: ['user'],
    queryFn: async () => {
      try {
        // First validate the session
        const isValid = await validateSession();
        if (!isValid) {
          return null;
        }

        const { data: { user }, error } = await supabase.auth.getUser();
        if (error) {
          await handleAuthError(error);
          return null;
        }
        return user;
      } catch (error) {
        await handleAuthError(error);
        return null;
      }
    },
    retry: false, // Don't retry on auth errors
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Sign up
export const useSignUp = () => {
  const navigate = useNavigate();

  return useMutation({
    mutationFn: async ({
      email,
      password,
      fullName,
      country,
      course,
      institute,
    }: {
      email: string;
      password: string;
      fullName: string;
      country?: string;
      course?: string;
      institute?: string;
    }) => {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            country: country || null,
            course: course || null,
            institute: institute || null,
          },
        },
      });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      console.log('Registration successful, checking for group invite redirect');

      // Show success message
      toast.success("Welcome to StudyFam! 🎉");

      // Small delay to let the user see the success message
      setTimeout(() => {
        // Check for group invite redirect
        const inviteGroupId = localStorage.getItem('inviteGroupId');
        if (inviteGroupId) {
          localStorage.removeItem('inviteGroupId');
          console.log('Redirecting to group invite:', inviteGroupId);
          navigate(`/invite/group/${inviteGroupId}`, { replace: true });
          return;
        }

        // Check for other saved redirects
        const savedLocation = window.history.state?.from;
        const redirectTo = savedLocation && savedLocation !== '/' ? savedLocation : '/dashboard';

        console.log('Registration successful, redirecting to:', redirectTo);
        navigate(redirectTo, { replace: true });
      }, 1000);
    },
    onError: (error: any) => {
      console.error('Registration error:', error);

      // Handle specific error cases
      if (error.message?.includes('already registered')) {
        toast.error('This email is already registered. Please sign in instead.');
      } else if (error.message?.includes('invalid email')) {
        toast.error('Please enter a valid email address.');
      } else if (error.message?.includes('weak password')) {
        toast.error('Password is too weak. Please use at least 6 characters.');
      } else {
        toast.error(error.message || 'Failed to create account. Please try again.');
      }
    },
  });
};

// Sign in
export const useSignIn = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      email,
      password,
    }: {
      email: string;
      password: string;
    }) => {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      console.log('Login successful');
      queryClient.invalidateQueries({ queryKey: ['user'] });
      queryClient.invalidateQueries({ queryKey: ['profile'] });

      // Show success message
      toast.success("Welcome back! 👋");

      // Small delay to let the user see the success message
      setTimeout(() => {
        // Check for group invite redirect first
        const inviteGroupId = localStorage.getItem('inviteGroupId');
        if (inviteGroupId) {
          localStorage.removeItem('inviteGroupId');
          console.log('Redirecting to group invite after login:', inviteGroupId);
          navigate(`/invite/group/${inviteGroupId}`, { replace: true });
          return;
        }

        // Check if there's a saved intended destination
        const savedLocation = window.history.state?.from;
        const redirectTo = savedLocation && savedLocation !== '/' ? savedLocation : '/dashboard';

        console.log('Redirecting after login to:', redirectTo);
        navigate(redirectTo, { replace: true });
      }, 800);
    },
    onError: (error: any) => {
      console.error('Login error:', error);

      // Handle specific error cases
      if (error.message?.includes('Invalid login credentials')) {
        toast.error('Invalid email or password. Please check your credentials.');
      } else if (error.message?.includes('Email not confirmed')) {
        toast.error('Please check your email and confirm your account first.');
      } else if (error.message?.includes('Too many requests')) {
        toast.error('Too many login attempts. Please wait a moment and try again.');
      } else {
        toast.error(error.message || 'Failed to sign in. Please try again.');
      }
    },
  });
};

// Sign out
export const useSignOut = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      try {
        // Clear invalid session first
        await clearInvalidSession();

        const { error } = await supabase.auth.signOut();
        if (error && !error.message.includes('Invalid Refresh Token')) {
          throw error;
        }
      } catch (error) {
        // Even if signOut fails, we still want to clear local data
        console.warn('Sign out error (continuing anyway):', error);
      }
    },
    onSuccess: () => {
      queryClient.clear();
      navigate('/');
    },
    onError: () => {
      // Even on error, clear local data and navigate
      queryClient.clear();
      navigate('/');
    },
  });
};

// Reset password
export const useResetPassword = () => {
  return useMutation({
    mutationFn: async (email: string) => {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) throw error;
    },
  });
};

// Update password
export const useUpdatePassword = () => {
  return useMutation({
    mutationFn: async (newPassword: string) => {
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) throw error;
    },
  });
};

// Check if user is authenticated
export const useIsAuthenticated = () => {
  const { data: user, isLoading } = useUser();
  return {
    isAuthenticated: !!user,
    isLoading,
    user,
  };
};

// Auth state listener
export const useAuthStateChange = () => {
  const queryClient = useQueryClient();

  return useQuery({
    queryKey: ['auth-state'],
    queryFn: () => {
      const { data: { subscription } } = supabase.auth.onAuthStateChange(
        async (event, session) => {
          console.log('Auth state change:', event, session?.user?.id);

          if (event === 'SIGNED_IN') {
            queryClient.invalidateQueries({ queryKey: ['user'] });
            queryClient.invalidateQueries({ queryKey: ['profile'] });

            // Store authentication state in localStorage for persistence
            if (session?.user) {
              localStorage.setItem('studyfam_auth_state', 'authenticated');
              localStorage.setItem('studyfam_user_id', session.user.id);
            }
          } else if (event === 'SIGNED_OUT') {
            queryClient.clear();

            // Clear authentication state from localStorage
            localStorage.removeItem('studyfam_auth_state');
            localStorage.removeItem('studyfam_user_id');
          } else if (event === 'TOKEN_REFRESHED') {
            console.log('Token refreshed successfully');
          } else if (event === 'USER_UPDATED') {
            queryClient.invalidateQueries({ queryKey: ['user'] });
            queryClient.invalidateQueries({ queryKey: ['profile'] });
          }
        }
      );

      return subscription;
    },
    staleTime: Infinity,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });
};
