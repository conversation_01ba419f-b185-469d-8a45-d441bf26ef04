
import { useState, useEffect } from 'react';
import { useUser } from '@/hooks/useAuth';
import { getUserSubscriptionStatus, checkActiveSubscription, startFreeTrial } from '@/services/paystackService';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export interface SubscriptionStatus {
  subscription_id: string | null;
  plan_name: string | null;
  status: string;
  is_trial: boolean;
  trial_ends_at: string | null;
  current_period_end: string | null;
  days_remaining: number;
}

export const useSubscription = () => {
  const { data: user } = useUser();
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasActiveSubscription, setHasActiveSubscription] = useState(false);

  const fetchSubscriptionStatus = async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);
      const [status, hasActive] = await Promise.all([
        getUserSubscriptionStatus(user.id),
        checkActiveSubscription(user.id),
      ]);

      setSubscriptionStatus(status);
      setHasActiveSubscription(hasActive);
    } catch (error) {
      console.error('Error fetching subscription status:', error);
      toast.error('Failed to load subscription status');
    } finally {
      setIsLoading(false);
    }
  };

  const startTrial = async () => {
    if (!user?.id) return;

    try {
      // Get the default plan
      const { data: plans } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .limit(1)
        .single();

      if (!plans) {
        toast.error('No subscription plan available');
        return;
      }

      await startFreeTrial(user.id, plans.id);
      toast.success('Free trial started! You have 1 minute to explore premium features.');
      await fetchSubscriptionStatus();
    } catch (error) {
      console.error('Error starting trial:', error);
      toast.error('Failed to start free trial');
    }
  };

  useEffect(() => {
    fetchSubscriptionStatus();
  }, [user?.id]);

  return {
    subscriptionStatus,
    hasActiveSubscription,
    isLoading,
    startTrial,
    refetchStatus: fetchSubscriptionStatus,
  };
};
