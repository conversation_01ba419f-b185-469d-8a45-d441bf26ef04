
import { useState, useEffect } from 'react';
import { useUser } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export interface SubscriptionStatus {
  subscription_id: string | null;
  plan_name: string | null;
  status: string;
  is_trial: boolean;
  trial_ends_at: string | null;
  current_period_end: string | null;
  days_remaining: number;
}

// Subscription management functions
export async function getUserSubscriptionStatus(userId: string): Promise<SubscriptionStatus | null> {
  try {
    const { data, error } = await supabase
      .from('user_subscriptions')
      .select(`
        id,
        status,
        trial_ends_at,
        current_period_end,
        subscription_plans (
          name
        )
      `)
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (error || !data) {
      console.log('No active subscription found for user:', userId);
      return null;
    }

    const now = new Date();
    const trialEndsAt = data.trial_ends_at ? new Date(data.trial_ends_at) : null;
    const currentPeriodEnd = data.current_period_end ? new Date(data.current_period_end) : null;

    const isTrialActive = trialEndsAt && now < trialEndsAt;
    const daysRemaining = currentPeriodEnd
      ? Math.max(0, Math.ceil((currentPeriodEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)))
      : 0;

    return {
      subscription_id: data.id,
      plan_name: data.subscription_plans?.name || 'Premium Plan',
      status: data.status,
      is_trial: !!isTrialActive,
      trial_ends_at: data.trial_ends_at,
      current_period_end: data.current_period_end,
      days_remaining: daysRemaining,
    };
  } catch (error) {
    console.error('Error in getUserSubscriptionStatus:', error);
    return null;
  }
}

export async function checkActiveSubscription(userId: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('user_subscriptions')
      .select('id, status, trial_ends_at, current_period_end')
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (error || !data) {
      console.log('No active subscription found for user:', userId);
      return false;
    }

    const now = new Date();
    const trialEndsAt = data.trial_ends_at ? new Date(data.trial_ends_at) : null;
    const currentPeriodEnd = data.current_period_end ? new Date(data.current_period_end) : null;

    // Check if trial is active or subscription is active
    const isTrialActive = trialEndsAt && now < trialEndsAt;
    const isSubscriptionActive = currentPeriodEnd && now < currentPeriodEnd;

    return isTrialActive || isSubscriptionActive;
  } catch (error) {
    console.error('Error in checkActiveSubscription:', error);
    return false;
  }
}

export async function startFreeTrial(userId: string, planId: string) {
  const trialEndDate = new Date();
  trialEndDate.setMinutes(trialEndDate.getMinutes() + 1); // 1 minute trial

  const { data, error } = await supabase
    .from('user_subscriptions')
    .upsert({
      user_id: userId,
      plan_id: planId,
      status: 'active',
      trial_ends_at: trialEndDate.toISOString(),
    }, {
      onConflict: 'user_id',
    })
    .select()
    .single();

  if (error) throw error;
  return data;
}

export const useSubscription = () => {
  const { data: user } = useUser();
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasActiveSubscription, setHasActiveSubscription] = useState(false);

  const fetchSubscriptionStatus = async () => {
    if (!user?.id) {
      setIsLoading(false);
      return;
    }

    console.log('🔄 Fetching subscription status for user:', user.id);

    try {
      setIsLoading(true);
      const [status, hasActive] = await Promise.all([
        getUserSubscriptionStatus(user.id),
        checkActiveSubscription(user.id),
      ]);

      console.log('📊 Subscription status:', status);
      console.log('✅ Has active subscription:', hasActive);

      setSubscriptionStatus(status);
      setHasActiveSubscription(hasActive);
    } catch (error) {
      console.error('❌ Error fetching subscription status:', error);
      // Don't show error toast, just set defaults
      setSubscriptionStatus(null);
      setHasActiveSubscription(false);
    } finally {
      setIsLoading(false);
    }
  };

  const startTrial = async () => {
    if (!user?.id) return;

    try {
      // Get the default plan
      const { data: plans } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .limit(1)
        .single();

      if (!plans) {
        toast.error('No subscription plan available');
        return;
      }

      await startFreeTrial(user.id, plans.id);
      toast.success('Free trial started! You have 1 minute to explore premium features.');
      await fetchSubscriptionStatus();
    } catch (error) {
      console.error('Error starting trial:', error);
      toast.error('Failed to start free trial');
    }
  };

  useEffect(() => {
    if (user?.id) {
      fetchSubscriptionStatus();
    } else {
      setIsLoading(false);
      setSubscriptionStatus(null);
      setHasActiveSubscription(false);
    }
  }, [user?.id]);

  // Add a timeout to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (isLoading) {
        console.warn('⏰ Subscription check timed out, setting defaults');
        setIsLoading(false);
        setSubscriptionStatus(null);
        setHasActiveSubscription(false);
      }
    }, 5000); // 5 second timeout (reduced for faster testing)

    return () => clearTimeout(timeout);
  }, [isLoading]);

  return {
    subscriptionStatus,
    hasActiveSubscription,
    isLoading,
    startTrial,
    refetchStatus: fetchSubscriptionStatus,
  };
};
