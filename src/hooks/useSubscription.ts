import { useState, useEffect } from 'react';
import { useUser } from '@/hooks/useAuth';
import { subscriptionService, SubscriptionStatus } from '@/services/subscription';
import { toast } from 'sonner';

export function useSubscription() {
  const { data: user } = useUser();
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [hasActiveSubscription, setHasActiveSubscription] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const fetchSubscriptionStatus = async () => {
    if (!user?.id) {
      setIsLoading(false);
      return;
    }

    console.log('🔄 Fetching subscription status for user:', user.id);

    try {
      setIsLoading(true);
      const [status, hasActive] = await Promise.all([
        subscriptionService.getSubscriptionStatus(user.id),
        subscriptionService.checkActiveSubscription(user.id),
      ]);

      console.log('📊 Subscription status:', status);
      console.log('✅ Has active subscription:', hasActive);

      setSubscriptionStatus(status);
      setHasActiveSubscription(hasActive);
    } catch (error) {
      console.error('❌ Error fetching subscription status:', error);
      setSubscriptionStatus(null);
      setHasActiveSubscription(false);
    } finally {
      setIsLoading(false);
    }
  };

  const startFreeTrial = async () => {
    if (!user?.id) return;

    try {
      await subscriptionService.startFreeTrial(user.id);
      toast.success('Free trial started! You have 1 minute to explore premium features.');
      await fetchSubscriptionStatus();
    } catch (error) {
      console.error('Error starting free trial:', error);
      toast.error('Failed to start free trial. Please try again.');
    }
  };

  const refetchStatus = fetchSubscriptionStatus;

  useEffect(() => {
    if (user?.id) {
      fetchSubscriptionStatus();
    } else {
      setIsLoading(false);
      setSubscriptionStatus(null);
      setHasActiveSubscription(false);
    }
  }, [user?.id]);

  // Add a timeout to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (isLoading) {
        console.warn('⏰ Subscription check timed out, setting defaults');
        setIsLoading(false);
        setSubscriptionStatus(null);
        setHasActiveSubscription(false);
      }
    }, 5000); // 5 second timeout

    return () => clearTimeout(timeout);
  }, [isLoading]);

  return {
    subscriptionStatus,
    hasActiveSubscription,
    isLoading,
    startFreeTrial,
    refetchStatus,
  };
}
