
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// Types
export interface Unit {
  id: string;
  name: string;
  description?: string;
  color: string;
  topic_count: number;
  note_count: number;
  created_at: string;
  updated_at: string;
}

export interface Topic {
  id: string;
  name: string;
  description?: string;
  note_count: number;
  created_at: string;
  updated_at: string;
}

export interface Note {
  id: string;
  title: string;
  content?: string;
  file_url?: string;
  tags?: string[];
  created_at: string;
  updated_at: string;
}

export interface CreateUnitData {
  name: string;
  description?: string;
  color?: string;
}

export interface CreateTopicData {
  name: string;
  description?: string;
  unit_id: string;
}

export interface CreateNoteData {
  title: string;
  content?: string;
  unit_id: string;
  topic_id: string;
  file_url?: string;
  tags?: string[];
}

// Hook to get user's units
export const useUnits = () => {
  return useQuery({
    queryKey: ['units'],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('No user found in useUnits');
        throw new Error('Not authenticated');
      }





      // Get units with proper counts using a more complex query
      const { data: unitsData, error: unitsError } = await supabase
        .from('units')
        .select(`
          id,
          name,
          description,
          color,
          created_at,
          updated_at
        `)
        .eq('owner_id', user.id)
        .order('created_at', { ascending: false });

      if (unitsError) {
        console.error('Error fetching units:', unitsError);
        throw unitsError;
      }



      // For each unit, get the topic count and note count
      const unitsWithCounts = await Promise.all(
        (unitsData || []).map(async (unit) => {
          // Get topic count
          const { count: topicCount } = await supabase
            .from('topics')
            .select('*', { count: 'exact', head: true })
            .eq('unit_id', unit.id)
            .eq('owner_id', user.id);

          // Get note count
          const { count: noteCount } = await supabase
            .from('notes')
            .select('*', { count: 'exact', head: true })
            .eq('unit_id', unit.id)
            .eq('user_id', user.id);

          return {
            ...unit,
            topic_count: topicCount || 0,
            note_count: noteCount || 0,
          };
        })
      );


      return unitsWithCounts as Unit[];
    },
    retry: 1,
  });
};

// Hook to get topics for a specific unit
export const useTopics = (unitId: string) => {
  return useQuery({
    queryKey: ['topics', unitId],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      // Get topics with proper note counts
      const { data: topicsData, error: topicsError } = await supabase
        .from('topics')
        .select(`
          id,
          name,
          description,
          created_at,
          updated_at
        `)
        .eq('unit_id', unitId)
        .eq('owner_id', user.id)
        .order('created_at', { ascending: false });

      if (topicsError) throw topicsError;

      // For each topic, get the note count
      const topicsWithCounts = await Promise.all(
        (topicsData || []).map(async (topic) => {
          const { count: noteCount } = await supabase
            .from('notes')
            .select('*', { count: 'exact', head: true })
            .eq('topic_id', topic.id)
            .eq('user_id', user.id);

          return {
            ...topic,
            note_count: noteCount || 0,
          };
        })
      );

      return topicsWithCounts as Topic[];
    },
    enabled: !!unitId,
    retry: 1,
  });
};

// Hook to get notes for a specific topic
export const useNotes = (topicId: string) => {
  return useQuery({
    queryKey: ['notes', topicId],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      // Direct table query for notes
      const { data, error } = await supabase
        .from('notes')
        .select(`
          id,
          title,
          content,
          file_url,
          tags,
          created_at,
          updated_at
        `)
        .eq('topic_id', topicId)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Note[];
    },
    enabled: !!topicId,
    retry: 1,
  });
};

// Hook to create a new unit
export const useCreateUnit = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateUnitData) => {
      // Check authentication first
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError) {
        console.error('Auth error:', authError);
        throw new Error('Authentication error: ' + authError.message);
      }

      if (!user) {
        console.error('No user found');
        throw new Error('Not authenticated - please log in again');
      }



      const { data: result, error } = await supabase
        .from('units')
        .insert({
          name: data.name,
          description: data.description,
          color: data.color || '#6366f1',
          owner_id: user.id,
        })
        .select()
        .single();

      if (error) {
        console.error('Database error:', error);
        throw new Error(`Database error: ${error.message}`);
      }

      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['units'] });
      toast.success('Unit created successfully!');
    },
    onError: (error: any) => {
      console.error('Create unit error:', error);

      // Provide more specific error messages
      let errorMessage = 'Failed to create unit';
      if (error.message.includes('new row violates row-level security policy')) {
        errorMessage = 'Permission denied. Please make sure you are logged in.';
      } else if (error.message.includes('duplicate key value') || error.message.includes('units_name_owner_id_key')) {
        errorMessage = 'A unit with this name already exists. Please choose a different name.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
    },
  });
};

// Hook to create a new topic
export const useCreateTopic = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateTopicData) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data: result, error } = await supabase
        .from('topics')
        .insert({
          name: data.name,
          description: data.description,
          unit_id: data.unit_id,
          owner_id: user.id,
        })
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['topics', variables.unit_id] });
      queryClient.invalidateQueries({ queryKey: ['units'] });
      toast.success('Topic created successfully!');
    },
    onError: (error: any) => {
      console.error('Create topic error:', error);

      // Provide more specific error messages
      let errorMessage = 'Failed to create topic';
      if (error.message.includes('new row violates row-level security policy')) {
        errorMessage = 'Permission denied. Please make sure you are logged in.';
      } else if (error.message.includes('duplicate key value')) {
        errorMessage = 'A topic with this name already exists in this unit.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
    },
  });
};

// Hook to upload a file to Supabase storage
export const useUploadFile = () => {
  return useMutation({
    mutationFn: async (file: File) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `${user.id}/${fileName}`;

      const { data, error } = await supabase.storage
        .from('notes-files')
        .upload(filePath, file);

      if (error) throw error;

      const { data: { publicUrl } } = supabase.storage
        .from('notes-files')
        .getPublicUrl(filePath);

      return {
        path: data.path,
        url: publicUrl,
      };
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to upload file');
    },
  });
};

// Hook to create a new note
export const useCreateNote = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateNoteData) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data: result, error } = await supabase
        .from('notes')
        .insert({
          title: data.title,
          content: data.content,
          unit_id: data.unit_id,
          topic_id: data.topic_id,
          file_url: data.file_url,
          tags: data.tags,
          user_id: user.id, // Use user_id not owner_id
        })
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['notes', variables.topic_id] });
      queryClient.invalidateQueries({ queryKey: ['topics', variables.unit_id] });
      queryClient.invalidateQueries({ queryKey: ['units'] });
      toast.success('Note uploaded successfully!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create note');
    },
  });
};
