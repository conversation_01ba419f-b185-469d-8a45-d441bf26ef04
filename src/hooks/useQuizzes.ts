
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface Quiz {
  id: string;
  title: string;
  description?: string;
  subject?: string;
  difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
  time_limit?: number;
  randomize_questions: boolean;
  show_results_immediately: boolean;
  is_public: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface QuizQuestion {
  id: string;
  quiz_id: string;
  question_text: string;
  question_type: 'multiple_choice' | 'true_false' | 'short_answer';
  correct_answer: string;
  options?: string[];
  points: number;
  explanation?: string;
  order_index: number;
}

export interface QuizAttempt {
  id: string;
  quiz_id: string;
  user_id: string;
  score: number;
  total_questions: number;
  time_taken?: number;
  completed_at: string;
  answers: Record<string, string>;
}

export interface CreateQuizData {
  title: string;
  description?: string;
  subject?: string;
  difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
  time_limit?: number;
  randomize_questions: boolean;
  show_results_immediately: boolean;
  is_public: boolean;
}

export interface CreateQuestionData {
  quiz_id: string;
  question_text: string;
  question_type: 'multiple_choice' | 'true_false' | 'short_answer';
  correct_answer: string;
  options?: string[];
  points: number;
  explanation?: string;
  order_index: number;
}

// Hook to get user's quizzes
export const useUserQuizzes = () => {
  return useQuery({
    queryKey: ['user-quizzes'],
    queryFn: async () => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('Not authenticated');

      const { data, error } = await supabase.rpc('get_user_quizzes', {
        p_user_id: user.user.id
      });

      if (error) throw error;
      return data;
    },
  });
};

// Hook to get public quizzes
export const usePublicQuizzes = (limit = 20) => {
  return useQuery({
    queryKey: ['public-quizzes', limit],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_public_quizzes', {
        p_limit: limit
      });

      if (error) throw error;
      return data;
    },
  });
};

// Hook to get quiz with questions
export const useQuizWithQuestions = (quizId: string) => {
  return useQuery({
    queryKey: ['quiz-with-questions', quizId],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_quiz_with_questions', {
        p_quiz_id: quizId
      });

      if (error) throw error;
      return data;
    },
    enabled: !!quizId,
  });
};

// Hook to create a quiz
export const useCreateQuiz = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (quizData: CreateQuizData) => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('Not authenticated');

      const { data, error } = await supabase
        .from('quizzes')
        .insert({
          ...quizData,
          created_by: user.user.id,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-quizzes'] });
    },
  });
};

// Hook to add questions to a quiz
export const useAddQuizQuestion = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (questionData: CreateQuestionData) => {
      const { data, error } = await supabase
        .from('quiz_questions')
        .insert(questionData)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['quiz-with-questions', variables.quiz_id] });
    },
  });
};

// Hook to submit quiz attempt
export const useSubmitQuizAttempt = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ 
      quizId, 
      answers, 
      timeTaken 
    }: { 
      quizId: string; 
      answers: Record<string, string>; 
      timeTaken: number; 
    }) => {
      const { data, error } = await supabase.rpc('submit_quiz_attempt', {
        p_quiz_id: quizId,
        p_answers: answers,
        p_time_taken: timeTaken
      });

      if (error) throw error;
      return data[0];
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-quizzes'] });
    },
  });
};

// Hook to delete a quiz
export const useDeleteQuiz = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (quizId: string) => {
      const { error } = await supabase
        .from('quizzes')
        .delete()
        .eq('id', quizId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-quizzes'] });
    },
  });
};
