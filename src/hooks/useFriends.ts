
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export interface Friend {
  id: string;
  full_name: string;
  avatar_url?: string;
  is_online?: boolean;
  last_seen?: string;
  status_text?: string;
  email?: string;
  country?: string;
  course?: string;
  institute?: string;
}

export interface FriendRequest {
  id: string;
  requester_id: string;
  addressee_id: string;
  status: 'pending' | 'accepted' | 'blocked';
  created_at: string;
  requester: {
    id: string;
    full_name: string;
    avatar_url?: string;
    email: string;
  };
}

export interface DiscoverUser {
  id: string;
  full_name: string;
  avatar_url?: string;
  email: string;
  country?: string;
  course?: string;
  institute?: string;
}

// Get user's friends
export const useFriends = () => {
  return useQuery({
    queryKey: ['friends'],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data, error } = await supabase.rpc('get_user_friends', {
        p_user_id: user.id,
      });

      if (error) throw error;
      return data as Friend[];
    },
  });
};

// Get friend requests (incoming only)
export const useFriendRequests = () => {
  return useQuery({
    queryKey: ['friend-requests'],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data, error } = await supabase
        .from('friendships')
        .select(`
          *,
          requester:profiles!friendships_requester_id_fkey(id, full_name, avatar_url, email)
        `)
        .eq('addressee_id', user.id)
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as FriendRequest[];
    },
  });
};

// Main export for backward compatibility
export const useFriendshipRequests = useFriendRequests;

// Get sent friend requests
export const useSentFriendshipRequests = () => {
  return useQuery({
    queryKey: ['sent-friend-requests'],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data, error } = await supabase
        .from('friendships')
        .select(`
          *,
          addressee:profiles!friendships_addressee_id_fkey(id, full_name, avatar_url, email)
        `)
        .eq('requester_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
  });
};

// Get users available for friendship
export const useDiscoverUsers = () => {
  return useQuery({
    queryKey: ['discover-users'],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      // Get users who are not friends and haven't been sent requests
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, avatar_url, email, country, course, institute')
        .neq('id', user.id);

      if (error) throw error;

      // Filter out existing friends and requests
      const { data: friendships } = await supabase
        .from('friendships')
        .select('requester_id, addressee_id')
        .or(`requester_id.eq.${user.id},addressee_id.eq.${user.id}`);

      const excludeIds = new Set(
        friendships?.flatMap(f => [f.requester_id, f.addressee_id]) || []
      );
      excludeIds.add(user.id);

      return data.filter(u => !excludeIds.has(u.id)) as DiscoverUser[];
    },
  });
};

// Send friend request
export const useSendFriendRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (friendId: string) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      // Prevent sending friend request to yourself
      if (user.id === friendId) {
        throw new Error('You cannot send a friend request to yourself');
      }

      // Check if friendship already exists
      const { data: existingFriendship } = await supabase
        .from('friendships')
        .select('status')
        .or(`and(requester_id.eq.${user.id},addressee_id.eq.${friendId}),and(requester_id.eq.${friendId},addressee_id.eq.${user.id})`)
        .single();

      if (existingFriendship) {
        if (existingFriendship.status === 'accepted') {
          throw new Error('You are already friends with this user');
        } else if (existingFriendship.status === 'pending') {
          throw new Error('Friend request already sent');
        }
      }

      const { data, error } = await supabase
        .from('friendships')
        .insert({
          requester_id: user.id,
          addressee_id: friendId,
          status: 'pending',
        })
        .select()
        .single();

      if (error) {
        console.error('Database error:', error);
        if (error.code === '23505') { // Unique constraint violation
          throw new Error('Friend request already exists');
        }
        throw error;
      }
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['friend-requests'] });
      queryClient.invalidateQueries({ queryKey: ['discover-users'] });
      queryClient.invalidateQueries({ queryKey: ['sent-friend-requests'] });
      queryClient.invalidateQueries({ queryKey: ['friendship-status'] });
      toast.success('Friend request sent!');
    },
    onError: (error: any) => {
      console.error('Friend request error:', error);
      toast.error(error.message || 'Failed to send friend request');
    },
  });
};

// Accept friend request
export const useAcceptFriendRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (requestId: string) => {
      const { data, error } = await supabase
        .from('friendships')
        .update({ status: 'accepted' })
        .eq('id', requestId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['friend-requests'] });
      queryClient.invalidateQueries({ queryKey: ['friends'] });
      toast.success('Friend request accepted!');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to accept friend request');
    },
  });
};

// Decline/Reject friend request
export const useRejectFriendRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (requestId: string) => {
      const { error } = await supabase
        .from('friendships')
        .delete()
        .eq('id', requestId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['friend-requests'] });
      toast.success('Friend request rejected');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to reject friend request');
    },
  });
};

// Alias for backward compatibility
export const useDeclineFriendRequest = useRejectFriendRequest;

// Search for users to add as friends
export const useSearchUsers = () => {
  return useMutation({
    mutationFn: async (searchTerm: string) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, avatar_url, email')
        .or(`full_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`)
        .neq('id', user.id)
        .limit(10);

      if (error) throw error;
      return data;
    },
  });
};

// Remove friend
export const useRemoveFriend = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (friendId: string) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const { error } = await supabase
        .from('friendships')
        .delete()
        .or(`and(requester_id.eq.${user.id},addressee_id.eq.${friendId}),and(requester_id.eq.${friendId},addressee_id.eq.${user.id})`)
        .eq('status', 'accepted');

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['friends'] });
      queryClient.invalidateQueries({ queryKey: ['friend-requests'] });
      toast.success('Friend removed');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to remove friend');
    },
  });
};

// Get suggested friends (users with common interests or connections)
export const useSuggestedFriends = () => {
  return useQuery({
    queryKey: ['suggested-friends'],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      // Get users who are not already friends and not already requested
      const { data: currentUser } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (!currentUser) return [];

      const { data: suggestions, error: suggestionsError } = await supabase
        .from('profiles')
        .select('id, full_name, avatar_url, email, institute, country')
        .neq('id', user.id)
        .limit(5);

      if (suggestionsError) throw suggestionsError;
      return suggestions || [];
    },
  });
};

// Check friendship status with a specific user
export const useFriendshipStatus = (userId: string) => {
  return useQuery({
    queryKey: ['friendship-status', userId],
    queryFn: async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user || !userId || user.id === userId) {
        return { status: 'none', isFriend: false, requestSent: false, requestReceived: false };
      }

      const { data, error } = await supabase
        .from('friendships')
        .select('status, requester_id, addressee_id')
        .or(`and(requester_id.eq.${user.id},addressee_id.eq.${userId}),and(requester_id.eq.${userId},addressee_id.eq.${user.id})`)
        .single();

      if (error && error.code !== 'PGRST116') throw error; // PGRST116 is "not found"

      if (!data) {
        return { status: 'none', isFriend: false, requestSent: false, requestReceived: false };
      }

      const isFriend = data.status === 'accepted';
      const requestSent = data.status === 'pending' && data.requester_id === user.id;
      const requestReceived = data.status === 'pending' && data.addressee_id === user.id;

      return {
        status: data.status,
        isFriend,
        requestSent,
        requestReceived,
      };
    },
    enabled: !!userId,
  });
};

