import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useCreateStudyGroupPost } from '@/hooks/useStudyGroups';
import { useUploadFile } from '@/hooks/useNotes';
import { generateNotePDF, NotePDFOptions } from '@/utils/pdfGenerator';
import { toast } from 'sonner';

export interface ShareData {
  type: 'note' | 'past_paper';
  item_id: string;
  title: string;
  content?: string;
  file_url?: string;
  recipients: {
    friends?: string[];
    study_groups?: string[];
  };
  message?: string;
}

export const useShareItem = () => {
  const queryClient = useQueryClient();
  const createStudyGroupPostMutation = useCreateStudyGroupPost();
  const uploadFileMutation = useUploadFile();

  return useMutation({
    mutationFn: async (shareData: ShareData) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('Not authenticated');

      const results = [];

      // Share to study groups
      if (shareData.recipients.study_groups && shareData.recipients.study_groups.length > 0) {
        for (const groupId of shareData.recipients.study_groups) {
          try {
            let fileUrl = shareData.file_url;
            let fileName = shareData.title;

            // If no file URL but has content, generate PDF
            if (!fileUrl && shareData.content) {
              const pdfOptions: NotePDFOptions = {
                title: shareData.title,
                content: shareData.content,
                includeMetadata: true,
                author: user.user_metadata?.full_name || user.email || 'Unknown'
              };
              const pdfBlob = generateNotePDF(pdfOptions);
              const pdfFile = new File([pdfBlob], `${shareData.title}.pdf`, { type: 'application/pdf' });
              const uploadResult = await uploadFileMutation.mutateAsync(pdfFile);
              fileUrl = uploadResult.url;
              fileName = `${shareData.title}.pdf`;
            }

            // Create post in study group
            await createStudyGroupPostMutation.mutateAsync({
              group_id: groupId,
              title: shareData.title,
              content: shareData.message || `Shared ${shareData.type}: ${shareData.title}`,
              post_type: 'note',
              file: fileUrl ? undefined : undefined, // File is already uploaded
            });

            results.push({ type: 'group', id: groupId, success: true });
          } catch (error) {
            console.error(`Failed to share to group ${groupId}:`, error);
            results.push({ type: 'group', id: groupId, success: false, error });
          }
        }
      }

      // Share to friends via messaging system
      if (shareData.recipients.friends && shareData.recipients.friends.length > 0) {
        for (const friendId of shareData.recipients.friends) {
          try {
            let fileUrl = shareData.file_url;
            let fileName = shareData.title;

            // If no file URL but has content, generate PDF
            if (!fileUrl && shareData.content) {
              const pdfOptions: NotePDFOptions = {
                title: shareData.title,
                content: shareData.content,
                includeMetadata: true,
                author: user.user_metadata?.full_name || user.email || 'Unknown'
              };
              const pdfBlob = generateNotePDF(pdfOptions);
              const pdfFile = new File([pdfBlob], `${shareData.title}.pdf`, { type: 'application/pdf' });
              const uploadResult = await uploadFileMutation.mutateAsync(pdfFile);
              fileUrl = uploadResult.url;
              fileName = `${shareData.title}.pdf`;
            }

            // Create or get conversation with friend
            const { data: conversationData, error: conversationError } = await supabase
              .rpc('get_or_create_conversation', {
                p_user1_id: user.id,
                p_user2_id: friendId
              });

            if (conversationError) throw conversationError;

            // Send message with document
            const messageContent = shareData.message
              ? `${shareData.message}\n\n📎 Shared ${shareData.type}: ${shareData.title}`
              : `📎 Shared ${shareData.type}: ${shareData.title}`;

            const { error: messageError } = await supabase
              .rpc('send_message', {
                p_conversation_id: conversationData,
                p_content: messageContent,
                p_message_type: 'file',
                p_file_url: fileUrl,
                p_file_name: fileName,
                p_file_size: null,
                p_reply_to_id: null
              });

            if (messageError) throw messageError;

            results.push({ type: 'friend', id: friendId, success: true });
          } catch (error) {
            console.error(`Failed to share to friend ${friendId}:`, error);
            results.push({ type: 'friend', id: friendId, success: false, error });
          }
        }
      }

      return results;
    },
    onSuccess: (results) => {
      const successCount = results.filter(r => r.success).length;
      const totalCount = results.length;
      const friendCount = results.filter(r => r.type === 'friend' && r.success).length;
      const groupCount = results.filter(r => r.type === 'group' && r.success).length;

      if (successCount === totalCount) {
        let message = 'Successfully shared';
        if (friendCount > 0 && groupCount > 0) {
          message += ` to ${friendCount} friend${friendCount !== 1 ? 's' : ''} and ${groupCount} group${groupCount !== 1 ? 's' : ''}!`;
        } else if (friendCount > 0) {
          message += ` to ${friendCount} friend${friendCount !== 1 ? 's' : ''}!`;
        } else if (groupCount > 0) {
          message += ` to ${groupCount} group${groupCount !== 1 ? 's' : ''}!`;
        } else {
          message += `!`;
        }
        toast.success(message);
      } else if (successCount > 0) {
        toast.warning(`Shared to ${successCount} of ${totalCount} recipients. Some failed.`);
      } else {
        toast.error('Failed to share to any recipients.');
      }

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['study-group-posts'] });
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      queryClient.invalidateQueries({ queryKey: ['messages'] });
    },
    onError: (error) => {
      console.error('Share error:', error);
      toast.error('Failed to share item');
    },
  });
};
