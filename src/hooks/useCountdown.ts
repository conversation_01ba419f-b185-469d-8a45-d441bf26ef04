import { useState, useEffect, useMemo } from 'react';
import { differenceInDays, differenceInHours, differenceInMinutes, isToday, isTomorrow, isPast } from 'date-fns';

export interface CountdownResult {
  days: number;
  hours: number;
  minutes: number;
  isToday: boolean;
  isTomorrow: boolean;
  isPast: boolean;
  displayText: string;
  urgencyLevel: 'past' | 'today' | 'tomorrow' | 'urgent' | 'soon' | 'normal';
}

export const useCountdown = (targetDate: Date): CountdownResult => {
  // Memoize the target date to prevent unnecessary re-renders
  const memoizedTargetDate = useMemo(() => new Date(targetDate), [targetDate.getTime()]);

  const [countdown, setCountdown] = useState<CountdownResult>(() =>
    calculateCountdown(memoizedTargetDate)
  );

  useEffect(() => {
    const updateCountdown = () => {
      setCountdown(calculateCountdown(memoizedTargetDate));
    };

    // Update every minute
    const interval = setInterval(updateCountdown, 60000);

    return () => clearInterval(interval);
  }, [memoizedTargetDate]);

  return countdown;
};

function calculateCountdown(targetDate: Date): CountdownResult {
  const now = new Date();
  const target = new Date(targetDate);
  
  // Set both dates to start of day for accurate day comparison
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const targetStart = new Date(target.getFullYear(), target.getMonth(), target.getDate());
  
  const days = differenceInDays(targetStart, todayStart);
  const hours = differenceInHours(target, now) % 24;
  const minutes = differenceInMinutes(target, now) % 60;
  
  const isTargetToday = isToday(target);
  const isTargetTomorrow = isTomorrow(target);
  const isTargetPast = isPast(targetStart);
  
  let displayText = '';
  let urgencyLevel: CountdownResult['urgencyLevel'] = 'normal';
  
  if (isTargetPast && !isTargetToday) {
    displayText = 'Past due';
    urgencyLevel = 'past';
  } else if (isTargetToday) {
    if (hours > 0) {
      displayText = `Today (${hours}h ${minutes}m)`;
    } else if (minutes > 0) {
      displayText = `Today (${minutes}m)`;
    } else {
      displayText = 'Now!';
    }
    urgencyLevel = 'today';
  } else if (isTargetTomorrow) {
    displayText = 'Tomorrow';
    urgencyLevel = 'tomorrow';
  } else if (days <= 3) {
    displayText = days === 1 ? '1 day left' : `${days} days left`;
    urgencyLevel = 'urgent';
  } else if (days <= 7) {
    displayText = `${days} days left`;
    urgencyLevel = 'soon';
  } else {
    displayText = `${days} days left`;
    urgencyLevel = 'normal';
  }
  
  return {
    days,
    hours,
    minutes,
    isToday: isTargetToday,
    isTomorrow: isTargetTomorrow,
    isPast: isTargetPast && !isTargetToday,
    displayText,
    urgencyLevel
  };
}

export const getUrgencyStyles = (urgencyLevel: CountdownResult['urgencyLevel']) => {
  switch (urgencyLevel) {
    case 'past':
      return {
        badge: 'bg-gray-100 text-gray-600',
        border: 'border-l-gray-400',
        background: 'bg-gray-50/30'
      };
    case 'today':
      return {
        badge: 'bg-red-100 text-red-800 animate-pulse',
        border: 'border-l-red-500',
        background: 'bg-red-50/30'
      };
    case 'tomorrow':
      return {
        badge: 'bg-orange-100 text-orange-800',
        border: 'border-l-orange-500',
        background: 'bg-orange-50/30'
      };
    case 'urgent':
      return {
        badge: 'bg-red-100 text-red-700',
        border: 'border-l-red-500',
        background: 'bg-red-50/30'
      };
    case 'soon':
      return {
        badge: 'bg-yellow-100 text-yellow-700',
        border: 'border-l-yellow-500',
        background: 'bg-yellow-50/30'
      };
    case 'normal':
    default:
      return {
        badge: 'bg-green-100 text-green-700',
        border: 'border-l-green-500',
        background: 'bg-green-50/30'
      };
  }
};
