
import React, { useState, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Link } from "react-router-dom";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { SearchableSelect } from "@/components/ui/searchable-select";
import SimpleHeader from "@/components/SimpleHeader";
import { useSignUp } from "@/hooks/useAuth";
import { useCountries, useCourses, useInstitutes } from "@/hooks/useProfile";
import { COUNTRIES } from "@/data/countries";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

export default function Register() {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [institute, setInstitute] = useState("");
  const [country, setCountry] = useState("");
  const [courseInput, setCourseInput] = useState("");
  const [showCustomCourseInput, setShowCustomCourseInput] = useState(false);
  const [showCustomInstituteInput, setShowCustomInstituteInput] = useState(false);
  const [courses, setCourses] = useState<string[]>([]);

  // Refs for auto-focus
  const courseInputRef = useRef<HTMLInputElement>(null);
  const instituteInputRef = useRef<HTMLInputElement>(null);

  const signUpMutation = useSignUp();

  // Backend data hooks
  const { data: countries, isLoading: countriesLoading } = useCountries();
  const { data: availableCourses, isLoading: coursesLoading } = useCourses();
  const { data: institutes, isLoading: institutesLoading } = useInstitutes();

  // Auto-focus when switching to custom input mode
  useEffect(() => {
    if (showCustomCourseInput && courseInputRef.current) {
      courseInputRef.current.focus();
    }
  }, [showCustomCourseInput]);

  useEffect(() => {
    if (showCustomInstituteInput && instituteInputRef.current) {
      instituteInputRef.current.focus();
    }
  }, [showCustomInstituteInput]);

  const handleAddCourse = (e: React.FormEvent | React.MouseEvent) => {
    e.preventDefault();
    const value = courseInput.trim();

    if (value && value !== "__custom__" && !courses.includes(value)) {
      setCourses([...courses, value]);
      setCourseInput("");
      // Don't reset showCustomCourseInput here to allow adding multiple custom courses
    }
  };

  const handleRemoveCourse = (index: number) => {
    setCourses((prev) => prev.filter((_, i) => i !== index));
  };

  const handleRegister = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!name || !email || !password) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (password.length < 6) {
      toast.error("Password must be at least 6 characters");
      return;
    }

    try {
      await signUpMutation.mutateAsync({
        email,
        password,
        fullName: name,
        country: country.trim() || undefined,
        course: courses.length > 0 ? courses.join(', ') : undefined,
        institute: institute.trim() || undefined,
      });
      // Success toast is now handled in the auth hook
    } catch (error: any) {
      // Error toast is now handled in the auth hook
      console.error('Registration error:', error);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-white via-[#ecd7fa] to-[#b475ea]">
      <SimpleHeader />
      <div className="flex flex-col items-center justify-center flex-1">
        <div className="bg-white shadow-2xl rounded-2xl px-7 py-10 w-full max-w-md mx-auto">
          <h2 className="text-2xl font-extrabold text-center text-violet-800 mb-2">Create your StudyFam account</h2>
          <p className="text-slate-600 text-center mb-7">Get started for free and join thousands of students!</p>
          <form className="space-y-5" onSubmit={handleRegister}>
            <div>
              <Label htmlFor="name" className="mb-1 block">Name *</Label>
              <Input
                type="text"
                id="name"
                placeholder="Your full name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                autoFocus
              />
            </div>
            <div>
              <Label htmlFor="email" className="mb-1 block">Email *</Label>
              <Input
                type="email"
                id="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="password" className="mb-1 block">Password *</Label>
              <Input
                type="password"
                id="password"
                placeholder="Password (min 6 characters)"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                minLength={6}
              />
            </div>
            <div>
              <Label htmlFor="institute" className="mb-1 block">Institute</Label>
              <p className="text-xs text-gray-600 mb-2">Select from dropdown or enter custom institute</p>
              <div className="flex gap-2">
                {showCustomInstituteInput ? (
                  <Input
                    ref={instituteInputRef}
                    type="text"
                    id="institute"
                    placeholder="Enter your institute name"
                    value={institute}
                    onChange={(e) => setInstitute(e.target.value)}
                    className="flex-1"
                  />
                ) : (
                  <Select
                    value={institute}
                    onValueChange={(value) => {
                      if (value === "__custom__") {
                        setShowCustomInstituteInput(true);
                        setInstitute("");
                      } else {
                        setInstitute(value);
                      }
                    }}
                    disabled={institutesLoading}
                  >
                    <SelectTrigger className="flex-1">
                      <SelectValue placeholder={institutesLoading ? "Loading institutes..." : "Select your institute"} />
                    </SelectTrigger>
                    <SelectContent>
                      {institutes?.map((instituteOption: any) => (
                        <SelectItem key={instituteOption.name} value={instituteOption.name}>
                          {instituteOption.name}
                        </SelectItem>
                      ))}
                      <SelectItem value="__custom__">+ Enter custom institute</SelectItem>
                    </SelectContent>
                  </Select>
                )}
                {showCustomInstituteInput && (
                  <button
                    type="button"
                    onClick={() => {
                      setShowCustomInstituteInput(false);
                      setInstitute("");
                    }}
                    className="px-3 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
                    aria-label="Back to dropdown"
                  >
                    ↩
                  </button>
                )}
              </div>
            </div>
            <div>
              <Label htmlFor="country" className="mb-1 block">Country</Label>
              <SearchableSelect
                options={COUNTRIES}
                value={country}
                onValueChange={setCountry}
                placeholder="Select your country"
                searchPlaceholder="Search countries..."
                className="w-full"
              />
            </div>
            <div>
              <Label htmlFor="courses" className="mb-1 block">Courses</Label>
              <p className="text-xs text-gray-600 mb-2">Select from dropdown or enter custom courses</p>
              <div className="flex gap-2 mb-2">
                {showCustomCourseInput ? (
                  <Input
                    ref={courseInputRef}
                    id="courses"
                    name="courses"
                    type="text"
                    placeholder="Enter course name and press Enter or +"
                    value={courseInput}
                    onChange={(e) => setCourseInput(e.target.value)}
                    onKeyDown={e => {
                      if ((e.key === "Enter" || e.key === "," || e.key === "Tab") && courseInput.trim()) {
                        e.preventDefault();
                        handleAddCourse(e);
                      }
                    }}
                    className="flex-1"
                    autoComplete="off"
                  />
                ) : (
                  <Select
                    value={courseInput}
                    onValueChange={(value) => {
                      if (value === "__custom__") {
                        setShowCustomCourseInput(true);
                        setCourseInput("");
                      } else {
                        setCourseInput(value);
                      }
                    }}
                    disabled={coursesLoading}
                  >
                    <SelectTrigger className="flex-1">
                      <SelectValue placeholder={coursesLoading ? "Loading courses..." : "Select a course to add"} />
                    </SelectTrigger>
                    <SelectContent>
                      {availableCourses?.map((courseOption: any) => (
                        <SelectItem key={courseOption.name} value={courseOption.name}>
                          {courseOption.name}
                        </SelectItem>
                      ))}
                      <SelectItem value="__custom__">+ Enter custom course</SelectItem>
                    </SelectContent>
                  </Select>
                )}
                <button
                  type="button"
                  onClick={handleAddCourse}
                  className="px-4 py-2 bg-violet-600 text-white font-bold rounded-lg hover:bg-violet-700 disabled:opacity-50"
                  disabled={!courseInput.trim() || courseInput === "__custom__"}
                  aria-label="Add course"
                >
                  +
                </button>
                {showCustomCourseInput && (
                  <button
                    type="button"
                    onClick={() => {
                      setShowCustomCourseInput(false);
                      setCourseInput("");
                    }}
                    className="px-3 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
                    aria-label="Back to dropdown"
                  >
                    ↩
                  </button>
                )}
              </div>
              <div className="flex flex-wrap gap-2 min-h-[1.75rem]">
                {courses.map((course, idx) => (
                  <span
                    key={course + idx}
                    className="bg-violet-100 text-violet-800 px-3 py-1 rounded-full flex items-center gap-1 text-sm"
                  >
                    {course}
                    <button
                      type="button"
                      onClick={() => handleRemoveCourse(idx)}
                      className="ml-1 text-violet-700 hover:text-white hover:bg-violet-400 rounded-full w-4 h-4 flex items-center justify-center"
                      aria-label={`Remove ${course}`}
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
              <input type="hidden" name="courses[]"
                value={courses.join(",")} />
            </div>
            <button
              type="submit"
              className="w-full bg-violet-600 hover:bg-violet-700 transition text-white rounded-lg font-bold py-3 text-lg mt-3 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
              disabled={signUpMutation.isPending}
            >
              {signUpMutation.isPending && <Loader2 className="w-4 h-4 animate-spin" />}
              {signUpMutation.isPending ? "Creating account..." : "Create Account"}
            </button>
          </form>
          <div className="mt-5 text-center text-slate-600">
            Already have an account?{" "}
            <Link to="/login" className="text-violet-700 font-semibold hover:underline">Sign in</Link>
          </div>
        </div>
      </div>
    </div>
  );
}
