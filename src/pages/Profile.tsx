
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { UserPlus, Users, UserCheck, TestTube, MessageCircle } from "lucide-react";
import ProfileAvatar from "@/components/profile/ProfileAvatar";
import { ProfileEditDialog } from "@/components/profile/ProfileEditDialog";

import ProfileFriendRequests from "@/components/profile/ProfileFriendRequests";

import ProfilePreviewDialog from "@/components/profile/ProfilePreviewDialog";

import { supabase } from "@/integrations/supabase/client";
import { useProfile, useUpdateProfile, useCountries, useCourses, useInstitutes, useUploadAvatar } from "@/hooks/useProfile";
import { useFriends, useFriendshipRequests } from "@/hooks/useFriends";
import { useCreateConversation } from "@/hooks/useMessaging";


import { useQueryClient } from "@tanstack/react-query";
import { useSignOut } from "@/hooks/useAuth";
import { toast } from "sonner";
import { Loader2, LogOut } from "lucide-react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useIsMobile } from "@/hooks/use-mobile";

const TABS = [
  { label: "Friends", icon: Users },
  { label: "Requests", icon: UserCheck },
];

const Profile: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const queryClient = useQueryClient();
  const isMobile = useIsMobile();
  const [activeTab, setActiveTab] = useState(() => {
    const tabParam = searchParams.get('tab');
    return tabParam && TABS.some(tab => tab.label === tabParam) ? tabParam : "Friends";
  });
  const [editOpen, setEditOpen] = useState(false);

  const [profilePreviewUserId, setProfilePreviewUserId] = useState<string | null>(null);

  // Backend hooks
  const { data: profile, isLoading: profileLoading, error: profileError } = useProfile();
  const { data: friends } = useFriends();


  const { data: friendRequests } = useFriendshipRequests();
  const { data: countries } = useCountries();
  const { data: courses } = useCourses();
  const { data: institutes } = useInstitutes();

  // Debug logging
  console.log('Profile component state:', {
    profileLoading,
    profileError: profileError?.message,
    hasProfile: !!profile,
    profileId: profile?.id
  });

  const updateProfileMutation = useUpdateProfile();
  const uploadAvatarMutation = useUploadAvatar();
  const signOutMutation = useSignOut();

  // Handle avatar change
  const handleAvatarChange = async (file: File) => {
    console.log('Avatar change requested:', file);

    // Basic validation before attempting upload
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File size must be less than 5MB");
      return;
    }

    if (!file.type.startsWith('image/')) {
      toast.error("Please select an image file");
      return;
    }

    try {
      const result = await uploadAvatarMutation.mutateAsync(file);
      console.log('Avatar upload result:', result);
      // Success toast is handled by the mutation's onSuccess callback
    } catch (error) {
      console.error("Avatar upload error:", error);
      // Error toast is handled by the mutation's onError callback
    }
  };

  const onSave = async (values: any) => {
    console.log('Profile update values:', values);

    // Prevent multiple simultaneous updates
    if (updateProfileMutation.isPending) {
      console.log('Update already in progress, skipping...');
      return;
    }

    try {
      const updateData = {
        full_name: values.name?.trim() || null,
        country: values.country?.trim() || null,
        course: values.course?.trim() || null,
        institute: values.institute?.trim() || null,
      };
      console.log('Sending update data:', updateData);

      const result = await updateProfileMutation.mutateAsync(updateData);
      console.log('Update result:', result);

      setEditOpen(false);
      toast.success("Profile updated successfully!");
    } catch (error) {
      console.error("Profile update error:", error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      // Handle specific error cases
      if (errorMessage.includes('not authenticated')) {
        toast.error('Please sign in again to update your profile');
      } else if (errorMessage.includes('network')) {
        toast.error('Network error. Please check your connection and try again.');
      } else {
        toast.error(`Failed to update profile: ${errorMessage}`);
      }
    }
  };

  // Loading state
  if (profileLoading) {
    console.log('Profile loading...');
    return (
      <div className="min-h-screen bg-gradient-to-br from-violet-900 via-purple-900 to-indigo-900 flex items-center justify-center pb-20 md:pb-0">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-white mb-4" />
          <p className="text-white">Loading profile...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (profileError || !profile) {
    console.error('Profile error:', profileError);
    console.log('Profile data:', profile);
    return (
      <div className="min-h-screen bg-gradient-to-br from-violet-900 via-purple-900 to-indigo-900 flex items-center justify-center pb-20 md:pb-0">
        <div className="text-white text-center max-w-md mx-auto p-6">
          <p className="text-xl mb-4">Failed to load profile</p>
          {profileError && (
            <div className="mb-4 p-4 bg-red-500/20 rounded-lg">
              <p className="text-sm font-semibold mb-2">Error Details:</p>
              <p className="text-xs text-red-200">{profileError.message}</p>
            </div>
          )}
          <div className="space-y-2">
            <Button onClick={() => window.location.reload()} className="w-full">
              Retry
            </Button>
            <Button
              variant="outline"
              onClick={() => window.location.href = '/login'}
              className="w-full"
            >
              Go to Login
            </Button>
          </div>
        </div>
      </div>
    );
  }



  // Transform friends data to match ProfileFriendsListV2 interface
  const friendStatuses = friends?.map(friend => ({
    id: friend.id,
    full_name: friend.full_name || 'Unknown',
    email: friend.email || '',
    avatar_url: friend.avatar_url || '',
    country: friend.country || '',
    course: friend.course || '',
    institute: friend.institute || '',
    is_online: friend.is_online || false,
    friendship_status: 'accepted' // Since these are confirmed friends
  })) || [];

  // Transform friend requests - ensure avatar_url is always a string
  const transformedFriendRequests = friendRequests?.map(request => ({
    ...request,
    requester: {
      ...request.requester,
      avatar_url: request.requester.avatar_url || '', // Ensure avatar_url is always a string
    }
  })) || [];



  // Handle profile preview
  const handleProfilePreview = (userId: string) => {
    setProfilePreviewUserId(userId);
  };

  // Handle starting conversation with a friend
  const handleStartConversation = (friendId: string) => {
    // Navigate to messages page where users can start conversations
    navigate('/messages');
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-900 via-purple-900 to-indigo-900 relative pb-20 md:pb-0">
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 right-4 sm:right-20 w-48 sm:w-72 h-48 sm:h-72 bg-gradient-to-br from-pink-400/20 to-rose-600/20 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 left-4 sm:left-20 w-48 sm:w-80 h-48 sm:h-80 bg-gradient-to-br from-cyan-400/15 to-blue-600/15 rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 container mx-auto px-2 sm:px-4 py-3 sm:py-6 md:py-8 pb-20 md:pb-8">
          {/* Main Profile Section */}
          <div className="max-w-6xl mx-auto mb-3 sm:mb-6 md:mb-8">
            <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-3 sm:p-6 md:p-8">

              {/* Profile Header - Stacked on mobile, side by side on larger screens */}
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-6 md:gap-8 items-center sm:items-start">

                {/* Profile Photo */}
                <div className="flex-shrink-0">
                  <ProfileAvatar name={profile.full_name || 'User'} image={profile.avatar_url} onPhotoChange={handleAvatarChange} />
                </div>

                {/* User Information */}
                <div className="flex-1 w-full text-center sm:text-left">
                  {/* Name and Email */}
                  <h1 className="text-2xl xs:text-3xl sm:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-pink-300 via-purple-300 to-cyan-300 bg-clip-text text-transparent mb-1.5 sm:mb-3 break-words">
                    {profile.full_name || 'User'}
                  </h1>
                  <p className="text-sm xs:text-base sm:text-xl text-white/80 mb-3 sm:mb-6 break-all">{profile.email}</p>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-1.5 sm:gap-3 justify-center sm:justify-start mb-3 sm:mb-6 md:mb-8">
                    {profile.country && (
                      <span className="px-3 py-1.5 sm:px-4 sm:py-2 bg-gradient-to-r from-pink-500/80 to-rose-500/80 text-white rounded-full text-xs sm:text-sm font-semibold border border-white/20">
                        📍 {profile.country}
                      </span>
                    )}
                    {profile.course && (
                      <span className="px-3 py-1.5 sm:px-4 sm:py-2 bg-gradient-to-r from-cyan-500/80 to-blue-500/80 text-white rounded-full text-xs sm:text-sm font-semibold border border-white/20">
                        📚 {profile.course}
                      </span>
                    )}
                    {profile.institute && (
                      <span className="px-3 py-1.5 sm:px-4 sm:py-2 bg-gradient-to-r from-emerald-500/80 to-teal-500/80 text-white rounded-full text-xs sm:text-sm font-semibold border border-white/20">
                        🏫 {profile.institute}
                      </span>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="w-full grid grid-cols-2 gap-3 sm:flex sm:flex-row sm:gap-4 sm:w-auto">
                    <Button
                      className="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white shadow-xl transition-all duration-300 rounded-full px-4 sm:px-8 py-2.5 sm:py-3 text-sm sm:text-base font-semibold w-full sm:w-auto"
                      onClick={() => navigate('/discover')}
                    >
                      <UserPlus size={20} className="mr-2" />
                      Discover
                    </Button>
                    <Button
                      className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white shadow-xl transition-all duration-300 rounded-full px-4 sm:px-8 py-2.5 sm:py-3 text-sm sm:text-base font-semibold w-full sm:w-auto"
                      onClick={() => navigate('/study-groups')}
                    >
                      <Users size={20} className="mr-2" />
                      Study Groups
                    </Button>
                  </div>
                </div>
              </div>

              {/* Edit and Logout Buttons */}
              <div className="absolute top-3 right-3 sm:top-6 sm:right-6 flex gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => signOutMutation.mutate()}
                  className="bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white border border-white/30 rounded-full shadow-2xl hover:shadow-white/25 transition-all duration-300 hover:scale-110"
                  title="Sign Out"
                >
                  <LogOut className="w-4 h-4 sm:w-5 sm:h-5" />
                </Button>
                <ProfileEditDialog
                  user={{
                    name: profile.full_name || '',
                    email: profile.email,
                    country: profile.country || '',
                    course: profile.course || '',
                    institute: profile.institute || ''
                  }}
                  open={editOpen}
                  setOpen={setEditOpen}
                  onSave={onSave}
                  courses={courses?.map((c: any) => c.name) || []}
                  institutes={institutes?.map((i: any) => i.name) || []}
                  countries={countries?.map((c: any) => c.name) || []}
                />
              </div>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="max-w-6xl mx-auto mb-3 sm:mb-6 md:mb-8">
            <div className="bg-white/10 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-1.5 sm:p-3 overflow-x-auto">
              <div className="flex w-max max-w-full mx-auto gap-1.5 sm:gap-3 md:gap-4">
                {TABS.map(({ label, icon: Icon }) => (
                  <button
                    key={label}
                    className={`py-2 sm:py-3 px-2.5 sm:px-6 text-xs xs:text-sm sm:text-base font-semibold transition-all duration-300 rounded-xl flex items-center gap-1 sm:gap-2 relative ${
                      activeTab === label
                        ? "bg-gradient-to-r from-pink-500 to-violet-500 text-white shadow-lg"
                        : "text-white/70 hover:text-white hover:bg-white/10"
                    }`}
                    onClick={() => setActiveTab(label)}
                  >
                    <Icon className="w-4 h-4 sm:w-5 sm:h-5" />
                    <span className="hidden sm:inline">{label}</span>

                    {label === "Requests" && transformedFriendRequests && transformedFriendRequests.length > 0 && (
                      <Badge
                        variant="destructive"
                        className="absolute -top-1.5 -right-1.5 h-4 w-4 sm:h-5 sm:w-5 flex items-center justify-center p-0 text-[10px] sm:text-xs"
                      >
                        {transformedFriendRequests.length}
                      </Badge>
                    )}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Content Area */}
          <div className="max-w-6xl mx-auto px-0 sm:px-0">

            {activeTab === "Friends" && (
              <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-white">Friends</h2>
                  <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                    {friendStatuses.length} {friendStatuses.length === 1 ? 'friend' : 'friends'}
                  </Badge>
                </div>

                <div className="space-y-3">
                  {friendStatuses.length > 0 ? (
                    friendStatuses.map((friend) => (
                      <div
                        key={friend.id}
                        className="flex items-center gap-4 p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-all duration-200 border border-white/10 hover:border-white/20"
                      >
                        {/* Avatar */}
                        <Avatar
                          className="w-14 h-14 cursor-pointer hover:scale-105 transition-transform duration-200 border-2 border-white/20 shadow-lg"
                          onClick={() => handleProfilePreview(friend.id)}
                        >
                          <AvatarImage src={friend.avatar_url} alt={friend.full_name} />
                          <AvatarFallback className="bg-gradient-to-r from-pink-500 to-violet-500 text-white font-semibold">
                            {friend.full_name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")
                              .slice(0, 2)
                              .toUpperCase()}
                          </AvatarFallback>
                        </Avatar>

                        {/* Friend Info */}
                        <div
                          className="flex-1 cursor-pointer min-w-0"
                          onClick={() => handleProfilePreview(friend.id)}
                        >
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-semibold text-white text-lg truncate">{friend.full_name}</h3>
                            {friend.is_online && (
                              <div className="w-3 h-3 bg-green-500 rounded-full shadow-lg animate-pulse" title="Online"></div>
                            )}
                          </div>

                          {/* Tags for Country and Course */}
                          <div className="flex flex-wrap gap-2 mt-2">
                            {friend.country && (
                              <span className="inline-flex items-center px-2.5 py-1 bg-violet-500/20 text-violet-200 rounded-full text-xs font-medium border border-violet-400/30">
                                📍 {friend.country}
                              </span>
                            )}
                            {friend.course && (
                              <span className="inline-flex items-center px-2.5 py-1 bg-emerald-500/20 text-emerald-200 rounded-full text-xs font-medium border border-emerald-400/30">
                                📚 {friend.course.length > 20 ? `${friend.course.substring(0, 20)}...` : friend.course}
                              </span>
                            )}
                          </div>
                        </div>

                        {/* Action Button */}
                        <div className="flex-shrink-0">
                          <Button
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleStartConversation(friend.id);
                            }}
                            className="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 px-4 py-2"
                            title="Send Message"
                          >
                            <MessageCircle size={16} className="mr-2" />
                            <span className="hidden sm:inline">Message</span>
                            <span className="sm:hidden">Chat</span>
                          </Button>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-12">
                      <div className="w-20 h-20 bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-white/20">
                        <span className="text-3xl">👋</span>
                      </div>
                      <p className="text-white/80 text-lg font-medium mb-2">No friends yet</p>
                      <p className="text-white/60 text-sm mb-4">Start by discovering new people!</p>
                      <Button
                        onClick={() => navigate('/discover')}
                        className="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                      >
                        <UserPlus size={16} className="mr-2" />
                        Discover People
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            )}
            {activeTab === "Requests" && (
              <ProfileFriendRequests friendRequests={transformedFriendRequests} />
            )}
          </div>
        </div>



        {/* Profile Preview Dialog */}
        <ProfilePreviewDialog
          userId={profilePreviewUserId}
          open={!!profilePreviewUserId}
          onOpenChange={(open) => !open && setProfilePreviewUserId(null)}
        />
    </div>
  );
};

export default Profile;
