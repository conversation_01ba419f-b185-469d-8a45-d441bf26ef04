
import React, { useState, useEffect, useRef } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Send,
  MessageCircle,
  Search,
  Plus,
  ArrowLeft,
  MoreVertical
} from 'lucide-react';
import {
  useConversations,
  useMessages,
  useSendMessage,
  useMarkAsRead,
  type Conversation,
  type Message
} from '@/hooks/useMessaging';
import { useProfile } from '@/hooks/useProfile';
import { formatDistanceToNow } from 'date-fns';
import AuthGuard from '@/components/AuthGuard';
import { NewMessageDialog } from '@/components/messaging/NewMessageDialog';
import { FileMessage } from '@/components/messages/FileMessage';
import { toast } from 'sonner';

const Messages = () => {
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const { data: profile } = useProfile();
  const { data: conversations = [], isLoading: conversationsLoading } = useConversations();
  const { data: messages = [], isLoading: messagesLoading } = useMessages(selectedConversation || '');
  const sendMessageMutation = useSendMessage();
  const markAsReadMutation = useMarkAsRead();

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Mark conversation as read when selected
  useEffect(() => {
    if (selectedConversation) {
      markAsReadMutation.mutate(selectedConversation);
    }
  }, [selectedConversation]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !selectedConversation) return;

    try {
      await sendMessageMutation.mutateAsync({
        conversationId: selectedConversation,
        content: newMessage.trim(),
      });
      setNewMessage('');
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const getOtherParticipant = (conversation: Conversation) => {
    return conversation.participants?.find(p => p.user_id !== profile?.id)?.user;
  };

  const getConversationName = (conversation: Conversation) => {
    if (conversation.name) return conversation.name;
    const otherUser = getOtherParticipant(conversation);
    return otherUser?.full_name || 'Unknown User';
  };

  const getConversationAvatar = (conversation: Conversation) => {
    if (conversation.avatar_url) return conversation.avatar_url;
    const otherUser = getOtherParticipant(conversation);
    return otherUser?.avatar_url;
  };

  const filteredConversations = conversations.filter(conv =>
    getConversationName(conv).toLowerCase().includes(searchQuery.toLowerCase())
  );

  const selectedConv = conversations.find(c => c.id === selectedConversation);

  return (
    <AuthGuard>
      <div className="h-screen bg-gradient-to-br from-white via-[#ecd7fa] to-[#b475ea] flex overflow-hidden pb-20 md:pb-0">
        {/* Conversations List */}
        <div className={`w-full md:w-80 bg-white/95 backdrop-blur-sm border-r border-white/20 flex flex-col ${
          selectedConversation ? 'hidden md:flex' : 'flex'
        }`}>
          {/* Header */}
          <div className="p-4 border-b border-white/20">
            <div className="flex items-center justify-between mb-4">
              <h1 className="text-xl font-semibold text-gray-900">Messages</h1>
              <NewMessageDialog onConversationCreated={setSelectedConversation}>
                <Button size="sm" variant="outline" className="border-white/30 hover:bg-white/20">
                  <Plus className="w-4 h-4" />
                </Button>
              </NewMessageDialog>
            </div>

            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search conversations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 border-white/30 bg-white/80 backdrop-blur-sm"
              />
            </div>
          </div>

          {/* Conversations */}
          <ScrollArea className="flex-1">
            {conversationsLoading ? (
              <div className="p-4 text-center text-gray-500">Loading conversations...</div>
            ) : filteredConversations.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                {searchQuery ? 'No conversations found' : 'No conversations yet'}
              </div>
            ) : (
              <div className="divide-y">
                {filteredConversations.map((conversation) => {
                  const otherUser = getOtherParticipant(conversation);
                  const isUnread = conversation.participants?.some(p =>
                    p.user_id === profile?.id &&
                    new Date(conversation.last_message_at) > new Date(p.last_read_at)
                  );

                  return (
                    <button
                      key={conversation.id}
                      onClick={() => setSelectedConversation(conversation.id)}
                      className={`w-full p-4 text-left hover:bg-white/20 transition-colors ${
                        selectedConversation === conversation.id ? 'bg-violet-100/50 border-r-2 border-violet-500' : ''
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <Avatar className="w-10 h-10">
                          <AvatarImage src={getConversationAvatar(conversation)} />
                          <AvatarFallback>
                            {getConversationName(conversation).charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <p className={`text-sm truncate ${isUnread ? 'font-semibold' : 'font-medium'}`}>
                              {getConversationName(conversation)}
                            </p>
                            {conversation.last_message_at && (
                              <span className="text-xs text-gray-500">
                                {formatDistanceToNow(new Date(conversation.last_message_at), { addSuffix: true })}
                              </span>
                            )}
                          </div>

                          {conversation.last_message && (
                            <p className={`text-sm text-gray-600 truncate ${isUnread ? 'font-medium' : ''}`}>
                              {conversation.last_message.content || '📎 File'}
                            </p>
                          )}
                        </div>

                        {isUnread && (
                          <Badge variant="default" className="w-2 h-2 p-0 bg-blue-500" />
                        )}
                      </div>
                    </button>
                  );
                })}
              </div>
            )}
          </ScrollArea>
        </div>

        {/* Chat Area */}
        <div className={`flex-1 flex flex-col min-h-0 ${
          selectedConversation ? 'flex' : 'hidden md:flex'
        }`}>
          {selectedConversation && selectedConv ? (
            <>
              {/* Chat Header */}
              <div className="p-4 border-b border-white/20 bg-white/95 backdrop-blur-sm flex items-center space-x-3 flex-shrink-0">
                <Button
                  variant="ghost"
                  size="sm"
                  className="md:hidden"
                  onClick={() => setSelectedConversation(null)}
                >
                  <ArrowLeft className="w-4 h-4" />
                </Button>

                <Avatar className="w-8 h-8">
                  <AvatarImage src={getConversationAvatar(selectedConv)} />
                  <AvatarFallback>
                    {getConversationName(selectedConv).charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1">
                  <h2 className="font-semibold">{getConversationName(selectedConv)}</h2>
                  <p className="text-sm text-green-500">● Online</p>
                </div>

                <Button variant="ghost" size="sm">
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </div>

              {/* Messages */}
              <div className="flex-1 overflow-hidden bg-white/30 backdrop-blur-sm">
                <ScrollArea className="h-full p-4">
                  {messagesLoading ? (
                    <div className="text-center text-gray-500">Loading messages...</div>
                  ) : messages.length === 0 ? (
                    <div className="text-center text-gray-500">No messages yet. Start the conversation!</div>
                  ) : (
                    <div className="space-y-4">
                      {messages.map((message) => {
                        // Handle file messages
                        if (message.message_type === 'file' && message.file_url) {
                          return (
                            <FileMessage
                              key={message.id}
                              message={{
                                id: message.id,
                                content: message.content || '',
                                file_url: message.file_url,
                                file_name: message.file_name || 'Unknown file',
                                sender_name: message.sender_name || 'Unknown',
                                sender_avatar: message.sender_avatar || '',
                                created_at: message.created_at,
                                is_own: message.is_own,
                              }}
                            />
                          );
                        }

                        // Handle regular text messages
                        const isOwn = message.sender_id === profile?.id;
                        return (
                          <div
                            key={message.id}
                            className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}
                          >
                            <div className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-sm ${
                              isOwn
                                ? 'bg-gradient-to-r from-violet-500 to-purple-600 text-white'
                                : 'bg-white/95 backdrop-blur-sm text-gray-900 border border-white/20'
                            }`}>
                              <p className="text-sm">{message.content}</p>
                              <p className={`text-xs mt-1 ${
                                isOwn ? 'text-violet-100' : 'text-gray-500'
                              }`}>
                                {formatDistanceToNow(new Date(message.created_at), { addSuffix: true })}
                              </p>
                            </div>
                          </div>
                        );
                      })}
                      <div ref={messagesEndRef} />
                    </div>
                  )}
                </ScrollArea>
              </div>

              {/* Message Input - Always visible when conversation is selected */}
              <div className="p-4 border-t border-white/20 bg-white/95 backdrop-blur-sm flex-shrink-0 shadow-lg mb-20 md:mb-0">
                <form onSubmit={handleSendMessage} className="w-full">
                  <div className="flex space-x-3">
                    <Input
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      placeholder="Type a message..."
                      className="flex-1 h-12 text-base border-2 border-white/30 focus:border-violet-500 rounded-xl bg-white/80 backdrop-blur-sm"
                      disabled={sendMessageMutation.isPending}
                      autoComplete="off"
                    />
                    <Button
                      type="submit"
                      disabled={!newMessage.trim() || sendMessageMutation.isPending}
                      className="px-6 h-12 bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700 text-white rounded-xl shadow-lg"
                    >
                      {sendMessageMutation.isPending ? (
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      ) : (
                        <Send className="w-5 h-5" />
                      )}
                    </Button>
                  </div>
                </form>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center bg-white/30 backdrop-blur-sm">
              <div className="text-center">
                <MessageCircle className="w-16 h-16 text-violet-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Select a conversation</h3>
                <p className="text-gray-600">Choose a conversation from the sidebar to start messaging</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </AuthGuard>
  );
};

export default Messages;
