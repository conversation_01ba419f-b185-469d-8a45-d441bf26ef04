
import React from "react";
import SimpleHeader from "@/components/SimpleHeader";
import { Users, Heart, Star, BookOpenText } from "lucide-react";

const coreValues = [
  {
    icon: <Users className="w-9 h-9 text-violet-600 mb-2" />,
    title: "Community",
    desc: "We bring learners together to collaborate and share knowledge."
  },
  {
    icon: <BookOpenText className="w-9 h-9 text-violet-600 mb-2" />,
    title: "Growth",
    desc: "Every feature is designed to support personal and academic growth."
  },
  {
    icon: <Star className="w-9 h-9 text-violet-600 mb-2" />,
    title: "Excellence",
    desc: "We believe in quality, support, and always raising the bar for our users."
  },
  {
    icon: <Heart className="w-9 h-9 text-violet-600 mb-2" />,
    title: "Empathy",
    desc: "We listen first and design with care—every learner matters."
  },
];

export default function AboutUs() {
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-white via-[#ecd7fa] to-[#b475ea]">
      <SimpleHeader />
      <main className="flex-1 flex flex-col items-center px-4 py-10">
        <div className="max-w-2xl w-full bg-white rounded-xl shadow-lg p-8 mt-10">
          <h1 className="text-3xl font-bold text-violet-700 mb-4 text-center">
            About Us
          </h1>
          <p className="text-slate-700 mb-3 text-center">
            <strong>StudyHub</strong> is built by passionate educators and technologists to make learning easier, smarter, and more collaborative for students everywhere.
          </p>
          <p className="text-slate-600 text-center mb-2">
            We believe in the power of community and technology to help students succeed in their studies and beyond.
          </p>
          <div className="py-6 text-center">
            <h2 className="text-lg font-semibold text-violet-700 mb-2">Our Mission</h2>
            <p className="text-slate-700">
              To empower every student with tools, guidance, and a supportive community so they can reach their full potential—no matter where they're starting from.
            </p>
          </div>
        </div>

        {/* Core Values */}
        <section className="max-w-4xl w-full bg-white rounded-xl shadow-lg p-8 mt-8">
          <h2 className="text-xl font-bold text-violet-700 mb-6 text-center">Our Core Values</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-5">
            {coreValues.map((val) => (
              <div
                key={val.title}
                className="flex flex-col items-center rounded-lg bg-violet-50 p-5 text-center shadow hover:shadow-md transition"
              >
                {val.icon}
                <h4 className="font-bold text-violet-600">{val.title}</h4>
                <p className="text-xs text-slate-700">{val.desc}</p>
              </div>
            ))}
          </div>
        </section>
      </main>
      <footer className="p-4 text-xs text-slate-400 text-center mt-auto">
        &copy; {new Date().getFullYear()} StudyHub. All rights reserved.
      </footer>
    </div>
  );
}
