import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Download, ExternalLink } from 'lucide-react';
import DocumentViewer from '@/components/DocumentViewer';
import { useNotes } from '@/hooks/useNotes';
import AuthGuard from '@/components/AuthGuard';
import { toast } from 'sonner';

const DocumentViewerPage: React.FC = () => {
  const { noteId } = useParams<{ noteId: string }>();
  const navigate = useNavigate();
  const [note, setNote] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  // This is a simplified approach - in a real app you'd want a specific hook for getting a single note
  const { data: allNotes = [] } = useNotes(''); // This won't work as expected, we need a different approach

  useEffect(() => {
    // For now, we'll create a simple document viewer page
    // In a production app, you'd want to fetch the specific note by ID
    if (noteId) {
      // Simulate loading a note
      setLoading(false);
    }
  }, [noteId]);

  const handleBack = () => {
    navigate('/sort-notes');
  };

  if (loading) {
    return (
      <AuthGuard>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading document...</p>
          </div>
        </div>
      </AuthGuard>
    );
  }

  if (!note) {
    return (
      <AuthGuard>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">Document Not Found</h1>
            <p className="text-gray-600 mb-6">The document you're looking for doesn't exist or you don't have permission to view it.</p>
            <Button onClick={handleBack}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Notes
            </Button>
          </div>
        </div>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between max-w-7xl mx-auto">
            <div className="flex items-center gap-4">
              <Button variant="ghost" onClick={handleBack}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-xl font-semibold text-gray-800">{note.title}</h1>
                <p className="text-sm text-gray-600">Document Viewer</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <ExternalLink className="w-4 h-4 mr-2" />
                Open in New Tab
              </Button>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Download
              </Button>
            </div>
          </div>
        </div>

        {/* Document Viewer */}
        <div className="h-[calc(100vh-80px)]">
          <DocumentViewer
            open={true}
            onOpenChange={() => {}} // Don't allow closing in this context
            fileUrl={note.file_url}
            fileName={note.title}
            title={note.title}
          />
        </div>
      </div>
    </AuthGuard>
  );
};

export default DocumentViewerPage;
