
import React, { useState, useRef, useCallback } from "react";
import PageHeader from "@/components/PageHeader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  Upload,
  Image as ImageIcon,
  RotateCw,
  ZoomIn,
  ZoomOut,
  Crop,
  FileText,
  Download,
  Save,
  Loader2,
  Eye,
  Edit3,
  FolderOpen,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import ImageUploader from "@/components/image-to-notes/ImageUploader";
import ImageEditor from "@/components/image-to-notes/ImageEditor";
import OCRProcessor from "@/components/image-to-notes/OCRProcessor";
import TextEditor from "@/components/image-to-notes/TextEditor";
import SaveToFolderModal from "@/components/image-to-notes/SaveToFolderModal";
import AuthGuard from "@/components/AuthGuard";
import { toast } from "sonner";

interface ProcessedImage {
  file: File;
  url: string;
  editedUrl?: string;
  extractedText?: string;
  editedText?: string;
}

const ImageToNotes = () => {
  const [currentStep, setCurrentStep] = useState<'upload' | 'edit' | 'ocr' | 'text' | 'save'>('upload');
  const [processedImage, setProcessedImage] = useState<ProcessedImage | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showSaveModal, setShowSaveModal] = useState(false);

  const handleImageUpload = useCallback((file: File) => {
    const url = URL.createObjectURL(file);
    setProcessedImage({
      file,
      url,
    });
    setCurrentStep('edit');
    toast.success('Image uploaded successfully!');
  }, []);

  const handleImageEdit = useCallback((editedImageUrl: string) => {
    if (processedImage) {
      setProcessedImage({
        ...processedImage,
        editedUrl: editedImageUrl,
      });
      setCurrentStep('ocr');
    }
  }, [processedImage]);

  const handleOCRComplete = useCallback((extractedText: string) => {
    if (processedImage) {
      setProcessedImage({
        ...processedImage,
        extractedText,
        editedText: extractedText, // Initialize edited text with extracted text
      });
      setCurrentStep('text');
    }
  }, [processedImage]);

  const handleTextEdit = useCallback((editedText: string) => {
    if (processedImage) {
      setProcessedImage({
        ...processedImage,
        editedText,
      });
    }
  }, [processedImage]);

  const handleSaveToFolder = useCallback(() => {
    setShowSaveModal(true);
  }, []);

  const handleReset = useCallback(() => {
    if (processedImage) {
      URL.revokeObjectURL(processedImage.url);
      if (processedImage.editedUrl) {
        URL.revokeObjectURL(processedImage.editedUrl);
      }
    }
    setProcessedImage(null);
    setCurrentStep('upload');
    setIsProcessing(false);
  }, [processedImage]);

  const getStepStatus = (step: string) => {
    const steps = ['upload', 'edit', 'ocr', 'text', 'save'];
    const currentIndex = steps.indexOf(currentStep);
    const stepIndex = steps.indexOf(step);

    if (stepIndex < currentIndex) return 'completed';
    if (stepIndex === currentIndex) return 'current';
    return 'pending';
  };

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gradient-to-br from-white via-[#ecd7fa] to-[#b475ea] pb-20 md:pb-0">
        <div className="max-w-7xl mx-auto px-4 py-4 md:py-8">
          {/* Mobile-friendly header */}
          <div className="mb-6">
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
              Image to Notes
            </h1>
            <p className="text-gray-600 text-sm md:text-base mb-4">
              Convert images to editable text notes using OCR technology
            </p>
            {processedImage && (
              <Button
                onClick={handleReset}
                variant="outline"
                className="mb-4"
              >
                <Upload className="w-4 h-4 mr-2" />
                Start Over
              </Button>
            )}
          </div>

          {/* Mobile-responsive Progress Steps */}
          <Card className="mb-6 md:mb-8 p-4 md:p-6 bg-white/95 backdrop-blur-sm">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              {[
                { key: 'upload', label: 'Upload', fullLabel: 'Upload Image', icon: Upload },
                { key: 'edit', label: 'Edit', fullLabel: 'Edit & Crop', icon: Edit3 },
                { key: 'ocr', label: 'Extract', fullLabel: 'Extract Text', icon: FileText },
                { key: 'text', label: 'Edit Text', fullLabel: 'Edit Text', icon: Edit3 },
                { key: 'save', label: 'Save', fullLabel: 'Save Notes', icon: Save },
              ].map((step, index) => {
                const status = getStepStatus(step.key);
                const Icon = step.icon;

                return (
                  <div key={step.key} className="flex items-center">
                    <div className={`flex items-center gap-2 px-2 md:px-3 py-2 rounded-lg transition-colors ${
                      status === 'completed' ? 'bg-green-100 text-green-700' :
                      status === 'current' ? 'bg-blue-100 text-blue-700' :
                      'bg-gray-100 text-gray-500'
                    }`}>
                      {status === 'completed' ? (
                        <CheckCircle className="w-4 h-4" />
                      ) : (
                        <Icon className="w-4 h-4" />
                      )}
                      <span className="text-xs md:text-sm font-medium">
                        <span className="md:hidden">{step.label}</span>
                        <span className="hidden md:inline">{step.fullLabel}</span>
                      </span>
                    </div>
                    {index < 4 && (
                      <div className={`w-4 md:w-8 h-0.5 mx-1 md:mx-2 ${
                        getStepStatus(['upload', 'edit', 'ocr', 'text', 'save'][index + 1]) !== 'pending'
                          ? 'bg-green-300' : 'bg-gray-300'
                      }`} />
                    )}
                  </div>
                );
              })}
            </div>
          </Card>

          {/* Main Content */}
          <div className="flex flex-col lg:grid lg:grid-cols-3 gap-6 lg:gap-8">
            {/* Main Panel - Current Step */}
            <div className="lg:col-span-2 order-2 lg:order-1">
              {currentStep === 'upload' && (
                <ImageUploader onImageUpload={handleImageUpload} />
              )}

              {currentStep === 'edit' && processedImage && (
                <ImageEditor
                  imageUrl={processedImage.url}
                  onEditComplete={handleImageEdit}
                  onNext={() => setCurrentStep('ocr')}
                />
              )}

              {currentStep === 'ocr' && processedImage && (
                <OCRProcessor
                  imageUrl={processedImage.editedUrl || processedImage.url}
                  onOCRComplete={handleOCRComplete}
                  isProcessing={isProcessing}
                  setIsProcessing={setIsProcessing}
                />
              )}

              {currentStep === 'text' && processedImage && (
                <TextEditor
                  initialText={processedImage.extractedText || ''}
                  onTextChange={handleTextEdit}
                  onSave={handleSaveToFolder}
                />
              )}
            </div>

            {/* Side Panel - Preview & Actions (shows first on mobile) */}
            <div className="space-y-4 lg:space-y-6 order-1 lg:order-2">
              {/* Mobile-responsive panels */}
              {processedImage && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-4 lg:gap-6">
                  {/* Image Preview */}
                  <Card className="p-4 bg-white/95 backdrop-blur-sm">
                    <h3 className="text-base lg:text-lg font-semibold mb-3 flex items-center gap-2">
                      <ImageIcon className="w-4 h-4 lg:w-5 lg:h-5" />
                      Image Preview
                    </h3>
                    <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                      <img
                        src={processedImage.editedUrl || processedImage.url}
                        alt="Uploaded image"
                        className="w-full h-full object-contain"
                      />
                    </div>
                    <div className="mt-3 text-xs lg:text-sm text-gray-600">
                      <p className="truncate">File: {processedImage.file.name}</p>
                      <p>Size: {(processedImage.file.size / 1024 / 1024).toFixed(2)} MB</p>
                    </div>
                  </Card>

                  {/* Text Preview */}
                  {processedImage.extractedText && (
                    <Card className="p-4 bg-white/95 backdrop-blur-sm">
                      <h3 className="text-base lg:text-lg font-semibold mb-3 flex items-center gap-2">
                        <FileText className="w-4 h-4 lg:w-5 lg:h-5" />
                        Extracted Text
                      </h3>
                      <div className="max-h-48 lg:max-h-64 overflow-y-auto bg-gray-50 p-3 rounded-lg text-xs lg:text-sm">
                        <p className="whitespace-pre-wrap">
                          {processedImage.editedText || processedImage.extractedText}
                        </p>
                      </div>
                      <div className="mt-3 text-xs lg:text-sm text-gray-600">
                        <p>Characters: {(processedImage.editedText || processedImage.extractedText).length}</p>
                        <p>Words: {(processedImage.editedText || processedImage.extractedText).split(/\s+/).filter(w => w.length > 0).length}</p>
                      </div>
                    </Card>
                  )}
                </div>
              )}

              {/* Quick Actions */}
              {processedImage && (
                <Card className="p-4 bg-white/95 backdrop-blur-sm">
                  <h3 className="text-base lg:text-lg font-semibold mb-3">Quick Actions</h3>
                  <div className="grid grid-cols-2 lg:grid-cols-1 gap-2 lg:gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="justify-start text-xs lg:text-sm"
                      onClick={() => setCurrentStep('edit')}
                      disabled={currentStep === 'upload'}
                    >
                      <Edit3 className="w-3 h-3 lg:w-4 lg:h-4 mr-1 lg:mr-2" />
                      <span className="hidden sm:inline">Re-edit Image</span>
                      <span className="sm:hidden">Edit</span>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="justify-start text-xs lg:text-sm"
                      onClick={() => setCurrentStep('ocr')}
                      disabled={currentStep === 'upload' || currentStep === 'edit'}
                    >
                      <FileText className="w-3 h-3 lg:w-4 lg:h-4 mr-1 lg:mr-2" />
                      <span className="hidden sm:inline">Re-extract Text</span>
                      <span className="sm:hidden">Extract</span>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="justify-start text-xs lg:text-sm"
                      onClick={handleSaveToFolder}
                      disabled={!processedImage.extractedText}
                    >
                      <FolderOpen className="w-3 h-3 lg:w-4 lg:h-4 mr-1 lg:mr-2" />
                      <span className="hidden sm:inline">Save to Folder</span>
                      <span className="sm:hidden">Save</span>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="justify-start text-xs lg:text-sm"
                      onClick={handleReset}
                    >
                      <Upload className="w-3 h-3 lg:w-4 lg:h-4 mr-1 lg:mr-2" />
                      <span className="hidden sm:inline">Start Over</span>
                      <span className="sm:hidden">Reset</span>
                    </Button>
                  </div>
                </Card>
              )}
            </div>
          </div>

          {/* Save to Folder Modal */}
          {showSaveModal && processedImage && (
            <SaveToFolderModal
              open={showSaveModal}
              onOpenChange={setShowSaveModal}
              extractedText={processedImage.editedText || processedImage.extractedText || ''}
              imageFile={processedImage.file}
              onSaveComplete={handleReset}
            />
          )}
        </div>
      </div>
    </AuthGuard>
  );
};

export default ImageToNotes;
