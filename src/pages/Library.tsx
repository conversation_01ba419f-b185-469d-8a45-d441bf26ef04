
import React, { useState } from "react";
import PageHeader from "@/components/PageHeader";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

const Library = () => {
  const [search, setSearch] = useState("");
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-white via-[#ecd7fa] to-[#b475ea]">
      <div className="max-w-4xl mx-auto px-4 py-12 flex-1 flex flex-col">
        <PageHeader
          title="Library"
          description="All your uploaded and saved files."
          buttonLabel="Go to Uploads"
          onButtonClick={() => alert("Feature coming soon!")}
        >
          <form className="flex gap-3">
            <Input
              value={search}
              onChange={e => setSearch(e.target.value)}
              placeholder="Search entire library..."
              className="max-w-xs"
            />
            <Button variant="outline" type="button" className="flex-shrink-0">
              Search
            </Button>
          </form>
        </PageHeader>

        <div className="p-8 bg-white rounded-xl shadow">
          <h2 className="font-semibold text-violet-700 mb-2">Your library is empty.</h2>
          <p className="text-slate-600 text-sm">Feature coming soon!</p>
        </div>
      </div>
    </div>
  );
};
export default Library;
