import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { UserPlus, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { useDiscoverUsers, useSendFriendRequest, useSentFriendshipRequests, DiscoverUser } from "@/hooks/useFriends";
import { useProfile } from "@/hooks/useProfile";
import ProfilePreviewDialog from "@/components/profile/ProfilePreviewDialog";
import { toast } from "sonner";

const unique = (arr: (string | null | undefined)[]) =>
  Array.from(new Set(arr.filter(Boolean))) as string[];

const Discover: React.FC = () => {
  const [filter, setFilter] = useState("");
  const [selectedCountry, setSelectedCountry] = useState<string>("");
  const [selectedCourse, setSelectedCourse] = useState<string>("");
  const [selectedInstitute, setSelectedInstitute] = useState<string>("");
  const [profilePreviewUserId, setProfilePreviewUserId] = useState<string | null>(null);

  // Backend hooks
  const { data: users, isLoading, error } = useDiscoverUsers();
  const { data: sentRequests } = useSentFriendshipRequests();
  const { data: currentUserProfile } = useProfile();
  const sendFriendRequestMutation = useSendFriendRequest();


  // Extract unique values for filters
  const countries = unique(users?.map(u => u.country) || []);
  const courses = unique(users?.map(u => u.course) || []);
  const institutes = unique(users?.map(u => u.institute) || []);

  // Smart Matching Algorithm
  const calculateMatchScore = (user: DiscoverUser): number => {
    if (!currentUserProfile) return 0;

    let score = 0;

    // Perfect matches get highest priority
    if (user.course === currentUserProfile.course && user.institute === currentUserProfile.institute) {
      score += 100; // Same course AND same university - perfect match
    } else if (user.course === currentUserProfile.course) {
      score += 75;  // Same course, different university
    } else if (user.institute === currentUserProfile.institute) {
      score += 60;  // Same university, different course
    }

    // Country match adds bonus points
    if (user.country === currentUserProfile.country) {
      score += 25;  // Same country
    }

    // Add small random factor to prevent identical scores from always having same order
    score += Math.random() * 5;

    return score;
  };

  // Sort users by match score (highest first), then apply filters
  const sortedUsers = users?.map(user => ({
    ...user,
    matchScore: calculateMatchScore(user)
  })).sort((a, b) => b.matchScore - a.matchScore) || [];

  // Filter users based on search criteria
  const filteredUsers = sortedUsers.filter((u) => {
    const matchesText =
      u.full_name?.toLowerCase().includes(filter.toLowerCase()) ||
      u.email?.toLowerCase().includes(filter.toLowerCase());

    const matchesCountry =
      !selectedCountry || u.country === selectedCountry;
    const matchesCourse =
      !selectedCourse || u.course === selectedCourse;
    const matchesInstitute =
      !selectedInstitute || u.institute === selectedInstitute;
    return matchesText && matchesCountry && matchesCourse && matchesInstitute;
  });

  // Check if request was sent to a user
  const isRequestSent = (userId: string) => {
    return sentRequests?.some(request => request.addressee_id === userId) || false;
  };

  // Handle sending friend request
  const handleSendFriendRequest = async (userId: string, userName: string) => {
    try {
      await sendFriendRequestMutation.mutateAsync(userId);
      toast.success(`Friend request sent to ${userName}!`);
    } catch (error) {
      toast.error("Failed to send friend request");
    }
  };

  // Handle profile preview
  const handleProfilePreview = (userId: string) => {
    setProfilePreviewUserId(userId);
  };



  // Loading state
  if (isLoading) {
    return (
      <div className="w-full flex flex-col items-center min-h-[70vh] px-3 sm:px-4 md:px-6 py-4 sm:py-6 pb-20 md:pb-6 bg-gradient-to-b from-[#f0f2f5] to-white">
        <div className="flex flex-col items-center justify-center h-64 text-center">
          <Loader2 className="w-8 h-8 sm:w-10 sm:h-10 animate-spin text-violet-600 mb-3" />
          <span className="text-sm sm:text-base text-gray-600">Loading users...</span>
          <span className="text-xs text-gray-500 mt-1">Finding study buddies for you</span>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="w-full flex flex-col items-center min-h-[70vh] px-3 sm:px-4 md:px-6 py-4 sm:py-6 pb-20 md:pb-6 bg-gradient-to-b from-[#f0f2f5] to-white">
        <Card className="p-6 sm:p-8 text-center text-red-600 max-w-md">
          <div className="text-base sm:text-lg font-medium mb-2">Oops! Something went wrong</div>
          <div className="text-sm text-red-500">Failed to load users. Please try again later.</div>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col items-center min-h-[70vh] px-2 sm:px-4 md:px-6 py-3 sm:py-4 md:py-6 pb-16 sm:pb-20 md:pb-6 bg-gradient-to-b from-[#f0f2f5] to-white">
      {/* Page Header */}
      <div className="w-full max-w-4xl mb-4 sm:mb-6 text-center px-2">
        <h1 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-800 mb-2">Discover Study Buddies</h1>
        <p className="text-xs sm:text-sm md:text-base text-gray-600">Connect with fellow students and expand your study network</p>
      </div>

      {/* Search and Filters Section */}
      <div className="w-full max-w-4xl mb-4 sm:mb-6 px-2">
        {/* Search Input */}
        <div className="mb-3 sm:mb-4">
          <Input
            placeholder="Search by name or email..."
            value={filter}
            onChange={e => setFilter(e.target.value)}
            className="w-full text-sm h-11 px-4"
          />
        </div>

        {/* Filter Dropdowns */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-4">
          <div className="flex flex-col gap-1">
            <label className="text-xs text-muted-foreground pl-1 font-medium">Country</label>
            <Select
              value={selectedCountry || "all"}
              onValueChange={v => setSelectedCountry(v === "all" ? "" : v)}
            >
              <SelectTrigger className="w-full h-10">
                <SelectValue placeholder="All Countries" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Countries</SelectItem>
                {countries.map((country) => (
                  <SelectItem value={country} key={country}>{country}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-col gap-1">
            <label className="text-xs text-muted-foreground pl-1 font-medium">Course</label>
            <Select
              value={selectedCourse || "all"}
              onValueChange={v => setSelectedCourse(v === "all" ? "" : v)}
            >
              <SelectTrigger className="w-full h-10">
                <SelectValue placeholder="All Courses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Courses</SelectItem>
                {courses.map((course) => (
                  <SelectItem value={course} key={course}>{course}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-col gap-1">
            <label className="text-xs text-muted-foreground pl-1 font-medium">Institute</label>
            <Select
              value={selectedInstitute || "all"}
              onValueChange={v => setSelectedInstitute(v === "all" ? "" : v)}
            >
              <SelectTrigger className="w-full h-10">
                <SelectValue placeholder="All Institutes" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Institutes</SelectItem>
                {institutes.map((institute) => (
                  <SelectItem value={institute} key={institute}>{institute}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
      {/* Users List */}
      <div className="w-full max-w-4xl px-2">
        {filteredUsers.length === 0 ? (
          <Card className="p-4 sm:p-6 md:p-8 text-center text-muted-foreground mx-auto max-w-sm">
            <div className="flex flex-col items-center gap-2 sm:gap-3">
              <div className="text-3xl sm:text-4xl md:text-5xl">
                {users?.length === 0 ? "🎉" : "🔍"}
              </div>
              <div className="text-sm sm:text-base md:text-lg font-medium text-gray-700">
                {users?.length === 0
                  ? "Everyone is already your friend!"
                  : "No users found"}
              </div>
              <div className="text-xs sm:text-sm text-gray-500 text-center">
                {users?.length === 0
                  ? "You've connected with all available study buddies"
                  : "Try adjusting your search filters or check back later"}
              </div>
            </div>
          </Card>
        ) : (
          <div className="bg-white rounded-lg sm:rounded-xl shadow-lg border border-gray-200 p-3 sm:p-4 md:p-6">
            <div className="flex flex-col gap-3 mb-4 sm:mb-6">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                <div>
                  <h2 className="text-lg sm:text-xl font-bold text-gray-800">Available Study Buddies</h2>
                  <p className="text-xs sm:text-sm text-gray-600 mt-0.5">
                    {currentUserProfile ? 'Sorted by compatibility - best matches first!' : 'Connect with fellow students'}
                  </p>
                </div>
                <div className="flex flex-wrap items-center gap-1.5 sm:gap-2">
                  <span className="bg-violet-100 text-violet-800 px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium">
                    {filteredUsers.length} {filteredUsers.length === 1 ? 'person' : 'people'}
                  </span>
                  {currentUserProfile && filteredUsers.some(u => u.matchScore >= 75) && (
                    <span className="bg-gradient-to-r from-pink-500 to-violet-500 text-white px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium">
                      🎯 Great matches!
                    </span>
                  )}
                </div>
              </div>
            </div>

            <div className="space-y-2 sm:space-y-3">
              {filteredUsers.map((u) => (
                <div
                  key={u.id}
                  className="flex items-start sm:items-center gap-3 sm:gap-4 p-3 sm:p-4 bg-gray-50 rounded-lg sm:rounded-xl hover:bg-gray-100 transition-all duration-200 border border-gray-100 hover:border-gray-200"
                >
                  {/* Avatar */}
                  <Avatar
                    className="w-12 h-12 sm:w-14 sm:h-14 cursor-pointer hover:scale-105 transition-transform duration-200 border-2 border-gray-200 shadow-sm flex-shrink-0"
                    onClick={() => handleProfilePreview(u.id)}
                  >
                    <AvatarImage src={u.avatar_url || ''} alt={u.full_name || 'User'} />
                    <AvatarFallback className="bg-gradient-to-r from-violet-500 to-purple-600 text-white font-semibold text-sm">
                      {(u.full_name || 'U')
                        .split(" ")
                        .map((n) => n[0])
                        .join("")
                        .slice(0, 2)
                        .toUpperCase()}
                    </AvatarFallback>
                  </Avatar>

                  {/* User Info */}
                  <div
                    className="flex-1 cursor-pointer min-w-0"
                    onClick={() => handleProfilePreview(u.id)}
                  >
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 mb-2">
                      <h3 className="font-semibold text-gray-900 text-base sm:text-lg truncate">{u.full_name || 'Unknown User'}</h3>
                      {/* Match Quality Indicator */}
                      {currentUserProfile && u.matchScore >= 100 && (
                        <span className="inline-flex items-center px-1.5 sm:px-2 py-0.5 bg-gradient-to-r from-pink-500 to-violet-500 text-white rounded-full text-[10px] sm:text-xs font-bold w-fit">
                          ⭐ Perfect
                        </span>
                      )}
                      {currentUserProfile && u.matchScore >= 75 && u.matchScore < 100 && (
                        <span className="inline-flex items-center px-1.5 sm:px-2 py-0.5 bg-gradient-to-r from-emerald-500 to-teal-500 text-white rounded-full text-[10px] sm:text-xs font-bold w-fit">
                          🎯 Great
                        </span>
                      )}
                      {currentUserProfile && u.matchScore >= 60 && u.matchScore < 75 && (
                        <span className="inline-flex items-center px-1.5 sm:px-2 py-0.5 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-full text-[10px] sm:text-xs font-bold w-fit">
                          👍 Good
                        </span>
                      )}
                    </div>

                    {/* Tags for Country and Course */}
                    <div className="flex flex-wrap gap-1 sm:gap-2">
                      {u.country && (
                        <span className={`inline-flex items-center px-2 sm:px-2.5 py-0.5 sm:py-1 rounded-full text-[10px] sm:text-xs font-medium border ${
                          currentUserProfile?.country === u.country
                            ? 'bg-violet-200 text-violet-900 border-violet-300 ring-1 sm:ring-2 ring-violet-400/50'
                            : 'bg-violet-100 text-violet-800 border-violet-200'
                        }`}>
                          📍 {u.country}
                        </span>
                      )}
                      {u.course && (
                        <span className={`inline-flex items-center px-2 sm:px-2.5 py-0.5 sm:py-1 rounded-full text-[10px] sm:text-xs font-medium border ${
                          currentUserProfile?.course === u.course
                            ? 'bg-emerald-200 text-emerald-900 border-emerald-300 ring-1 sm:ring-2 ring-emerald-400/50'
                            : 'bg-emerald-100 text-emerald-800 border-emerald-200'
                        }`}>
                          📚 <span className="sm:hidden">{u.course.length > 12 ? `${u.course.substring(0, 12)}...` : u.course}</span>
                          <span className="hidden sm:inline">{u.course.length > 20 ? `${u.course.substring(0, 20)}...` : u.course}</span>
                        </span>
                      )}
                      {u.institute && (
                        <span className={`inline-flex items-center px-2 sm:px-2.5 py-0.5 sm:py-1 rounded-full text-[10px] sm:text-xs font-medium border ${
                          currentUserProfile?.institute === u.institute
                            ? 'bg-cyan-200 text-cyan-900 border-cyan-300 ring-1 sm:ring-2 ring-cyan-400/50'
                            : 'bg-cyan-100 text-cyan-800 border-cyan-200'
                        }`}>
                          🏫 <span className="sm:hidden">{u.institute.length > 10 ? `${u.institute.substring(0, 10)}...` : u.institute}</span>
                          <span className="hidden sm:inline">{u.institute.length > 15 ? `${u.institute.substring(0, 15)}...` : u.institute}</span>
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Action Button */}
                  <div className="flex-shrink-0 self-start sm:self-center mt-1 sm:mt-0">
                    {isRequestSent(u.id) ? (
                      <Button
                        variant="secondary"
                        size="sm"
                        className="bg-green-50 border-green-200 text-green-700 hover:bg-green-50 px-2 sm:px-4 py-1.5 sm:py-2 h-8 sm:h-9 text-xs sm:text-sm"
                        disabled
                      >
                        <UserPlus size={14} className="mr-1 sm:mr-2" />
                        <span className="hidden xs:inline sm:hidden">Sent</span>
                        <span className="xs:hidden sm:inline">Request Sent</span>
                      </Button>
                    ) : (
                      <Button
                        size="sm"
                        className="bg-violet-600 hover:bg-violet-700 text-white shadow-sm hover:shadow-md transition-all duration-200 px-2 sm:px-4 py-1.5 sm:py-2 h-8 sm:h-9 text-xs sm:text-sm"
                        onClick={() => handleSendFriendRequest(u.id, u.full_name || 'User')}
                        disabled={sendFriendRequestMutation.isPending}
                      >
                        {sendFriendRequestMutation.isPending ? (
                          <Loader2 size={14} className="mr-1 sm:mr-2 animate-spin" />
                        ) : (
                          <UserPlus size={14} className="mr-1 sm:mr-2" />
                        )}
                        <span className="hidden xs:inline sm:hidden">Add</span>
                        <span className="xs:hidden sm:inline">Add Friend</span>
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Profile Preview Dialog */}
      <ProfilePreviewDialog
        userId={profilePreviewUserId}
        open={!!profilePreviewUserId}
        onOpenChange={(open) => !open && setProfilePreviewUserId(null)}
      />
    </div>
  );
};

export default Discover;
