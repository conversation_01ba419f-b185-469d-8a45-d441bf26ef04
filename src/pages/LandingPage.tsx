import React from "react";
import { <PERSON> } from "react-router-dom";
import SimpleHeader from "@/components/SimpleHeader";
import {
  Users,
  FileText,
  Sparkles,
  BookOpen,
  Pencil,
  PieChart,
  MessageSquare,
  Activity,
  Star,
  Tag,
  Code,
  Settings,
  Book,
  Video,
  Bell,
  Award,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const features = [
  {
    icon: <Sparkles className="text-violet-600 mb-2" size={32} />,
    title: "AI Study Assistant",
    desc: "Summarize notes, answer tricky questions, or plan your revision. Your AI-powered study partner, available anytime.",
  },
  {
    icon: <FileText className="text-pink-500 mb-2" size={32} />,
    title: "Effortless Organization",
    desc: "Upload, sort, and search your materials. Never lose track of your notes again.",
  },
  {
    icon: <Users className="text-blue-500 mb-2" size={32} />,
    title: "Study Groups",
    desc: "Chat, share, and learn with classmates! Find your community or join live sessions.",
  },
  {
    icon: <BookOpen className="text-emerald-600 mb-2" size={32} />,
    title: "Resource Library",
    desc: "Access a curated library of resources and past exam papers for extra practice.",
  },
  {
    icon: <Pencil className="text-yellow-600 mb-2" size={32} />,
    title: "Smart Notes",
    desc: "Take digital notes, highlight key concepts, and organize your study sessions.",
  },
  {
    icon: <PieChart className="text-green-600 mb-2" size={32} />,
    title: "Progress Tracking",
    desc: "Monitor your revision progress and stay motivated with clear study stats.",
  },
  {
    icon: <MessageSquare className="text-blue-400 mb-2" size={32} />,
    title: "Instant Q&A",
    desc: "Ask questions and get instant answers whenever you're stuck.",
  },
  {
    icon: <Activity className="text-red-400 mb-2" size={32} />,
    title: "Revision Planner",
    desc: "Create personalized revision timetables that automatically adapt to your goals.",
  },
  {
    icon: <Tag className="text-violet-400 mb-2" size={32} />,
    title: "Topics & Tags",
    desc: "Tag notes and resources to find them fast by subject, topic, or priority.",
  },
  {
    icon: <Settings className="text-gray-500 mb-2" size={32} />,
    title: "Personalized Settings",
    desc: "Customize your dashboard, themes, and notification preferences.",
  },
  {
    icon: <Book className="text-indigo-400 mb-2" size={32} />,
    title: "Take Notes from Images",
    desc: "Snap or upload a photo and auto-convert it to organized digital notes.",
  },
  {
    icon: <Bell className="text-orange-400 mb-2" size={32} />,
    title: "Reminders",
    desc: "Set reminders for sessions, deadlines, and daily revision tasks.",
  },
];

export default function LandingPage() {
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-white via-[#ecd7fa] to-[#b475ea]">
      <SimpleHeader />
      {/* Removed duplicate header block previously here */}
      {/* Hero */}
      <main className="flex-1 flex flex-col items-center justify-center px-2 sm:px-4 pb-7">
        <div className="max-w-xl w-full text-center mt-4 mb-8 md:mt-8 md:mb-10 animate-fade-in">
          <h1 className="text-lg xs:text-xl sm:text-2xl md:text-3xl font-extrabold bg-gradient-to-r from-violet-800 via-purple-500 to-pink-400 text-transparent bg-clip-text mb-3 md:mb-4 leading-tight">
            All your study tools.<span className="text-violet-700">One modern hub.</span>
          </h1>
          <p className="text-base sm:text-lg md:text-xl text-slate-700 mb-4 md:mb-7 font-medium">
            Ace your exams and projects with AI-powered summaries, group chats, revision plans, and instant answers—all in one place.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center items-center">
            <Link
              to="/register"
              className="inline-flex items-center bg-violet-600 text-white px-7 py-2 rounded-xl font-bold text-base sm:text-lg shadow-xl hover:bg-violet-700 transition group"
            >
              Get Started
            </Link>
            <Link
              to="/login"
              className="inline-flex items-center border-2 border-violet-600 text-violet-600 px-6 py-2 rounded-xl font-bold text-base sm:text-lg hover:bg-violet-50 transition"
            >
              Sign In
            </Link>
          </div>
        </div>
        {/* All Features Grid */}
        <section className="w-full max-w-6xl mx-auto mb-7 sm:mb-12">
          <h2 className="text-lg sm:text-xl md:text-2xl font-bold text-violet-800 text-center mb-4 md:mb-6">
            Explore Features
          </h2>
          <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-5 md:gap-6">
            {features.map((feature) => (
              <Card
                className="bg-white/80 backdrop-blur shadow-xl hover:shadow-2xl transition rounded-xl p-3"
                key={feature.title}
              >
                <CardHeader className="flex flex-col items-center pb-2 px-2 pt-3">
                  {feature.icon}
                  <CardTitle className="text-sm sm:text-base font-bold text-slate-800 text-center">
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-slate-700 text-center text-xs sm:text-sm pb-4 pt-0 px-2 md:px-3">
                  {feature.desc}
                </CardContent>
              </Card>
            ))}
          </div>
        </section>
      </main>
      {/* Footer */}
      <footer className="p-4 md:p-6 mt-auto text-center text-xs text-slate-400">
        &copy; {new Date().getFullYear()} StudyHub. All rights reserved.
      </footer>
    </div>
  );
}
