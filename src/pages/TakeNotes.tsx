import { useState } from "react";
import { useNavigate } from "react-router-dom";
import PageHeader from "@/components/PageHeader";
import RichTextEditor from "@/components/notes/RichTextEditor";
import FolderSelector from "@/components/notes/FolderSelector";
import NoteSuccessDialog from "@/components/notes/NoteSuccessDialog";
import { useCreateNote, useUnits, useTopics, useUploadFile } from "@/hooks/useNotes";
import { generateNotePDF } from "@/utils/pdfGenerator";
import { toast } from "sonner";
import AuthGuard from "@/components/AuthGuard";

const TakeNotes = () => {
  const navigate = useNavigate();

  // State management
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [showFolderSelector, setShowFolderSelector] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [savedNote, setSavedNote] = useState(null);
  const [selectedUnit, setSelectedUnit] = useState(null);
  const [selectedTopic, setSelectedTopic] = useState(null);

  // Hooks
  const createNoteMutation = useCreateNote();
  const uploadFileMutation = useUploadFile();
  const { data: units = [] } = useUnits();
  const { data: topics = [] } = useTopics(selectedUnit?.id || '');

  // Handle save note
  const handleSave = () => {
    if (!title.trim()) {
      toast.error('Please enter a note title');
      return;
    }
    if (!content.trim()) {
      toast.error('Please enter note content');
      return;
    }
    setShowFolderSelector(true);
  };

  // Handle folder selection and save note
  const handleFolderSelected = async (unitId: string, topicId: string) => {
    try {
      // Generate PDF from the note content
      const pdfBlob = generateNotePDF({
        title: title.trim(),
        content: content.trim(),
        author: 'Study App User',
        subject: 'Study Notes',
        keywords: extractTagsFromContent(content),
        includeMetadata: true
      });

      // Upload PDF to storage
      const pdfFileName = `${title.trim().replace(/[^a-zA-Z0-9]/g, '_')}_notes.pdf`;
      const pdfFile = new File([pdfBlob], pdfFileName, { type: 'application/pdf' });
      const uploadResult = await uploadFileMutation.mutateAsync(pdfFile);

      // Create note with PDF file URL
      const noteData = {
        title: title.trim(),
        content: content.trim(),
        unit_id: unitId,
        topic_id: topicId,
        file_url: uploadResult.url, // Store PDF URL
        tags: [...extractTagsFromContent(content), 'pdf', 'generated-notes']
      };

      const note = await createNoteMutation.mutateAsync(noteData);

      // Get unit and topic details for success dialog
      const unit = units.find(u => u.id === unitId);
      const topic = topics.find(t => t.id === topicId);

      setSavedNote(note);
      setSelectedUnit(unit);
      setSelectedTopic(topic);
      setShowFolderSelector(false);
      setShowSuccessDialog(true);

      // Reset form
      setTitle('');
      setContent('');

      toast.success('Note saved as PDF successfully!');
    } catch (error) {
      console.error('Failed to save note:', error);
      toast.error('Failed to save note. Please try again.');
    }
  };

  // Extract tags from content (simple implementation)
  const extractTagsFromContent = (content: string) => {
    const tagRegex = /#(\w+)/g;
    const matches = content.match(tagRegex);
    return matches ? matches.map(tag => tag.substring(1)) : [];
  };

  // Handle success dialog actions
  const handleCreateAnother = () => {
    setShowSuccessDialog(false);
    setSavedNote(null);
    setSelectedUnit(null);
    setSelectedTopic(null);
  };

  const handleViewNotes = () => {
    setShowSuccessDialog(false);
    navigate('/sort-notes');
  };



  const handleNewNote = () => {
    setTitle('');
    setContent('');
    setSavedNote(null);
    setSelectedUnit(null);
    setSelectedTopic(null);
  };



  return (
    <AuthGuard>
      <div className="min-h-screen bg-gradient-to-br from-white via-[#f0f4ff] to-[#e0e7ff] pb-20 md:pb-0">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <PageHeader
            title="Take Notes"
            description="Create rich, organized notes with formatting options"
            buttonLabel="New Note"
            onButtonClick={handleNewNote}
          />

          <div className="mt-8">
            <RichTextEditor
              title={title}
              content={content}
              onTitleChange={setTitle}
              onContentChange={setContent}
              onSave={handleSave}
              isSaving={createNoteMutation.isPending}
            />
          </div>

          {/* Folder Selector Dialog */}
          <FolderSelector
            open={showFolderSelector}
            onOpenChange={setShowFolderSelector}
            onFolderSelected={handleFolderSelected}
            isLoading={createNoteMutation.isPending}
          />

          {/* Success Dialog */}
          <NoteSuccessDialog
            open={showSuccessDialog}
            onOpenChange={setShowSuccessDialog}
            note={savedNote}
            unit={selectedUnit}
            topic={selectedTopic}
            onCreateAnother={handleCreateAnother}
            onViewNotes={handleViewNotes}
          />
        </div>
      </div>
    </AuthGuard>
  );
};

export default TakeNotes;
