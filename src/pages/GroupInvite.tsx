
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useStudyGroupDetails, useJoinStudyGroup } from '@/hooks/useStudyGroups';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Users, Lock, Globe, ArrowLeft } from 'lucide-react';
import { toast } from 'sonner';
import AuthGuard from '@/components/AuthGuard';

const GroupInvite: React.FC = () => {
  const { groupId } = useParams<{ groupId: string }>();
  const navigate = useNavigate();
  const [isJoining, setIsJoining] = useState(false);

  const { data: group, isLoading, error } = useStudyGroupDetails(groupId || '');
  const joinGroupMutation = useJoinStudyGroup();

  useEffect(() => {
    if (error) {
      toast.error('Failed to load group details');
    }
  }, [error]);

  const handleJoinGroup = async () => {
    if (!groupId) return;
    
    setIsJoining(true);
    try {
      await joinGroupMutation.mutateAsync(groupId);
      toast.success('Successfully joined the group!');
      navigate(`/study-groups`);
    } catch (error) {
      toast.error('Failed to join group');
    } finally {
      setIsJoining(false);
    }
  };

  const handleGoBack = () => {
    navigate('/study-groups');
  };

  if (isLoading) {
    return (
      <AuthGuard>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-violet-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading group details...</p>
          </div>
        </div>
      </AuthGuard>
    );
  }

  if (!group) {
    return (
      <AuthGuard>
        <div className="min-h-screen flex items-center justify-center">
          <Card className="p-8 max-w-md mx-auto text-center">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">Group Not Found</h1>
            <p className="text-gray-600 mb-6">
              The group you're looking for doesn't exist or the invitation link is invalid.
            </p>
            <Button onClick={handleGoBack}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Study Groups
            </Button>
          </Card>
        </div>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gradient-to-br from-violet-900 via-purple-900 to-indigo-900 py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8">
              <Button 
                variant="ghost" 
                onClick={handleGoBack}
                className="mb-4 text-white hover:bg-white/10"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Study Groups
              </Button>
              <h1 className="text-3xl font-bold text-white mb-2">Join Study Group</h1>
              <p className="text-white/70">You've been invited to join this study group</p>
            </div>

            {/* Group Card */}
            <Card className="bg-white/10 backdrop-blur-2xl border-white/20 p-8 text-center">
              {/* Group Image */}
              <div className="w-24 h-24 mx-auto mb-6 rounded-full overflow-hidden bg-gradient-to-r from-violet-500 to-purple-500 flex items-center justify-center">
                {group.cover_image_url ? (
                  <img 
                    src={group.cover_image_url} 
                    alt={group.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <Users className="w-12 h-12 text-white" />
                )}
              </div>

              {/* Group Info */}
              <h2 className="text-2xl font-bold text-white mb-2">{group.name}</h2>
              <p className="text-white/70 mb-4">{group.description || 'No description available'}</p>
              
              <div className="flex items-center justify-center gap-4 mb-6 text-white/60">
                <div className="flex items-center gap-1">
                  <Users className="w-4 h-4" />
                  <span>{group.member_count} {group.member_count === 1 ? 'member' : 'members'}</span>
                </div>
                <div className="flex items-center gap-1">
                  {group.privacy === 'private' ? <Lock className="w-4 h-4" /> : <Globe className="w-4 h-4" />}
                  <span className="capitalize">{group.privacy}</span>
                </div>
              </div>

              {/* Join Button */}
              <Button
                onClick={handleJoinGroup}
                disabled={isJoining || joinGroupMutation.isPending}
                className="bg-gradient-to-r from-violet-500 to-purple-500 hover:from-violet-600 hover:to-purple-600 text-white px-8 py-3 text-lg"
              >
                {isJoining || joinGroupMutation.isPending ? 'Joining...' : 'Join Group'}
              </Button>
            </Card>
          </div>
        </div>
      </div>
    </AuthGuard>
  );
};

export default GroupInvite;
