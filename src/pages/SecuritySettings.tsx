import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Key, 
  Eye, 
  EyeOff, 
  Download, 
  Upload,
  AlertTriangle,
  CheckCircle,
  Settings,
  Lock
} from 'lucide-react';
import { useSecurity } from '@/contexts/SecurityContext';
import { useSecurityMiddleware } from '@/hooks/useSecurityMiddleware';
import SecurityDashboard from '@/components/SecurityDashboard';
import { toast } from 'sonner';

const SecuritySettings: React.FC = () => {
  const security = useSecurity();
  const { secureFormSubmit } = useSecurityMiddleware();
  
  const [activeTab, setActiveTab] = useState<'overview' | 'settings' | 'logs'>('overview');
  const [showPassword, setShowPassword] = useState(false);
  const [securitySettings, setSecuritySettings] = useState({
    twoFactorEnabled: false,
    sessionTimeout: 120, // minutes
    loginNotifications: true,
    suspiciousActivityAlerts: true,
    dataEncryption: true,
    secureHeaders: true,
  });

  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const handleSettingChange = (setting: string, value: boolean | number) => {
    setSecuritySettings(prev => ({
      ...prev,
      [setting]: value
    }));
    
    // Log security setting change
    security.reportSuspiciousActivity('Security setting changed', {
      setting,
      newValue: value
    });
    
    toast.success(`Security setting updated: ${setting}`);
  };

  const handlePasswordChange = () => {
    const validation = secureFormSubmit(passwordForm, 'password_change');
    
    if (!validation.success) {
      toast.error(validation.error || 'Password change failed');
      return;
    }

    // Validate password requirements
    const passwordValidation = security.validateInput(passwordForm.newPassword, 'password');
    if (!passwordValidation.valid) {
      toast.error(passwordValidation.errors?.join(', ') || 'Invalid password');
      return;
    }

    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    // Here you would typically call an API to change the password
    toast.success('Password changed successfully');
    setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' });
  };

  const exportSecurityLogs = () => {
    const logs = JSON.parse(localStorage.getItem('security_logs') || '[]');
    const dataStr = JSON.stringify(logs, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `security-logs-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
    toast.success('Security logs exported successfully');
  };

  const clearAllData = () => {
    if (window.confirm('Are you sure you want to clear all security data? This action cannot be undone.')) {
      localStorage.removeItem('security_logs');
      security.reportSuspiciousActivity('Security data cleared by user');
      toast.success('Security data cleared');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 md:p-8">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
              <Shield className="w-8 h-8 text-blue-600" />
              Security Center
            </h1>
            <p className="text-gray-600 mt-2">
              Manage your account security and privacy settings
            </p>
          </div>
          <Badge variant={security.securityStatus.isBlocked ? 'destructive' : 'default'}>
            {security.securityStatus.isBlocked ? 'Account Blocked' : 'Account Secure'}
          </Badge>
        </div>

        {/* Security Status Alert */}
        {security.securityStatus.isBlocked && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Your account has been temporarily blocked due to suspicious activity. 
              Some features may be limited until the issue is resolved.
            </AlertDescription>
          </Alert>
        )}

        {/* Navigation Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
          {[
            { id: 'overview', label: 'Security Overview', icon: Shield },
            { id: 'settings', label: 'Security Settings', icon: Settings },
            { id: 'logs', label: 'Activity Logs', icon: Eye },
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && <SecurityDashboard />}

        {activeTab === 'settings' && (
          <div className="space-y-6">
            {/* Password Security */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Key className="w-5 h-5" />
                  Password Security
                </CardTitle>
                <CardDescription>
                  Change your password and manage authentication settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="current-password">Current Password</Label>
                    <div className="relative">
                      <Input
                        id="current-password"
                        type={showPassword ? 'text' : 'password'}
                        value={passwordForm.currentPassword}
                        onChange={(e) => setPasswordForm(prev => ({
                          ...prev,
                          currentPassword: e.target.value
                        }))}
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="new-password">New Password</Label>
                    <Input
                      id="new-password"
                      type={showPassword ? 'text' : 'password'}
                      value={passwordForm.newPassword}
                      onChange={(e) => setPasswordForm(prev => ({
                        ...prev,
                        newPassword: e.target.value
                      }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirm-password">Confirm New Password</Label>
                    <Input
                      id="confirm-password"
                      type={showPassword ? 'text' : 'password'}
                      value={passwordForm.confirmPassword}
                      onChange={(e) => setPasswordForm(prev => ({
                        ...prev,
                        confirmPassword: e.target.value
                      }))}
                    />
                  </div>
                </div>
                <Button onClick={handlePasswordChange} className="w-full md:w-auto">
                  <Lock className="w-4 h-4 mr-2" />
                  Change Password
                </Button>
              </CardContent>
            </Card>

            {/* Security Preferences */}
            <Card>
              <CardHeader>
                <CardTitle>Security Preferences</CardTitle>
                <CardDescription>
                  Configure your security and privacy preferences
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Two-Factor Authentication</Label>
                      <p className="text-sm text-gray-600">Add an extra layer of security to your account</p>
                    </div>
                    <Switch
                      checked={securitySettings.twoFactorEnabled}
                      onCheckedChange={(checked) => handleSettingChange('twoFactorEnabled', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Login Notifications</Label>
                      <p className="text-sm text-gray-600">Get notified when someone logs into your account</p>
                    </div>
                    <Switch
                      checked={securitySettings.loginNotifications}
                      onCheckedChange={(checked) => handleSettingChange('loginNotifications', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Suspicious Activity Alerts</Label>
                      <p className="text-sm text-gray-600">Receive alerts for unusual account activity</p>
                    </div>
                    <Switch
                      checked={securitySettings.suspiciousActivityAlerts}
                      onCheckedChange={(checked) => handleSettingChange('suspiciousActivityAlerts', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Data Encryption</Label>
                      <p className="text-sm text-gray-600">Encrypt sensitive data stored in your account</p>
                    </div>
                    <Switch
                      checked={securitySettings.dataEncryption}
                      onCheckedChange={(checked) => handleSettingChange('dataEncryption', checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Data Management */}
            <Card>
              <CardHeader>
                <CardTitle>Data Management</CardTitle>
                <CardDescription>
                  Export or clear your security data
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button onClick={exportSecurityLogs} variant="outline">
                    <Download className="w-4 h-4 mr-2" />
                    Export Security Logs
                  </Button>
                  <Button onClick={clearAllData} variant="destructive">
                    <AlertTriangle className="w-4 h-4 mr-2" />
                    Clear All Security Data
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'logs' && (
          <Card>
            <CardHeader>
              <CardTitle>Security Activity Logs</CardTitle>
              <CardDescription>
                Detailed view of all security-related activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SecurityDashboard />
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default SecuritySettings;
