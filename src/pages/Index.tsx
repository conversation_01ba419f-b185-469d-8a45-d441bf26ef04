import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  SortAsc,
  Calendar,
  Clock,
  Brain,
  Image,
  Users,
  FileArchive,
  PencilLine,
  Menu,
  FileQuestion,
  Sparkles,
} from "lucide-react";
import { Navigation } from "@/components/Navigation";
import { useProfile } from "@/hooks/useProfile";
import { useExams } from "@/hooks/useRevisionPlanner";
import ExamCountdownCard from "@/components/ExamCountdownCard";
import { PaymentWall } from "@/components/PaymentWall";
import { TrialStatus } from "@/components/TrialStatus";
import { useSubscription } from "@/hooks/useSubscription";
import { useUser } from "@/hooks/useAuth";
// import { paystackService } from "@/services/paystackService"; // Removed - using WooCommerce now
import { toast } from "sonner";

const quickAccess = [
  {
    title: "Sort Notes",
    subtitle: "Organize your materials",
    icon: SortAsc,
    url: "/sort-notes",
  },
  {
    title: "Study Groups",
    subtitle: "Learn with others",
    icon: Users,
    url: "/study-groups",
  },
  {
    title: "Timetable",
    subtitle: "Plan reading sessions",
    icon: Clock,
    url: "/reading-timetable",
  },
  {
    title: "Ask AI Tutor",
    subtitle: "Get learning assistance",
    icon: Brain,
    url: "/ask-ai-tutor",
  },
  {
    title: "Quiz Generator",
    subtitle: "Create quizzes from content",
    icon: FileQuestion,
    url: "/quiz-generator",
  },
  {
    title: "Image to Notes",
    subtitle: "Convert images to text",
    icon: Image,
    url: "/image-to-notes",
  },
  {
    title: "AI Notes",
    subtitle: "Generate study notes with AI",
    icon: Sparkles,
    url: "/ai-notes",
  },
  {
    title: "Revision Planner",
    subtitle: "Schedule your studies",
    icon: Calendar,
    url: "/revision-planner",
  },
  {
    title: "Past Papers",
    subtitle: "Practice & Solutions",
    icon: FileArchive,
    url: "/past-papers",
  },
  {
    title: "Take Notes",
    subtitle: "Record, edit & share notes",
    icon: PencilLine,
    url: "/take-notes",
  },
];



const Index = () => {
  const navigate = useNavigate();
  const { data: profile, isLoading: profileLoading } = useProfile();
  const { data: exams = [], isLoading: examsLoading } = useExams();
  const { hasActiveSubscription, subscriptionStatus, startFreeTrial, isLoading: subscriptionLoading } = useSubscription();
  const { data: user } = useUser();

  const handleStartTrial = async () => {
    await startFreeTrial();
  };


  // Filter upcoming exams (today and future, excluding past exams)
  const upcomingExams = exams.filter(exam => {
    const examDate = new Date(exam.exam_date);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day
    examDate.setHours(0, 0, 0, 0); // Reset time to start of day
    return examDate >= today;
  }).sort((a, b) => new Date(a.exam_date).getTime() - new Date(b.exam_date).getTime());

  // Auto-start trial for new users, show payment wall only after trial expires
  useEffect(() => {
    const autoStartTrial = async () => {
      if (!subscriptionLoading && !hasActiveSubscription && !subscriptionStatus && user?.id) {
        console.log('🎁 Auto-starting free trial for new user');
        await handleStartTrial();
      }
    };

    autoStartTrial();
  }, [subscriptionLoading, hasActiveSubscription, subscriptionStatus, user?.id]);

  // Show payment wall only if user has no subscription AND no active trial
  if (!subscriptionLoading && !hasActiveSubscription && subscriptionStatus && !subscriptionStatus.isTrial) {
    return (
      <>
        <div className="min-h-screen flex flex-col bg-gradient-to-br from-white via-[#ecd7fa] to-[#b475ea]">
          <div className="flex-1 px-4 pb-24">
            {/* Blurred content behind payment wall */}
            <div className="blur-sm pointer-events-none">
              <div className="mt-6 rounded-xl bg-gradient-to-r from-[#e3eaff] to-[#f7f7fd] p-5 shadow-sm mb-6">
                <h2 className="font-semibold text-lg text-slate-800 mb-1">
                  Welcome back, {profileLoading ? '...' : (profile?.full_name || 'Student')}
                </h2>
                <p className="text-slate-500">Let's continue your learning journey today.</p>
              </div>
            </div>
          </div>
        </div>
        <PaymentWall onStartTrial={handleStartTrial} />
      </>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-white via-[#ecd7fa] to-[#b475ea]">
      {/* Remove Header here, handled globally */}
      <div className="flex-1 px-4 pb-24">
        {/* Welcome panel */}
        <div className="mt-6 rounded-xl bg-gradient-to-r from-[#e3eaff] to-[#f7f7fd] p-5 shadow-sm mb-6">
          <h2 className="font-semibold text-lg text-slate-800 mb-1">
            Welcome back, {profileLoading ? '...' : (profile?.full_name || 'Student')}
          </h2>
          <p className="text-slate-500">Let's continue your learning journey today.</p>
        </div>

        {/* Show trial status if user is on trial */}
        {subscriptionStatus?.isTrial && subscriptionStatus?.expiresAt && (
          <TrialStatus
            expiresAt={subscriptionStatus.expiresAt}
            onUpgrade={() => {
              // This will trigger the payment wall to show
              window.location.reload();
            }}
          />
        )}

        {/* --- UPCOMING EXAMS SECTION --- */}
        {!examsLoading && upcomingExams.length > 0 && (
          <div className="mb-6 animate-fade-in">
            <h3 className="font-semibold text-slate-800 text-base mb-3 flex items-center">
              <Calendar className="w-5 h-5 mr-2 text-orange-600" />
              Upcoming Exams & Assignments
            </h3>
            <div className="flex flex-col gap-3">
              {upcomingExams.slice(0, 5).map((exam) => (
                <ExamCountdownCard
                  key={exam.id}
                  exam={exam}
                  onClick={() => navigate('/revision-planner')}
                />
              ))}
            </div>

            {/* Show link to revision planner if there are more exams */}
            {upcomingExams.length > 5 && (
              <div className="mt-3 text-center">
                <button
                  onClick={() => navigate('/revision-planner')}
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  View all {upcomingExams.length} upcoming exams →
                </button>
              </div>
            )}
          </div>
        )}

        {/* Loading state for exams */}
        {examsLoading && (
          <div className="mb-6">
            <h3 className="font-semibold text-slate-800 text-base mb-3 flex items-center">
              <Calendar className="w-5 h-5 mr-2 text-orange-600" />
              Upcoming Exams & Assignments
            </h3>
            <div className="flex flex-col gap-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-white rounded-lg shadow-sm p-4 animate-pulse">
                  <div className="flex items-center">
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                    <div className="ml-4">
                      <div className="h-4 bg-gray-200 rounded w-20 mb-1"></div>
                      <div className="h-3 bg-gray-200 rounded w-16"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        {/* --- END UPCOMING EXAMS SECTION --- */}

        <h3 className="text-base font-semibold text-slate-800 mb-3">Quick Access</h3>
        <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
          {quickAccess.map((item) => (
            <button
              key={item.title}
              className="rounded-xl bg-white border border-gray-100 hover:shadow-md py-6 px-2 flex flex-col items-center transition group"
              onClick={() => navigate(item.url)}
            >
              <item.icon size={32} className="text-[#635bff] mb-2 group-hover:scale-110 transition-transform"/>
              <span className="font-medium text-slate-800">{item.title}</span>
              <span className="text-xs text-slate-500 mt-1 text-center">{item.subtitle}</span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Index;
