import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import {
  Bell,
  Check,
  CheckCheck,
  Clock,
  Users,
  MessageSquare,
  BookOpen,
  FileText,
  Settings,
  Loader2,
  ArrowLeft,
  Filter,
  Trash2,
  Volume2,
  VolumeX,
  Smartphone,
  Mail,
  BellRing,
  Shield,
  Palette,
  Calendar
} from 'lucide-react';
import {
  useNotifications,
  useUnreadNotificationCount,
  useMarkNotificationRead,
  useMarkAllNotificationsRead,
  usePushNotifications,
  Notification
} from '@/hooks/useNotifications';
import { formatDistanceToNow } from 'date-fns';
import AuthGuard from '@/components/AuthGuard';

const NotificationsPage = () => {
  const [activeTab, setActiveTab] = useState<'notifications' | 'settings'>('notifications');
  const [notificationFilter, setNotificationFilter] = useState<'all' | 'unread'>('all');
  const [page, setPage] = useState(0);
  const limit = 20;

  // Notification settings state
  const [settings, setSettings] = useState({
    pushNotifications: true,
    emailNotifications: true,
    soundEnabled: true,
    friendRequests: true,
    messages: true,
    studyGroups: true,
    noteSharing: true,
    timetableNotifications: true,
    systemUpdates: true,
    marketingEmails: false,
    weeklyDigest: true,
    instantNotifications: true,
    quietHours: false,
    quietStart: '22:00',
    quietEnd: '08:00',
  });

  const { data: allNotifications = [], isLoading: allLoading, refetch: refetchAll } = useNotifications(limit, page * limit, false);
  const { data: unreadNotifications = [], isLoading: unreadLoading, refetch: refetchUnread } = useNotifications(limit, page * limit, true);
  const { data: unreadCount = 0 } = useUnreadNotificationCount();
  const markAsReadMutation = useMarkNotificationRead();
  const markAllAsReadMutation = useMarkAllNotificationsRead();
  const { requestPermission, isSupported, permission } = usePushNotifications();

  const notifications = notificationFilter === 'all' ? allNotifications : unreadNotifications;
  const isLoading = notificationFilter === 'all' ? allLoading : unreadLoading;

  // Load settings from localStorage on component mount
  useEffect(() => {
    try {
      const savedSettings = localStorage.getItem('studyfam_notification_settings');
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(prev => ({ ...prev, ...parsedSettings }));
      }
    } catch (error) {
      console.error('Error loading notification settings:', error);
    }
  }, []);

  const handleMarkAsRead = async (notificationId: string, actionUrl?: string) => {
    try {
      await markAsReadMutation.mutateAsync(notificationId);
      
      if (actionUrl) {
        window.location.href = actionUrl;
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsReadMutation.mutateAsync();
      refetchAll();
      refetchUnread();
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const getNotificationIcon = (type: Notification['type']) => {
    const iconMap = {
      friend_request: Users,
      friend_accepted: Check,
      message: MessageSquare,
      study_group_invite: BookOpen,
      study_group_post: FileText,
      note_shared: FileText,
      system: Settings,
    };

    const IconComponent = iconMap[type] || Bell;
    return <IconComponent className="w-5 h-5" />;
  };

  const getNotificationColor = (type: Notification['type'], read: boolean) => {
    if (read) return 'text-gray-500 bg-gray-50 border-gray-200';
    
    const colorMap = {
      friend_request: 'text-blue-600 bg-blue-50 border-blue-200',
      friend_accepted: 'text-green-600 bg-green-50 border-green-200',
      message: 'text-purple-600 bg-purple-50 border-purple-200',
      study_group_invite: 'text-orange-600 bg-orange-50 border-orange-200',
      study_group_post: 'text-indigo-600 bg-indigo-50 border-indigo-200',
      note_shared: 'text-cyan-600 bg-cyan-50 border-cyan-200',
      system: 'text-gray-600 bg-gray-50 border-gray-200',
    };

    return colorMap[type] || 'text-gray-600 bg-gray-50 border-gray-200';
  };

  const getTypeLabel = (type: Notification['type']) => {
    const labelMap = {
      friend_request: 'Friend Request',
      friend_accepted: 'Friend Accepted',
      message: 'Message',
      study_group_invite: 'Group Invite',
      study_group_post: 'Group Post',
      note_shared: 'Note Shared',
      system: 'System',
    };

    return labelMap[type] || 'Notification';
  };

  const handleSettingChange = (key: string, value: boolean | string) => {
    setSettings(prev => ({ ...prev, [key]: value }));

    // Save specific settings to localStorage for service workers to access
    try {
      if (key === 'timetableNotifications') {
        localStorage.setItem('studyfam_timetable_notifications', value.toString());
      } else if (key === 'pushNotifications') {
        localStorage.setItem('studyfam_push_notifications', value.toString());
      } else if (key === 'soundEnabled') {
        localStorage.setItem('studyfam_sound_notifications', value.toString());
      }

      // Save all settings to localStorage
      const updatedSettings = { ...settings, [key]: value };
      localStorage.setItem('studyfam_notification_settings', JSON.stringify(updatedSettings));

      console.log('Setting changed:', key, value);
    } catch (error) {
      console.error('Error saving notification settings:', error);
    }
  };

  const handleEnablePushNotifications = async () => {
    const granted = await requestPermission();
    if (granted) {
      handleSettingChange('pushNotifications', true);
    }
  };

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gradient-to-br from-violet-900 via-purple-900 to-indigo-900 relative pb-20 md:pb-0">
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 right-4 sm:right-20 w-48 sm:w-72 h-48 sm:h-72 bg-gradient-to-br from-pink-400/20 to-rose-600/20 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 left-4 sm:left-20 w-48 sm:w-80 h-48 sm:h-80 bg-gradient-to-br from-cyan-400/15 to-blue-600/15 rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 container mx-auto px-4 py-8">
          {/* Header */}
          <div className="max-w-4xl mx-auto mb-8">
            <div className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <Link
                    to="/dashboard"
                    className="p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
                  >
                    <ArrowLeft className="w-5 h-5 text-white" />
                  </Link>
                  <div>
                    <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-pink-300 via-purple-300 to-cyan-300 bg-clip-text text-transparent">
                      Notifications
                    </h1>
                    <p className="text-white/70 text-sm sm:text-base">
                      Manage your notifications and preferences
                    </p>
                  </div>
                </div>
                {unreadCount > 0 && (
                  <Badge variant="destructive" className="text-lg px-3 py-1">
                    {unreadCount}
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="max-w-4xl mx-auto">
            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'notifications' | 'settings')}>
              {/* Tab Navigation */}
              <div className="bg-white/10 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-3 mb-6">
                <TabsList className="grid w-full grid-cols-2 bg-transparent">
                  <TabsTrigger
                    value="notifications"
                    className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-pink-500 data-[state=active]:to-violet-500 data-[state=active]:text-white text-white/70"
                  >
                    <Bell className="w-4 h-4" />
                    <span className="hidden sm:inline">Notifications</span>
                    {unreadCount > 0 && (
                      <Badge variant="destructive" className="ml-1 text-xs">
                        {unreadCount}
                      </Badge>
                    )}
                  </TabsTrigger>
                  <TabsTrigger
                    value="settings"
                    className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-pink-500 data-[state=active]:to-violet-500 data-[state=active]:text-white text-white/70"
                  >
                    <Settings className="w-4 h-4" />
                    <span className="hidden sm:inline">Settings</span>
                  </TabsTrigger>
                </TabsList>
              </div>

              {/* Notifications Tab */}
              <TabsContent value="notifications">
                <Card className="bg-white/10 backdrop-blur-xl border-white/20 shadow-xl">
                  {/* Notifications Header */}
                  <div className="p-6 border-b border-white/20">
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-xl font-semibold text-white">
                        Your Notifications
                      </h2>
                      {unreadCount > 0 && (
                        <Button
                          onClick={handleMarkAllAsRead}
                          disabled={markAllAsReadMutation.isPending}
                          className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
                        >
                          {markAllAsReadMutation.isPending ? (
                            <Loader2 className="w-4 h-4 animate-spin mr-2" />
                          ) : (
                            <CheckCheck className="w-4 h-4 mr-2" />
                          )}
                          Mark all as read
                        </Button>
                      )}
                    </div>

                    {/* Filter Tabs */}
                    <div className="flex gap-2">
                      <Button
                        variant={notificationFilter === 'all' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setNotificationFilter('all')}
                        className={notificationFilter === 'all' ? 'bg-gradient-to-r from-pink-500 to-violet-500 text-white' : 'border-white/30 text-black hover:bg-white/10 hover:text-black'}
                      >
                        <Bell className="w-4 h-4 mr-2" />
                        All
                      </Button>
                      <Button
                        variant={notificationFilter === 'unread' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setNotificationFilter('unread')}
                        className={notificationFilter === 'unread' ? 'bg-gradient-to-r from-pink-500 to-violet-500 text-white' : 'border-white/30 text-black hover:bg-white/10 hover:text-black'}
                      >
                        <Filter className="w-4 h-4 mr-2" />
                        Unread ({unreadCount})
                      </Button>
                    </div>
                  </div>

                  {/* Notifications List */}
                  <div className="p-6">
                    <NotificationsList
                      notifications={notifications}
                      isLoading={isLoading}
                      onMarkAsRead={handleMarkAsRead}
                      getNotificationIcon={getNotificationIcon}
                      getNotificationColor={getNotificationColor}
                      getTypeLabel={getTypeLabel}
                    />
                  </div>
                </Card>
              </TabsContent>

              {/* Settings Tab */}
              <TabsContent value="settings">
                <div className="space-y-6">
                  {/* Push Notifications */}
                  <Card className="bg-white/10 backdrop-blur-xl border-white/20 shadow-xl">
                    <div className="p-6">
                      <div className="flex items-center gap-3 mb-4">
                        <Smartphone className="w-6 h-6 text-cyan-400" />
                        <h3 className="text-lg font-semibold text-white">Push Notifications</h3>
                      </div>

                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <Label className="text-white font-medium">Enable Push Notifications</Label>
                            <p className="text-white/60 text-sm">Receive notifications on this device</p>
                            {!isSupported && (
                              <p className="text-red-400 text-xs mt-1">Push notifications not supported on this browser</p>
                            )}
                            {permission === 'denied' && (
                              <p className="text-red-400 text-xs mt-1">Push notifications are blocked. Please enable in browser settings.</p>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            {permission !== 'granted' && isSupported && (
                              <Button
                                size="sm"
                                onClick={handleEnablePushNotifications}
                                className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
                              >
                                Enable
                              </Button>
                            )}
                            <Switch
                              checked={settings.pushNotifications && permission === 'granted'}
                              onCheckedChange={(checked) => handleSettingChange('pushNotifications', checked)}
                              disabled={!isSupported || permission !== 'granted'}
                            />
                          </div>
                        </div>

                        <Separator className="bg-white/20" />

                        <div className="flex items-center justify-between">
                          <div>
                            <Label className="text-white font-medium">Sound Notifications</Label>
                            <p className="text-white/60 text-sm">Play sound when receiving notifications</p>
                          </div>
                          <Switch
                            checked={settings.soundEnabled}
                            onCheckedChange={(checked) => handleSettingChange('soundEnabled', checked)}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <Label className="text-white font-medium">Instant Notifications</Label>
                            <p className="text-white/60 text-sm">Receive notifications immediately</p>
                          </div>
                          <Switch
                            checked={settings.instantNotifications}
                            onCheckedChange={(checked) => handleSettingChange('instantNotifications', checked)}
                          />
                        </div>
                      </div>
                    </div>
                  </Card>

                  {/* Email Notifications */}
                  <Card className="bg-white/10 backdrop-blur-xl border-white/20 shadow-xl">
                    <div className="p-6">
                      <div className="flex items-center gap-3 mb-4">
                        <Mail className="w-6 h-6 text-blue-400" />
                        <h3 className="text-lg font-semibold text-white">Email Notifications</h3>
                      </div>

                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <Label className="text-white font-medium">Email Notifications</Label>
                            <p className="text-white/60 text-sm">Receive notifications via email</p>
                          </div>
                          <Switch
                            checked={settings.emailNotifications}
                            onCheckedChange={(checked) => handleSettingChange('emailNotifications', checked)}
                          />
                        </div>

                        <Separator className="bg-white/20" />

                        <div className="flex items-center justify-between">
                          <div>
                            <Label className="text-white font-medium">Weekly Digest</Label>
                            <p className="text-white/60 text-sm">Receive a weekly summary of your activity</p>
                          </div>
                          <Switch
                            checked={settings.weeklyDigest}
                            onCheckedChange={(checked) => handleSettingChange('weeklyDigest', checked)}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <Label className="text-white font-medium">Marketing Emails</Label>
                            <p className="text-white/60 text-sm">Receive updates about new features and tips</p>
                          </div>
                          <Switch
                            checked={settings.marketingEmails}
                            onCheckedChange={(checked) => handleSettingChange('marketingEmails', checked)}
                          />
                        </div>
                      </div>
                    </div>
                  </Card>

                  {/* Notification Types */}
                  <Card className="bg-white/10 backdrop-blur-xl border-white/20 shadow-xl">
                    <div className="p-6">
                      <div className="flex items-center gap-3 mb-4">
                        <BellRing className="w-6 h-6 text-purple-400" />
                        <h3 className="text-lg font-semibold text-white">Notification Types</h3>
                      </div>

                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Users className="w-5 h-5 text-blue-400" />
                            <div>
                              <Label className="text-white font-medium">Friend Requests</Label>
                              <p className="text-white/60 text-sm">New friend requests and acceptances</p>
                            </div>
                          </div>
                          <Switch
                            checked={settings.friendRequests}
                            onCheckedChange={(checked) => handleSettingChange('friendRequests', checked)}
                          />
                        </div>

                        <Separator className="bg-white/20" />

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <MessageSquare className="w-5 h-5 text-green-400" />
                            <div>
                              <Label className="text-white font-medium">Messages</Label>
                              <p className="text-white/60 text-sm">New messages and conversations</p>
                            </div>
                          </div>
                          <Switch
                            checked={settings.messages}
                            onCheckedChange={(checked) => handleSettingChange('messages', checked)}
                          />
                        </div>

                        <Separator className="bg-white/20" />

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <BookOpen className="w-5 h-5 text-orange-400" />
                            <div>
                              <Label className="text-white font-medium">Study Groups</Label>
                              <p className="text-white/60 text-sm">Study group invites and posts</p>
                            </div>
                          </div>
                          <Switch
                            checked={settings.studyGroups}
                            onCheckedChange={(checked) => handleSettingChange('studyGroups', checked)}
                          />
                        </div>

                        <Separator className="bg-white/20" />

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <FileText className="w-5 h-5 text-cyan-400" />
                            <div>
                              <Label className="text-white font-medium">Note Sharing</Label>
                              <p className="text-white/60 text-sm">When someone shares notes with you</p>
                            </div>
                          </div>
                          <Switch
                            checked={settings.noteSharing}
                            onCheckedChange={(checked) => handleSettingChange('noteSharing', checked)}
                          />
                        </div>

                        <Separator className="bg-white/20" />

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Calendar className="w-5 h-5 text-indigo-400" />
                            <div>
                              <Label className="text-white font-medium">Timetable Notifications</Label>
                              <p className="text-white/60 text-sm">Study session reminders and updates</p>
                            </div>
                          </div>
                          <Switch
                            checked={settings.timetableNotifications}
                            onCheckedChange={(checked) => handleSettingChange('timetableNotifications', checked)}
                          />
                        </div>

                        <Separator className="bg-white/20" />

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Settings className="w-5 h-5 text-gray-400" />
                            <div>
                              <Label className="text-white font-medium">System Updates</Label>
                              <p className="text-white/60 text-sm">Important system announcements</p>
                            </div>
                          </div>
                          <Switch
                            checked={settings.systemUpdates}
                            onCheckedChange={(checked) => handleSettingChange('systemUpdates', checked)}
                          />
                        </div>
                      </div>
                    </div>
                  </Card>

                  {/* Quiet Hours */}
                  <Card className="bg-white/10 backdrop-blur-xl border-white/20 shadow-xl">
                    <div className="p-6">
                      <div className="flex items-center gap-3 mb-4">
                        <VolumeX className="w-6 h-6 text-indigo-400" />
                        <h3 className="text-lg font-semibold text-white">Quiet Hours</h3>
                      </div>

                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <Label className="text-white font-medium">Enable Quiet Hours</Label>
                            <p className="text-white/60 text-sm">Disable notifications during specific hours</p>
                          </div>
                          <Switch
                            checked={settings.quietHours}
                            onCheckedChange={(checked) => handleSettingChange('quietHours', checked)}
                          />
                        </div>

                        {settings.quietHours && (
                          <>
                            <Separator className="bg-white/20" />
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label className="text-white font-medium">Start Time</Label>
                                <input
                                  type="time"
                                  value={settings.quietStart}
                                  onChange={(e) => handleSettingChange('quietStart', e.target.value)}
                                  className="w-full mt-1 px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white"
                                />
                              </div>
                              <div>
                                <Label className="text-white font-medium">End Time</Label>
                                <input
                                  type="time"
                                  value={settings.quietEnd}
                                  onChange={(e) => handleSettingChange('quietEnd', e.target.value)}
                                  className="w-full mt-1 px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white"
                                />
                              </div>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </AuthGuard>
  );
};

interface NotificationsListProps {
  notifications: Notification[];
  isLoading: boolean;
  onMarkAsRead: (id: string, actionUrl?: string) => void;
  getNotificationIcon: (type: Notification['type']) => React.ReactNode;
  getNotificationColor: (type: Notification['type'], read: boolean) => string;
  getTypeLabel: (type: Notification['type']) => string;
}

const NotificationsList: React.FC<NotificationsListProps> = ({
  notifications,
  isLoading,
  onMarkAsRead,
  getNotificationIcon,
  getNotificationColor,
  getTypeLabel,
}) => {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="w-8 h-8 animate-spin text-white/60" />
        <span className="ml-3 text-white/70">Loading notifications...</span>
      </div>
    );
  }

  if (notifications.length === 0) {
    return (
      <div className="text-center py-12">
        <Bell className="w-16 h-16 text-white/30 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-white mb-2">No notifications</h3>
        <p className="text-white/70">
          You're all caught up! New notifications will appear here.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {notifications.map((notification) => (
        <Card
          key={notification.id}
          className={`p-4 cursor-pointer transition-all hover:shadow-lg bg-white/5 backdrop-blur-sm border-white/20 hover:bg-white/10 ${
            !notification.read ? 'ring-1 ring-cyan-400/50' : ''
          }`}
          onClick={() => onMarkAsRead(notification.id, notification.action_url)}
        >
          <div className="flex items-start gap-4">
            {/* Icon */}
            <div className={`p-2 rounded-full ${getNotificationColor(notification.type, notification.read)}`}>
              {getNotificationIcon(notification.type)}
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <Badge variant="outline" className="text-xs border-white/30 text-white/80">
                      {getTypeLabel(notification.type)}
                    </Badge>
                    {!notification.read && (
                      <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse" />
                    )}
                  </div>

                  <h4 className={`text-sm font-medium mb-1 ${
                    notification.read ? 'text-white/70' : 'text-white'
                  }`}>
                    {notification.title}
                  </h4>

                  <p className={`text-sm ${
                    notification.read ? 'text-white/50' : 'text-white/80'
                  }`}>
                    {notification.message}
                  </p>
                </div>
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between mt-3">
                <span className="text-xs text-white/40 flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                </span>

                {notification.action_url && (
                  <span className="text-xs text-cyan-400 hover:text-cyan-300 font-medium">
                    Click to view →
                  </span>
                )}
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

export default NotificationsPage;
