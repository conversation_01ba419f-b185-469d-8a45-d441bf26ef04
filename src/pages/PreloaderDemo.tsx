import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import Preloader from '@/components/Preloader';
import SimplePreloader from '@/components/SimplePreloader';
import { Play, RotateCcw, <PERSON>rkles, Zap } from 'lucide-react';

const PreloaderDemo: React.FC = () => {
  const [showPreloader, setShowPreloader] = useState(false);
  const [showSimplePreloader, setShowSimplePreloader] = useState(false);
  const [preloaderType, setPreloaderType] = useState<'advanced' | 'simple'>('advanced');

  const handleShowPreloader = (type: 'advanced' | 'simple') => {
    setPreloaderType(type);
    if (type === 'advanced') {
      setShowPreloader(true);
    } else {
      setShowSimplePreloader(true);
    }
  };

  const handlePreloaderComplete = () => {
    setShowPreloader(false);
    setShowSimplePreloader(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 p-4 md:p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
            StudyFam Preloader Demo
          </h1>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Experience our beautiful loading animations designed to create an engaging first impression for users.
          </p>
        </div>

        {/* Demo Cards */}
        <div className="grid md:grid-cols-2 gap-6">
          {/* Advanced Preloader */}
          <Card className="border-2 border-blue-200 hover:border-blue-300 transition-colors">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-blue-700">
                <Sparkles className="w-5 h-5" />
                Advanced Preloader
              </CardTitle>
              <CardDescription>
                Powered by Framer Motion with smooth animations, particle effects, and advanced transitions.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-semibold text-gray-700">Features:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Animated floating particles</li>
                  <li>• Smooth logo scaling and rotation</li>
                  <li>• Gradient text animations</li>
                  <li>• Progress bar with shimmer effect</li>
                  <li>• Rotating decorative rings</li>
                </ul>
              </div>
              <Button 
                onClick={() => handleShowPreloader('advanced')}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                <Play className="w-4 h-4 mr-2" />
                Preview Advanced Preloader
              </Button>
            </CardContent>
          </Card>

          {/* Simple Preloader */}
          <Card className="border-2 border-purple-200 hover:border-purple-300 transition-colors">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-purple-700">
                <Zap className="w-5 h-5" />
                Simple Preloader
              </CardTitle>
              <CardDescription>
                CSS-only animations for better performance and compatibility across all devices.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-semibold text-gray-700">Features:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Pure CSS animations</li>
                  <li>• Lightweight and fast</li>
                  <li>• Cross-browser compatible</li>
                  <li>• Responsive design</li>
                  <li>• Fallback for older devices</li>
                </ul>
              </div>
              <Button 
                onClick={() => handleShowPreloader('simple')}
                className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
              >
                <Play className="w-4 h-4 mr-2" />
                Preview Simple Preloader
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Technical Details */}
        <Card className="border-2 border-gray-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RotateCcw className="w-5 h-5" />
              Technical Implementation
            </CardTitle>
            <CardDescription>
              How the preloader system works in StudyFam
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-700">Integration</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Integrated with AuthProvider</li>
                  <li>• Custom usePreloader hook</li>
                  <li>• Configurable duration and dependencies</li>
                  <li>• Auto-hide when app loads</li>
                </ul>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-700">Performance</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Optimized animations</li>
                  <li>• Minimal bundle impact</li>
                  <li>• GPU-accelerated transforms</li>
                  <li>• Responsive across devices</li>
                </ul>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-semibold text-gray-700 mb-2">Usage Example:</h4>
              <pre className="text-sm text-gray-600 overflow-x-auto">
{`const { isLoading, hidePreloader } = usePreloader({
  minDuration: 2500,
  dependencies: [user],
  autoHide: true
});

return (
  <>
    {isLoading && <Preloader onComplete={hidePreloader} />}
    {children}
  </>
);`}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Reset Button */}
        <div className="text-center">
          <Button 
            variant="outline" 
            onClick={() => {
              setShowPreloader(false);
              setShowSimplePreloader(false);
            }}
            className="border-gray-300 hover:border-gray-400"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset Demo
          </Button>
        </div>
      </div>

      {/* Preloader Overlays */}
      {showPreloader && (
        <Preloader 
          onComplete={handlePreloaderComplete}
          duration={3000}
        />
      )}

      {showSimplePreloader && (
        <SimplePreloader 
          onComplete={handlePreloaderComplete}
          duration={3000}
        />
      )}
    </div>
  );
};

export default PreloaderDemo;
