
import React, { useState } from "react";

const FILTERS = [
  { label: "All", value: "all" },
  { label: "Notes", value: "notes" },
  { label: "Groups", value: "groups" },
  { label: "Files", value: "files" },
];

export default function Search() {
  const [query, setQuery] = useState("");
  const [activeFilter, setActiveFilter] = useState("all");

  // Placeholder for future results logic
  const results = [];

  return (
    <div className="min-h-screen bg-white flex flex-col items-center px-4 pt-8">
      <div className="w-full max-w-lg">
        <h1 className="text-2xl font-bold mb-4">Search StudyHub</h1>
        <form className="flex gap-2 mb-6" onSubmit={e => e.preventDefault()}>
          <input
            className="w-full border border-gray-200 rounded-lg px-4 py-2 focus:ring-2 focus:ring-violet-500 transition outline-none"
            type="text"
            placeholder="Search for notes, groups, or files..."
            value={query}
            onChange={e => setQuery(e.target.value)}
            autoFocus
          />
          <button
            type="submit"
            className="px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition"
          >
            Search
          </button>
        </form>
        <div className="flex gap-3 mb-6">
          {FILTERS.map(filter => (
            <button
              key={filter.value}
              className={`px-4 py-1 rounded-full border ${
                activeFilter === filter.value
                  ? "bg-violet-600 text-white border-violet-600"
                  : "bg-gray-100 text-gray-700 border-gray-200 hover:bg-violet-50"
              } transition`}
              onClick={() => setActiveFilter(filter.value)}
              type="button"
            >
              {filter.label}
            </button>
          ))}
        </div>
        <div>
          <h2 className="text-lg font-semibold mb-2">Results</h2>
          {results.length === 0 ? (
            <div className="text-gray-500">No results found for your query yet.</div>
          ) : (
            <ul>
              {/* Future: map real results here */}
            </ul>
          )}
        </div>
      </div>
    </div>
  );
}

