import { useState, useEffect } from 'react';
import { Plus, Star, Sword, Bell, BellOff } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

import { useReadingTimetable } from '@/hooks/useReadingTimetable';
import { CreateSessionModal } from '@/components/timetable/CreateSessionModal';
import { WeeklyView } from '@/components/timetable/WeeklyView';
import { MonthlyView } from '@/components/timetable/MonthlyView';
import { ProgressOverview } from '@/components/timetable/ProgressOverview';
import { toast } from 'sonner';

const ReadingTimetable = () => {
  const [activeTab, setActiveTab] = useState('weekly');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingSession, setEditingSession] = useState<any>(null);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);

  const {
    sessions,
    subjects,
    progress,
    streaks,
    createSession,
    updateSession,
    deleteSession,
    markSessionComplete,
    createSubject
  } = useReadingTimetable();



  // Check notification settings on component mount
  useEffect(() => {
    const checkNotificationSettings = () => {
      try {
        const timetableNotifications = localStorage.getItem('studyfam_timetable_notifications');
        setNotificationsEnabled(timetableNotifications !== 'false');
      } catch (error) {
        console.error('Error checking notification settings:', error);
        setNotificationsEnabled(true);
      }
    };

    checkNotificationSettings();

    // Listen for storage changes to update notification status
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'studyfam_timetable_notifications') {
        setNotificationsEnabled(e.newValue !== 'false');
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // Wrapper functions to handle the type mismatch
  const handleCreateSession = async (sessionData: any): Promise<void> => {
    if (sessionData.id) {
      // Update existing session
      const { id, ...updates } = sessionData;
      await updateSession({ id, ...updates });
    } else {
      // Create new session
      await createSession(sessionData);
    }
  };

  const handleCreateSubject = async (subjectData: any): Promise<void> => {
    await createSubject(subjectData);
  };

  const handleMarkComplete = async (data: { sessionId: string; sessionDate: string; completionPercentage?: number }): Promise<void> => {
    await markSessionComplete(data);
  };

  const handleEditSession = (session: any) => {
    setEditingSession(session);
    setShowCreateModal(true);
  };

  const handleDeleteSession = async (sessionId: string) => {
    if (window.confirm('Are you sure you want to delete this reading session?')) {
      try {
        await deleteSession(sessionId);
      } catch (error) {
        console.error('Error deleting session:', error);
        toast.error('Failed to delete session');
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-3 sm:p-4 md:p-6 lg:p-8 pb-20 md:pb-6">
      <div className="max-w-7xl mx-auto space-y-4 sm:space-y-6 md:space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4 md:gap-6">
          <div className="text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white flex items-center justify-center sm:justify-start gap-2 sm:gap-3 md:gap-4">
              <Sword className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 text-yellow-400 flex-shrink-0" />
              <span className="bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                Reading Quest
              </span>
            </h1>
            <p className="text-purple-100 mt-1 text-sm sm:text-base md:text-lg">
              Embark on your learning adventure and track your progress!
            </p>
            <div className="flex items-center gap-2 mt-2">
              {notificationsEnabled ? (
                <div className="flex items-center gap-1 text-green-300 text-xs sm:text-sm">
                  <Bell className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span>Notifications Active</span>
                </div>
              ) : (
                <div className="flex items-center gap-1 text-yellow-300 text-xs sm:text-sm">
                  <BellOff className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span>Notifications Disabled</span>
                </div>
              )}
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
            <Button
              onClick={() => setShowCreateModal(true)}
              className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-bold shadow-lg border-2 border-yellow-400/30 hover:border-yellow-300/50 transition-all duration-200 text-sm sm:text-base md:text-lg px-4 sm:px-6 md:px-8 py-2 sm:py-3 md:py-4"
            >
              <Plus className="w-4 h-4 md:w-5 md:h-5 mr-2 flex-shrink-0" />
              <span className="hidden sm:inline">Add Reading Session</span>
              <span className="sm:hidden">Add Session</span>
            </Button>


          </div>
        </div>







        {/* Beautiful Main Content */}
        <Card className="bg-gradient-to-br from-slate-900/80 to-slate-800/80 border-slate-400/50 backdrop-blur-sm shadow-xl">
          <CardHeader className="pb-4 md:pb-6">
            <CardTitle className="text-white flex items-center gap-2 md:gap-3 text-lg sm:text-xl md:text-2xl font-bold drop-shadow-sm">
              <Star className="w-5 h-5 md:w-6 md:h-6 text-yellow-400 drop-shadow-sm" />
              Reading Schedule
            </CardTitle>
            <CardDescription className="text-gray-100 text-sm sm:text-base md:text-lg font-medium">
              View and manage your reading sessions across different time periods
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0 md:px-8">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3 bg-slate-700/80 border border-slate-500/60 p-1 md:p-2 shadow-inner">
                <TabsTrigger
                  value="weekly"
                  className="data-[state=active]:bg-purple-600 data-[state=active]:text-white data-[state=active]:shadow-lg text-slate-100 hover:text-white transition-all duration-200 text-xs sm:text-sm md:text-base font-semibold py-2 md:py-3"
                >
                  <span className="hidden sm:inline">Weekly</span>
                  <span className="sm:hidden">Week</span>
                </TabsTrigger>
                <TabsTrigger
                  value="monthly"
                  className="data-[state=active]:bg-purple-600 data-[state=active]:text-white data-[state=active]:shadow-lg text-slate-100 hover:text-white transition-all duration-200 text-xs sm:text-sm md:text-base font-semibold py-2 md:py-3"
                >
                  <span className="hidden sm:inline">Monthly</span>
                  <span className="sm:hidden">Month</span>
                </TabsTrigger>
                <TabsTrigger
                  value="progress"
                  className="data-[state=active]:bg-purple-600 data-[state=active]:text-white data-[state=active]:shadow-lg text-slate-100 hover:text-white transition-all duration-200 text-xs sm:text-sm md:text-base font-semibold py-2 md:py-3"
                >
                  <span className="hidden sm:inline">Progress</span>
                  <span className="sm:hidden">Stats</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="weekly" className="mt-4 md:mt-6">
                <WeeklyView
                  sessions={sessions || []}
                  subjects={subjects || []}
                  progress={progress || []}
                  selectedDate={selectedDate}
                  onDateChange={setSelectedDate}
                  onMarkComplete={handleMarkComplete}
                  onEditSession={handleEditSession}
                  onDeleteSession={handleDeleteSession}
                />
              </TabsContent>

              <TabsContent value="monthly" className="mt-4 md:mt-6">
                <MonthlyView
                  sessions={sessions || []}
                  progress={progress || []}
                  selectedDate={selectedDate}
                  onDateChange={setSelectedDate}
                />
              </TabsContent>

              <TabsContent value="progress" className="mt-4 md:mt-6">
                <ProgressOverview
                  sessions={sessions || []}
                  subjects={subjects || []}
                  progress={progress || []}
                  streaks={streaks || []}
                />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Create Session Modal */}
        <CreateSessionModal
          open={showCreateModal}
          onOpenChange={(open) => {
            setShowCreateModal(open);
            if (!open) {
              setEditingSession(null);
            }
          }}
          subjects={subjects || []}
          onCreateSession={handleCreateSession}
          onCreateSubject={handleCreateSubject}
          editingSession={editingSession}
        />
      </div>
    </div>
  );
};

export default ReadingTimetable;
