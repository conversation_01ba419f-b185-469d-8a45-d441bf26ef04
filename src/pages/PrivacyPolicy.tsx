
import React from "react";
import SimpleHeader from "@/components/SimpleHeader";

export default function PrivacyPolicy() {
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-white via-[#ecd7fa] to-[#b475ea]">
      <SimpleHeader />
      <main className="flex-1 flex flex-col items-center justify-center px-4 py-10">
        <div className="max-w-2xl w-full bg-white rounded-xl shadow-lg p-8 mt-10">
          <h1 className="text-2xl font-bold text-violet-700 mb-4 text-center">Privacy Policy</h1>
          <p className="text-slate-700 mb-4 text-center">
            Your privacy is important to us. This policy explains how we collect, use, and protect your personal information.
          </p>
          <ul className="list-disc ml-8 text-slate-600 mb-4">
            <li>We do not share your data with third parties.</li>
            <li>Your personal information remains confidential.</li>
            <li>You can request to delete your account at any time.</li>
          </ul>
          <p className="text-slate-600 text-sm text-center">
            For more details or questions, <NAME_EMAIL>.
          </p>
        </div>
      </main>
      <footer className="p-4 text-xs text-slate-400 text-center mt-auto">
        &copy; {new Date().getFullYear()} StudyHub. All rights reserved.
      </footer>
    </div>
  );
}
