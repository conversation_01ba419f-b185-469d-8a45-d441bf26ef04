
import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { woocommerceService } from '@/services/woocommerceService';
import { toast } from 'sonner';

const PaymentCallback = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState<'loading' | 'success' | 'failed'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const handleCallback = async () => {
      const orderId = searchParams.get('order-id') || searchParams.get('order_id');
      const orderKey = searchParams.get('key');

      // Check if this is a success callback
      if (window.location.pathname.includes('order-received') ||
          searchParams.get('status') === 'success' ||
          orderId) {
        setStatus('success');
        setMessage('Payment successful! Your subscription is now active.');
        toast.success('Welcome to StudyFam Premium! 🎉');

        // Notify parent window if in iframe
        if (window.parent !== window) {
          window.parent.postMessage({
            type: 'woocommerce_payment_success',
            orderId: orderId,
            orderKey: orderKey,
          }, '*');
        }
      } else {
        setStatus('failed');
        setMessage('Payment was not completed successfully.');
        toast.error('Payment failed or was cancelled');
      }
    };

    handleCallback();
  }, [searchParams]);

  const handleContinue = () => {
    if (status === 'success') {
      navigate('/subscription');
    } else {
      navigate('/subscription');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4">
            {status === 'loading' && (
              <div className="bg-blue-100 w-full h-full rounded-full flex items-center justify-center">
                <Loader2 className="w-8 h-8 text-blue-600 animate-spin" />
              </div>
            )}
            {status === 'success' && (
              <div className="bg-green-100 w-full h-full rounded-full flex items-center justify-center">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            )}
            {status === 'failed' && (
              <div className="bg-red-100 w-full h-full rounded-full flex items-center justify-center">
                <XCircle className="w-8 h-8 text-red-600" />
              </div>
            )}
          </div>
          <CardTitle>
            {status === 'loading' && 'Verifying Payment...'}
            {status === 'success' && 'Payment Successful!'}
            {status === 'failed' && 'Payment Failed'}
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-gray-600">{message}</p>
          
          {status !== 'loading' && (
            <Button onClick={handleContinue} className="w-full">
              {status === 'success' ? 'Continue to Dashboard' : 'Try Again'}
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default PaymentCallback;
