import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Crown, CreditCard, CheckCircle, Loader2, ArrowLeft } from 'lucide-react';
import { useUser } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';

export default function SimplePayment() {
  const { data: user } = useUser();
  const navigate = useNavigate();
  const [step, setStep] = useState<'payment' | 'confirm' | 'processing' | 'success'>('payment');

  // Check if user just returned from payment
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const returnFromPayment = urlParams.get('return');
    
    if (returnFromPayment === 'true') {
      setStep('confirm');
    }
  }, []);

  const handlePayNow = () => {
    if (!user?.id) {
      alert('Please log in first');
      return;
    }

    // Store user info for when they return
    localStorage.setItem('studyfam_payment_user', user.id);
    localStorage.setItem('studyfam_payment_time', Date.now().toString());

    // Redirect to WooCommerce with return URL
    const returnUrl = encodeURIComponent(`${window.location.origin}/simple-payment?return=true`);
    const paymentUrl = `https://studyfam.co.ke/product/studyfam-subscripton/?return_url=${returnUrl}&user_id=${user.id}`;
    
    window.location.href = paymentUrl;
  };

  const handleConfirmPayment = async () => {
    if (!user?.id) return;

    setStep('processing');

    try {
      // Get the active subscription plan
      const { data: plan } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .single();

      if (!plan) {
        throw new Error('No active subscription plan found');
      }

      // Calculate subscription period (1 month from now)
      const now = new Date();
      const periodEnd = new Date(now);
      periodEnd.setMonth(periodEnd.getMonth() + 1);

      // Create or update user subscription
      const { error: subscriptionError } = await supabase
        .from('user_subscriptions')
        .upsert({
          user_id: user.id,
          plan_id: plan.id,
          status: 'active',
          current_period_start: now.toISOString(),
          current_period_end: periodEnd.toISOString(),
          payment_provider: 'woocommerce',
          payment_reference: `manual_${Date.now()}`,
          last_payment_date: now.toISOString(),
          is_trial: false,
        }, {
          onConflict: 'user_id',
        });

      if (subscriptionError) {
        throw subscriptionError;
      }

      // Clear payment tracking
      localStorage.removeItem('studyfam_payment_user');
      localStorage.removeItem('studyfam_payment_time');

      setStep('success');

      // Redirect to dashboard after 3 seconds
      setTimeout(() => {
        navigate('/dashboard');
      }, 3000);

    } catch (error) {
      console.error('Error activating subscription:', error);
      alert('Failed to activate subscription. Please contact support.');
      setStep('confirm');
    }
  };

  const handleBackToDashboard = () => {
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center mb-4">
            <Crown className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl">StudyFam Premium</CardTitle>
          <CardDescription>
            Unlock all premium features
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {step === 'payment' && (
            <>
              <div className="space-y-3 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Unlimited AI Notes & Tutoring</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Advanced Study Tools</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Priority Support</span>
                </div>
              </div>

              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">$9.99</div>
                <div className="text-sm text-gray-500">per month</div>
              </div>

              <Button onClick={handlePayNow} className="w-full" size="lg">
                <CreditCard className="w-4 h-4 mr-2" />
                Pay Now
              </Button>

              <Button onClick={handleBackToDashboard} variant="outline" className="w-full">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </>
          )}

          {step === 'confirm' && (
            <>
              <div className="text-center">
                <div className="text-lg font-semibold text-green-600 mb-2">
                  Welcome Back!
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  If you completed your payment on the StudyFam website, click the button below to activate your subscription.
                </p>
              </div>

              <Button onClick={handleConfirmPayment} className="w-full" size="lg">
                <CheckCircle className="w-4 h-4 mr-2" />
                Confirm Payment & Activate
              </Button>

              <Button onClick={handlePayNow} variant="outline" className="w-full">
                <CreditCard className="w-4 h-4 mr-2" />
                Back to Payment
              </Button>
            </>
          )}

          {step === 'processing' && (
            <div className="text-center py-8">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-purple-600" />
              <div className="text-lg font-semibold mb-2">Activating Subscription...</div>
              <p className="text-sm text-gray-600">Please wait while we set up your premium account.</p>
            </div>
          )}

          {step === 'success' && (
            <div className="text-center py-8">
              <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-500" />
              <div className="text-xl font-bold text-green-600 mb-2">Welcome to Premium!</div>
              <p className="text-sm text-gray-600 mb-4">
                Your subscription has been activated successfully. Redirecting to dashboard...
              </p>
              <Button onClick={handleBackToDashboard} className="w-full">
                Go to Dashboard
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
