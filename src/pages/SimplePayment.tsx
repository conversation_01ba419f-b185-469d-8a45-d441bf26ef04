import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Crown, CreditCard, CheckCircle, Loader2, ArrowLeft, RefreshCw } from 'lucide-react';
import { useUser } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import { woocommerce } from '@/services/woocommerce';
import { toast } from 'sonner';

export default function SimplePayment() {
  const { data: user } = useUser();
  const navigate = useNavigate();
  const [step, setStep] = useState<'payment' | 'creating' | 'checkout' | 'processing' | 'success'>('payment');
  const [currentOrder, setCurrentOrder] = useState<any>(null);
  const [checkingPayment, setCheckingPayment] = useState(false);

  // Check if user has pending order
  useEffect(() => {
    const checkPendingOrder = async () => {
      if (!user?.id) return;

      const orderId = localStorage.getItem('studyfam_pending_order');
      if (orderId) {
        setCurrentOrder({ id: parseInt(orderId) });
        setStep('checkout');
      }
    };

    checkPendingOrder();
  }, [user?.id]);

  const handleCreateOrder = async () => {
    if (!user?.id || !user?.email) {
      toast.error('Please log in first');
      return;
    }

    setStep('creating');

    try {
      // Create order via WooCommerce API
      const order = await woocommerce.createOrder({
        email: user.email,
        firstName: user.user_metadata?.full_name?.split(' ')[0] || 'Student',
        lastName: user.user_metadata?.full_name?.split(' ')[1] || '',
        userId: user.id,
      });

      console.log('✅ Order created:', order);
      setCurrentOrder(order);

      // Store order ID for tracking
      localStorage.setItem('studyfam_pending_order', order.id.toString());
      localStorage.setItem('studyfam_order_key', order.order_key);

      // Get checkout URL
      const checkoutUrl = woocommerce.getOrderCheckoutUrl(order.id, order.order_key);

      toast.success('Order created! Redirecting to payment...');
      setStep('checkout');

      // Redirect to WooCommerce checkout
      setTimeout(() => {
        window.location.href = checkoutUrl;
      }, 1500);

    } catch (error) {
      console.error('❌ Error creating order:', error);
      toast.error('Failed to create order. Please try again.');
      setStep('payment');
    }
  };

  const handleCheckPaymentStatus = async () => {
    if (!currentOrder?.id) return;

    setCheckingPayment(true);

    try {
      // Check if order is paid via API
      const isPaid = await woocommerce.isOrderPaid(currentOrder.id);

      if (isPaid) {
        toast.success('Payment confirmed! Activating subscription...');
        setStep('processing');

        // Clear pending order
        localStorage.removeItem('studyfam_pending_order');
        localStorage.removeItem('studyfam_order_key');

        // Wait a moment for webhook to process
        setTimeout(() => {
          setStep('success');
          setTimeout(() => navigate('/dashboard'), 3000);
        }, 2000);

      } else {
        toast.info('Payment not yet confirmed. Please complete your payment first.');
      }
    } catch (error) {
      console.error('❌ Error checking payment status:', error);
      toast.error('Failed to check payment status. Please try again.');
    } finally {
      setCheckingPayment(false);
    }
  };

  // Removed handleConfirmPayment - now using API-based order tracking

  const handleBackToDashboard = () => {
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center mb-4">
            <Crown className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl">StudyFam Premium</CardTitle>
          <CardDescription>
            Unlock all premium features
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {step === 'payment' && (
            <>
              <div className="space-y-3 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Unlimited AI Notes & Tutoring</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Advanced Study Tools</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Priority Support</span>
                </div>
              </div>

              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">$9.99</div>
                <div className="text-sm text-gray-500">per month</div>
              </div>

              <Button onClick={handleCreateOrder} className="w-full" size="lg">
                <CreditCard className="w-4 h-4 mr-2" />
                Create Order & Pay
              </Button>

              <Button onClick={handleBackToDashboard} variant="outline" className="w-full">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </>
          )}

          {step === 'creating' && (
            <div className="text-center py-8">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-purple-600" />
              <div className="text-lg font-semibold mb-2">Creating Your Order...</div>
              <p className="text-sm text-gray-600">Please wait while we prepare your subscription order.</p>
            </div>
          )}

          {step === 'checkout' && (
            <>
              <div className="text-center">
                <div className="text-lg font-semibold text-blue-600 mb-2">
                  Order Created!
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  Your order #{currentOrder?.id} has been created. Complete your payment and return here to check status.
                </p>
              </div>

              <Button onClick={handleCheckPaymentStatus} className="w-full" size="lg" disabled={checkingPayment}>
                {checkingPayment ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Checking Payment...
                  </>
                ) : (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Check Payment Status
                  </>
                )}
              </Button>

              <Button
                onClick={() => {
                  const checkoutUrl = woocommerce.getOrderCheckoutUrl(currentOrder.id, currentOrder.order_key || '');
                  window.open(checkoutUrl, '_blank');
                }}
                variant="outline"
                className="w-full"
              >
                <CreditCard className="w-4 h-4 mr-2" />
                Complete Payment
              </Button>
            </>
          )}

          {step === 'processing' && (
            <div className="text-center py-8">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-purple-600" />
              <div className="text-lg font-semibold mb-2">Activating Subscription...</div>
              <p className="text-sm text-gray-600">Please wait while we set up your premium account.</p>
            </div>
          )}

          {step === 'success' && (
            <div className="text-center py-8">
              <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-500" />
              <div className="text-xl font-bold text-green-600 mb-2">Welcome to Premium!</div>
              <p className="text-sm text-gray-600 mb-4">
                Your subscription has been activated successfully. Redirecting to dashboard...
              </p>
              <Button onClick={handleBackToDashboard} className="w-full">
                Go to Dashboard
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
