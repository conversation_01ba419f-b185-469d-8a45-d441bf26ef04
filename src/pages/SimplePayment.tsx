import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Crown, CreditCard, CheckCircle, Loader2, ArrowLeft, RefreshCw } from 'lucide-react';
import { useUser } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import { woocommerce } from '@/services/woocommerce';
import { toast } from 'sonner';

export default function SimplePayment() {
  const { data: user } = useUser();
  const navigate = useNavigate();
  const [step, setStep] = useState<'payment' | 'creating' | 'checkout' | 'processing' | 'success'>('payment');
  const [currentOrder, setCurrentOrder] = useState<any>(null);
  const [checkingPayment, setCheckingPayment] = useState(false);

  // Check if user has pending payment or just returned
  useEffect(() => {
    const checkPendingPayment = async () => {
      if (!user?.id) return;

      const paymentUser = localStorage.getItem('studyfam_payment_user');
      const trackingId = localStorage.getItem('studyfam_tracking_id');
      const urlParams = new URLSearchParams(window.location.search);
      const returnFromPayment = urlParams.get('return') === 'true';

      if (paymentUser === user.id && trackingId) {
        setCurrentOrder({
          id: trackingId,
          tracking_id: trackingId,
          user_id: user.id
        });
        setStep('checkout');

        if (returnFromPayment) {
          toast.info('Welcome back! Click "Check Payment Status" to verify your payment.');
        }
      }
    };

    checkPendingPayment();
  }, [user?.id]);

  const handleCreateOrder = async () => {
    if (!user?.id || !user?.email) {
      toast.error('Please log in first');
      return;
    }

    setStep('creating');

    try {
      // Store user info for tracking
      localStorage.setItem('studyfam_payment_user', user.id);
      localStorage.setItem('studyfam_payment_email', user.email);
      localStorage.setItem('studyfam_payment_time', Date.now().toString());

      // Create a simple tracking ID
      const trackingId = `sf_${Date.now()}_${user.id.slice(-8)}`;
      localStorage.setItem('studyfam_tracking_id', trackingId);

      // Get product URL with tracking parameters and return URL
      const returnUrl = encodeURIComponent(`${window.location.origin}/simple-payment?return=true`);
      const productUrl = `https://studyfam.co.ke/product/studyfam-subscripton/?user_id=${user.id}&tracking_id=${trackingId}&email=${encodeURIComponent(user.email)}&return_url=${returnUrl}`;

      console.log('🛒 Redirecting to product page:', productUrl);

      toast.success('Redirecting to payment page...');
      setStep('checkout');

      // Set current order info for tracking
      setCurrentOrder({
        id: trackingId,
        tracking_id: trackingId,
        user_id: user.id
      });

      // Redirect to WooCommerce product page
      setTimeout(() => {
        window.location.href = productUrl;
      }, 1500);

    } catch (error) {
      console.error('❌ Error preparing checkout:', error);
      toast.error('Failed to prepare checkout. Please try again.');
      setStep('payment');
    }
  };

  const handleCheckPaymentStatus = async () => {
    if (!user?.id) return;

    setCheckingPayment(true);

    try {
      console.log('🔍 Checking payment status for user:', user.id);

      // Check if user now has an active subscription
      const { data: subscription, error: subscriptionError } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      if (subscriptionError && subscriptionError.code !== 'PGRST116') {
        // PGRST116 is "no rows returned" which is expected if no subscription
        console.error('❌ Error checking subscription:', subscriptionError);
        throw subscriptionError;
      }

      console.log('📊 Subscription check result:', subscription);

      if (subscription) {
        toast.success('Payment confirmed! Welcome to StudyFam Premium! 🎉');
        setStep('processing');

        // Clear tracking data
        localStorage.removeItem('studyfam_payment_user');
        localStorage.removeItem('studyfam_payment_email');
        localStorage.removeItem('studyfam_payment_time');
        localStorage.removeItem('studyfam_tracking_id');

        // Show success and redirect
        setTimeout(() => {
          setStep('success');
          setTimeout(() => navigate('/dashboard'), 3000);
        }, 2000);

      } else {
        console.log('ℹ️ No active subscription found');
        toast.info('Payment not yet confirmed. Please complete your payment first, then try again.');
      }
    } catch (error) {
      console.error('❌ Error checking subscription status:', error);
      toast.error(`Failed to check payment status: ${error.message}`);
    } finally {
      setCheckingPayment(false);
    }
  };

  const handleManualActivation = async () => {
    if (!user?.id) return;

    setStep('processing');

    try {
      console.log('🔄 Starting manual activation for user:', user.id);

      // Get the active subscription plan
      const { data: plan, error: planError } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .single();

      if (planError) {
        console.error('❌ Error fetching plan:', planError);
        throw new Error(`Failed to fetch subscription plan: ${planError.message}`);
      }

      if (!plan) {
        throw new Error('No active subscription plan found');
      }

      console.log('✅ Found active plan:', plan);

      // Calculate subscription period based on plan interval
      const now = new Date();
      const periodEnd = new Date(now);

      if (plan.interval_type === 'daily') {
        periodEnd.setDate(periodEnd.getDate() + (plan.interval_count || 1));
      } else if (plan.interval_type === 'monthly') {
        periodEnd.setMonth(periodEnd.getMonth() + (plan.interval_count || 1));
      } else if (plan.interval_type === 'yearly') {
        periodEnd.setFullYear(periodEnd.getFullYear() + (plan.interval_count || 1));
      } else {
        // Default to 1 month
        periodEnd.setMonth(periodEnd.getMonth() + 1);
      }

      console.log('📅 Subscription period:', now.toISOString(), 'to', periodEnd.toISOString());

      // Create or update user subscription
      const subscriptionData = {
        user_id: user.id,
        plan_id: plan.id,
        status: 'active',
        current_period_start: now.toISOString(),
        current_period_end: periodEnd.toISOString(),
        payment_provider: 'woocommerce',
        payment_reference: localStorage.getItem('studyfam_tracking_id') || `manual_${Date.now()}`,
        last_payment_date: now.toISOString(),
        is_trial: false,
      };

      console.log('💾 Creating subscription with data:', subscriptionData);

      // Now we can use upsert with the unique constraint
      const { error: subscriptionError, data: subscriptionResult } = await supabase
        .from('user_subscriptions')
        .upsert(subscriptionData, {
          onConflict: 'user_id',
        })
        .select();

      if (subscriptionError) {
        console.error('❌ Subscription error:', subscriptionError);
        throw new Error(`Failed to create subscription: ${subscriptionError.message}`);
      }

      console.log('✅ Subscription created/updated:', subscriptionResult);

      // Clear tracking data
      localStorage.removeItem('studyfam_payment_user');
      localStorage.removeItem('studyfam_payment_email');
      localStorage.removeItem('studyfam_payment_time');
      localStorage.removeItem('studyfam_tracking_id');

      toast.success('Subscription activated successfully! 🎉');
      setStep('success');

      // Redirect to dashboard after 3 seconds
      setTimeout(() => {
        navigate('/dashboard');
      }, 3000);

    } catch (error) {
      console.error('❌ Error activating subscription:', error);
      toast.error(`Failed to activate subscription: ${error.message}`);
      setStep('checkout');
    }
  };

  // Removed handleConfirmPayment - now using API-based order tracking

  const handleBackToDashboard = () => {
    navigate('/dashboard');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center mb-4">
            <Crown className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl">StudyFam Premium</CardTitle>
          <CardDescription>
            Unlock all premium features
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {step === 'payment' && (
            <>
              <div className="space-y-3 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Unlimited AI Notes & Tutoring</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Advanced Study Tools</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Priority Support</span>
                </div>
              </div>

              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">$9.99</div>
                <div className="text-sm text-gray-500">per month</div>
              </div>

              <Button onClick={handleCreateOrder} className="w-full" size="lg">
                <CreditCard className="w-4 h-4 mr-2" />
                Create Order & Pay
              </Button>

              <Button onClick={handleBackToDashboard} variant="outline" className="w-full">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>

              {process.env.NODE_ENV === 'development' && (
                <div className="mt-4 p-3 bg-gray-100 rounded text-xs">
                  <p><strong>Debug Info:</strong></p>
                  <p>User ID: {user?.id}</p>
                  <p>Email: {user?.email}</p>
                </div>
              )}
            </>
          )}

          {step === 'creating' && (
            <div className="text-center py-8">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-purple-600" />
              <div className="text-lg font-semibold mb-2">Creating Your Order...</div>
              <p className="text-sm text-gray-600">Please wait while we prepare your subscription order.</p>
            </div>
          )}

          {step === 'checkout' && (
            <>
              <div className="text-center">
                <div className="text-lg font-semibold text-blue-600 mb-2">
                  Order Created!
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  Your order #{currentOrder?.id} has been created. Complete your payment and return here to check status.
                </p>
              </div>

              <Button onClick={handleCheckPaymentStatus} className="w-full" size="lg" disabled={checkingPayment}>
                {checkingPayment ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Checking Payment...
                  </>
                ) : (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Check Payment Status
                  </>
                )}
              </Button>

              <Button
                onClick={() => {
                  const productUrl = `https://studyfam.co.ke/product/studyfam-subscripton/?user_id=${user?.id}`;
                  window.open(productUrl, '_blank');
                }}
                variant="outline"
                className="w-full"
              >
                <CreditCard className="w-4 h-4 mr-2" />
                Complete Payment
              </Button>

              <div className="text-center">
                <p className="text-xs text-gray-500 mb-2">
                  Already completed payment?
                </p>
                <Button
                  onClick={handleManualActivation}
                  variant="ghost"
                  size="sm"
                  className="text-green-600 hover:text-green-700"
                >
                  <CheckCircle className="w-4 h-4 mr-1" />
                  Activate Subscription
                </Button>
              </div>
            </>
          )}

          {step === 'processing' && (
            <div className="text-center py-8">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-purple-600" />
              <div className="text-lg font-semibold mb-2">Activating Subscription...</div>
              <p className="text-sm text-gray-600">Please wait while we set up your premium account.</p>
            </div>
          )}

          {step === 'success' && (
            <div className="text-center py-8">
              <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-500" />
              <div className="text-xl font-bold text-green-600 mb-2">Welcome to Premium!</div>
              <p className="text-sm text-gray-600 mb-4">
                Your subscription has been activated successfully. Redirecting to dashboard...
              </p>
              <Button onClick={handleBackToDashboard} className="w-full">
                Go to Dashboard
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
