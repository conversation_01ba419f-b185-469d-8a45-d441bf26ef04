import React, { useState, useEffect } from "react";
import PageHeader from "@/components/PageHeader";
import ExamModal, { Exam } from "@/components/revision-planner/ExamModal";
import Preferences from "@/components/revision-planner/Preferences";
import ScheduleView, { ScheduledSession } from "@/components/revision-planner/ScheduleView";
import { toast } from "sonner";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar, Clock, Target, TrendingUp, BookOpen, CheckCircle } from "lucide-react";
import AuthGuard from "@/components/AuthGuard";
import {
  useExams,
  useStudySessions,
  useCreateExam,
  useCreateStudySession,
  useUpdateSessionCompletion,
  useDeleteExam
} from "@/hooks/useRevisionPlanner";
import { format, addDays, differenceInDays, isToday, isPast } from "date-fns";

function autoGenerateSchedule(
  exams: Exam[],
  hoursPerDay: number
): ScheduledSession[] {
  // For demonstration, assign 1 topic per day per exam, earliest exam first, higher difficulty = more sessions.
  const sessions: ScheduledSession[] = [];
  const today = new Date();
  exams
    .sort((a, b) => a.date.getTime() - b.date.getTime())
    .forEach((exam) => {
      const daysAvailable = Math.max(
        1,
        Math.floor((exam.date.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
      );
      // Give more sessions if hard, less if easy
      const numSessions = Math.min(daysAvailable, exam.difficulty * 3);
      for (let i = 0; i < numSessions; i++) {
        const studyDate = new Date();
        studyDate.setDate(today.getDate() + i);
        sessions.push({
          id: `${exam.id}-${i}`,
          examId: exam.id,
          date: studyDate,
          subject: exam.subject,
          topic: exam.title,
          isDone: false,
        });
      }
    });
  return sessions;
}

const LOCALSTORAGE_EXAMS_KEY = "revisionPlanner_exams_v1";

const RevisionPlanner = () => {
  const [studyHours, setStudyHours] = useState(2);
  const [activeView, setActiveView] = useState<"daily" | "weekly">("daily");
  const [activeTab, setActiveTab] = useState("overview");
  const [showExamModal, setShowExamModal] = useState(false);

  // Backend hooks
  const { data: exams = [], isLoading: examsLoading } = useExams();
  const { data: studySessions = [], isLoading: sessionsLoading } = useStudySessions();
  const createExamMutation = useCreateExam();
  const createSessionMutation = useCreateStudySession();
  const updateSessionMutation = useUpdateSessionCompletion();
  const deleteExamMutation = useDeleteExam();

  // Auto-generate study sessions when exams are added
  const autoGenerateStudySessions = async (exam: any) => {
    const daysUntilExam = differenceInDays(new Date(exam.exam_date), new Date());
    const difficultyMultiplier = exam.difficulty === 'easy' ? 1 : exam.difficulty === 'medium' ? 2 : 3;
    const sessionsNeeded = Math.min(daysUntilExam, difficultyMultiplier * 3);

    for (let i = 0; i < sessionsNeeded; i++) {
      const sessionDate = format(addDays(new Date(), i), 'yyyy-MM-dd');

      if (new Date(sessionDate) < new Date(exam.exam_date)) {
        await createSessionMutation.mutateAsync({
          exam_id: exam.id,
          title: `Study: ${exam.title}`,
          subject: exam.subject,
          session_date: sessionDate,
          duration_minutes: studyHours * 60,
        });
      }
    }
  };

  // Extract subject from title or use default
  const extractSubjectFromTitle = (title: string): string => {
    const titleLower = title.toLowerCase();
    const subjects = {
      'math': ['math', 'algebra', 'geometry', 'calculus', 'statistics'],
      'science': ['physics', 'chemistry', 'biology', 'science'],
      'english': ['english', 'literature', 'writing', 'essay'],
      'history': ['history', 'social studies', 'geography'],
      'computer science': ['programming', 'coding', 'computer', 'software'],
      'art': ['art', 'drawing', 'painting', 'design'],
      'music': ['music', 'piano', 'guitar', 'singing'],
      'pe': ['pe', 'physical education', 'sports', 'fitness']
    };

    for (const [subject, keywords] of Object.entries(subjects)) {
      if (keywords.some(keyword => titleLower.includes(keyword))) {
        return subject.charAt(0).toUpperCase() + subject.slice(1);
      }
    }

    return 'General';
  };

  // Handle adding exam
  const handleAddExam = async (examData: any) => {
    try {
      // Convert difficulty number to string
      const difficultyMap = ['easy', 'medium', 'hard'];
      const difficulty = difficultyMap[examData.difficulty - 1] as 'easy' | 'medium' | 'hard';

      // Extract subject from title if not provided
      const subject = examData.subject === 'General'
        ? extractSubjectFromTitle(examData.title)
        : examData.subject;

      const newExam = await createExamMutation.mutateAsync({
        title: examData.title,
        subject,
        exam_date: format(examData.date, 'yyyy-MM-dd'),
        difficulty,
        description: examData.description || '',
      });

      // Auto-generate study sessions
      await autoGenerateStudySessions(newExam);
      toast.success(`${examData.title} added successfully!`);
    } catch (error) {
      console.error('Error adding exam:', error);
      toast.error('Failed to add exam');
    }
  };

  // Handle marking session as done
  const handleMarkDone = async (sessionId: string) => {
    const session = studySessions.find(s => s.id === sessionId);
    if (session) {
      await updateSessionMutation.mutateAsync({
        sessionId,
        isCompleted: !session.is_completed,
      });
    }
  };

  // Calculate statistics
  const stats = {
    totalExams: exams.length,
    upcomingExams: exams.filter(e => new Date(e.exam_date) >= new Date()).length,
    completedSessions: studySessions.filter(s => s.is_completed).length,
    totalSessions: studySessions.length,
    studyStreak: 7, // TODO: Calculate actual streak
  };

  // Get upcoming deadlines
  const upcomingDeadlines = exams
    .filter(e => new Date(e.exam_date) >= new Date())
    .sort((a, b) => new Date(a.exam_date).getTime() - new Date(b.exam_date).getTime())
    .slice(0, 5);

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gradient-to-br from-white via-[#ecd7fa] to-[#b475ea] pb-20 md:pb-0">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <PageHeader
            title="Revision Planner"
            description="Smart study scheduling for your exams and assignments"
            buttonLabel="Add Exam/Assignment"
            onButtonClick={() => setShowExamModal(true)}
          />

          <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-8">
            <TabsList className="grid w-full grid-cols-4 mb-8">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="schedule">Schedule</TabsTrigger>
              <TabsTrigger value="exams">Exams</TabsTrigger>
              <TabsTrigger value="progress">Progress</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <Card className="p-4 bg-white/80 backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <BookOpen className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-gray-800">{stats.totalExams}</p>
                      <p className="text-sm text-gray-600">Total Exams</p>
                    </div>
                  </div>
                </Card>

                <Card className="p-4 bg-white/80 backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <Calendar className="w-5 h-5 text-orange-600" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-gray-800">{stats.upcomingExams}</p>
                      <p className="text-sm text-gray-600">Upcoming</p>
                    </div>
                  </div>
                </Card>

                <Card className="p-4 bg-white/80 backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-gray-800">{stats.completedSessions}</p>
                      <p className="text-sm text-gray-600">Completed</p>
                    </div>
                  </div>
                </Card>

                <Card className="p-4 bg-white/80 backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Clock className="w-5 h-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-gray-800">{stats.totalSessions}</p>
                      <p className="text-sm text-gray-600">Sessions</p>
                    </div>
                  </div>
                </Card>

                <Card className="p-4 bg-white/80 backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-yellow-100 rounded-lg">
                      <TrendingUp className="w-5 h-5 text-yellow-600" />
                    </div>
                    <div>
                      <p className="text-2xl font-bold text-gray-800">{stats.studyStreak}</p>
                      <p className="text-sm text-gray-600">Day Streak</p>
                    </div>
                  </div>
                </Card>
              </div>

              {/* Quick Actions */}
              <div className="grid md:grid-cols-2 gap-6">
                <Card className="p-6 bg-white/80 backdrop-blur-sm">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    Quick Actions
                  </h3>
                  <div className="space-y-3">
                    <ExamModal onAdd={handleAddExam} />
                    <Preferences hours={studyHours} setHours={setStudyHours} />
                  </div>
                </Card>

                <Card className="p-6 bg-white/80 backdrop-blur-sm">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    Upcoming Deadlines
                  </h3>
                  <div className="space-y-3">
                    {upcomingDeadlines.length === 0 ? (
                      <p className="text-gray-500 text-sm">No upcoming exams</p>
                    ) : (
                      upcomingDeadlines.map((exam) => (
                        <div key={exam.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium text-sm">{exam.title}</p>
                            {exam.subject !== 'General' && (
                              <p className="text-xs text-gray-600">{exam.subject}</p>
                            )}
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium">
                              {format(new Date(exam.exam_date), 'MMM d')}
                            </p>
                            <p className="text-xs text-gray-600">
                              {differenceInDays(new Date(exam.exam_date), new Date())} days
                            </p>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="schedule" className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold">Study Schedule</h2>
                <div className="flex gap-2">
                  <Button
                    variant={activeView === "daily" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setActiveView("daily")}
                  >
                    Daily View
                  </Button>
                  <Button
                    variant={activeView === "weekly" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setActiveView("weekly")}
                  >
                    Weekly View
                  </Button>
                </div>
              </div>

              {/* Schedule View Component */}
              <div className="space-y-4">
                {studySessions.length === 0 ? (
                  <Card className="p-8 text-center bg-white/80 backdrop-blur-sm">
                    <Calendar className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-600 mb-2">No study sessions scheduled</h3>
                    <p className="text-gray-500 mb-4">Add an exam to automatically generate study sessions</p>
                    <ExamModal onAdd={handleAddExam} />
                  </Card>
                ) : (
                  <div className="grid gap-4">
                    {studySessions
                      .filter(session => new Date(session.session_date) >= new Date())
                      .slice(0, 10)
                      .map((session) => (
                        <Card key={session.id} className="p-4 bg-white/80 backdrop-blur-sm">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                              <div className={`p-2 rounded-lg ${session.is_completed ? 'bg-green-100' : 'bg-blue-100'}`}>
                                <BookOpen className={`w-5 h-5 ${session.is_completed ? 'text-green-600' : 'text-blue-600'}`} />
                              </div>
                              <div>
                                <h4 className="font-medium">{session.title}</h4>
                                <p className="text-sm text-gray-600">{session.subject}</p>
                                <p className="text-xs text-gray-500">
                                  {format(new Date(session.session_date), 'MMM d, yyyy')} • {session.duration_minutes} min
                                </p>
                              </div>
                            </div>
                            <Button
                              variant={session.is_completed ? "default" : "outline"}
                              size="sm"
                              onClick={() => handleMarkDone(session.id)}
                            >
                              {session.is_completed ? 'Completed' : 'Mark Done'}
                            </Button>
                          </div>
                        </Card>
                      ))}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="exams" className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold">Your Exams</h2>
                <ExamModal onAdd={handleAddExam} />
              </div>

              <div className="grid gap-4">
                {exams.length === 0 ? (
                  <Card className="p-8 text-center bg-white/80 backdrop-blur-sm">
                    <BookOpen className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-600 mb-2">No exams added yet</h3>
                    <p className="text-gray-500 mb-4">Start by adding your first exam or assignment</p>
                    <ExamModal onAdd={handleAddExam} />
                  </Card>
                ) : (
                  exams.map((exam) => (
                    <Card key={exam.id} className="p-6 bg-white/80 backdrop-blur-sm">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-lg font-semibold">{exam.title}</h3>
                          {exam.subject !== 'General' && (
                            <p className="text-gray-600">{exam.subject}</p>
                          )}
                          <p className="text-sm text-gray-500 mt-1">
                            {format(new Date(exam.exam_date), 'MMMM d, yyyy')} •
                            Difficulty: {exam.difficulty.charAt(0).toUpperCase() + exam.difficulty.slice(1)}
                          </p>
                          {exam.description && (
                            <p className="text-sm text-gray-600 mt-2">{exam.description}</p>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                            differenceInDays(new Date(exam.exam_date), new Date()) <= 3
                              ? 'bg-red-100 text-red-800'
                              : differenceInDays(new Date(exam.exam_date), new Date()) <= 7
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-green-100 text-green-800'
                          }`}>
                            {differenceInDays(new Date(exam.exam_date), new Date())} days left
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => deleteExamMutation.mutate(exam.id)}
                          >
                            Delete
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))
                )}
              </div>
            </TabsContent>

            <TabsContent value="progress" className="space-y-6">
              <Card className="p-6 bg-white/80 backdrop-blur-sm">
                <h3 className="text-lg font-semibold mb-4">Study Progress</h3>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Sessions Completed</span>
                      <span>{stats.completedSessions}/{stats.totalSessions}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-600 h-2 rounded-full"
                        style={{ width: `${stats.totalSessions > 0 ? (stats.completedSessions / stats.totalSessions) * 100 : 0}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mt-6">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <p className="text-2xl font-bold text-blue-600">{stats.studyStreak}</p>
                      <p className="text-sm text-blue-600">Day Streak</p>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <p className="text-2xl font-bold text-green-600">
                        {Math.round((stats.completedSessions / Math.max(stats.totalSessions, 1)) * 100)}%
                      </p>
                      <p className="text-sm text-green-600">Completion Rate</p>
                    </div>
                  </div>
                </div>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Controlled ExamModal for PageHeader button */}
      <ExamModal
        onAdd={handleAddExam}
        open={showExamModal}
        onOpenChange={setShowExamModal}
        showTrigger={false}
      />
    </AuthGuard>
  );
};

export default RevisionPlanner;
