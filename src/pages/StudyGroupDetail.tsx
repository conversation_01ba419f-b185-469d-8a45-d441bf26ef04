
import React, { useEffect } from "react";
import { useParams, useNavigate, useSearchParams } from "react-router-dom";
import StudyGroupsPage from "@/components/StudyGroups/StudyGroupsPage";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Loader2 } from "lucide-react";
import FloatingPostButton from "@/components/StudyGroups/FloatingPostButton";
import { useStudyGroupDetails, useCheckGroupMembership } from "@/hooks/useStudyGroups";

// Hardcoded list of example group IDs for matching - now unused

export default function StudyGroupDetail() {
  const { groupId } = useParams<{ groupId: string }>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Use backend hooks to get group details and membership status
  const { data: group, isLoading, error } = useStudyGroupDetails(groupId || '');
  const { data: membership } = useCheckGroupMembership(groupId || '');

  // Get shared post ID from URL parameters
  const sharedPostId = searchParams.get('post');

  // Defensive: If invalid groupId, redirect back
  useEffect(() => {
    if (!groupId) {
      navigate("/study-groups");
    }
  }, [groupId, navigate]);

  // Redirect if group not found after loading
  useEffect(() => {
    if (!isLoading && !group && !error) {
      navigate("/study-groups");
    }
  }, [isLoading, group, error, navigate]);

  // Loading state
  if (isLoading) {
    return (
      <div className="relative min-h-screen bg-gradient-to-br from-violet-900 via-purple-900 to-indigo-900 overflow-hidden pb-20 md:pb-0">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-pink-500/20 to-violet-500/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-3xl animate-pulse delay-500"></div>
        </div>

        <div className="relative z-10 max-w-5xl mx-auto pt-6 px-1 sm:px-6 lg:px-4 flex items-center gap-2">
          <Button
            size="sm"
            variant="ghost"
            className="mb-4 bg-white/10 backdrop-blur-sm text-white border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-105 shadow-xl"
            onClick={() => navigate("/study-groups")}
          >
            <ArrowLeft className="w-5 h-5 mr-1" />
            Back to Groups
          </Button>
        </div>
        <div className="relative z-10 flex items-center justify-center h-64">
          <div className="bg-white/10 backdrop-blur-2xl rounded-3xl p-8 shadow-2xl border border-white/20 animate-pulse">
            <Loader2 className="w-8 h-8 animate-spin text-cyan-300 mx-auto" />
            <span className="ml-2 text-white/80 block mt-4 text-center">Loading group details...</span>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !group) {
    return (
      <div className="relative min-h-screen bg-gradient-to-br from-violet-900 via-purple-900 to-indigo-900 overflow-hidden pb-20 md:pb-0">
        {/* Animated background elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-pink-500/20 to-violet-500/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div className="relative z-10 max-w-5xl mx-auto pt-6 px-1 sm:px-6 lg:px-4 flex items-center gap-2">
          <Button
            size="sm"
            variant="ghost"
            className="mb-4 bg-white/10 backdrop-blur-sm text-white border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-105 shadow-xl"
            onClick={() => navigate("/study-groups")}
          >
            <ArrowLeft className="w-5 h-5 mr-1" />
            Back to Groups
          </Button>
        </div>
        <div className="relative z-10 text-center py-16">
          <div className="bg-white/10 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/20 p-12 max-w-md mx-auto animate-fade-in">
            <h3 className="text-lg font-medium text-white mb-2">Group not found</h3>
            <p className="text-white/70">This group may have been deleted or you don't have access to it.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-violet-900 via-purple-900 to-indigo-900 overflow-hidden pb-20 md:pb-0">
      {/* Animated background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-pink-500/20 to-violet-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-3xl animate-pulse delay-500"></div>
        <div className="absolute top-10 right-1/4 w-48 h-48 bg-gradient-to-r from-indigo-500/20 to-purple-500/20 rounded-full blur-3xl animate-pulse delay-700"></div>
        <div className="absolute bottom-10 left-1/4 w-56 h-56 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-full blur-3xl animate-pulse delay-300"></div>
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-white/30 rounded-full animate-float"></div>
        <div className="absolute top-3/4 right-1/3 w-1 h-1 bg-cyan-300/50 rounded-full animate-float delay-1000"></div>
        <div className="absolute bottom-1/4 left-1/2 w-1.5 h-1.5 bg-pink-300/40 rounded-full animate-float delay-500"></div>
        <div className="absolute top-1/2 right-1/4 w-1 h-1 bg-purple-300/50 rounded-full animate-float delay-700"></div>
      </div>

      <div className="relative z-10 max-w-5xl mx-auto pt-6 px-1 sm:px-6 lg:px-4 flex items-center gap-2">
        <Button
          size="sm"
          variant="ghost"
          className="mb-4 bg-white/10 backdrop-blur-sm text-white border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-105 shadow-xl hover:shadow-2xl hover:shadow-white/10"
          onClick={() => navigate("/study-groups")}
        >
          <ArrowLeft className="w-5 h-5 mr-1" />
          Back to Groups
        </Button>
      </div>
      <div className="relative z-10 max-w-5xl mx-auto px-1 sm:px-6 lg:px-4 pb-8">
        <StudyGroupsPage
          group={group}
          isMember={membership?.isMember || false}
          isAdmin={membership?.isAdmin || false}
          highlightPostId={sharedPostId}
        />
      </div>
      {/* Floating "+" post button appears only for group members */}
      {membership?.isMember && <FloatingPostButton groupId={group.id} />}
    </div>
  );
}
