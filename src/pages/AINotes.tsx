import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Brain,
  Sparkles,
  FileText,
  Download,
  Save,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import RichTextEditor from '@/components/RichTextEditor';
import { exportToPDF, downloadAsText } from '@/utils/pdfExport';
import FolderSelector from '@/components/notes/FolderSelector';
import { useCreateNote } from '@/hooks/useNotes';
import { cleanMarkdownForDisplay, cleanMarkdownForPlainText } from '@/utils/markdownCleaner';
import { generateNotePDF } from '@/utils/pdfGenerator';
import {
  generateAINotes,
  getUserPreferences,
  type GenerateNotesRequest,
  type AINotesPreferences
} from '@/services/aiNotesService';

const AINotes: React.FC = () => {
  const [formData, setFormData] = useState<GenerateNotesRequest>({
    subject: '',
    topic: '',
    noteLength: 'medium',
    includeExamples: true,
    includeKeyPoints: true,
    includeSummary: true,
    customInstructions: ''
  });

  const [generatedContent, setGeneratedContent] = useState<string>('');
  const [editedContent, setEditedContent] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [showFolderSelector, setShowFolderSelector] = useState(false);
  const [showPreviewByDefault, setShowPreviewByDefault] = useState(false);

  const [preferences, setPreferences] = useState<AINotesPreferences | null>(null);
  const [metadata, setMetadata] = useState<any>(null);

  // Hooks for notes system
  const createNoteMutation = useCreateNote();

  useEffect(() => {
    loadUserPreferences();
  }, []);

  const loadUserPreferences = async () => {
    const result = await getUserPreferences();
    if (result.success && result.preferences) {
      setPreferences(result.preferences);
      // Apply preferences to form
      setFormData(prev => ({
        ...prev,
        noteLength: result.preferences!.default_note_length,
        includeExamples: result.preferences!.include_examples,
        includeKeyPoints: result.preferences!.include_key_points,
        includeSummary: result.preferences!.include_summary
      }));
    } else if (result.error) {
      console.warn('Could not load user preferences:', result.error);
      // Continue with default preferences
    }
  };

  const handleInputChange = (field: keyof GenerateNotesRequest, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleGenerate = async () => {
    if (!formData.subject || !formData.topic) {
      toast.error('Please enter both subject and topic');
      return;
    }

    setIsGenerating(true);
    try {
      const result = await generateAINotes(formData);
      
      if (result.success && result.content) {
        setGeneratedContent(result.content);
        setEditedContent(result.content);
        setMetadata(result.metadata);
        setShowPreviewByDefault(true); // Show in preview mode by default
        toast.success('Notes generated successfully!');
      } else {
        toast.error(result.error || 'Failed to generate notes');
      }
    } catch (error) {
      console.error('Error generating notes:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSave = () => {
    if (!generatedContent) {
      toast.error('No content to save');
      return;
    }

    if (!formData.subject || !formData.topic) {
      toast.error('Subject and topic are required');
      return;
    }

    setShowFolderSelector(true);
  };

  const handleFolderSelected = async (unitId: string, topicId: string) => {
    try {
      // Generate PDF from the AI notes content (for future use)
      generateNotePDF({
        title: `${formData.subject} - ${formData.topic}`,
        content: editedContent,
        author: 'StudyFam AI Notes',
        subject: formData.subject,
        keywords: [formData.subject, formData.topic, 'ai-generated', 'study-notes'],
        includeMetadata: true
      });

      // Create note with the edited content
      const noteData = {
        title: `${formData.subject} - ${formData.topic}`,
        content: editedContent,
        unit_id: unitId,
        topic_id: topicId,
        tags: [formData.subject, formData.topic, 'ai-generated', 'study-notes']
      };

      await createNoteMutation.mutateAsync(noteData);

      toast.success('AI Notes saved successfully!');
      setShowFolderSelector(false);

      // Reset form
      setFormData({
        subject: '',
        topic: '',
        noteLength: preferences?.default_note_length || 'medium',
        includeExamples: preferences?.include_examples ?? true,
        includeKeyPoints: preferences?.include_key_points ?? true,
        includeSummary: preferences?.include_summary ?? true,
        customInstructions: ''
      });
      setGeneratedContent('');
      setEditedContent('');
      setMetadata(null);
      setShowPreviewByDefault(false);
    } catch (error) {
      console.error('Failed to save AI notes:', error);
      toast.error('Failed to save notes. Please try again.');
    }
  };

  const handleDownloadPDF = async () => {
    if (!editedContent || !formData.subject || !formData.topic) {
      toast.error('Please generate notes first');
      return;
    }

    try {
      await exportToPDF({
        title: `${formData.subject} - ${formData.topic}`,
        subject: formData.subject,
        topic: formData.topic,
        content: editedContent,
        author: 'StudyFam User',
        includeMetadata: true
      });
      toast.success('PDF export initiated! Check your browser\'s print dialog.');
    } catch (error) {
      console.error('PDF export error:', error);
      toast.error('Failed to export PDF. Downloading as text file instead...');

      // Fallback to text download
      downloadAsText({
        title: `${formData.subject} - ${formData.topic}`,
        subject: formData.subject,
        topic: formData.topic,
        content: editedContent,
        author: 'StudyFam User'
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-pink-50 p-3 sm:p-4 md:p-6 pb-20 md:pb-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row items-center justify-center gap-2 sm:gap-3 mb-3 sm:mb-4">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-violet-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Brain className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
            </div>
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent text-center sm:text-left">
              AI Notes Generator
            </h1>
          </div>
          <p className="text-gray-600 text-base sm:text-lg px-4 sm:px-0">
            Generate comprehensive study notes instantly with AI
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          {/* Input Section */}
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-3 sm:pb-4">
              <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                <Sparkles className="w-4 h-4 sm:w-5 sm:h-5 text-violet-600" />
                Generate Notes
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 sm:space-y-4 p-4 sm:p-6">
              {/* Subject Input */}
              <div className="space-y-1.5 sm:space-y-2">
                <Label htmlFor="subject" className="text-sm sm:text-base font-medium">Subject/Unit Name</Label>
                <Input
                  id="subject"
                  placeholder="e.g., Biology, Mathematics, History"
                  value={formData.subject}
                  onChange={(e) => handleInputChange('subject', e.target.value)}
                  className="border-gray-200 focus:border-violet-400 h-10 sm:h-11"
                />
              </div>

              {/* Topic Input */}
              <div className="space-y-1.5 sm:space-y-2">
                <Label htmlFor="topic" className="text-sm sm:text-base font-medium">Topic Title</Label>
                <Input
                  id="topic"
                  placeholder="e.g., Cell Structure and Function"
                  value={formData.topic}
                  onChange={(e) => handleInputChange('topic', e.target.value)}
                  className="border-gray-200 focus:border-violet-400 h-10 sm:h-11"
                />
              </div>

              {/* Note Length */}
              <div className="space-y-1.5 sm:space-y-2">
                <Label className="text-sm sm:text-base font-medium">Note Length</Label>
                <Select
                  value={formData.noteLength}
                  onValueChange={(value) => handleInputChange('noteLength', value)}
                >
                  <SelectTrigger className="h-10 sm:h-11">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="short">Short (300-500 words)</SelectItem>
                    <SelectItem value="medium">Medium (500-800 words)</SelectItem>
                    <SelectItem value="long">Long (800-1200 words)</SelectItem>
                    <SelectItem value="detailed">Detailed (1200+ words)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Options */}
              <div className="space-y-2 sm:space-y-3">
                <Label className="text-sm sm:text-base font-medium">Include in Notes</Label>
                <div className="space-y-2 sm:space-y-2.5">
                  <div className="flex items-center space-x-2 sm:space-x-3">
                    <Checkbox
                      id="examples"
                      checked={formData.includeExamples}
                      onCheckedChange={(checked) => handleInputChange('includeExamples', checked)}
                      className="h-4 w-4 sm:h-5 sm:w-5"
                    />
                    <Label htmlFor="examples" className="text-sm sm:text-base">Examples and illustrations</Label>
                  </div>
                  <div className="flex items-center space-x-2 sm:space-x-3">
                    <Checkbox
                      id="keypoints"
                      checked={formData.includeKeyPoints}
                      onCheckedChange={(checked) => handleInputChange('includeKeyPoints', checked)}
                      className="h-4 w-4 sm:h-5 sm:w-5"
                    />
                    <Label htmlFor="keypoints" className="text-sm sm:text-base">Key points section</Label>
                  </div>
                  <div className="flex items-center space-x-2 sm:space-x-3">
                    <Checkbox
                      id="summary"
                      checked={formData.includeSummary}
                      onCheckedChange={(checked) => handleInputChange('includeSummary', checked)}
                      className="h-4 w-4 sm:h-5 sm:w-5"
                    />
                    <Label htmlFor="summary" className="text-sm sm:text-base">Summary section</Label>
                  </div>
                </div>
              </div>

              {/* Custom Instructions */}
              <div className="space-y-1.5 sm:space-y-2">
                <Label htmlFor="instructions" className="text-sm sm:text-base font-medium">Custom Instructions (Optional)</Label>
                <Textarea
                  id="instructions"
                  placeholder="Any specific requirements or focus areas..."
                  value={formData.customInstructions}
                  onChange={(e) => handleInputChange('customInstructions', e.target.value)}
                  className="border-gray-200 focus:border-violet-400 min-h-[70px] sm:min-h-[80px] text-sm sm:text-base"
                />
              </div>

              {/* Generate Button */}
              <Button
                onClick={handleGenerate}
                disabled={isGenerating || !formData.subject || !formData.topic}
                className="w-full bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white shadow-lg h-11 sm:h-12 text-sm sm:text-base"
                size="lg"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    <span className="hidden sm:inline">Generating Notes...</span>
                    <span className="sm:hidden">Generating...</span>
                  </>
                ) : (
                  <>
                    <Sparkles className="w-4 h-4 mr-2" />
                    <span className="hidden sm:inline">Generate AI Notes</span>
                    <span className="sm:hidden">Generate Notes</span>
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Preview/Edit Section */}
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="pb-3 sm:pb-4">
              <CardTitle className="flex items-center gap-2 text-lg sm:text-xl">
                <FileText className="w-4 h-4 sm:w-5 sm:h-5 text-violet-600" />
                {generatedContent ? 'Generated Notes' : 'Notes Preview'}
              </CardTitle>
              {metadata && (
                <div className="flex flex-wrap gap-1.5 sm:gap-2 mt-2">
                  <Badge variant="secondary" className="text-xs sm:text-sm">{metadata.wordCount} words</Badge>
                  <Badge variant="secondary" className="text-xs sm:text-sm">{metadata.tokensUsed} tokens</Badge>
                  <Badge variant="secondary" className="text-xs sm:text-sm">{metadata.generationTime}ms</Badge>
                  <Badge variant="secondary" className="text-xs sm:text-sm hidden sm:inline-flex">{metadata.model}</Badge>
                </div>
              )}
            </CardHeader>
            <CardContent className="p-4 sm:p-6">
              {!generatedContent ? (
                <div className="text-center py-8 sm:py-12 text-gray-500">
                  <Brain className="w-10 h-10 sm:w-12 sm:h-12 mx-auto mb-3 sm:mb-4 opacity-50" />
                  <p className="text-base sm:text-lg font-medium mb-2">No notes generated yet</p>
                  <p className="text-sm px-4 sm:px-0">Fill in the subject and topic, then click "Generate AI Notes"</p>
                </div>
              ) : (
                <div className="space-y-3 sm:space-y-4">
                  {/* Rich Text Editor */}
                  <RichTextEditor
                    value={editedContent}
                    onChange={setEditedContent}
                    placeholder="Generated notes will appear here for editing..."
                    className="border-gray-200 focus-within:border-violet-400"
                    defaultPreviewMode={showPreviewByDefault}
                  />

                  <Separator />

                  {/* Save Options */}
                  <div className="space-y-3 sm:space-y-4">
                    <Label className="text-sm sm:text-base font-medium">Save Options</Label>

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
                      <Button
                        onClick={handleSave}
                        disabled={createNoteMutation.isPending}
                        className="w-full sm:flex-1 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white h-10 sm:h-11 text-sm sm:text-base"
                      >
                        {createNoteMutation.isPending ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            <span className="hidden sm:inline">Saving...</span>
                            <span className="sm:hidden">Saving...</span>
                          </>
                        ) : (
                          <>
                            <Save className="w-4 h-4 mr-2" />
                            <span className="hidden sm:inline">Save to Notes</span>
                            <span className="sm:hidden">Save Notes</span>
                          </>
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={handleDownloadPDF}
                        className="w-full sm:flex-1 h-10 sm:h-11 text-sm sm:text-base"
                      >
                        <Download className="w-4 h-4 mr-2" />
                        <span className="hidden sm:inline">Download PDF</span>
                        <span className="sm:hidden">PDF</span>
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Folder Selector Dialog */}
        <FolderSelector
          open={showFolderSelector}
          onOpenChange={setShowFolderSelector}
          onFolderSelected={handleFolderSelected}
          isLoading={createNoteMutation.isPending}
        />
      </div>
    </div>
  );
};

export default AINotes;
