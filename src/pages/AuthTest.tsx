import React, { useState } from 'react';
import { useUser, useSignIn } from '@/hooks/useAuth';
import { useProfile } from '@/hooks/useProfile';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Link } from 'react-router-dom';
import { toast } from 'sonner';

const AuthTest: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('');

  const { data: user, isLoading: userLoading, error: userError } = useUser();
  const { data: profile, isLoading: profileLoading, error: profileError } = useProfile();
  const signInMutation = useSignIn();

  const handleQuickLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await signInMutation.mutateAsync({ email, password });
      toast.success('Signed in successfully!');
    } catch (error: any) {
      toast.error(error.message || 'Failed to sign in');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-900 via-purple-900 to-indigo-900 p-8">
      <div className="max-w-2xl mx-auto bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-8">
        <h1 className="text-3xl font-bold text-white mb-8 text-center">Authentication Test</h1>
        
        <div className="space-y-6">
          {/* User Status */}
          <div className="bg-white/5 rounded-xl p-6">
            <h2 className="text-xl font-semibold text-white mb-4">User Authentication</h2>
            {userLoading && (
              <div className="text-yellow-400">Loading user...</div>
            )}
            {userError && (
              <div className="text-red-400">Error: {userError.message}</div>
            )}
            {user && (
              <div className="text-green-400">
                <p>✓ User authenticated</p>
                <p className="text-sm text-gray-300">Email: {user.email}</p>
                <p className="text-sm text-gray-300">ID: {user.id}</p>
              </div>
            )}
            {!user && !userLoading && (
              <div className="text-red-400">❌ Not authenticated</div>
            )}
          </div>

          {/* Profile Status */}
          <div className="bg-white/5 rounded-xl p-6">
            <h2 className="text-xl font-semibold text-white mb-4">Profile Data</h2>
            {profileLoading && (
              <div className="text-yellow-400">Loading profile...</div>
            )}
            {profileError && (
              <div className="text-red-400">Error: {profileError.message}</div>
            )}
            {profile && (
              <div className="text-green-400">
                <p>✓ Profile loaded</p>
                <p className="text-sm text-gray-300">Name: {profile.full_name}</p>
                <p className="text-sm text-gray-300">Email: {profile.email}</p>
                <p className="text-sm text-gray-300">Country: {profile.country || 'Not set'}</p>
                <p className="text-sm text-gray-300">Course: {profile.course || 'Not set'}</p>
              </div>
            )}
            {!profile && !profileLoading && (
              <div className="text-red-400">❌ No profile data</div>
            )}
          </div>

          {/* Quick Login Form */}
          {!user && (
            <div className="bg-white/5 rounded-xl p-6">
              <h2 className="text-xl font-semibold text-white mb-4">Quick Login Test</h2>
              <form onSubmit={handleQuickLogin} className="space-y-4">
                <div>
                  <label className="block text-white text-sm mb-2">Email</label>
                  <Input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="bg-white/10 border-white/20 text-white"
                    placeholder="Enter email"
                  />
                </div>
                <div>
                  <label className="block text-white text-sm mb-2">Password</label>
                  <Input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="bg-white/10 border-white/20 text-white"
                    placeholder="Enter password"
                  />
                </div>
                <Button
                  type="submit"
                  disabled={signInMutation.isPending}
                  className="w-full bg-blue-600 hover:bg-blue-700"
                >
                  {signInMutation.isPending ? 'Signing in...' : 'Sign In'}
                </Button>
              </form>
            </div>
          )}

          {/* Navigation */}
          <div className="flex gap-4 justify-center">
            <Link to="/login">
              <Button className="bg-blue-600 hover:bg-blue-700">
                Go to Login
              </Button>
            </Link>
            <Link to="/register">
              <Button className="bg-green-600 hover:bg-green-700">
                Go to Register
              </Button>
            </Link>
            <Link to="/profile">
              <Button className="bg-purple-600 hover:bg-purple-700">
                Go to Profile
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthTest;
