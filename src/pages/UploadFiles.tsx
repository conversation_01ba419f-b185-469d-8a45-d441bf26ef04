
import React, { useRef, useState } from "react";
import PageHeader from "@/components/PageHeader";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { Upload, Trash2 } from "lucide-react";

// Helper for formatting file size
const formatSize = (size: number) => {
  if (size < 1024) return size + " B";
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + " KB";
  return (size / (1024 * 1024)).toFixed(1) + " MB";
};

const UploadFiles = () => {
  const [search, setSearch] = useState("");
  const [files, setFiles] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);

  // Add files, avoiding duplicates by name
  const handleAddFiles = (fileList: FileList | null) => {
    if (!fileList) return;
    const newFiles: File[] = Array.from(fileList).filter(
      f => !files.some(existing => existing.name === f.name && existing.size === f.size)
    );
    if (newFiles.length) {
      setFiles(prev => [...prev, ...newFiles]);
      toast({ title: "Files uploaded!", description: `${newFiles.length} file(s) added.` });
    }
  };

  // Handle input file change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleAddFiles(e.target.files);
    if (fileInputRef.current) fileInputRef.current.value = "";
  };

  // Drag/Drop Handlers
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(true);
  };
  const handleDragLeave = () => setDragActive(false);
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setDragActive(false);
    handleAddFiles(e.dataTransfer.files);
  };

  // Remove file
  const handleRemoveFile = (index: number) => {
    const removedFile = files[index];
    setFiles(f => f.filter((_, i) => i !== index));
    toast({ title: "File deleted", description: removedFile.name });
  };

  // Filter files via search bar
  const filteredFiles = files.filter((f) =>
    f.name.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className="max-w-4xl mx-auto px-4 py-12 pb-20 md:pb-12">
      <PageHeader
        title="Upload Files"
        description="Add your study materials here. Search, upload, and manage files."
        buttonLabel="Upload New File"
        onButtonClick={() => fileInputRef.current?.click()}
      >
        <form className="flex gap-3" onSubmit={e => e.preventDefault()}>
          <Input
            value={search}
            onChange={e => setSearch(e.target.value)}
            placeholder="Search your files..."
            className="max-w-xs"
          />
          <Button variant="outline" type="submit" className="flex-shrink-0">
            Search
          </Button>
        </form>
      </PageHeader>

      <div
        className={`border-2 border-dashed rounded-xl p-8 bg-white flex flex-col items-center transition-all cursor-pointer mb-8 ${
          dragActive ? "border-violet-500 bg-violet-50" : "border-slate-200"
        }`}
        tabIndex={0}
        onClick={() => fileInputRef.current?.click()}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        aria-label="Drag and drop files here or click to browse"
      >
        <Upload size={48} className="mb-2 text-violet-600" />
        <div className="font-semibold text-lg text-violet-700 mb-1">
          Drag &amp; drop files here, or <span className="underline cursor-pointer text-blue-600">browse</span>
        </div>
        <div className="text-slate-500 text-sm mb-2">
          Supported: PDFs, DOCX, images, slides, and more.
        </div>
        <Input
          type="file"
          multiple
          className="hidden"
          ref={fileInputRef}
          onChange={handleInputChange}
        />
      </div>

      {/* Files List */}
      <div className="p-8 bg-white rounded-xl shadow w-full transition-all">
        <h2 className="font-semibold text-violet-700 mb-4 flex items-center gap-2">
          <Upload size={20} /> Your files
        </h2>
        {filteredFiles.length === 0 ? (
          <p className="text-slate-600 text-sm">No files uploaded yet.</p>
        ) : (
          <div className="overflow-auto max-h-72">
            <table className="min-w-full text-left divide-y divide-slate-200">
              <thead>
                <tr>
                  <th className="py-2 px-2 text-xs text-slate-700 font-semibold">File Name</th>
                  <th className="py-2 px-2 text-xs text-slate-700 font-semibold">Size</th>
                  <th className="py-2 px-2 text-xs text-slate-700 font-semibold"></th>
                </tr>
              </thead>
              <tbody>
                {filteredFiles.map((f, idx) => (
                  <tr key={f.name + f.size} className="hover:bg-slate-50 transition">
                    <td className="py-2 px-2">{f.name}</td>
                    <td className="py-2 px-2">{formatSize(f.size)}</td>
                    <td className="py-2 px-2 text-right">
                      <Button
                        variant="ghost"
                        size="icon"
                        aria-label={`Delete ${f.name}`}
                        onClick={e => {
                          e.stopPropagation();
                          handleRemoveFile(
                            files.findIndex(
                              file =>
                                file.name === f.name && file.size === f.size
                            )
                          );
                        }}
                      >
                        <Trash2 size={18} className="text-destructive" />
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default UploadFiles;
