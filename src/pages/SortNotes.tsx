import React, { useState } from "react";
import PageHeader from "@/components/PageHeader";
import { Folder, FileText, Plus, Search, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import NoteCard from "@/components/sort-notes/NoteCard";
import FileItem from "@/components/sort-notes/FileItem";
import UploadNotesModal from "@/components/sort-notes/UploadNotesModal";
import CreateUnitModal from "@/components/sort-notes/CreateUnitModal";
import CreateTopicModal from "@/components/sort-notes/CreateTopicModal";
import CreateNoteModal from "@/components/sort-notes/CreateNoteModal";
import DocumentViewer from "@/components/DocumentViewer";
import { ShareModal } from "@/components/sharing/ShareModal";
import { useUnits, useTopics, useNotes } from "@/hooks/useNotes";
import { useDocumentViewer } from "@/hooks/useDocumentViewer";
import AuthGuard from "@/components/AuthGuard";
import { supabase } from "@/integrations/supabase/client";
import { useEffect } from "react";

type NavigationLevel =
  | { level: "root" }
  | { level: "unit"; unitId: string }
  | { level: "topic"; unitId: string; topicId: string };

const SortNotes = () => {
  const [nav, setNav] = useState<NavigationLevel>({ level: "root" });
  const [search, setSearch] = useState("");
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showCreateUnitModal, setShowCreateUnitModal] = useState(false);
  const [showCreateTopicModal, setShowCreateTopicModal] = useState(false);
  const [showCreateNoteModal, setShowCreateNoteModal] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [itemToShare, setItemToShare] = useState<{
    id: string;
    title: string;
    type: 'note' | 'past_paper';
    content?: string;
    file_url?: string;
  } | null>(null);

  // Document viewer hook
  const { isOpen: isDocumentViewerOpen, currentDocument, openDocument, closeDocument } = useDocumentViewer();

  // Hooks for data fetching
  const { data: units = [], isLoading: unitsLoading } = useUnits();
  const { data: topics = [], isLoading: topicsLoading } = useTopics(
    nav.level === "unit" || nav.level === "topic" ? nav.unitId : ""
  );
  const { data: notes = [], isLoading: notesLoading } = useNotes(
    nav.level === "topic" ? nav.topicId : ""
  );

  // Get current unit and topic for breadcrumbs and modals
  const currentUnit = nav.level === "unit" || nav.level === "topic"
    ? units.find(u => u.id === nav.unitId)
    : null;
  const currentTopic = nav.level === "topic"
    ? topics.find(t => t.id === nav.topicId)
    : null;

  // Handle sharing
  const handleShareNote = (note: any) => {
    setItemToShare({
      id: note.id,
      title: note.title,
      type: 'note',
      content: note.content,
      file_url: note.file_url,
    });
    setShowShareModal(true);
  };

  // Debug authentication
  useEffect(() => {
    const checkAuth = async () => {
      const { data: { user }, error } = await supabase.auth.getUser();
      console.log('SortNotes - Current user:', user);
      console.log('SortNotes - Auth error:', error);
    };
    checkAuth();
  }, []);

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gradient-to-br from-violet-900 via-purple-900 to-indigo-900 relative pb-20 md:pb-0">
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 right-4 sm:right-20 w-48 sm:w-72 h-48 sm:h-72 bg-gradient-to-br from-pink-400/20 to-rose-600/20 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 left-4 sm:left-20 w-48 sm:w-80 h-48 sm:h-80 bg-gradient-to-br from-cyan-400/15 to-blue-600/15 rounded-full blur-3xl"></div>
        </div>
        
        <div className="relative z-10 mx-auto max-w-5xl w-full py-10 px-3">
          {/* Updated PageHeader with glass-morphism style */}
          <div className="mb-8">
            <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-6">
              <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
                <div>
                  <h1 className="text-3xl font-bold text-white mb-2">Sort Notes</h1>
                  <p className="text-white/70">Organize your PDF notes by subject and unit.</p>
                </div>
                <div className="flex flex-col lg:flex-row items-start lg:items-center gap-4 w-full lg:w-auto">
                  {/* Search bar */}
                  <div className="relative flex-1 lg:w-96">
                    <Search size={20} className="absolute left-3 top-1/2 -translate-y-1/2 text-white/50" />
                    <input
                      type="text"
                      placeholder="Search files, folders..."
                      className="w-full rounded-lg pl-10 pr-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-white/50 focus:bg-white/20 focus:outline-none focus:ring-2 focus:ring-white/30 transition"
                      aria-label="Search files, folders"
                      value={search}
                      onChange={e => setSearch(e.target.value)}
                    />
                  </div>
                  <Button
                    onClick={() => setShowUploadModal(true)}
                    className="bg-white text-violet-900 hover:bg-gray-100 font-medium px-6 py-2"
                  >
                    Upload New File
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Breadcrumb Navigation */}
          {nav.level !== "root" && (
            <div className="flex items-center gap-2 mb-6 text-sm">
              <button
                className="flex items-center gap-1 text-white/80 hover:text-white transition-colors"
                onClick={() => setNav({ level: "root" })}
              >
                <ArrowLeft size={16} />
                All Units
              </button>
              {currentUnit && (
                <>
                  <span className="text-white/50">/</span>
                  <span className="text-white">{currentUnit.name}</span>
                </>
              )}
              {currentTopic && (
                <>
                  <span className="text-white/50">/</span>
                  <span className="text-white">{currentTopic.name}</span>
                </>
              )}
            </div>
          )}

          {/* Main content grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-7">
            {/* Root: show units */}
            {nav.level === "root" && (
              <>
                {unitsLoading ? (
                  <div className="col-span-full flex justify-center py-8">
                    <div className="text-white/70">Loading units...</div>
                  </div>
                ) : (
                  <>
                    {units
                      .filter(unit =>
                        search === "" ||
                        unit.name.toLowerCase().includes(search.toLowerCase())
                      )
                      .map((unit) => (
                        <div key={unit.id} className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl p-6 hover:bg-white/20 transition-all cursor-pointer" onClick={() => setNav({ level: "unit", unitId: unit.id })}>
                          <NoteCard
                            type="subject"
                            name={unit.name}
                            color={`bg-[${unit.color}20] text-[${unit.color}]`}
                            unitsCount={unit.topic_count}
                            onClick={() => setNav({ level: "unit", unitId: unit.id })}
                          />
                        </div>
                      ))}

                    {/* Add Unit button */}
                    <button
                      className="flex flex-col items-center justify-center border-2 border-dashed border-white/30 rounded-xl min-h-[180px] bg-white/5 text-white/70 hover:bg-white/10 hover:text-white transition-all text-base font-medium outline-none backdrop-blur-sm"
                      onClick={() => setShowCreateUnitModal(true)}
                    >
                      <Plus size={28} />
                      Add Unit
                    </button>
                  </>
                )}
              </>
            )}

            {/* Unit: show topics */}
            {nav.level === "unit" && (
              <>
                {topicsLoading ? (
                  <div className="col-span-full flex justify-center py-8">
                    <div className="text-white/70">Loading topics...</div>
                  </div>
                ) : (
                  <>
                    {topics
                      .filter(topic =>
                        search === "" ||
                        topic.name.toLowerCase().includes(search.toLowerCase())
                      )
                      .map((topic) => (
                        <div key={topic.id} className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl p-6 hover:bg-white/20 transition-all">
                          <NoteCard
                            type="unit"
                            name={topic.name}
                            color="bg-blue-200 text-blue-600"
                            filesCount={topic.note_count}
                            onClick={() => setNav({ level: "topic", unitId: nav.unitId, topicId: topic.id })}
                            onAdd={(e) => {
                              e.stopPropagation();
                              setShowCreateNoteModal(true);
                            }}
                          />
                        </div>
                      ))}

                    {/* Add Topic button */}
                    <button
                      className="flex flex-col items-center justify-center border-2 border-dashed border-white/30 rounded-xl min-h-[180px] bg-white/5 text-white/70 hover:bg-white/10 hover:text-white transition-all text-base font-medium outline-none backdrop-blur-sm"
                      onClick={() => setShowCreateTopicModal(true)}
                    >
                      <Plus size={28} />
                      Add Topic
                    </button>

                    {topics.length === 0 && (
                      <div className="col-span-full flex flex-col items-center justify-center text-white/50 italic min-h-[90px]">
                        No topics in this unit yet. Click "Add Topic" to get started.
                      </div>
                    )}
                  </>
                )}
              </>
            )}

            {/* Topic: show notes */}
            {nav.level === "topic" && (
              <>
                {notesLoading ? (
                  <div className="col-span-full flex justify-center py-8">
                    <div className="text-white/70">Loading notes...</div>
                  </div>
                ) : (
                  <>
                    {notes
                      .filter(note =>
                        search === "" ||
                        note.title.toLowerCase().includes(search.toLowerCase())
                      )
                      .map((note) => (
                        <div key={note.id} className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl p-6 hover:bg-white/20 transition-all">
                          <NoteCard
                            type="file"
                            name={note.title}
                            hasFile={!!note.file_url}
                            onClick={note.file_url ? () => openDocument({
                              url: note.file_url!,
                              name: note.title,
                              title: note.title
                            }) : undefined}
                            onShare={(e) => {
                              e.stopPropagation();
                              handleShareNote(note);
                            }}
                          />
                        </div>
                      ))}

                    {/* Add Note button */}
                    <button
                      className="flex flex-col items-center justify-center border-2 border-dashed border-white/30 rounded-xl min-h-[180px] bg-white/5 text-white/70 hover:bg-white/10 hover:text-white transition-all text-base font-medium outline-none backdrop-blur-sm"
                      onClick={() => setShowCreateNoteModal(true)}
                    >
                      <Plus size={28} />
                      Add Note
                    </button>

                    {notes.length === 0 && (
                      <div className="col-span-full flex flex-col items-center justify-center text-white/50 italic min-h-[90px]">
                        No notes in this topic yet. Click "Add Note" to get started.
                      </div>
                    )}
                  </>
                )}
              </>
            )}
          </div>

          {/* Upload Modal */}
          <UploadNotesModal
            open={showUploadModal}
            onOpenChange={setShowUploadModal}
          />

          {/* Create Unit Modal */}
          <CreateUnitModal
            open={showCreateUnitModal}
            onOpenChange={setShowCreateUnitModal}
          />

          {/* Create Topic Modal */}
          <CreateTopicModal
            open={showCreateTopicModal}
            onOpenChange={setShowCreateTopicModal}
            unitId={nav.level === "unit" || nav.level === "topic" ? nav.unitId : ""}
            unitName={currentUnit?.name}
          />

          {/* Create Note Modal */}
          {(nav.level === "unit" || nav.level === "topic") && (
            <CreateNoteModal
              open={showCreateNoteModal}
              onOpenChange={setShowCreateNoteModal}
              unitId={nav.unitId}
              topicId={nav.level === "topic" ? nav.topicId : undefined}
              unitName={currentUnit?.name}
              topicName={currentTopic?.name}
            />
          )}

          {/* Share Modal */}
          {itemToShare && (
            <ShareModal
              open={showShareModal}
              onOpenChange={setShowShareModal}
              item={itemToShare}
            />
          )}

          {/* Document Viewer */}
          {currentDocument && (
            <DocumentViewer
              open={isDocumentViewerOpen}
              onOpenChange={closeDocument}
              fileUrl={currentDocument.url}
              fileName={currentDocument.name}
              title={currentDocument.title}
            />
          )}
        </div>
      </div>
    </AuthGuard>
  );
};

export default SortNotes;
