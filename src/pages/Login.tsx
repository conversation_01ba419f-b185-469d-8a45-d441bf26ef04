
import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Link, useLocation } from "react-router-dom";
import SimpleHeader from "@/components/SimpleHeader";
import { useSignIn } from "@/hooks/useAuth";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

export default function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const location = useLocation();
  const signInMutation = useSignIn();

  // Get the intended destination from location state
  const from = location.state?.from || '/dashboard';

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !password) {
      toast.error("Please fill in all fields");
      return;
    }

    try {
      await signInMutation.mutateAsync({ email, password });
      // Success toast is now handled in the auth hook
    } catch (error: any) {
      // Error toast is now handled in the auth hook
      console.error('Login error:', error);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-white via-[#ecd7fa] to-[#b475ea]">
      <SimpleHeader />
      <div className="flex flex-col items-center justify-center flex-1">
        <div className="bg-white shadow-2xl rounded-2xl px-7 py-10 w-full max-w-md mx-auto">
          <h2 className="text-2xl font-extrabold text-center text-violet-800 mb-2">Sign in to StudyFam</h2>
          <p className="text-slate-600 text-center mb-4">Welcome back! Please enter your details.</p>
          {from !== '/dashboard' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
              <p className="text-blue-800 text-sm text-center">
                Please sign in to continue to your requested page.
              </p>
            </div>
          )}
          <form className="space-y-5" onSubmit={handleSubmit}>
            <div>
              <Label htmlFor="email" className="mb-1 block">Email</Label>
              <Input
                type="email"
                id="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                autoFocus
              />
            </div>
            <div>
              <Label htmlFor="password" className="mb-1 block">Password</Label>
              <Input
                type="password"
                id="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                minLength={6}
              />
            </div>
            <button
              type="submit"
              disabled={signInMutation.isPending}
              className="w-full bg-violet-600 hover:bg-violet-700 transition text-white rounded-lg font-bold py-3 text-lg mt-3 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {signInMutation.isPending && <Loader2 className="w-4 h-4 animate-spin" />}
              {signInMutation.isPending ? "Signing In..." : "Sign In"}
            </button>
          </form>
          <div className="mt-5 text-center text-slate-600">
            Don't have an account?{" "}
            <Link to="/register" className="text-violet-700 font-semibold hover:underline">Register</Link>
          </div>
        </div>
      </div>
    </div>
  );
}
