@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    /* Site-wide gradient background from white to light purple */
    @apply text-foreground;
    background: linear-gradient(135deg, #fff 0%, #ecd7fa 45%, #e2baff 100%);
  }
}

@layer utilities {
  .bg-grid-pattern {
    background-image: radial-gradient(circle, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }
  
  .animation-delay-2000 {
    animation-delay: 2s;
  }
  
  .animation-delay-4000 {
    animation-delay: 4s;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(99, 102, 241, 0.6);
  }
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

/* React Quill Custom Styles */
.ql-editor {
  min-height: 300px !important;
  font-size: 16px;
  line-height: 1.6;
  color: #374151;
}

.ql-editor.ql-blank::before {
  color: #9ca3af;
  font-style: italic;
}

.ql-toolbar {
  border-top: 1px solid #e5e7eb !important;
  border-left: 1px solid #e5e7eb !important;
  border-right: 1px solid #e5e7eb !important;
  border-bottom: none !important;
  background: #f9fafb;
  border-radius: 8px 8px 0 0;
}

.ql-container {
  border-bottom: 1px solid #e5e7eb !important;
  border-left: 1px solid #e5e7eb !important;
  border-right: 1px solid #e5e7eb !important;
  border-top: none !important;
  border-radius: 0 0 8px 8px;
  background: white;
}

.ql-toolbar .ql-stroke {
  stroke: #6b7280;
}

.ql-toolbar .ql-fill {
  fill: #6b7280;
}

.ql-toolbar .ql-picker-label {
  color: #6b7280;
}

.ql-toolbar button:hover .ql-stroke {
  stroke: #3b82f6;
}

.ql-toolbar button:hover .ql-fill {
  fill: #3b82f6;
}

.ql-toolbar button.ql-active .ql-stroke {
  stroke: #3b82f6;
}

.ql-toolbar button.ql-active .ql-fill {
  fill: #3b82f6;
}

.ql-editor h1 {
  font-size: 2em;
  font-weight: bold;
  margin: 0.67em 0;
}

.ql-editor h2 {
  font-size: 1.5em;
  font-weight: bold;
  margin: 0.83em 0;
}

.ql-editor h3 {
  font-size: 1.17em;
  font-weight: bold;
  margin: 1em 0;
}

.ql-editor h4 {
  font-size: 1em;
  font-weight: bold;
  margin: 1.33em 0;
}

.ql-editor h5 {
  font-size: 0.83em;
  font-weight: bold;
  margin: 1.67em 0;
}

.ql-editor h6 {
  font-size: 0.67em;
  font-weight: bold;
  margin: 2.33em 0;
}

.ql-editor ul, .ql-editor ol {
  padding-left: 1.5em;
}

.ql-editor blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 16px;
  margin: 16px 0;
  font-style: italic;
  color: #6b7280;
}

.ql-editor a {
  color: #3b82f6;
  text-decoration: underline;
}

.ql-editor a:hover {
  color: #1d4ed8;
}
