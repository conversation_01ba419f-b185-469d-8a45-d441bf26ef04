
import React, { useState, useRef } from 'react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { FileText, Search, Calendar, Plus, Download, Eye, Loader2, Upload, X, Share2 } from 'lucide-react';
import { ShareModal } from '@/components/sharing/ShareModal';
import { usePastPapers, useCreatePastPaper, useIncrementDownloadCount } from '@/hooks/usePastPapers';
import { useUploadFile } from '@/hooks/useNotes';
import { useToast } from '@/hooks/use-toast';

interface PastPapersProps {
  selectedCourse: any;
}

// Helper function to format file size
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Helper function to get file extension from URL
const getFileExtension = (url: string): string => {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const extension = pathname.split('.').pop()?.toLowerCase() || '';
    return extension;
  } catch {
    return '';
  }
};

// Helper function to get appropriate file name
const getDownloadFileName = (title: string, fileUrl: string): string => {
  const extension = getFileExtension(fileUrl);
  const cleanTitle = title.replace(/[^a-zA-Z0-9\s]/g, '_').replace(/\s+/g, '_');

  if (extension) {
    return `${cleanTitle}.${extension}`;
  }
  return `${cleanTitle}.pdf`; // Default to PDF if no extension found
};

export const PastPapers = ({ selectedCourse }: PastPapersProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedYear, setSelectedYear] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [paperToShare, setPaperToShare] = useState<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Form state for creating new past paper
  const [paperForm, setPaperForm] = useState({
    title: '',
    subject: '',
    university: '',
    course_code: '',
    year: new Date().getFullYear(),
    exam_type: 'Final',
    has_answers: false,
    has_marking_scheme: false,
    is_public: true,
    tags: [] as string[]
  });

  const { toast } = useToast();
  const createPaperMutation = useCreatePastPaper();
  const uploadFileMutation = useUploadFile();
  const downloadMutation = useIncrementDownloadCount();
  
  const filters = {
    subject: searchTerm ? undefined : (selectedCourse?.unit || undefined),
    year: selectedYear !== 'all' ? selectedYear : undefined,
    exam_type: selectedType !== 'all' ? selectedType : undefined,
    search: searchTerm || undefined
  };



  const { data: pastPapers = [], isLoading } = usePastPapers(filters);

  // File handling functions
  const handleFileSelect = (file: File) => {
    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'image/jpeg',
      'image/jpg',
      'image/png'
    ];

    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "Invalid file type",
        description: "Please upload a PDF, DOC, DOCX, JPG, or PNG file.",
        variant: "destructive",
      });
      return;
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      toast({
        title: "File too large",
        description: "Please upload a file smaller than 10MB.",
        variant: "destructive",
      });
      return;
    }

    setSelectedFile(file);
    // Auto-fill title if empty
    if (!paperForm.title.trim()) {
      const fileName = file.name.replace(/\.[^/.]+$/, ""); // Remove extension
      setPaperForm(prev => ({ ...prev, title: fileName }));
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = () => {
    setDragActive(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
    const file = e.dataTransfer.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const removeSelectedFile = () => {
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleCreatePaper = async () => {
    if (!paperForm.title.trim() || !paperForm.subject.trim()) {
      toast({
        title: "Error",
        description: "Please fill in the required fields.",
        variant: "destructive",
      });
      return;
    }

    if (!selectedFile) {
      toast({
        title: "Error",
        description: "Please select a file to upload.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Upload file first
      const uploadResult = await uploadFileMutation.mutateAsync(selectedFile);

      // Create past paper with file URL
      await createPaperMutation.mutateAsync({
        ...paperForm,
        file_url: uploadResult.url
      });

      toast({
        title: "Success",
        description: "Past paper uploaded successfully!",
      });

      setIsDialogOpen(false);
      setPaperForm({
        title: '',
        subject: '',
        university: '',
        course_code: '',
        year: new Date().getFullYear(),
        exam_type: 'Final',
        has_answers: false,
        has_marking_scheme: false,
        is_public: true,
        tags: []
      });
      setSelectedFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error creating past paper:', error);
      toast({
        title: "Error",
        description: "Failed to upload past paper. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleViewPaper = (fileUrl: string, title: string) => {
    if (!fileUrl) {
      toast({
        title: "Error",
        description: "No file available to view.",
        variant: "destructive",
      });
      return;
    }

    // Open the file in a new tab/window
    window.open(fileUrl, '_blank', 'noopener,noreferrer');

    toast({
      title: "Opening Paper",
      description: `Opening ${title} in a new tab...`,
    });
  };

  const handleDownloadPaper = async (paperId: string, fileUrl: string, title: string) => {
    if (!fileUrl) {
      toast({
        title: "Error",
        description: "No file available to download.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Increment download count
      await downloadMutation.mutateAsync(paperId);

      // Create a temporary link element to trigger download
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = getDownloadFileName(title, fileUrl);
      link.target = '_blank';

      // Append to body, click, and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "Download Started",
        description: `Downloading ${title}...`,
      });
    } catch (error) {
      console.error('Error downloading paper:', error);
      toast({
        title: "Error",
        description: "Failed to start download. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSharePaper = (paper: any) => {
    setPaperToShare({
      id: paper.id,
      title: paper.title,
      type: 'past_paper' as const,
      file_url: paper.file_url,
    });
    setShowShareModal(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-white">Past Papers</h2>
          <p className="text-white/70">
            Access categorized past papers with solutions for {selectedCourse?.unit || 'all subjects'}
          </p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-white text-violet-900 hover:bg-gray-100 font-medium">
              <Plus className="w-4 h-4 mr-2" />
              Upload Paper
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Upload Past Paper</DialogTitle>
              <DialogDescription>
                Share a past paper with the community to help other students study.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    placeholder="e.g., Final Exam - Data Structures"
                    value={paperForm.title}
                    onChange={(e) => setPaperForm(prev => ({ ...prev, title: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="subject">Subject *</Label>
                  <Input
                    id="subject"
                    placeholder="e.g., Computer Science"
                    value={paperForm.subject}
                    onChange={(e) => setPaperForm(prev => ({ ...prev, subject: e.target.value }))}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="university">University</Label>
                  <Input
                    id="university"
                    placeholder="e.g., MIT"
                    value={paperForm.university}
                    onChange={(e) => setPaperForm(prev => ({ ...prev, university: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="course_code">Course Code</Label>
                  <Input
                    id="course_code"
                    placeholder="e.g., CS101"
                    value={paperForm.course_code}
                    onChange={(e) => setPaperForm(prev => ({ ...prev, course_code: e.target.value }))}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="year">Year</Label>
                  <Input
                    id="year"
                    type="number"
                    min="2000"
                    max={new Date().getFullYear()}
                    value={paperForm.year}
                    onChange={(e) => setPaperForm(prev => ({ ...prev, year: parseInt(e.target.value) }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="exam_type">Exam Type</Label>
                  <Select value={paperForm.exam_type} onValueChange={(value) => setPaperForm(prev => ({ ...prev, exam_type: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Final">Final Exam</SelectItem>
                      <SelectItem value="Midterm">Midterm</SelectItem>
                      <SelectItem value="Quiz">Quiz</SelectItem>
                      <SelectItem value="Assignment">Assignment</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              {/* File Upload Area */}
              <div className="space-y-2">
                <Label>Past Paper File *</Label>
                {!selectedFile ? (
                  <div
                    className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
                      dragActive
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                    onClick={() => fileInputRef.current?.click()}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                  >
                    <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                    <p className="text-sm text-gray-600 mb-1">
                      <span className="font-medium text-blue-600">Click to upload</span> or drag and drop
                    </p>
                    <p className="text-xs text-gray-500">
                      PDF, DOC, DOCX, JPG, PNG (Max 10MB)
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      Supported formats: Academic papers, exam sheets, answer keys
                    </p>
                    <input
                      ref={fileInputRef}
                      type="file"
                      className="hidden"
                      accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                      onChange={handleFileInputChange}
                    />
                  </div>
                ) : (
                  <div className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <FileText className="w-8 h-8 text-blue-600" />
                        <div>
                          <p className="font-medium text-gray-900">{selectedFile.name}</p>
                          <p className="text-sm text-gray-500">
                            {formatFileSize(selectedFile.size)}
                          </p>
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={removeSelectedFile}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </div>
              <div className="flex gap-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={paperForm.has_answers}
                    onChange={(e) => setPaperForm(prev => ({ ...prev, has_answers: e.target.checked }))}
                  />
                  <span className="text-sm">Has Solutions</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={paperForm.has_marking_scheme}
                    onChange={(e) => setPaperForm(prev => ({ ...prev, has_marking_scheme: e.target.checked }))}
                  />
                  <span className="text-sm">Has Marking Scheme</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={paperForm.is_public}
                    onChange={(e) => setPaperForm(prev => ({ ...prev, is_public: e.target.checked }))}
                  />
                  <span className="text-sm">Make Public</span>
                </label>
              </div>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleCreatePaper}
                disabled={createPaperMutation.isPending || uploadFileMutation.isPending}
              >
                {(createPaperMutation.isPending || uploadFileMutation.isPending) ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {uploadFileMutation.isPending ? 'Uploading File...' : 'Saving Paper...'}
                  </>
                ) : (
                  'Upload Paper'
                )}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card className="bg-white/10 backdrop-blur-xl border border-white/20 p-6">
        <div className="grid md:grid-cols-3 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-white/50" />
            <input
              type="text"
              placeholder="Search papers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-white/50 rounded-lg focus:bg-white/20 focus:outline-none focus:ring-2 focus:ring-white/30 transition"
            />
          </div>
          <select
            value={selectedYear}
            onChange={(e) => setSelectedYear(e.target.value)}
            className="p-2 bg-white/10 backdrop-blur-sm border border-white/20 text-white rounded-lg focus:bg-white/20 focus:outline-none focus:ring-2 focus:ring-white/30 transition"
          >
            <option value="all" className="bg-violet-900 text-white">All Years</option>
            <option value="2024" className="bg-violet-900 text-white">2024</option>
            <option value="2023" className="bg-violet-900 text-white">2023</option>
            <option value="2022" className="bg-violet-900 text-white">2022</option>
            <option value="2021" className="bg-violet-900 text-white">2021</option>
          </select>
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="p-2 bg-white/10 backdrop-blur-sm border border-white/20 text-white rounded-lg focus:bg-white/20 focus:outline-none focus:ring-2 focus:ring-white/30 transition"
          >
            <option value="all" className="bg-violet-900 text-white">All Types</option>
            <option value="Final" className="bg-violet-900 text-white">Final Exam</option>
            <option value="Midterm" className="bg-violet-900 text-white">Midterm</option>
            <option value="Quiz" className="bg-violet-900 text-white">Quiz</option>
            <option value="Assignment" className="bg-violet-900 text-white">Assignment</option>
          </select>
        </div>
      </Card>

      {/* Papers Grid */}
      {isLoading ? (
        <div className="flex justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-white" />
        </div>
      ) : pastPapers.length > 0 ? (
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {pastPapers.map((paper) => (
            <Card key={paper.id} className="bg-white/10 backdrop-blur-xl border border-white/20 p-6 hover:bg-white/20 transition-all">
              <div className="flex items-start justify-between mb-4">
                <div className="relative">
                  <FileText className="w-8 h-8 text-blue-400" />
                  {!paper.file_url && (
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border border-white"></div>
                  )}
                </div>
                <div className="flex gap-2">
                  <span className="text-xs bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full border border-blue-400/30">
                    {paper.exam_type}
                  </span>
                  {!paper.file_url && (
                    <span className="text-xs bg-red-500/20 text-red-300 px-2 py-1 rounded-full border border-red-400/30">
                      No File
                    </span>
                  )}
                </div>
              </div>
              
              <h3 className="font-semibold text-white mb-2">{paper.title}</h3>
              
              <div className="space-y-1 mb-4">
                <p className="text-sm text-white/70 flex items-center">
                  <Calendar className="w-3 h-3 mr-1" />
                  {paper.year}
                </p>
                <p className="text-sm text-white/70">{paper.university}</p>
                <p className="text-sm text-white/70">{paper.subject}</p>
                {paper.course_code && (
                  <p className="text-sm text-white/70">{paper.course_code}</p>
                )}
              </div>

              <div className="flex flex-wrap gap-1 mb-4">
                {paper.has_answers && (
                  <span className="text-xs bg-green-500/20 text-green-300 px-2 py-1 rounded-full border border-green-400/30">
                    Solutions
                  </span>
                )}
                {paper.has_marking_scheme && (
                  <span className="text-xs bg-purple-500/20 text-purple-300 px-2 py-1 rounded-full border border-purple-400/30">
                    Marking Scheme
                  </span>
                )}
                <span className="text-xs bg-gray-500/20 text-gray-300 px-2 py-1 rounded-full border border-gray-400/30">
                  {paper.download_count} downloads
                </span>
              </div>

              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full bg-white/10 border-white/20 text-white hover:bg-white/20 hover:text-white"
                  onClick={() => handleViewPaper(paper.file_url || '', paper.title)}
                  disabled={!paper.file_url}
                >
                  <Eye className="w-3 h-3 mr-1" />
                  View Paper
                </Button>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    size="sm"
                    className="bg-white text-violet-900 hover:bg-gray-100"
                    onClick={() => handleDownloadPaper(paper.id, paper.file_url || '', paper.title)}
                    disabled={downloadMutation.isPending || !paper.file_url}
                  >
                    {downloadMutation.isPending ? (
                      <>
                        <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                        Downloading...
                      </>
                    ) : (
                      <>
                        <Download className="w-3 h-3 mr-1" />
                        Download
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-blue-500/20 border-blue-400/30 text-blue-300 hover:bg-blue-500/30"
                    onClick={() => handleSharePaper(paper)}
                  >
                    <Share2 className="w-3 h-3 mr-1" />
                    Share
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : (
        <Card className="bg-white/10 backdrop-blur-xl border border-white/20 p-12 text-center">
          <FileText className="w-16 h-16 text-white/50 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">No Past Papers Found</h3>
          <p className="text-white/70 mb-6">
            {searchTerm || selectedYear !== 'all' || selectedType !== 'all'
              ? "No papers match your current filters. Try adjusting your search criteria or upload a new paper!"
              : "No papers have been uploaded yet. Be the first to share a past paper with the community!"
            }
          </p>
          <Button
            onClick={() => setIsDialogOpen(true)}
            className="bg-white text-violet-900 hover:bg-gray-100"
          >
            <Plus className="w-4 h-4 mr-2" />
            Upload Paper
          </Button>
        </Card>
      )}

      {/* Share Modal */}
      {paperToShare && (
        <ShareModal
          open={showShareModal}
          onOpenChange={setShowShareModal}
          item={paperToShare}
        />
      )}
    </div>
  );
};
