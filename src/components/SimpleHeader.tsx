
import React from "react";
import { <PERSON> } from "react-router-dom";
import { Menu } from "lucide-react";
import { Navigation } from "@/components/Navigation";

// Public site nav items (SimpleHeader)
const publicNavLinks = [
  { to: "/about-us", label: "About Us" },
  { to: "/contact-us", label: "Contact Us" },
  { to: "/privacy-policy", label: "Privacy Policy" },
];

export default function SimpleHeader() {
  const [navDrawerOpen, setNavDrawerOpen] = React.useState(false);

  return (
    <header className="w-full bg-white/80 backdrop-blur-sm shadow-sm px-3 sm:px-7 py-3 relative z-20">
      <div className="flex items-center justify-between gap-4 max-w-7xl mx-auto">
        {/* Hamburger always visible */}
        <button
          className="inline-flex p-2 rounded-lg hover:bg-violet-50 transition focus:outline-none mr-3"
          aria-label="Open navigation"
          onClick={() => setNavDrawerO<PERSON>(true)}
        >
          <Menu className="w-7 h-7 text-violet-700" />
        </button>

        {/* Logo Centered */}
        <Link
          to="/"
          className="text-2xl sm:text-3xl font-extrabold text-[#635bff] tracking-tighter whitespace-nowrap ml-2"
          style={{ letterSpacing: "-0.02em" }}
        >
          StudyFam
        </Link>
      </div>
      {/* Navigation Drawer */}
      <Navigation
        open={navDrawerOpen}
        setOpen={setNavDrawerOpen}
        links={publicNavLinks}
        showAuthButtons={true}
      />
    </header>
  );
}
