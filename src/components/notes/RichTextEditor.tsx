import React, { useState, useRef, useCallback } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { generateAndDownloadNotePDF } from '@/utils/pdfGenerator';
import { toast } from 'sonner';
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Link,
  Image,
  Type,
  Save,
  FileText,
  Loader2,
  Eye,
  Download
} from 'lucide-react';

interface RichTextEditorProps {
  title: string;
  content: string;
  onTitleChange: (title: string) => void;
  onContentChange: (content: string) => void;
  onSave: () => void;
  isSaving?: boolean;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  title,
  content,
  onTitleChange,
  onContentChange,
  onSave,
  isSaving = false
}) => {
  const quillRef = useRef<ReactQuill>(null);
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);

  // Custom toolbar configuration
  const modules = {
    toolbar: [
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      ['link', 'image'],
      [{ 'align': [] }],
      [{ 'color': [] }, { 'background': [] }],
      ['clean']
    ],
  };

  const formats = [
    'header', 'font', 'size',
    'bold', 'italic', 'underline', 'strike', 'blockquote',
    'list', 'bullet', 'indent',
    'link', 'image', 'video',
    'align', 'color', 'background'
  ];

  // Handle content change and update stats
  const handleContentChange = useCallback((value: string) => {
    onContentChange(value);
    
    // Calculate word and character count (strip HTML tags for accurate count)
    const textContent = value.replace(/<[^>]*>/g, '').trim();
    const words = textContent.split(/\s+/).filter(word => word.length > 0);
    
    setWordCount(words.length);
    setCharCount(textContent.length);
  }, [onContentChange]);

  // Handle PDF preview
  const handlePreviewPDF = () => {
    if (!title.trim()) {
      toast.error('Please enter a note title first');
      return;
    }
    if (!content.trim()) {
      toast.error('Please enter note content first');
      return;
    }

    try {
      generateAndDownloadNotePDF({
        title: title.trim(),
        content: content.trim(),
        author: 'Study App User',
        subject: 'Study Notes Preview',
        includeMetadata: true
      });
      toast.success('PDF preview downloaded!');
    } catch (error) {
      toast.error('Failed to generate PDF preview');
    }
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 's':
          e.preventDefault();
          onSave();
          break;
        case 'p':
          e.preventDefault();
          handlePreviewPDF();
          break;
        case 'b':
          e.preventDefault();
          // Bold formatting will be handled by Quill
          break;
        case 'i':
          e.preventDefault();
          // Italic formatting will be handled by Quill
          break;
        case 'u':
          e.preventDefault();
          // Underline formatting will be handled by Quill
          break;
      }
    }
  };

  return (
    <Card className="p-6 bg-white/95 backdrop-blur-sm">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <FileText className="w-12 h-12 mx-auto text-blue-500 mb-3" />
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Create New Note</h2>
          <p className="text-gray-600">Write your notes with rich formatting options</p>
        </div>

        {/* Title Input */}
        <div className="space-y-2">
          <Label htmlFor="note-title" className="text-sm font-medium text-gray-700">
            Note Title
          </Label>
          <Input
            id="note-title"
            value={title}
            onChange={(e) => onTitleChange(e.target.value)}
            placeholder="Enter note title..."
            className="text-lg font-medium"
            onKeyDown={handleKeyDown}
          />
        </div>

        {/* Stats Bar */}
        <div className="flex flex-wrap gap-3 justify-center">
          <Badge variant="secondary" className="flex items-center gap-1">
            <Type className="w-3 h-3" />
            {charCount} characters
          </Badge>
          <Badge variant="secondary" className="flex items-center gap-1">
            <FileText className="w-3 h-3" />
            {wordCount} words
          </Badge>
        </div>

        {/* Rich Text Editor */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700">
            Note Content
          </Label>
          <div
            className="border rounded-lg overflow-hidden"
            onKeyDown={handleKeyDown}
          >
            <ReactQuill
              ref={quillRef}
              theme="snow"
              value={content}
              onChange={handleContentChange}
              modules={modules}
              formats={formats}
              placeholder="Start writing your note..."
              style={{ minHeight: '300px' }}
              preserveWhitespace
            />
          </div>
        </div>

        {/* Formatting Tips */}
        <div className="bg-blue-50 rounded-lg p-4">
          <h4 className="font-medium text-blue-800 mb-2">✨ Formatting Tips:</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-blue-700">
            <div>• <kbd>Ctrl+S</kbd> to save</div>
            <div>• <kbd>Ctrl+P</kbd> for PDF preview</div>
            <div>• <kbd>Ctrl+B</kbd> for bold</div>
            <div>• <kbd>Ctrl+I</kbd> for italic</div>
            <div>• <kbd>Ctrl+U</kbd> for underline</div>
            <div>• Use toolbar for headings</div>
            <div>• Insert images and links</div>
            <div>• Notes are saved as PDF files</div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <Button
            onClick={handlePreviewPDF}
            disabled={!title.trim() || !content.trim()}
            variant="outline"
            size="lg"
          >
            <Eye className="w-4 h-4 mr-2" />
            Preview PDF
          </Button>

          <Button
            onClick={onSave}
            disabled={!title.trim() || !content.trim() || isSaving}
            size="lg"
          >
            {isSaving ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Saving as PDF...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Save as PDF
              </>
            )}
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default RichTextEditor;
