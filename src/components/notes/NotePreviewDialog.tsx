import React from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  Calendar, 
  User, 
  Folder,
  Edit,
  Share,
  Download,
  ArrowRight,
  Tag
} from 'lucide-react';
import { Note, Unit, Topic } from '@/hooks/useNotes';

interface NotePreviewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  note: Note | null;
  unit: Unit | null;
  topic: Topic | null;
  onEdit?: () => void;
  onShare?: () => void;
  onDownload?: () => void;
}

const NotePreviewDialog: React.FC<NotePreviewDialogProps> = ({
  open,
  onOpenChange,
  note,
  unit,
  topic,
  onEdit,
  onShare,
  onDownload
}) => {
  if (!note || !unit || !topic) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const stripHtmlTags = (html: string) => {
    const doc = new DOMParser().parseFromString(html, 'text/html');
    return doc.body.textContent || '';
  };

  const getWordCount = (content: string) => {
    const text = stripHtmlTags(content);
    return text.split(/\s+/).filter(word => word.length > 0).length;
  };

  const getCharCount = (content: string) => {
    return stripHtmlTags(content).length;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Note Preview
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Note Header */}
          <div className="space-y-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-gray-800 mb-2">{note.title}</h1>
                
                {/* Metadata */}
                <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    <span>Created: {formatDate(note.created_at)}</span>
                  </div>
                  
                  {note.updated_at && note.updated_at !== note.created_at && (
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      <span>Updated: {formatDate(note.updated_at)}</span>
                    </div>
                  )}
                  
                  <div className="flex items-center gap-1">
                    <User className="w-4 h-4" />
                    <span>By you</span>
                  </div>
                </div>

                {/* Location */}
                <div className="flex items-center gap-2 mt-2">
                  <Folder className="w-4 h-4 text-gray-500" />
                  <span className="flex items-center gap-1 text-sm text-gray-600">
                    <span 
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: unit.color }}
                    />
                    {unit.name}
                  </span>
                  <ArrowRight className="w-3 h-3 text-gray-400" />
                  <span className="text-sm text-gray-600">{topic.name}</span>
                </div>

                {/* Stats */}
                <div className="flex gap-3 mt-3">
                  <Badge variant="secondary" className="text-xs">
                    {getWordCount(note.content || '')} words
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    {getCharCount(note.content || '')} characters
                  </Badge>
                  {note.tags && note.tags.length > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      {note.tags.length} tag{note.tags.length !== 1 ? 's' : ''}
                    </Badge>
                  )}
                </div>

                {/* Tags */}
                {note.tags && note.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {note.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs flex items-center gap-1">
                        <Tag className="w-3 h-3" />
                        #{tag}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                {onEdit && (
                  <Button variant="outline" size="sm" onClick={onEdit}>
                    <Edit className="w-4 h-4 mr-1" />
                    Edit
                  </Button>
                )}
                {onShare && (
                  <Button variant="outline" size="sm" onClick={onShare}>
                    <Share className="w-4 h-4 mr-1" />
                    Share
                  </Button>
                )}
                {onDownload && (
                  <Button variant="outline" size="sm" onClick={onDownload}>
                    <Download className="w-4 h-4 mr-1" />
                    Export
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Note Content */}
          <Card className="p-6 bg-gray-50">
            <div className="prose prose-gray max-w-none">
              <div 
                className="text-gray-800 leading-relaxed"
                dangerouslySetInnerHTML={{ __html: note.content || '' }}
              />
            </div>
          </Card>

          {/* File Attachment */}
          {note.file_url && (
            <Card className="p-4 bg-blue-50 border-blue-200">
              <div className="flex items-center gap-3">
                <FileText className="w-5 h-5 text-blue-600" />
                <div className="flex-1">
                  <h4 className="font-medium text-blue-800">Attached File</h4>
                  <p className="text-sm text-blue-600">Click to view or download the attached file</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(note.file_url, '_blank')}
                  className="border-blue-300 text-blue-700 hover:bg-blue-100"
                >
                  <Download className="w-4 h-4 mr-1" />
                  View File
                </Button>
              </div>
            </Card>
          )}

          {/* Footer Actions */}
          <div className="flex justify-between items-center pt-4 border-t">
            <div className="text-sm text-gray-500">
              Note ID: {note.id}
            </div>
            <Button onClick={() => onOpenChange(false)}>
              Close Preview
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default NotePreviewDialog;
