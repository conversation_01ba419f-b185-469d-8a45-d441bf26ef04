import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  Folder, 
  FolderPlus, 
  Plus, 
  ChevronRight, 
  Save,
  ArrowLeft,
  Loader2
} from 'lucide-react';
import { useUnits, useTopics, useCreateUnit, useCreateTopic } from '@/hooks/useNotes';
import { useUser } from '@/hooks/useAuth';
import { toast } from 'sonner';

interface FolderSelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onFolderSelected: (unitId: string, topicId: string) => void;
  isLoading?: boolean;
}

const FolderSelector: React.FC<FolderSelectorProps> = ({
  open,
  onOpenChange,
  onFolderSelected,
  isLoading = false
}) => {
  const [selectedUnit, setSelectedUnit] = useState<string | null>(null);
  const [selectedTopic, setSelectedTopic] = useState<string | null>(null);
  const [showCreateUnit, setShowCreateUnit] = useState(false);
  const [showCreateTopic, setShowCreateTopic] = useState(false);
  const [newUnitName, setNewUnitName] = useState('');
  const [newUnitDescription, setNewUnitDescription] = useState('');
  const [newUnitColor, setNewUnitColor] = useState('#6366f1');
  const [newTopicName, setNewTopicName] = useState('');
  const [newTopicDescription, setNewTopicDescription] = useState('');

  const { data: currentUser } = useUser();
  const { data: units = [], isLoading: unitsLoading } = useUnits();
  const { data: topics = [], isLoading: topicsLoading } = useTopics(selectedUnit || '');
  const createUnitMutation = useCreateUnit();
  const createTopicMutation = useCreateTopic();

  const colors = [
    '#6366f1', '#8b5cf6', '#ec4899', '#ef4444', '#f97316',
    '#eab308', '#22c55e', '#10b981', '#06b6d4', '#3b82f6'
  ];

  const handleCreateUnit = async () => {
    if (!newUnitName.trim()) {
      toast.error('Please enter a unit name');
      return;
    }

    if (!currentUser) {
      toast.error('You must be logged in to create a unit');
      return;
    }

    // Check if unit name already exists
    const existingUnit = units.find(unit =>
      unit.name.toLowerCase() === newUnitName.trim().toLowerCase()
    );

    if (existingUnit) {
      toast.error('A unit with this name already exists. Please choose a different name.');
      return;
    }

    try {
      console.log('Creating unit with data:', {
        name: newUnitName.trim(),
        description: newUnitDescription.trim() || undefined,
        color: newUnitColor
      });

      const unit = await createUnitMutation.mutateAsync({
        name: newUnitName.trim(),
        description: newUnitDescription.trim() || undefined,
        color: newUnitColor
      });

      console.log('Unit created successfully:', unit);
      setSelectedUnit(unit.id);
      setShowCreateUnit(false);
      setNewUnitName('');
      setNewUnitDescription('');
      setNewUnitColor('#6366f1');
      toast.success('Unit created successfully!');
    } catch (error) {
      console.error('Failed to create unit:', error);
      toast.error(`Failed to create unit: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleCreateTopic = async () => {
    if (!newTopicName.trim() || !selectedUnit) {
      toast.error('Please enter a topic name and select a unit');
      return;
    }

    // Check if topic name already exists in this unit
    const existingTopic = topics.find(topic =>
      topic.name.toLowerCase() === newTopicName.trim().toLowerCase()
    );

    if (existingTopic) {
      toast.error('A topic with this name already exists in this unit. Please choose a different name.');
      return;
    }

    try {
      console.log('Creating topic with data:', {
        name: newTopicName.trim(),
        description: newTopicDescription.trim() || undefined,
        unit_id: selectedUnit
      });

      const topic = await createTopicMutation.mutateAsync({
        name: newTopicName.trim(),
        description: newTopicDescription.trim() || undefined,
        unit_id: selectedUnit
      });

      console.log('Topic created successfully:', topic);
      setSelectedTopic(topic.id);
      setShowCreateTopic(false);
      setNewTopicName('');
      setNewTopicDescription('');
      toast.success('Topic created successfully!');
    } catch (error) {
      console.error('Failed to create topic:', error);
      toast.error(`Failed to create topic: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleSave = () => {
    if (!selectedUnit || !selectedTopic) {
      toast.error('Please select both a unit and topic');
      return;
    }
    onFolderSelected(selectedUnit, selectedTopic);
  };

  const resetSelection = () => {
    setSelectedUnit(null);
    setSelectedTopic(null);
    setShowCreateUnit(false);
    setShowCreateTopic(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto mx-4 sm:mx-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-lg sm:text-xl">
            <Folder className="w-5 h-5" />
            Choose Destination Folder
          </DialogTitle>

        </DialogHeader>

        <div className="space-y-4 sm:space-y-6">
          {/* Step 1: Select Unit */}
          <div className="space-y-3 sm:space-y-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0">
              <h3 className="text-base sm:text-lg font-semibold">1. Select Unit (Main Folder)</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCreateUnit(true)}
                className="flex items-center gap-2 w-full sm:w-auto"
              >
                <FolderPlus className="w-4 h-4" />
                New Unit
              </Button>
            </div>

            {unitsLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="w-6 h-6 animate-spin" />
                <span className="ml-2">Loading units...</span>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                {units.map((unit) => (
                  <Card
                    key={unit.id}
                    className={`p-3 sm:p-4 cursor-pointer transition-all hover:shadow-md ${
                      selectedUnit === unit.id
                        ? 'ring-2 ring-blue-500 bg-blue-50'
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedUnit(unit.id)}
                  >
                    <div className="flex items-center gap-2 sm:gap-3">
                      <div
                        className="w-3 h-3 sm:w-4 sm:h-4 rounded-full flex-shrink-0"
                        style={{ backgroundColor: unit.color }}
                      />
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm sm:text-base truncate">{unit.name}</h4>
                        {unit.description && (
                          <p className="text-xs sm:text-sm text-gray-600 line-clamp-2">{unit.description}</p>
                        )}
                        <div className="flex flex-wrap gap-1 sm:gap-2 mt-1">
                          <Badge variant="secondary" className="text-xs">
                            {unit.topic_count} topics
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {unit.note_count} notes
                          </Badge>
                        </div>
                      </div>
                      {selectedUnit === unit.id && (
                        <ChevronRight className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500 flex-shrink-0" />
                      )}
                    </div>
                  </Card>
                ))}
              </div>
            )}

            {/* Create Unit Form */}
            {showCreateUnit && (
              <Card className="p-3 sm:p-4 bg-blue-50 border-blue-200">
                <div className="space-y-3 sm:space-y-4">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0">
                    <h4 className="font-medium text-sm sm:text-base">Create New Unit</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowCreateUnit(false)}
                      className="w-full sm:w-auto"
                    >
                      Cancel
                    </Button>
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="unit-name">Unit Name *</Label>
                      <Input
                        id="unit-name"
                        value={newUnitName}
                        onChange={(e) => setNewUnitName(e.target.value)}
                        placeholder="e.g., Mathematics, Science, History"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="unit-description">Description</Label>
                      <Textarea
                        id="unit-description"
                        value={newUnitDescription}
                        onChange={(e) => setNewUnitDescription(e.target.value)}
                        placeholder="Optional description"
                        rows={2}
                      />
                    </div>
                    
                    <div>
                      <Label className="text-sm">Color</Label>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {colors.map((color) => (
                          <button
                            key={color}
                            className={`w-6 h-6 sm:w-7 sm:h-7 rounded-full border-2 transition-all ${
                              newUnitColor === color ? 'border-gray-800 scale-110' : 'border-gray-300'
                            }`}
                            style={{ backgroundColor: color }}
                            onClick={() => setNewUnitColor(color)}
                          />
                        ))}
                      </div>
                    </div>
                    
                    <Button
                      onClick={handleCreateUnit}
                      disabled={createUnitMutation.isPending || !newUnitName.trim()}
                      className="w-full"
                    >
                      {createUnitMutation.isPending ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Creating...
                        </>
                      ) : (
                        <>
                          <Plus className="w-4 h-4 mr-2" />
                          Create Unit
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </Card>
            )}
          </div>

          {/* Step 2: Select Topic */}
          {selectedUnit && (
            <div className="space-y-3 sm:space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0">
                <h3 className="text-base sm:text-lg font-semibold">2. Select Topic (Subfolder)</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowCreateTopic(true)}
                  className="flex items-center gap-2 w-full sm:w-auto"
                >
                  <Plus className="w-4 h-4" />
                  New Topic
                </Button>
              </div>

              {topicsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="w-6 h-6 animate-spin" />
                  <span className="ml-2">Loading topics...</span>
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                  {topics.map((topic) => (
                    <Card
                      key={topic.id}
                      className={`p-3 sm:p-4 cursor-pointer transition-all hover:shadow-md ${
                        selectedTopic === topic.id
                          ? 'ring-2 ring-green-500 bg-green-50'
                          : 'hover:bg-gray-50'
                      }`}
                      onClick={() => setSelectedTopic(topic.id)}
                    >
                      <div className="flex items-center gap-2 sm:gap-3">
                        <Folder className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0" />
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-sm sm:text-base truncate">{topic.name}</h4>
                          {topic.description && (
                            <p className="text-xs sm:text-sm text-gray-600 line-clamp-2">{topic.description}</p>
                          )}
                          <Badge variant="secondary" className="text-xs mt-1">
                            {topic.note_count} notes
                          </Badge>
                        </div>
                        {selectedTopic === topic.id && (
                          <ChevronRight className="w-4 h-4 sm:w-5 sm:h-5 text-green-500 flex-shrink-0" />
                        )}
                      </div>
                    </Card>
                  ))}
                </div>
              )}

              {/* Create Topic Form */}
              {showCreateTopic && (
                <Card className="p-3 sm:p-4 bg-green-50 border-green-200">
                  <div className="space-y-3 sm:space-y-4">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0">
                      <h4 className="font-medium text-sm sm:text-base">Create New Topic</h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowCreateTopic(false)}
                        className="w-full sm:w-auto"
                      >
                        Cancel
                      </Button>
                    </div>
                    
                    <div className="space-y-3">
                      <div>
                        <Label htmlFor="topic-name">Topic Name *</Label>
                        <Input
                          id="topic-name"
                          value={newTopicName}
                          onChange={(e) => setNewTopicName(e.target.value)}
                          placeholder="e.g., Algebra, Geometry, Calculus"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="topic-description">Description</Label>
                        <Textarea
                          id="topic-description"
                          value={newTopicDescription}
                          onChange={(e) => setNewTopicDescription(e.target.value)}
                          placeholder="Optional description"
                          rows={2}
                        />
                      </div>
                      
                      <Button
                        onClick={handleCreateTopic}
                        disabled={createTopicMutation.isPending || !newTopicName.trim() || !selectedUnit}
                        className="w-full"
                      >
                        {createTopicMutation.isPending ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Creating...
                          </>
                        ) : (
                          <>
                            <Plus className="w-4 h-4 mr-2" />
                            Create Topic
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </Card>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={resetSelection}
              className="w-full sm:flex-1 h-10 sm:h-auto"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              <span className="hidden sm:inline">Reset Selection</span>
              <span className="sm:hidden">Reset</span>
            </Button>
            <Button
              onClick={handleSave}
              disabled={!selectedUnit || !selectedTopic || isLoading}
              className="w-full sm:flex-1 h-10 sm:h-auto"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  <span className="hidden sm:inline">Saving...</span>
                  <span className="sm:hidden">Saving...</span>
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  <span className="hidden sm:inline">Save to Notes</span>
                  <span className="sm:hidden">Save</span>
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FolderSelector;
