import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  CheckCircle,
  Eye,
  FileText,
  Folder,
  Plus,
  ArrowRight
} from 'lucide-react';
import { Note, Unit, Topic } from '@/hooks/useNotes';
import NotePreviewDialog from './NotePreviewDialog';

interface NoteSuccessDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  note: Note | null;
  unit: Unit | null;
  topic: Topic | null;
  onCreateAnother: () => void;
  onViewNotes: () => void;
}

const NoteSuccessDialog: React.FC<NoteSuccessDialogProps> = ({
  open,
  onOpenChange,
  note,
  unit,
  topic,
  onCreateAnother,
  onViewNotes
}) => {
  const [showPreview, setShowPreview] = useState(false);

  if (!note || !unit || !topic) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getContentPreview = (content: string) => {
    // Strip HTML tags and get first 150 characters
    const textContent = content.replace(/<[^>]*>/g, '').trim();
    return textContent.length > 150 
      ? textContent.substring(0, 150) + '...'
      : textContent;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-green-600">
            <CheckCircle className="w-6 h-6" />
            Note Saved Successfully!
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Success Message */}
          <div className="text-center py-4">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              Your note has been saved as PDF!
            </h3>
            <p className="text-gray-600">
              The note has been converted to PDF format and stored in your selected folder for easy viewing.
            </p>
          </div>

          {/* Note Preview Card */}
          <Card className="p-4 bg-gray-50">
            <div className="space-y-3">
              {/* Note Title */}
              <div className="flex items-start gap-3">
                <FileText className="w-5 h-5 text-blue-500 mt-0.5" />
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-800">{note.title}</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    {getContentPreview(note.content || '')}
                  </p>
                </div>
              </div>

              {/* Location */}
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Folder className="w-4 h-4" />
                <span className="flex items-center gap-1">
                  <span 
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: unit.color }}
                  />
                  {unit.name}
                </span>
                <ArrowRight className="w-3 h-3" />
                <span>{topic.name}</span>
              </div>

              {/* Metadata */}
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary" className="text-xs">
                  Created: {formatDate(note.created_at)}
                </Badge>
                <Badge variant="secondary" className="text-xs bg-red-100 text-red-700">
                  📄 PDF Format
                </Badge>
                {note.tags && note.tags.length > 0 && (
                  <Badge variant="outline" className="text-xs">
                    {note.tags.length} tag{note.tags.length !== 1 ? 's' : ''}
                  </Badge>
                )}
              </div>

              {/* Tags */}
              {note.tags && note.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {note.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      #{tag}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </Card>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button
              onClick={() => setShowPreview(true)}
              className="w-full"
              variant="outline"
            >
              <Eye className="w-4 h-4 mr-2" />
              Preview Note
            </Button>
            
            <div className="grid grid-cols-2 gap-3">
              <Button
                onClick={onCreateAnother}
                variant="outline"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Another
              </Button>
              
              <Button
                onClick={onViewNotes}
              >
                <Folder className="w-4 h-4 mr-2" />
                View All Notes
              </Button>
            </div>
          </div>

          {/* Tips */}
          <div className="bg-blue-50 rounded-lg p-4">
            <h4 className="font-medium text-blue-800 mb-2">💡 What's Next?</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Your note is saved as a PDF and accessible from the Sort Notes page</li>
              <li>• Click on the note in Sort Notes to view the PDF</li>
              <li>• PDF format ensures your formatting is preserved</li>
              <li>• You can download, share, or print the PDF anytime</li>
              <li>• Use tags to make notes easier to find</li>
              <li>• Create more topics to better organize your notes</li>
            </ul>
          </div>
        </div>

        {/* Note Preview Dialog */}
        <NotePreviewDialog
          open={showPreview}
          onOpenChange={setShowPreview}
          note={note}
          unit={unit}
          topic={topic}
        />
      </DialogContent>
    </Dialog>
  );
};

export default NoteSuccessDialog;
