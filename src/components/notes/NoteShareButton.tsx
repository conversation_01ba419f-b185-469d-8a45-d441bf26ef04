
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { DocumentShareModal } from '@/components/sharing/DocumentShareModal';
import { Share2 } from 'lucide-react';

interface NoteShareButtonProps {
  note: {
    id: string;
    title: string;
    file_url?: string;
    file_name?: string;
  };
  size?: 'sm' | 'default' | 'lg';
  variant?: 'default' | 'outline' | 'ghost';
}

export const NoteShareButton: React.FC<NoteShareButtonProps> = ({
  note,
  size = 'sm',
  variant = 'outline',
}) => {
  const [showShareModal, setShowShareModal] = useState(false);

  // Only show share button if note has a file
  if (!note.file_url) {
    return null;
  }

  return (
    <>
      <Button
        size={size}
        variant={variant}
        onClick={() => setShowShareModal(true)}
        className="flex items-center gap-2"
      >
        <Share2 className="w-4 h-4" />
        Share
      </Button>

      <DocumentShareModal
        open={showShareModal}
        onOpenChange={setShowShareModal}
        document={{
          url: note.file_url,
          name: note.file_name || `${note.title}.pdf`,
          title: note.title,
        }}
      />
    </>
  );
};
