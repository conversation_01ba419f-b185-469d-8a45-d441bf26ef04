
import React from "react";
import { Link, useLocation } from "react-router-dom";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";

// Now accepts custom links and showAuthButtons prop.
type NavLink = {
  to: string;
  label: string;
};

type NavigationProps = {
  open?: boolean;
  setOpen?: (open: boolean) => void;
  links: NavLink[];
  showAuthButtons?: boolean;
};

export const Navigation: React.FC<NavigationProps> = ({
  open,
  setOpen,
  links,
  showAuthButtons = false,
}) => {
  const location = useLocation();

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetContent side="left" className="w-64 p-0 flex flex-col">
        <div className="h-16 flex items-center px-5 border-b flex-shrink-0">
          <a href="/" className="text-2xl font-extrabold text-[#635bff] tracking-tight">
            StudyFam
          </a>
        </div>
        <nav className="flex flex-col gap-1 py-2 overflow-y-auto flex-1 min-h-0">
          {links.map((l) => (
            <Link
              key={l.to}
              to={l.to}
              className={`flex items-center gap-3 px-6 py-3 text-lg font-medium transition-colors flex-shrink-0 ${
                location.pathname === l.to
                  ? "bg-violet-100 text-violet-700"
                  : "text-gray-700 hover:bg-gray-50"
              }`}
              onClick={() => setOpen && setOpen(false)}
            >
              {l.label}
            </Link>
          ))}
          {showAuthButtons && (
            <div className="px-6 pt-3 flex flex-col gap-2 flex-shrink-0">
              <Link to="/login" onClick={() => setOpen && setOpen(false)}>
                <Button
                  variant="outline"
                  className="w-full text-violet-700 border-violet-600 hover:bg-violet-50 font-semibold"
                >
                  Login
                </Button>
              </Link>
              <Link to="/register" onClick={() => setOpen && setOpen(false)}>
                <Button
                  variant="default"
                  className="w-full bg-violet-600 hover:bg-violet-700 text-white font-bold"
                  style={{ boxShadow: "0 4px 14px 0 rgba(99,91,255,0.13)" }}
                >
                  Get Started
                </Button>
              </Link>
            </div>
          )}
        </nav>
      </SheetContent>
    </Sheet>
  );
};
