import React, { useState } from 'react';
import { useOffline } from '@/hooks/useOffline';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  WifiOff,
  FileText,
  Calendar,
  Users,
  BookOpen,
  MessageSquare,
  Database,
  RefreshCw,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import OfflineIndicator from './OfflineIndicator';
import OfflineNotesBrowser from './OfflineNotesBrowser';
import OfflineTimetable from './OfflineTimetable';
import OfflineGroups from './OfflineGroups';
import OfflinePastPapers from './OfflinePastPapers';

interface OfflineDashboardProps {
  className?: string;
}

export const OfflineDashboard: React.FC<OfflineDashboardProps> = ({ className }) => {
  const { status, capabilities } = useOffline();
  const [activeTab, setActiveTab] = useState('overview');

  const getStatusIcon = () => {
    if (status.isOnline) {
      return status.pendingSyncCount > 0 ? 
        <AlertCircle className="h-5 w-5 text-yellow-500" /> : 
        <CheckCircle className="h-5 w-5 text-green-500" />;
    }
    return <WifiOff className="h-5 w-5 text-red-500" />;
  };

  const getStatusMessage = () => {
    if (status.isOnline) {
      return status.pendingSyncCount > 0 ? 
        `Online - ${status.pendingSyncCount} items pending sync` : 
        'Online - All data synced';
    }
    return 'Offline - Limited functionality available';
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Status */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-3">
            StudyFam
            {status.isOffline && (
              <Badge variant="secondary" className="text-sm">
                <WifiOff className="h-4 w-4 mr-1" />
                Offline Mode
              </Badge>
            )}
          </h1>
          <div className="flex items-center gap-2 mt-2">
            {getStatusIcon()}
            <p className="text-gray-600">{getStatusMessage()}</p>
          </div>
        </div>
        
        <OfflineIndicator />
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="notes">Notes</TabsTrigger>
          <TabsTrigger value="timetable">Timetable</TabsTrigger>
          <TabsTrigger value="groups">Groups</TabsTrigger>
          <TabsTrigger value="papers">Past Papers</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Status Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Connection Status */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  {getStatusIcon()}
                  Connection
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="text-2xl font-bold">
                  {status.isOnline ? 'Online' : 'Offline'}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {status.isOnline ? 'All features available' : 'Limited features'}
                </p>
              </CardContent>
            </Card>

            {/* Offline Data */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  Offline Data
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="text-2xl font-bold">
                  {status.hasOfflineData ? 'Available' : 'None'}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Local content stored
                </p>
              </CardContent>
            </Card>

            {/* Pending Sync */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <RefreshCw className="h-4 w-4" />
                  Pending Sync
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="text-2xl font-bold">
                  {status.pendingSyncCount}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Items to sync
                </p>
              </CardContent>
            </Card>

            {/* Last Sync */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Last Sync
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="text-sm font-bold">
                  {status.lastSyncTime ? 
                    new Date(status.lastSyncTime).toLocaleTimeString() : 
                    'Never'
                  }
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Data synchronization
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Offline Capabilities */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <WifiOff className="h-5 w-5" />
                Offline Capabilities
              </CardTitle>
              <CardDescription>
                Features available when you're offline
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="flex items-center gap-3 p-3 rounded-lg bg-green-50 border border-green-200">
                  <FileText className="h-5 w-5 text-green-600" />
                  <div>
                    <div className="font-medium text-green-800">Notes</div>
                    <div className="text-sm text-green-600">Create & view notes</div>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 rounded-lg bg-blue-50 border border-blue-200">
                  <Calendar className="h-5 w-5 text-blue-600" />
                  <div>
                    <div className="font-medium text-blue-800">Timetable</div>
                    <div className="text-sm text-blue-600">Manage schedule</div>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 rounded-lg bg-purple-50 border border-purple-200">
                  <Users className="h-5 w-5 text-purple-600" />
                  <div>
                    <div className="font-medium text-purple-800">Groups</div>
                    <div className="text-sm text-purple-600">View & discuss</div>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 rounded-lg bg-orange-50 border border-orange-200">
                  <MessageSquare className="h-5 w-5 text-orange-600" />
                  <div>
                    <div className="font-medium text-orange-800">Messages</div>
                    <div className="text-sm text-orange-600">Send & receive</div>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 rounded-lg bg-indigo-50 border border-indigo-200">
                  <BookOpen className="h-5 w-5 text-indigo-600" />
                  <div>
                    <div className="font-medium text-indigo-800">Past Papers</div>
                    <div className="text-sm text-indigo-600">View downloaded</div>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-50 border border-gray-200">
                  <Database className="h-5 w-5 text-gray-600" />
                  <div>
                    <div className="font-medium text-gray-800">Local Storage</div>
                    <div className="text-sm text-gray-600">Auto-sync ready</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          {status.isOffline && (
            <Card>
              <CardHeader>
                <CardTitle>Offline Mode Tips</CardTitle>
                <CardDescription>
                  Make the most of your offline study session
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <span>All your changes are saved locally and will sync automatically when you're back online.</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <span>You can create notes, manage your timetable, and participate in group discussions.</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                    <span>Downloaded past papers are available for viewing and studying.</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <AlertCircle className="h-4 w-4 text-yellow-500 mt-0.5" />
                    <span>Some features like AI tutoring require an internet connection.</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="notes">
          <OfflineNotesBrowser />
        </TabsContent>

        <TabsContent value="timetable">
          <OfflineTimetable />
        </TabsContent>

        <TabsContent value="groups">
          <OfflineGroups />
        </TabsContent>

        <TabsContent value="papers">
          <OfflinePastPapers />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default OfflineDashboard;
