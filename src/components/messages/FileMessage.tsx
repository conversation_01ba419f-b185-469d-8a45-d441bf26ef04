
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { DocumentViewer } from '@/components/sharing/DocumentViewer';
import { useSaveDocumentToNotes } from '@/hooks/useDocumentSharing';
import { 
  FileText, 
  Image as ImageIcon, 
  File, 
  Download, 
  Eye,
  Save
} from 'lucide-react';

interface FileMessageProps {
  message: {
    id: string;
    content: string;
    file_url: string;
    file_name: string;
    sender_name: string;
    sender_avatar: string;
    created_at: string;
    is_own: boolean;
  };
}

export const FileMessage: React.FC<FileMessageProps> = ({ message }) => {
  const [showViewer, setShowViewer] = useState(false);
  const saveToNotesMutation = useSaveDocumentToNotes();

  const getFileExtension = () => {
    return message.file_name?.split('.').pop()?.toLowerCase() || 'file';
  };

  const getFileIcon = () => {
    const extension = getFileExtension();
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    
    if (extension === 'pdf') return <FileText className="w-5 h-5" />;
    if (imageTypes.includes(extension)) return <ImageIcon className="w-5 h-5" />;
    return <File className="w-5 h-5" />;
  };

  const isImage = () => {
    const extension = getFileExtension();
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    return imageTypes.includes(extension);
  };

  const handleSaveToNotes = () => {
    saveToNotesMutation.mutate({
      documentUrl: message.file_url,
      documentName: message.file_name,
      documentTitle: message.file_name.replace(/\.[^/.]+$/, ''), // Remove extension
    });
  };

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = message.file_url;
    link.download = message.file_name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <>
      <div className={`flex ${message.is_own ? 'justify-end' : 'justify-start'} mb-4`}>
        <div className={`flex items-start gap-3 max-w-md ${message.is_own ? 'flex-row-reverse' : ''}`}>
          {!message.is_own && (
            <Avatar className="w-8 h-8">
              <AvatarImage src={message.sender_avatar} />
              <AvatarFallback>
                {message.sender_name?.charAt(0)?.toUpperCase() || 'U'}
              </AvatarFallback>
            </Avatar>
          )}

          <div className={`rounded-2xl p-4 shadow-sm ${
            message.is_own 
              ? 'bg-gradient-to-r from-violet-500 to-purple-600 text-white' 
              : 'bg-white/95 backdrop-blur-sm text-gray-900 border border-white/20'
          }`}>
            {/* Message text if any */}
            {message.content && (
              <p className="text-sm mb-3">{message.content}</p>
            )}

            {/* File preview */}
            <div className="bg-white/10 rounded-lg p-3 mb-3">
              <div className="flex items-center gap-3 mb-2">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                  message.is_own ? 'bg-white/20' : 'bg-blue-100'
                }`}>
                  {getFileIcon()}
                </div>
                <div className="flex-1 min-w-0">
                  <p className={`font-medium text-sm truncate ${
                    message.is_own ? 'text-white' : 'text-gray-900'
                  }`}>
                    {message.file_name}
                  </p>
                  <p className={`text-xs ${
                    message.is_own ? 'text-violet-100' : 'text-gray-500'
                  }`}>
                    {getFileExtension().toUpperCase()} file
                  </p>
                </div>
              </div>

              {/* Image preview */}
              {isImage() && (
                <div className="mb-3">
                  <img
                    src={message.file_url}
                    alt={message.file_name}
                    className="w-full h-32 object-cover rounded-lg cursor-pointer"
                    onClick={() => setShowViewer(true)}
                  />
                </div>
              )}

              {/* Action buttons */}
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setShowViewer(true)}
                  className={`flex-1 ${
                    message.is_own 
                      ? 'border-white/30 text-white hover:bg-white/20' 
                      : 'border-gray-300'
                  }`}
                >
                  <Eye className="w-3 h-3 mr-1" />
                  View
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleSaveToNotes}
                  disabled={saveToNotesMutation.isPending}
                  className={`flex-1 ${
                    message.is_own 
                      ? 'border-white/30 text-white hover:bg-white/20' 
                      : 'border-gray-300'
                  }`}
                >
                  <Save className="w-3 h-3 mr-1" />
                  Save
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleDownload}
                  className={`flex-1 ${
                    message.is_own 
                      ? 'border-white/30 text-white hover:bg-white/20' 
                      : 'border-gray-300'
                  }`}
                >
                  <Download className="w-3 h-3 mr-1" />
                  Download
                </Button>
              </div>
            </div>

            <p className={`text-xs mt-1 ${
              message.is_own ? 'text-violet-100' : 'text-gray-500'
            }`}>
              {new Date(message.created_at).toLocaleTimeString()}
            </p>
          </div>
        </div>
      </div>

      {/* Document Viewer */}
      <DocumentViewer
        open={showViewer}
        onOpenChange={setShowViewer}
        document={{
          url: message.file_url,
          name: message.file_name,
          title: message.file_name.replace(/\.[^/.]+$/, ''),
          sharedBy: message.sender_name,
          sharedAt: message.created_at,
        }}
        onSaveToNotes={handleSaveToNotes}
      />
    </>
  );
};
