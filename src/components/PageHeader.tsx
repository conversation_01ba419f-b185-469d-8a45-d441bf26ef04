
import React from "react";
import { Button } from "@/components/ui/button";

interface PageHeaderProps {
  title: string;
  description?: string;
  buttonLabel?: string;
  onButtonClick?: () => void;
  children?: React.ReactNode; // for filters/search etc
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  description,
  buttonLabel,
  onButtonClick,
  children,
}) => {
  return (
    <div className="bg-muted rounded-2xl p-6 sm:p-8 mb-8">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
        <div>
          <h2 className="text-2xl font-bold text-foreground">{title}</h2>
          {description && (
            <p className="text-muted-foreground">{description}</p>
          )}
        </div>
        {buttonLabel && (
          <Button
            className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700"
            onClick={onButtonClick}
          >
            {buttonLabel}
          </Button>
        )}
      </div>
      {children && (
        <div className="mt-2">{children}</div>
      )}
    </div>
  );
};

export default PageHeader;
