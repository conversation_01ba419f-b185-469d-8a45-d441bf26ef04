
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Search, FileText, Upload, Wand2 } from 'lucide-react';
import { cleanMarkdownForDisplay } from '@/utils/markdownCleaner';

interface NoteSummarizerProps {
  selectedCourse: any;
}

export const NoteSummarizer = ({ selectedCourse }: NoteSummarizerProps) => {
  const [activeTab, setActiveTab] = useState('generate');
  const [generatedNotes, setGeneratedNotes] = useState('');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  const generateNotes = () => {
    // Simulate AI note generation
    const sampleNotes = `# ${selectedCourse.unit} - Comprehensive Notes

## Key Concepts

### 1. Introduction
- Overview of ${selectedCourse.unit}
- Core principles and foundations
- Historical context and evolution

### 2. Fundamental Concepts
- Primary theories and models
- Essential definitions and terminology
- Real-world applications

### 3. Advanced Topics
- Complex problem-solving approaches
- Case studies and examples
- Current research and developments

## Important Formulas
- Key equations and their applications
- Step-by-step derivations
- Practice problems

## Summary Points
• Master the fundamental concepts before advancing
• Practice regularly with real-world examples
• Connect theories to practical applications
• Review past papers for exam preparation

## Recommended Reading
- Primary textbook chapters 1-5
- Research papers on recent developments
- Online resources and tutorials`;

    setGeneratedNotes(sampleNotes);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-foreground">AI Note Summarizer</h2>
        <p className="text-muted-foreground">
          Generate comprehensive notes or summarize your existing materials
        </p>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
        <button
          onClick={() => setActiveTab('generate')}
          className={`px-4 py-2 rounded-md font-medium transition-colors ${
            activeTab === 'generate'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-muted-foreground hover:text-foreground'
          }`}
        >
          Generate Notes
        </button>
        <button
          onClick={() => setActiveTab('summarize')}
          className={`px-4 py-2 rounded-md font-medium transition-colors ${
            activeTab === 'summarize'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-muted-foreground hover:text-foreground'
          }`}
        >
          Summarize Files
        </button>
      </div>

      {activeTab === 'generate' && (
        <div className="grid lg:grid-cols-2 gap-6">
          {/* Generation Controls */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-foreground mb-4">Generate New Notes</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Note Type
                </label>
                <select className="w-full p-3 border border-input rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option>Comprehensive Lecture Notes</option>
                  <option>Summary Points</option>
                  <option>Concept Explanations</option>
                  <option>Exam Study Guide</option>
                  <option>Flashcard Content</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Focus Topics (Optional)
                </label>
                <textarea
                  placeholder="Enter specific topics you want to focus on..."
                  className="w-full p-3 border border-input rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Detail Level
                </label>
                <select className="w-full p-3 border border-input rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option>Comprehensive</option>
                  <option>Intermediate</option>
                  <option>Brief Summary</option>
                </select>
              </div>

              <Button 
                onClick={generateNotes}
                className="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700"
              >
                <Wand2 className="w-4 h-4 mr-2" />
                Generate Notes
              </Button>
            </div>
          </Card>

          {/* Generated Content */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-foreground">Generated Notes</h3>
              {generatedNotes && (
                <div className="space-x-2">
                  <Button variant="outline" size="sm">
                    Download PDF
                  </Button>
                  <Button variant="outline" size="sm">
                    Download Word
                  </Button>
                </div>
              )}
            </div>
            
            {generatedNotes ? (
              <div className="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
                <div
                  className="text-sm text-foreground"
                  dangerouslySetInnerHTML={{
                    __html: cleanMarkdownForDisplay(generatedNotes)
                  }}
                />
              </div>
            ) : (
              <div className="text-center py-12 text-muted-foreground">
                <Search className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <p>Generated notes will appear here</p>
              </div>
            )}
          </Card>
        </div>
      )}

      {activeTab === 'summarize' && (
        <div className="grid lg:grid-cols-2 gap-6">
          {/* File Upload */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-foreground mb-4">Upload Files to Summarize</h3>
            
            <div className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <Upload className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground mb-2">
                  Drag and drop files here, or click to browse
                </p>
                <p className="text-sm text-muted-foreground mb-4">
                  Supports PDF, DOC, DOCX, TXT files
                </p>
                <Button variant="outline">
                  Choose Files
                </Button>
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Summary Style
                </label>
                <select className="w-full p-3 border border-input rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option>Bullet Points</option>
                  <option>Paragraph Form</option>
                  <option>Mind Map Format</option>
                  <option>Q&A Style</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Length
                </label>
                <select className="w-full p-3 border border-input rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option>Brief (1-2 pages)</option>
                  <option>Moderate (3-5 pages)</option>
                  <option>Detailed (5+ pages)</option>
                </select>
              </div>

              <Button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
                <FileText className="w-4 h-4 mr-2" />
                Summarize Files
              </Button>
            </div>
          </Card>

          {/* Summary Output */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-foreground mb-4">Summary Output</h3>
            <div className="text-center py-12 text-muted-foreground">
              <FileText className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <p>Upload files to generate summaries</p>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};
