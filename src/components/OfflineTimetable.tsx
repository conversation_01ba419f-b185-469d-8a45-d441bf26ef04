import React, { useState, useEffect } from 'react';
import { useOffline } from '@/hooks/useOffline';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plus, 
  Calendar, 
  WifiOff, 
  Save, 
  Edit, 
  Trash2, 
  Clock,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { OfflineTimetableEntry } from '@/utils/offlineStorage';

interface OfflineTimetableProps {
  className?: string;
}

const DAYS_OF_WEEK = [
  'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'
];

const COLORS = [
  '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', 
  '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
];

const OfflineTimetable: React.FC<OfflineTimetableProps> = ({ className }) => {
  const { status, saveForOffline, getOfflineData } = useOffline();
  const [entries, setEntries] = useState<OfflineTimetableEntry[]>([]);
  const [sessions, setSessions] = useState<any[]>([]);
  const [currentWeek, setCurrentWeek] = useState(0); // 0 = current week
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('schedule');

  // Form state for creating/editing entries
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    start_time: '',
    end_time: '',
    day_of_week: 1, // Monday
    color: COLORS[0]
  });

  // Load timetable entries and sessions on component mount
  useEffect(() => {
    // Add a small delay to ensure IndexedDB is ready
    const timer = setTimeout(() => {
      loadTimetableEntries();
      loadTimetableSessions();
    }, 200);

    return () => clearTimeout(timer);
  }, []);

  const loadTimetableEntries = async () => {
    try {
      setIsLoading(true);
      console.log('Loading timetable entries...');
      const allEntries = await getOfflineData('timetable');
      console.log('Loaded timetable entries:', allEntries?.length || 0, allEntries);
      setEntries(allEntries || []);
    } catch (error) {
      console.error('Error loading timetable entries:', error);
      toast({
        title: "Error",
        description: "Failed to load timetable entries.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadTimetableSessions = async () => {
    try {
      const data = await getOfflineData('timetable_sessions');
      setSessions(data || []);
      console.log('Loaded', data?.length || 0, 'timetable sessions');
    } catch (error) {
      console.error('Error loading timetable sessions:', error);
    }
  };

  const handleCreateEntry = async () => {
    if (!formData.title.trim() || !formData.start_time || !formData.end_time) {
      toast({
        title: "Validation Error",
        description: "Please provide title, start time, and end time.",
        variant: "destructive",
      });
      return;
    }

    // Validate time order
    if (formData.start_time >= formData.end_time) {
      toast({
        title: "Validation Error",
        description: "End time must be after start time.",
        variant: "destructive",
      });
      return;
    }

    try {
      const entryData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        start_time: formData.start_time,
        end_time: formData.end_time,
        day_of_week: formData.day_of_week,
        color: formData.color,
        created_at: new Date().toISOString()
      };

      console.log('Creating timetable entry:', entryData);
      const entryId = await saveForOffline('timetable', entryData);
      console.log('Timetable entry created with ID:', entryId);

      // Verify the entry was saved
      const savedEntries = await getOfflineData('timetable');
      console.log('All timetable entries after save:', savedEntries?.length || 0, savedEntries);

      // Also create sessions for the next 4 weeks
      const sessionsCreated = await createSessionsForEntry(entryId, entryData);
      console.log('Created', sessionsCreated, 'sessions for the entry');

      // Reset form
      setFormData({
        title: '',
        description: '',
        start_time: '',
        end_time: '',
        day_of_week: 1,
        color: COLORS[0]
      });

      setIsCreateDialogOpen(false);

      // Reload entries and sessions
      await loadTimetableEntries();
      await loadTimetableSessions();

      toast({
        title: "Entry & Sessions Added",
        description: status.isOffline
          ? `Timetable entry and ${sessionsCreated} sessions saved offline. They will sync when you're back online.`
          : `Timetable entry and ${sessionsCreated} sessions added successfully.`,
      });
    } catch (error) {
      console.error('Error saving timetable entry:', error);
      toast({
        title: "Error",
        description: "Failed to save timetable entry.",
        variant: "destructive",
      });
    }
  };

  // Helper function to create sessions for a timetable entry
  const createSessionsForEntry = async (entryId: string, entryData: any): Promise<number> => {
    try {
      const sessions = [];
      const today = new Date();
      const weeksToCreate = 4; // Create sessions for next 4 weeks

      for (let week = 0; week < weeksToCreate; week++) {
        // Calculate the date for this session
        const sessionDate = new Date(today);
        sessionDate.setDate(today.getDate() + (week * 7) + (entryData.day_of_week - today.getDay()));

        // Skip if the date is in the past
        if (sessionDate < today) {
          continue;
        }

        const sessionData = {
          timetable_entry_id: entryId,
          title: entryData.title,
          description: entryData.description,
          start_time: entryData.start_time,
          end_time: entryData.end_time,
          date: sessionDate.toISOString().split('T')[0], // YYYY-MM-DD format
          status: 'scheduled' as const,
          notes: '',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        console.log('Creating session:', sessionData);
        await saveForOffline('timetable_session', sessionData);
        sessions.push(sessionData);
      }

      return sessions.length;
    } catch (error) {
      console.error('Error creating sessions:', error);
      return 0;
    }
  };

  // Function to mark a session as complete
  const markSessionComplete = async (sessionId: string) => {
    try {
      // Update session status to completed
      const sessionIndex = sessions.findIndex(s => s.id === sessionId);
      if (sessionIndex !== -1) {
        const updatedSession = {
          ...sessions[sessionIndex],
          status: 'completed',
          updated_at: new Date().toISOString()
        };

        // Save updated session
        const { offlineStorage } = await import('@/utils/offlineStorage');
        await offlineStorage.put('timetable_sessions', updatedSession);

        // Update local state
        const updatedSessions = [...sessions];
        updatedSessions[sessionIndex] = updatedSession;
        setSessions(updatedSessions);

        toast({
          title: "Session Completed",
          description: "Session marked as completed successfully.",
        });
      }
    } catch (error) {
      console.error('Error marking session complete:', error);
      toast({
        title: "Error",
        description: "Failed to mark session as complete.",
        variant: "destructive",
      });
    }
  };

  const getEntriesForDay = (dayOfWeek: number) => {
    return entries
      .filter(entry => entry.day_of_week === dayOfWeek)
      .sort((a, b) => a.start_time.localeCompare(b.start_time));
  };

  const formatTime = (time: string) => {
    return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const getCurrentWeekDates = () => {
    const today = new Date();
    const currentDay = today.getDay();
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - currentDay + (currentWeek * 7));
    
    return Array.from({ length: 7 }, (_, i) => {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      return date;
    });
  };

  const weekDates = getCurrentWeekDates();

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-gray-500">Loading timetable...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Calendar className="h-6 w-6" />
            Timetable
            {status.isOffline && (
              <Badge variant="secondary" className="ml-2">
                <WifiOff className="h-3 w-3 mr-1" />
                Offline
              </Badge>
            )}
          </h2>
          <p className="text-gray-600 mt-1">
            {status.isOffline
              ? "Manage your schedule offline. Changes will sync when you're back online."
              : "Manage your weekly schedule."
            }
          </p>
        </div>

        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Entry
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-lg">
            <DialogHeader>
              <DialogTitle>Add Timetable Entry</DialogTitle>
              <DialogDescription>
                {status.isOffline
                  ? "This entry will be saved offline and synced when you're back online."
                  : "Add a new entry to your timetable."
                }
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  placeholder="e.g., Mathematics Lecture"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description (Optional)</Label>
                <Textarea
                  id="description"
                  placeholder="Additional details..."
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="min-h-[80px]"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="start_time">Start Time</Label>
                  <Input
                    id="start_time"
                    type="time"
                    value={formData.start_time}
                    onChange={(e) => setFormData(prev => ({ ...prev, start_time: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="end_time">End Time</Label>
                  <Input
                    id="end_time"
                    type="time"
                    value={formData.end_time}
                    onChange={(e) => setFormData(prev => ({ ...prev, end_time: e.target.value }))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Day of Week</Label>
                <Select 
                  value={formData.day_of_week.toString()} 
                  onValueChange={(value) => setFormData(prev => ({ ...prev, day_of_week: parseInt(value) }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {DAYS_OF_WEEK.map((day, index) => (
                      <SelectItem key={index} value={index.toString()}>
                        {day}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Color</Label>
                <div className="flex gap-2 flex-wrap">
                  {COLORS.map((color) => (
                    <button
                      key={color}
                      type="button"
                      className={`w-8 h-8 rounded-full border-2 ${
                        formData.color === color ? 'border-gray-800' : 'border-gray-300'
                      }`}
                      style={{ backgroundColor: color }}
                      onClick={() => setFormData(prev => ({ ...prev, color }))}
                    />
                  ))}
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateEntry}>
                  <Save className="h-4 w-4 mr-2" />
                  Add Entry
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Tabs for Schedule and Sessions */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="schedule">Schedule</TabsTrigger>
          <TabsTrigger value="sessions">Sessions ({sessions.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="schedule" className="space-y-6">
          {/* Week Navigation */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setCurrentWeek(prev => prev - 1)}
        >
          <ChevronLeft className="h-4 w-4" />
          Previous Week
        </Button>
        
        <div className="text-center">
          <h3 className="font-medium">
            {currentWeek === 0 ? 'This Week' : 
             currentWeek === 1 ? 'Next Week' : 
             currentWeek === -1 ? 'Last Week' : 
             `Week of ${weekDates[0].toLocaleDateString()}`}
          </h3>
          <p className="text-sm text-gray-500">
            {weekDates[0].toLocaleDateString()} - {weekDates[6].toLocaleDateString()}
          </p>
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => setCurrentWeek(prev => prev + 1)}
        >
          Next Week
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Timetable Grid */}
      <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
        {DAYS_OF_WEEK.map((day, dayIndex) => {
          const dayEntries = getEntriesForDay(dayIndex);
          const dayDate = weekDates[dayIndex];
          const isToday = dayDate.toDateString() === new Date().toDateString();
          
          return (
            <Card key={dayIndex} className={`${isToday ? 'ring-2 ring-blue-500' : ''}`}>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">
                  {day}
                  {isToday && <Badge variant="default" className="ml-2 text-xs">Today</Badge>}
                </CardTitle>
                <CardDescription className="text-xs">
                  {dayDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0 space-y-2">
                {dayEntries.length === 0 ? (
                  <p className="text-xs text-gray-500 text-center py-4">No entries</p>
                ) : (
                  dayEntries.map((entry) => (
                    <div
                      key={entry.id}
                      className="p-2 rounded-md text-white text-xs relative"
                      style={{ backgroundColor: entry.color }}
                    >
                      {!entry.synced && (
                        <WifiOff className="absolute top-1 right-1 h-3 w-3 opacity-70" />
                      )}
                      <div className="font-medium truncate pr-4">{entry.title}</div>
                      <div className="opacity-90">
                        {formatTime(entry.start_time)} - {formatTime(entry.end_time)}
                      </div>
                      {entry.description && (
                        <div className="opacity-80 truncate mt-1">{entry.description}</div>
                      )}
                    </div>
                  ))
                )}
              </CardContent>
            </Card>
          );
        })}
          </div>
        </TabsContent>

        <TabsContent value="sessions" className="space-y-6">
          {/* Sessions List */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Upcoming Sessions</h3>
            {sessions.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <Clock className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-medium mb-2">No Sessions Yet</h3>
                  <p className="text-gray-500">
                    Sessions will appear here when you create timetable entries.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {sessions
                  .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
                  .map((session) => (
                    <Card key={session.id} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <CardTitle className="text-lg line-clamp-2">{session.title}</CardTitle>
                          {!session.synced && (
                            <Badge variant="outline" className="ml-2 shrink-0">
                              <WifiOff className="h-3 w-3 mr-1" />
                              Offline
                            </Badge>
                          )}
                        </div>
                        <CardDescription>
                          {new Date(session.date).toLocaleDateString('en-US', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Clock className="h-4 w-4" />
                            {formatTime(session.start_time)} - {formatTime(session.end_time)}
                          </div>
                          {session.description && (
                            <p className="text-sm text-gray-600 line-clamp-2">
                              {session.description}
                            </p>
                          )}
                          <div className="flex items-center justify-between">
                            <Badge
                              variant={session.status === 'completed' ? 'default' : 'secondary'}
                              className="capitalize"
                            >
                              {session.status}
                            </Badge>
                            {session.status === 'scheduled' && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => markSessionComplete(session.id)}
                              >
                                Mark Complete
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default OfflineTimetable;
