import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, AlertTriangle, Lock, RefreshCw } from 'lucide-react';
import { useSecurity } from '@/contexts/SecurityContext';
import { useSecurityMiddleware } from '@/hooks/useSecurityMiddleware';
import { SENSITIVE_ROUTES, ADMIN_IP_WHITELIST } from '@/config/security';

interface SecurityGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireAdmin?: boolean;
  allowedRoles?: string[];
  customValidation?: () => boolean;
  fallbackComponent?: React.ReactNode;
}

const SecurityGuard: React.FC<SecurityGuardProps> = ({
  children,
  requireAuth = false,
  requireAdmin = false,
  allowedRoles = [],
  customValidation,
  fallbackComponent,
}) => {
  const security = useSecurity();
  const location = useLocation();
  const [isValidating, setIsValidating] = useState(true);
  const [validationResult, setValidationResult] = useState<{
    allowed: boolean;
    reason?: string;
    action?: string;
  }>({ allowed: true });

  // Initialize security middleware
  useSecurityMiddleware({
    enableRateLimit: true,
    enableCSRFProtection: true,
    enableSessionValidation: true,
    enableRouteProtection: true,
  });

  useEffect(() => {
    const validateAccess = async () => {
      setIsValidating(true);

      try {
        // Check if user is blocked
        if (security.securityStatus.isBlocked) {
          setValidationResult({
            allowed: false,
            reason: 'Account temporarily blocked due to suspicious activity',
            action: 'contact_support'
          });
          return;
        }

        // Check session validity
        if (requireAuth && !security.isSessionValid()) {
          setValidationResult({
            allowed: false,
            reason: 'Session expired or invalid',
            action: 'login_required'
          });
          return;
        }

        // Check if route is sensitive
        const currentPath = location.pathname;
        const isSensitiveRoute = SENSITIVE_ROUTES.some(route => 
          currentPath.startsWith(route)
        );

        if (isSensitiveRoute) {
          // Additional security checks for sensitive routes
          const userAgent = navigator.userAgent;
          
          // Check for suspicious user agents
          if (/bot|crawler|spider|scraper/i.test(userAgent)) {
            security.reportSuspiciousActivity('Bot access to sensitive route', {
              route: currentPath,
              userAgent
            });
            setValidationResult({
              allowed: false,
              reason: 'Automated access detected',
              action: 'human_verification'
            });
            return;
          }

          // Rate limit check for sensitive routes
          if (!security.checkRateLimit('sensitive_route_access', 10)) {
            setValidationResult({
              allowed: false,
              reason: 'Too many requests to sensitive area',
              action: 'rate_limited'
            });
            return;
          }
        }

        // Admin route protection
        if (requireAdmin) {
          // Check IP whitelist for admin routes
          const userIP = await getUserIP();
          if (!ADMIN_IP_WHITELIST.includes(userIP) && userIP !== '127.0.0.1') {
            security.reportSuspiciousActivity('Admin access from non-whitelisted IP', {
              ip: userIP,
              route: currentPath
            });
            setValidationResult({
              allowed: false,
              reason: 'Admin access restricted to authorized IPs',
              action: 'ip_restricted'
            });
            return;
          }
        }

        // Custom validation
        if (customValidation && !customValidation()) {
          setValidationResult({
            allowed: false,
            reason: 'Custom security validation failed',
            action: 'custom_failed'
          });
          return;
        }

        // Role-based access control
        if (allowedRoles.length > 0) {
          // This would need to be implemented based on your user role system
          // For now, we'll assume all authenticated users have basic access
          setValidationResult({ allowed: true });
          return;
        }

        // All checks passed
        setValidationResult({ allowed: true });

      } catch (error) {
        console.error('Security validation error:', error);
        security.reportSuspiciousActivity('Security validation error', {
          error: error.message,
          route: location.pathname
        });
        setValidationResult({
          allowed: false,
          reason: 'Security validation failed',
          action: 'try_again'
        });
      } finally {
        setIsValidating(false);
      }
    };

    validateAccess();
  }, [
    location.pathname,
    requireAuth,
    requireAdmin,
    allowedRoles,
    customValidation,
    security
  ]);

  // Helper function to get user IP (simplified)
  const getUserIP = async (): Promise<string> => {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch {
      return '127.0.0.1'; // Fallback for localhost
    }
  };

  // Loading state
  if (isValidating) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-96">
          <CardContent className="flex items-center justify-center p-8">
            <div className="text-center space-y-4">
              <Shield className="w-12 h-12 mx-auto text-blue-600 animate-pulse" />
              <div className="text-lg font-semibold">Validating Access...</div>
              <div className="text-sm text-gray-600">
                Performing security checks
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Access denied
  if (!validationResult.allowed) {
    const handleAction = () => {
      switch (validationResult.action) {
        case 'login_required':
          window.location.href = '/login';
          break;
        case 'contact_support':
          // Implement contact support logic
          break;
        case 'try_again':
          window.location.reload();
          break;
        default:
          security.refreshSession();
      }
    };

    if (fallbackComponent) {
      return <>{fallbackComponent}</>;
    }

    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <Lock className="w-6 h-6 text-red-600" />
            </div>
            <CardTitle className="text-red-600">Access Denied</CardTitle>
            <CardDescription>
              {validationResult.reason}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                This area is protected by advanced security measures. 
                If you believe this is an error, please contact support.
              </AlertDescription>
            </Alert>
            
            <div className="flex flex-col gap-2">
              <Button onClick={handleAction} className="w-full">
                {validationResult.action === 'login_required' && (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Go to Login
                  </>
                )}
                {validationResult.action === 'contact_support' && (
                  <>
                    <AlertTriangle className="w-4 h-4 mr-2" />
                    Contact Support
                  </>
                )}
                {validationResult.action === 'try_again' && (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Try Again
                  </>
                )}
                {!validationResult.action && (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Refresh
                  </>
                )}
              </Button>
              
              <Button 
                variant="outline" 
                onClick={() => window.history.back()}
                className="w-full"
              >
                Go Back
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Access granted
  return <>{children}</>;
};

export default SecurityGuard;
