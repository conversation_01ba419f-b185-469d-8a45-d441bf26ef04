import { useState } from "react";
import { Users, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import PageHeader from "@/components/PageHeader";
import StudyGroupsTabs from "./StudyGroups/StudyGroupsTabs";
import GroupsGrid from "./StudyGroups/GroupsGrid";
import AddGroupDialog from "./StudyGroups/AddGroupDialog";
import PrivacyFilter from "./StudyGroups/PrivacyFilter";
import ProfilePreviewDialog from "@/components/profile/ProfilePreviewDialog";
import { StudyGroupsProvider, useStudyGroups } from "./StudyGroups/StudyGroupsContext";
import { useCreateStudyGroup, useJoinStudyGroup, useLeaveStudyGroup, StudyGroup } from "@/hooks/useStudyGroups";
import { toast } from "sonner";

// Placeholder group images
const GROUP_IMAGES = [
  "https://images.unsplash.com/photo-1466721591366-2d5fba72006d?auto=format&fit=crop&w=600&q=80",
  "https://images.unsplash.com/photo-1493962853295-0fd70327578a?auto=format&fit=crop&w=600&q=80",
];

// Add the Group type with required fields (add members array)
type Group = {
  id: string;
  name: string;
  description?: string;
  image: string;
  isPublic: boolean;
  isJoined: boolean;
  memberCount?: number;
  coverPhotoFile?: File | null;
  members: { id?: string; name: string; avatar: string }[]; // Added
};

const DEFAULT_MEMBERS = [
  { id: "user-1", name: "You", avatar: "https://randomuser.me/api/portraits/men/100.jpg" }
];

// Add a better new group handling for "discover" array for demo
const INITIAL_DISCOVER_GROUPS: Group[] = [
  {
    id: "physics",
    name: "Physics Masters",
    description: "Quantum mechanics and theoretical physics discussions",
    image: GROUP_IMAGES[0],
    isPublic: true,
    isJoined: false,
    memberCount: 32,
    coverPhotoFile: null,
    members: [
      { id: "user-2", name: "Sarah", avatar: "https://randomuser.me/api/portraits/women/68.jpg" },
      { id: "user-3", name: "Mike", avatar: "https://randomuser.me/api/portraits/men/31.jpg" }
    ]
  },
  {
    id: "chemistry",
    name: "Chemistry Lab",
    description: "Organic chemistry and lab experiments",
    image: GROUP_IMAGES[1],
    isPublic: true,
    isJoined: false,
    memberCount: 15,
    coverPhotoFile: null,
    members: [
      { id: "user-4", name: "Anna", avatar: "https://randomuser.me/api/portraits/women/44.jpg" }
    ]
  },
];

// --- INITIAL MY GROUPS ---
const INITIAL_MY_GROUPS: Group[] = [
  {
    id: "biology",
    name: "Biology Study Group",
    description: "Advanced molecular biology and genetics discussions",
    image: GROUP_IMAGES[0],
    isPublic: true,
    isJoined: true,
    memberCount: 24,
    coverPhotoFile: null,
    members: [
      { id: "user-5", name: "Sarah", avatar: "https://randomuser.me/api/portraits/women/68.jpg" },
      { id: "user-6", name: "Mike", avatar: "https://randomuser.me/api/portraits/men/31.jpg" }
    ]
  },
  {
    id: "law",
    name: "Constitutional Law",
    description: "Exploring constitutional principles and case studies",
    image: GROUP_IMAGES[1],
    isPublic: true,
    isJoined: true,
    memberCount: 18,
    coverPhotoFile: null,
    members: [
      { id: "user-7", name: "Anna", avatar: "https://randomuser.me/api/portraits/women/44.jpg" }
    ]
  },
];

function StudyGroupsInner() {
  const {
    myGroups,
    discoverGroups,
    isLoadingMyGroups,
    isLoadingDiscoverGroups,
    errorMyGroups,
    errorDiscoverGroups
  } = useStudyGroups();

  const [activeTab, setActiveTab] = useState<"my" | "discover">("my");
  const [privacyFilter, setPrivacyFilter] = useState<"all" | "public" | "private">("all");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [profilePreviewUserId, setProfilePreviewUserId] = useState<string | null>(null);

  // Backend mutations
  const createGroupMutation = useCreateStudyGroup();
  const joinGroupMutation = useJoinStudyGroup();
  const leaveGroupMutation = useLeaveStudyGroup();


  // Transform backend data to match UI expectations
  const transformedMyGroups = myGroups.map(group => ({
    id: group.id,
    name: group.name,
    description: group.description || '',
    image: group.cover_image_url || 'https://images.unsplash.com/photo-1466721591366-2d5fba72006d?auto=format&fit=crop&w=600&q=80',
    isPublic: group.privacy === 'public',
    isJoined: true, // Always true for myGroups
    memberCount: group.member_count,
    members: group.members || [],
  }));

  const transformedDiscoverGroups = discoverGroups.map(group => ({
    id: group.id,
    name: group.name,
    description: group.description || '',
    image: group.cover_image_url || 'https://images.unsplash.com/photo-1466721591366-2d5fba72006d?auto=format&fit=crop&w=600&q=80',
    isPublic: group.privacy === 'public',
    isJoined: false, // Always false for discoverGroups
    memberCount: group.member_count,
    members: group.members || [],
  }));

  // Apply privacy filter
  const filterGroupsByPrivacy = (groups: Group[]) => {
    if (privacyFilter === "all") return groups;
    return groups.filter(group =>
      privacyFilter === "public" ? group.isPublic : !group.isPublic
    );
  };

  // Used for unified UI grid
  const allGroups = activeTab === "my" ? transformedMyGroups : transformedDiscoverGroups;
  const groups = filterGroupsByPrivacy(allGroups);
  const isLoading = activeTab === "my" ? isLoadingMyGroups : isLoadingDiscoverGroups;
  const error = activeTab === "my" ? errorMyGroups : errorDiscoverGroups;

  // Privacy filter counts
  const publicCount = allGroups.filter(g => g.isPublic).length;
  const privateCount = allGroups.filter(g => !g.isPublic).length;
  const totalCount = allGroups.length;

  // Helper: on join/leave actions from GroupsGrid (using group id)
  const handleJoinGroup = async (groupId: string) => {
    try {
      await joinGroupMutation.mutateAsync(groupId);
      toast.success("Successfully joined the group!");
    } catch (error) {
      toast.error("Failed to join group");
    }
  };

  const handleLeaveGroup = async (groupId: string) => {
    try {
      await leaveGroupMutation.mutateAsync(groupId);
      toast.success("Successfully left the group!");
    } catch (error) {
      toast.error("Failed to leave group");
    }
  };

  // Handle profile preview
  const handleProfilePreview = (userId: string) => {
    setProfilePreviewUserId(userId);
  };



  // Handle new group add
  const handleAddGroup = async (data: {
    name: string;
    description: string;
    isPublic: boolean;
    coverPhoto: File | null;
    coverPhotoURL: string | null;
  }) => {
    console.log('Frontend: Creating group with data:', data);
    try {
      const result = await createGroupMutation.mutateAsync({
        name: data.name,
        description: data.description,
        privacy: data.isPublic ? 'public' : 'private',
        cover_image: data.coverPhoto || undefined,
      });
      console.log('Frontend: Group created successfully:', result);
      setActiveTab("my");
      toast.success("Study group created successfully!");
    } catch (error) {
      console.error("Frontend: Failed to create study group:", error);
      toast.error(`Failed to create study group: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-violet-900 via-purple-900 to-indigo-900 flex items-center justify-center pb-20 md:pb-0">
        <Loader2 className="w-8 h-8 animate-spin text-white" />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-violet-900 via-purple-900 to-indigo-900 flex items-center justify-center p-4 pb-20 md:pb-4">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Users className="w-8 h-8 text-red-400" />
          </div>
          <h3 className="text-lg font-medium text-white mb-2">Failed to load groups</h3>
          <p className="text-white/70 mb-4">Please try again later.</p>
          <Button 
            onClick={() => window.location.reload()}
            className="bg-white/10 hover:bg-white/20 text-white"
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }

  const emptyState = (
    <div className="text-center py-12">
      <div className="bg-white/5 rounded-2xl p-8 max-w-md mx-auto">
        <div className="w-16 h-16 bg-violet-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
          <Users className="w-8 h-8 text-violet-400" />
        </div>
        <h3 className="text-lg font-medium text-white mb-2">No groups found</h3>
        <p className="text-white/70 mb-6">
          {activeTab === "my"
            ? "You haven't joined any study groups yet."
            : "No groups available to discover right now."}
        </p>
        {activeTab === "discover" && (
          <Button 
            onClick={() => setDialogOpen(true)}
            className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white"
          >
            Create a Group
          </Button>
        )}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-900 via-purple-900 to-indigo-900 p-4 sm:p-6 pb-20 md:pb-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-white">Study Groups</h1>
            <p className="text-white/80 text-sm sm:text-base mt-1">
              Join and collaborate with fellow students in public or private study communities.
            </p>
          </div>
          <Button 
            onClick={() => setDialogOpen(true)}
            className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white shadow-lg"
          >
            Create Group
          </Button>
        </div>

        <AddGroupDialog open={dialogOpen} onOpenChange={setDialogOpen} onAddGroup={handleAddGroup} />

        {/* Tabs */}
        <div className="bg-white/5 rounded-xl p-1 mb-6">
          <StudyGroupsTabs
            activeTab={activeTab}
            onTabChange={setActiveTab}
            myGroupsCount={myGroups.length}
            discoverGroupsCount={discoverGroups.length}
          />
        </div>

        {/* Privacy Filter */}
        {totalCount > 0 && (
          <PrivacyFilter
            activeFilter={privacyFilter}
            onFilterChange={setPrivacyFilter}
            publicCount={publicCount}
            privateCount={privateCount}
            totalCount={totalCount}
          />
        )}

        {/* Groups Grid */}
        <div className="bg-white/5 rounded-xl p-4 sm:p-6">
          <GroupsGrid
            groups={groups}
            emptyState={emptyState}
            onJoinGroup={handleJoinGroup}
            onLeaveGroup={handleLeaveGroup}
            onProfilePreview={handleProfilePreview}
          />
        </div>

        {/* Profile Preview Dialog */}
        <ProfilePreviewDialog
          userId={profilePreviewUserId}
          open={!!profilePreviewUserId}
          onOpenChange={(open) => {
            if (!open) {
              setProfilePreviewUserId(null);
            }
          }}

        />
      </div>
    </div>
  );
}

// Main provider wrapper
export const StudyGroups = () => {
  return (
    <StudyGroupsProvider>
      <StudyGroupsInner />
    </StudyGroupsProvider>
  );
};
