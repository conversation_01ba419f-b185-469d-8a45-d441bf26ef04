import React from "react";
import {
  Home,
  BookOpen,
  UploadCloud,
  SortAsc,
  Calendar,
  Brain,
  Image,
  Users,
  PencilLine,
  TrendingUp,
  FileText,
  UserRound,
} from "lucide-react";
import { Link, useLocation } from "react-router-dom";

const sidebarLinks = [
  { to: "/", icon: Home, label: "Home" },
  { to: "/profile", icon: UserRound, label: "Profile" },
  { to: "/library", icon: BookOpen, label: "Library" },
  { to: "/upload-files", icon: UploadCloud, label: "Upload Files" },
  { to: "/sort-notes", icon: SortAsc, label: "Sort Notes" },
  { to: "/revision-planner", icon: Calendar, label: "Revision Planner" },
  { to: "/ask-ai-tutor", icon: Brain, label: "Ask AI Tutor" },
  { to: "/image-to-notes", icon: Image, label: "Image to Notes" },
  { to: "/study-groups", icon: Users, label: "Study Groups" },
  { to: "/take-notes", icon: PencilLine, label: "Take Notes" },
  { to: "/progress", icon: TrendingUp, label: "Progress" },
  { to: "/past-papers", icon: FileText, label: "Past Papers" },
];

const Sidebar = () => {
  const location = useLocation();
  return (
    <aside className="hidden md:flex flex-col bg-white w-20 pt-7 border-r min-h-screen items-center gap-8 shadow-sm">
      {sidebarLinks.map((l) => (
        <Link
          key={l.to}
          to={l.to}
          className={`p-3 rounded-lg ${
            location.pathname === l.to
              ? "bg-violet-100 text-violet-700"
              : "text-gray-400 hover:bg-gray-100"
          }`}
          title={l.label}
        >
          <l.icon size={28} />
        </Link>
      ))}
    </aside>
  );
};

export default Sidebar;
