import React, { useState, useRef, useCallback } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { 
  RotateCw, 
  RotateCcw, 
  ZoomIn, 
  ZoomOut, 
  Crop,
  RefreshCw,
  ArrowRight,
  Download
} from 'lucide-react';
import { toast } from 'sonner';

interface ImageEditorProps {
  imageUrl: string;
  onEditComplete: (editedImageUrl: string) => void;
  onNext: () => void;
}

const ImageEditor: React.FC<ImageEditorProps> = ({ 
  imageUrl, 
  onEditComplete, 
  onNext 
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const [rotation, setRotation] = useState(0);
  const [zoom, setZoom] = useState(100);
  const [brightness, setBrightness] = useState(100);
  const [contrast, setContrast] = useState(100);
  const [isProcessing, setIsProcessing] = useState(false);

  const applyFilters = useCallback(() => {
    const canvas = canvasRef.current;
    const image = imageRef.current;
    if (!canvas || !image) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = image.naturalWidth;
    canvas.height = image.naturalHeight;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Apply transformations
    ctx.save();
    
    // Move to center for rotation
    ctx.translate(canvas.width / 2, canvas.height / 2);
    
    // Apply rotation
    ctx.rotate((rotation * Math.PI) / 180);
    
    // Apply zoom
    const scale = zoom / 100;
    ctx.scale(scale, scale);
    
    // Apply filters
    ctx.filter = `brightness(${brightness}%) contrast(${contrast}%)`;
    
    // Draw image
    ctx.drawImage(
      image,
      -image.naturalWidth / 2,
      -image.naturalHeight / 2,
      image.naturalWidth,
      image.naturalHeight
    );
    
    ctx.restore();
  }, [rotation, zoom, brightness, contrast]);

  const handleRotateLeft = () => {
    setRotation(prev => prev - 90);
  };

  const handleRotateRight = () => {
    setRotation(prev => prev + 90);
  };

  const handleReset = () => {
    setRotation(0);
    setZoom(100);
    setBrightness(100);
    setContrast(100);
  };

  const handleApplyChanges = async () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    setIsProcessing(true);
    try {
      // Apply current filters
      applyFilters();
      
      // Convert canvas to blob
      canvas.toBlob((blob) => {
        if (blob) {
          const editedUrl = URL.createObjectURL(blob);
          onEditComplete(editedUrl);
          toast.success('Image edited successfully!');
        }
        setIsProcessing(false);
      }, 'image/png', 0.9);
    } catch (error) {
      toast.error('Failed to apply changes');
      setIsProcessing(false);
    }
  };

  const handleSkipEditing = () => {
    onNext();
  };

  const downloadEditedImage = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    applyFilters();
    
    const link = document.createElement('a');
    link.download = 'edited-image.png';
    link.href = canvas.toDataURL();
    link.click();
  };

  // Apply filters when values change
  React.useEffect(() => {
    if (imageRef.current?.complete) {
      applyFilters();
    }
  }, [applyFilters]);

  return (
    <Card className="p-4 md:p-6 bg-white/95 backdrop-blur-sm">
      <div className="text-center mb-4 md:mb-6">
        <h2 className="text-xl md:text-2xl font-bold text-gray-800 mb-2">Edit & Enhance Image</h2>
        <p className="text-sm md:text-base text-gray-600">
          Crop, rotate, and adjust your image for better OCR results
        </p>
      </div>

      <div className="flex flex-col lg:grid lg:grid-cols-2 gap-4 md:gap-6">
        {/* Image Preview */}
        <div className="space-y-3 md:space-y-4 order-1">
          <div className="relative bg-gray-100 rounded-lg overflow-hidden aspect-video">
            <img
              ref={imageRef}
              src={imageUrl}
              alt="Original"
              className="w-full h-full object-contain"
              onLoad={applyFilters}
              style={{
                transform: `rotate(${rotation}deg) scale(${zoom / 100})`,
                filter: `brightness(${brightness}%) contrast(${contrast}%)`,
                transition: 'transform 0.3s ease, filter 0.3s ease'
              }}
            />
          </div>

          {/* Quick Actions */}
          <div className="flex gap-2 justify-center">
            <Button variant="outline" size="sm" onClick={handleRotateLeft} className="p-2">
              <RotateCcw className="w-3 h-3 md:w-4 md:h-4" />
              <span className="sr-only">Rotate Left</span>
            </Button>
            <Button variant="outline" size="sm" onClick={handleRotateRight} className="p-2">
              <RotateCw className="w-3 h-3 md:w-4 md:h-4" />
              <span className="sr-only">Rotate Right</span>
            </Button>
            <Button variant="outline" size="sm" onClick={handleReset} className="p-2">
              <RefreshCw className="w-3 h-3 md:w-4 md:h-4" />
              <span className="sr-only">Reset</span>
            </Button>
            <Button variant="outline" size="sm" onClick={downloadEditedImage} className="p-2">
              <Download className="w-3 h-3 md:w-4 md:h-4" />
              <span className="sr-only">Download</span>
            </Button>
          </div>
        </div>

        {/* Controls */}
        <div className="space-y-4 md:space-y-6 order-2">
          {/* Rotation */}
          <div>
            <label className="block text-xs md:text-sm font-medium text-gray-700 mb-2">
              Rotation: {rotation}°
            </label>
            <Slider
              value={[rotation]}
              onValueChange={(value) => setRotation(value[0])}
              min={-180}
              max={180}
              step={1}
              className="w-full"
            />
          </div>

          {/* Zoom */}
          <div>
            <label className="block text-xs md:text-sm font-medium text-gray-700 mb-2">
              Zoom: {zoom}%
            </label>
            <Slider
              value={[zoom]}
              onValueChange={(value) => setZoom(value[0])}
              min={50}
              max={200}
              step={5}
              className="w-full"
            />
          </div>

          {/* Brightness */}
          <div>
            <label className="block text-xs md:text-sm font-medium text-gray-700 mb-2">
              Brightness: {brightness}%
            </label>
            <Slider
              value={[brightness]}
              onValueChange={(value) => setBrightness(value[0])}
              min={50}
              max={150}
              step={5}
              className="w-full"
            />
          </div>

          {/* Contrast */}
          <div>
            <label className="block text-xs md:text-sm font-medium text-gray-700 mb-2">
              Contrast: {contrast}%
            </label>
            <Slider
              value={[contrast]}
              onValueChange={(value) => setContrast(value[0])}
              min={50}
              max={150}
              step={5}
              className="w-full"
            />
          </div>

          {/* Action Buttons */}
          <div className="space-y-3 pt-4">
            <Button
              onClick={handleApplyChanges}
              className="w-full text-sm"
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <RefreshCw className="w-3 h-3 md:w-4 md:h-4 mr-2 animate-spin" />
                  <span className="hidden sm:inline">Applying Changes...</span>
                  <span className="sm:hidden">Applying...</span>
                </>
              ) : (
                <>
                  <ArrowRight className="w-3 h-3 md:w-4 md:h-4 mr-2" />
                  <span className="hidden sm:inline">Apply & Continue to OCR</span>
                  <span className="sm:hidden">Apply & Continue</span>
                </>
              )}
            </Button>

            <Button
              variant="outline"
              onClick={handleSkipEditing}
              className="w-full text-sm"
            >
              <span className="hidden sm:inline">Skip Editing & Continue</span>
              <span className="sm:hidden">Skip & Continue</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Hidden canvas for processing */}
      <canvas
        ref={canvasRef}
        className="hidden"
      />

      {/* Tips */}
      <div className="mt-4 md:mt-6 p-3 md:p-4 bg-yellow-50 rounded-lg">
        <h4 className="font-medium text-yellow-800 mb-2 text-sm md:text-base">✨ Editing Tips:</h4>
        <ul className="text-xs md:text-sm text-yellow-700 space-y-1">
          <li>• Rotate the image to make text horizontal</li>
          <li>• Increase contrast to make text more distinct</li>
          <li>• Adjust brightness if the image is too dark or bright</li>
          <li>• Use zoom to focus on specific text areas</li>
        </ul>
      </div>
    </Card>
  );
};

export default ImageEditor;
