import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  Loader2, 
  CheckCircle, 
  AlertCircle,
  RefreshCw,
  ArrowRight,
  Eye,
  Download
} from 'lucide-react';
import { toast } from 'sonner';
import Tesseract from 'tesseract.js';

interface OCRProcessorProps {
  imageUrl: string;
  onOCRComplete: (extractedText: string) => void;
  isProcessing: boolean;
  setIsProcessing: (processing: boolean) => void;
}

const OCRProcessor: React.FC<OCRProcessorProps> = ({
  imageUrl,
  onOCRComplete,
  isProcessing,
  setIsProcessing
}) => {
  const [progress, setProgress] = useState(0);
  const [status, setStatus] = useState<string>('');
  const [extractedText, setExtractedText] = useState<string>('');
  const [confidence, setConfidence] = useState<number>(0);
  const [processingTime, setProcessingTime] = useState<number>(0);
  const [hasStarted, setHasStarted] = useState(false);

  const startOCR = async () => {
    setIsProcessing(true);
    setHasStarted(true);
    setProgress(0);
    setStatus('Initializing OCR...');
    setExtractedText('');
    setConfidence(0);
    
    const startTime = Date.now();

    try {
      const worker = await Tesseract.createWorker('eng', 1, {
        logger: (m) => {
          if (m.status === 'recognizing text') {
            const progressPercent = Math.round(m.progress * 100);
            setProgress(progressPercent);
            setStatus(`Recognizing text... ${progressPercent}%`);
          } else {
            setStatus(m.status);
          }
        }
      });

      setStatus('Processing image...');
      
      const { data } = await worker.recognize(imageUrl);
      
      const endTime = Date.now();
      setProcessingTime(endTime - startTime);
      
      if (data.text.trim()) {
        setExtractedText(data.text);
        setConfidence(data.confidence);
        setStatus('Text extraction completed!');
        setProgress(100);
        onOCRComplete(data.text);
        toast.success(`Text extracted successfully! Confidence: ${data.confidence.toFixed(1)}%`);
      } else {
        setStatus('No text found in image');
        toast.warning('No text was detected in the image. Try adjusting the image or using a different one.');
      }

      await worker.terminate();
    } catch (error) {
      console.error('OCR Error:', error);
      setStatus('OCR processing failed');
      toast.error('Failed to extract text from image. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const retryOCR = () => {
    startOCR();
  };

  const downloadText = () => {
    if (!extractedText) return;
    
    const blob = new Blob([extractedText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'extracted-text.txt';
    link.click();
    URL.revokeObjectURL(url);
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'bg-green-100 text-green-800';
    if (confidence >= 60) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 80) return 'High';
    if (confidence >= 60) return 'Medium';
    return 'Low';
  };

  return (
    <Card className="p-4 md:p-6 bg-white/95 backdrop-blur-sm">
      <div className="text-center mb-4 md:mb-6">
        <FileText className="w-12 h-12 md:w-16 md:h-16 mx-auto text-blue-500 mb-3 md:mb-4" />
        <h2 className="text-xl md:text-2xl font-bold text-gray-800 mb-2">Extract Text with OCR</h2>
        <p className="text-sm md:text-base text-gray-600">
          Using advanced OCR technology to convert your image to editable text
        </p>
      </div>

      <div className="flex flex-col lg:grid lg:grid-cols-2 gap-4 md:gap-6">
        {/* Image Preview */}
        <div className="space-y-3 md:space-y-4 order-1">
          <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden">
            <img
              src={imageUrl}
              alt="Image for OCR"
              className="w-full h-full object-contain"
            />
          </div>

          {!hasStarted && (
            <Button onClick={startOCR} className="w-full" size="lg">
              <FileText className="w-4 h-4 mr-2" />
              <span className="hidden sm:inline">Start Text Extraction</span>
              <span className="sm:hidden">Start OCR</span>
            </Button>
          )}
        </div>

        {/* OCR Status and Results */}
        <div className="space-y-3 md:space-y-4 order-2">
          {/* Processing Status */}
          {isProcessing && (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Loader2 className="w-4 h-4 md:w-5 md:h-5 animate-spin text-blue-500" />
                <span className="font-medium text-sm md:text-base">{status}</span>
              </div>
              <Progress value={progress} className="w-full" />
              <p className="text-xs md:text-sm text-gray-600">
                This may take a few moments depending on image complexity...
              </p>
            </div>
          )}

          {/* Results */}
          {extractedText && !isProcessing && (
            <div className="space-y-3 md:space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                <h3 className="text-base md:text-lg font-semibold flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 md:w-5 md:h-5 text-green-500" />
                  <span className="hidden sm:inline">Extraction Complete</span>
                  <span className="sm:hidden">Complete</span>
                </h3>
                <div className="flex items-center gap-2">
                  <Badge className={`${getConfidenceColor(confidence)} text-xs`}>
                    {getConfidenceLabel(confidence)} ({confidence.toFixed(1)}%)
                  </Badge>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-2 md:gap-4 text-center">
                <div className="bg-gray-50 p-2 md:p-3 rounded-lg">
                  <p className="text-lg md:text-2xl font-bold text-blue-600">
                    {extractedText.length}
                  </p>
                  <p className="text-xs md:text-sm text-gray-600">
                    <span className="hidden sm:inline">Characters</span>
                    <span className="sm:hidden">Chars</span>
                  </p>
                </div>
                <div className="bg-gray-50 p-2 md:p-3 rounded-lg">
                  <p className="text-lg md:text-2xl font-bold text-green-600">
                    {extractedText.split(/\s+/).filter(w => w.length > 0).length}
                  </p>
                  <p className="text-xs md:text-sm text-gray-600">Words</p>
                </div>
                <div className="bg-gray-50 p-2 md:p-3 rounded-lg">
                  <p className="text-lg md:text-2xl font-bold text-purple-600">
                    {(processingTime / 1000).toFixed(1)}s
                  </p>
                  <p className="text-xs md:text-sm text-gray-600">Time</p>
                </div>
              </div>

              {/* Text Preview */}
              <div className="bg-gray-50 p-3 md:p-4 rounded-lg max-h-40 md:max-h-48 overflow-y-auto">
                <h4 className="font-medium mb-2 text-sm md:text-base">
                  <span className="hidden sm:inline">Extracted Text Preview:</span>
                  <span className="sm:hidden">Text Preview:</span>
                </h4>
                <p className="text-xs md:text-sm whitespace-pre-wrap text-gray-700">
                  {extractedText.substring(0, 500)}
                  {extractedText.length > 500 && '...'}
                </p>
              </div>

              {/* Action Buttons */}
              <div className="space-y-2">
                <Button onClick={() => onOCRComplete(extractedText)} className="w-full text-sm">
                  <ArrowRight className="w-3 h-3 md:w-4 md:h-4 mr-2" />
                  <span className="hidden sm:inline">Continue to Edit Text</span>
                  <span className="sm:hidden">Continue</span>
                </Button>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={downloadText} className="flex-1 text-xs md:text-sm">
                    <Download className="w-3 h-3 md:w-4 md:h-4 mr-1 md:mr-2" />
                    <span className="hidden sm:inline">Download Text</span>
                    <span className="sm:hidden">Download</span>
                  </Button>
                  <Button variant="outline" onClick={retryOCR} className="flex-1 text-xs md:text-sm">
                    <RefreshCw className="w-3 h-3 md:w-4 md:h-4 mr-1 md:mr-2" />
                    <span className="hidden sm:inline">Retry OCR</span>
                    <span className="sm:hidden">Retry</span>
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Error State */}
          {!isProcessing && hasStarted && !extractedText && (
            <div className="text-center space-y-3 md:space-y-4">
              <AlertCircle className="w-10 h-10 md:w-12 md:h-12 mx-auto text-red-500" />
              <div>
                <h3 className="text-base md:text-lg font-semibold text-red-700">No Text Detected</h3>
                <p className="text-sm md:text-base text-gray-600">
                  We couldn't find any text in this image. Try adjusting the image or using a different one.
                </p>
              </div>
              <Button onClick={retryOCR} variant="outline" className="text-sm">
                <RefreshCw className="w-3 h-3 md:w-4 md:h-4 mr-2" />
                Try Again
              </Button>
            </div>
          )}

          {/* Initial State */}
          {!hasStarted && (
            <div className="text-center space-y-3 md:space-y-4">
              <Eye className="w-10 h-10 md:w-12 md:h-12 mx-auto text-gray-400" />
              <div>
                <h3 className="text-base md:text-lg font-semibold text-gray-700">Ready for OCR</h3>
                <p className="text-sm md:text-base text-gray-600">
                  <span className="hidden sm:inline">Click "Start Text Extraction" to begin processing your image.</span>
                  <span className="sm:hidden">Tap "Start OCR" to begin processing.</span>
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Tips */}
      <div className="mt-4 md:mt-6 p-3 md:p-4 bg-blue-50 rounded-lg">
        <h4 className="font-medium text-blue-800 mb-2 text-sm md:text-base">🔍 OCR Tips:</h4>
        <ul className="text-xs md:text-sm text-blue-700 space-y-1">
          <li>• Higher confidence scores indicate more accurate text extraction</li>
          <li>• If confidence is low, try editing the image for better contrast</li>
          <li>• OCR works best with clear, horizontal text</li>
          <li>• Processing time varies based on image size and complexity</li>
        </ul>
      </div>
    </Card>
  );
};

export default OCRProcessor;
