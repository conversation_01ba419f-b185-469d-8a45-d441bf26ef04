import React, { useCallback, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Upload, Image as ImageIcon, X, FileImage } from 'lucide-react';
import { toast } from 'sonner';

interface ImageUploaderProps {
  onImageUpload: (file: File) => void;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({ onImageUpload }) => {
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const validateFile = (file: File): boolean => {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Please upload a valid image file (JPG, PNG, or WebP)');
      return false;
    }

    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      toast.error('File size must be less than 10MB');
      return false;
    }

    return true;
  };

  const handleFile = useCallback((file: File) => {
    if (!validateFile(file)) return;

    setSelectedFile(file);
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
  }, []);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFile(files[0]);
    }
  }, [handleFile]);

  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files[0]) {
      handleFile(files[0]);
    }
  }, [handleFile]);

  const handleUpload = () => {
    if (selectedFile) {
      onImageUpload(selectedFile);
    }
  };

  const handleRemove = () => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    setSelectedFile(null);
    setPreviewUrl(null);
  };

  return (
    <Card className="p-4 md:p-8 bg-white/95 backdrop-blur-sm">
      <div className="text-center mb-4 md:mb-6">
        <ImageIcon className="w-12 h-12 md:w-16 md:h-16 mx-auto text-violet-500 mb-3 md:mb-4" />
        <h2 className="text-xl md:text-2xl font-bold text-gray-800 mb-2">Upload Image</h2>
        <p className="text-sm md:text-base text-gray-600">
          Upload an image to extract text using OCR technology
        </p>
      </div>

      {!selectedFile ? (
        <div
          className={`border-2 border-dashed rounded-xl p-6 md:p-8 text-center transition-colors ${
            dragActive
              ? 'border-violet-500 bg-violet-50'
              : 'border-gray-300 hover:border-violet-400 hover:bg-gray-50'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <Upload className="w-10 h-10 md:w-12 md:h-12 mx-auto text-gray-400 mb-3 md:mb-4" />
          <h3 className="text-base md:text-lg font-semibold text-gray-700 mb-2">
            <span className="hidden sm:inline">Drag and drop your image here</span>
            <span className="sm:hidden">Upload your image</span>
          </h3>
          <p className="text-sm md:text-base text-gray-500 mb-4">
            <span className="hidden sm:inline">or click to browse files</span>
            <span className="sm:hidden">Tap to browse files</span>
          </p>
          
          <input
            type="file"
            accept="image/jpeg,image/jpg,image/png,image/webp"
            onChange={handleFileInput}
            className="hidden"
            id="file-upload"
          />
          <label htmlFor="file-upload">
            <Button asChild className="cursor-pointer">
              <span>
                <FileImage className="w-4 h-4 mr-2" />
                Choose Image
              </span>
            </Button>
          </label>

          <div className="mt-4 md:mt-6 text-xs md:text-sm text-gray-500">
            <p className="font-medium mb-1">Supported formats:</p>
            <p>JPG, JPEG, PNG, WebP (max 10MB)</p>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Image Preview */}
          <div className="relative">
            <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden">
              <img
                src={previewUrl!}
                alt="Preview"
                className="w-full h-full object-contain"
              />
            </div>
            <Button
              variant="destructive"
              size="sm"
              className="absolute top-2 right-2"
              onClick={handleRemove}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          {/* File Info */}
          <div className="bg-gray-50 p-3 md:p-4 rounded-lg">
            <h4 className="font-medium text-gray-800 mb-2 text-sm md:text-base">File Information</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4 text-xs md:text-sm">
              <div>
                <span className="text-gray-600">Name:</span>
                <p className="font-medium truncate">{selectedFile.name}</p>
              </div>
              <div>
                <span className="text-gray-600">Size:</span>
                <p className="font-medium">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
              <div>
                <span className="text-gray-600">Type:</span>
                <p className="font-medium">{selectedFile.type}</p>
              </div>
              <div>
                <span className="text-gray-600">Last Modified:</span>
                <p className="font-medium">
                  {new Date(selectedFile.lastModified).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button onClick={handleUpload} className="flex-1">
              <Upload className="w-4 h-4 mr-2" />
              Continue to Edit
            </Button>
            <Button variant="outline" onClick={handleRemove} className="sm:w-auto">
              <X className="w-4 h-4 mr-2" />
              Remove
            </Button>
          </div>
        </div>
      )}

      {/* Tips */}
      <div className="mt-6 md:mt-8 p-3 md:p-4 bg-blue-50 rounded-lg">
        <h4 className="font-medium text-blue-800 mb-2 text-sm md:text-base">💡 Tips for better OCR results:</h4>
        <ul className="text-xs md:text-sm text-blue-700 space-y-1">
          <li>• Use high-resolution images with clear text</li>
          <li>• Ensure good lighting and contrast</li>
          <li>• Avoid blurry or skewed images</li>
          <li>• Text should be horizontal and readable</li>
        </ul>
      </div>
    </Card>
  );
};

export default ImageUploader;
