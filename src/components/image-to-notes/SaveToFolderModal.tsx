import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Save,
  Loader2,
  FolderOpen,
  Plus,
  FileText
} from 'lucide-react';
import { useUnits, useTopics, useCreateUnit, useCreateTopic, useCreateNote, useUploadFile } from '@/hooks/useNotes';
import { toast } from 'sonner';
import jsPDF from 'jspdf';

interface SaveToFolderModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  extractedText: string;
  imageFile: File;
  onSaveComplete: () => void;
}

// Helper function to add image to PDF
const addImageToPDF = (pdf: jsPDF, imageFile: File, margin: number, pageWidth: number, pageHeight: number): Promise<void> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      try {
        const maxImgWidth = pageWidth - 2 * margin;
        const maxImgHeight = pageHeight - 2 * margin - 30;

        let imgWidth = img.width;
        let imgHeight = img.height;

        // Scale image to fit page while maintaining aspect ratio
        const widthRatio = maxImgWidth / imgWidth;
        const heightRatio = maxImgHeight / imgHeight;
        const scale = Math.min(widthRatio, heightRatio);

        imgWidth *= scale;
        imgHeight *= scale;

        canvas.width = imgWidth;
        canvas.height = imgHeight;
        ctx?.drawImage(img, 0, 0, imgWidth, imgHeight);

        const imgData = canvas.toDataURL('image/jpeg', 0.8);
        pdf.addImage(imgData, 'JPEG', margin, margin + 20, imgWidth, imgHeight);
        resolve();
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(imageFile);
  });
};

const SaveToFolderModal: React.FC<SaveToFolderModalProps> = ({
  open,
  onOpenChange,
  extractedText,
  imageFile,
  onSaveComplete
}) => {
  const [selectedUnit, setSelectedUnit] = useState<string>('');
  const [selectedTopic, setSelectedTopic] = useState<string>('');
  const [noteTitle, setNoteTitle] = useState('');
  const [noteContent, setNoteContent] = useState(extractedText);
  // Always save as PDF with both text and image
  const [unitMode, setUnitMode] = useState<'existing' | 'new'>('existing');
  const [topicMode, setTopicMode] = useState<'existing' | 'new'>('existing');
  const [newUnitName, setNewUnitName] = useState('');
  const [newUnitDescription, setNewUnitDescription] = useState('');
  const [newTopicName, setNewTopicName] = useState('');
  const [newTopicDescription, setNewTopicDescription] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const { data: units = [] } = useUnits();
  const { data: topics = [] } = useTopics(selectedUnit);
  const createUnitMutation = useCreateUnit();
  const createTopicMutation = useCreateTopic();
  const createNoteMutation = useCreateNote();
  const uploadFileMutation = useUploadFile();

  // Auto-generate note title from extracted text
  React.useEffect(() => {
    if (extractedText && !noteTitle) {
      const firstLine = extractedText.split('\n')[0].trim();
      const words = firstLine.split(' ').slice(0, 8).join(' '); // First 8 words
      const title = words.length > 50 ? words.substring(0, 50) + '...' : words;
      if (title) {
        setNoteTitle(title);
      }
    }
  }, [extractedText, noteTitle]);

  const generateSuggestions = () => {
    if (!extractedText) return;

    const text = extractedText.toLowerCase();
    const words = text.split(/\s+/);

    // Simple subject detection based on keywords
    const subjects = {
      'mathematics': ['math', 'equation', 'algebra', 'geometry', 'calculus', 'theorem', 'formula'],
      'science': ['experiment', 'hypothesis', 'molecule', 'atom', 'cell', 'biology', 'chemistry', 'physics'],
      'history': ['war', 'century', 'empire', 'revolution', 'ancient', 'medieval', 'modern'],
      'literature': ['poem', 'novel', 'author', 'character', 'plot', 'theme', 'metaphor'],
      'economics': ['market', 'economy', 'trade', 'business', 'finance', 'money', 'investment']
    };

    let suggestedSubject = '';
    let maxMatches = 0;

    Object.entries(subjects).forEach(([subject, keywords]) => {
      const matches = keywords.filter(keyword => words.includes(keyword)).length;
      if (matches > maxMatches) {
        maxMatches = matches;
        suggestedSubject = subject.charAt(0).toUpperCase() + subject.slice(1);
      }
    });

    if (suggestedSubject && unitMode === 'new') {
      setNewUnitName(suggestedSubject);
      toast.success(`Suggested unit: ${suggestedSubject}`);
    }

    // Suggest topic based on first meaningful line
    const lines = extractedText.split('\n').filter(line => line.trim().length > 10);
    if (lines.length > 0 && topicMode === 'new') {
      const topicSuggestion = lines[0].trim().split(' ').slice(0, 4).join(' ');
      if (topicSuggestion) {
        setNewTopicName(topicSuggestion);
        toast.success(`Suggested topic: ${topicSuggestion}`);
      }
    }
  };

  const handleSave = async () => {
    if (!noteTitle.trim()) {
      toast.error('Please enter a note title');
      return;
    }

    // Validate unit selection/creation
    if (unitMode === 'existing' && !selectedUnit) {
      toast.error('Please select a unit');
      return;
    }
    if (unitMode === 'new' && !newUnitName.trim()) {
      toast.error('Please enter a name for the new unit');
      return;
    }

    // Validate topic selection/creation
    if (topicMode === 'existing' && !selectedTopic) {
      toast.error('Please select a topic');
      return;
    }
    if (topicMode === 'new' && !newTopicName.trim()) {
      toast.error('Please enter a name for the new topic');
      return;
    }

    setIsLoading(true);
    try {
      let unitId = selectedUnit;
      let topicId = selectedTopic;

      // Create new unit if needed
      if (unitMode === 'new' && newUnitName.trim()) {
        toast.info('Creating new unit...');
        const newUnit = await createUnitMutation.mutateAsync({
          name: newUnitName.trim(),
          description: newUnitDescription.trim() || 'Created from Image to Notes',
          color: '#6366f1'
        });
        unitId = newUnit.id;
        toast.success(`Unit "${newUnitName}" created successfully!`);
      }

      // Create new topic if needed
      if (topicMode === 'new' && newTopicName.trim() && unitId) {
        toast.info('Creating new topic...');
        const newTopic = await createTopicMutation.mutateAsync({
          name: newTopicName.trim(),
          description: newTopicDescription.trim() || 'Created from Image to Notes',
          unit_id: unitId
        });
        topicId = newTopic.id;
        toast.success(`Topic "${newTopicName}" created successfully!`);
      }

      if (!unitId || !topicId) {
        toast.error('Failed to determine unit and topic for saving');
        return;
      }

      let fileUrl: string | undefined;

      // Always create PDF for SortNotes to ensure consistent format
      // Create PDF from text and original image
      const pdf = new jsPDF();
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const margin = 20;
      const maxWidth = pageWidth - 2 * margin;

      // Add title
      pdf.setFont('helvetica', 'bold');
      pdf.setFontSize(16);
      pdf.text(noteTitle, margin, margin + 10);

      // Add a note about the source
      pdf.setFont('helvetica', 'italic');
      pdf.setFontSize(10);
      pdf.text('Generated from image using OCR technology', margin, margin + 25);

      // Add content
      pdf.setFont('helvetica', 'normal');
      pdf.setFontSize(12);
      const lines = pdf.splitTextToSize(noteContent, maxWidth);

      let y = margin + 40;
      const lineHeight = 7;

      lines.forEach((line: string) => {
        if (y + lineHeight > pageHeight - margin) {
          pdf.addPage();
          y = margin;
        }
        pdf.text(line, margin, y);
        y += lineHeight;
      });

      // Always add original image on a new page
      try {
        // Add original image on a new page
        pdf.addPage();
        pdf.setFont('helvetica', 'bold');
        pdf.setFontSize(14);
        pdf.text('Original Image:', margin, margin + 10);

        // Use helper function to add image
        await addImageToPDF(pdf, imageFile, margin, pageWidth, pageHeight);
      } catch (error) {
        console.warn('Could not add original image to PDF:', error);
        // Continue without the image - add a note instead
        pdf.setFont('helvetica', 'italic');
        pdf.setFontSize(10);
        pdf.text('(Original image could not be included)', margin, margin + 30);
      }

      // Convert PDF to blob and upload
      const pdfBlob = pdf.output('blob');
      const pdfFile = new File([pdfBlob], `${noteTitle}.pdf`, { type: 'application/pdf' });
      const uploadResult = await uploadFileMutation.mutateAsync(pdfFile);
      fileUrl = uploadResult.url;

      // Create the note
      await createNoteMutation.mutateAsync({
        title: noteTitle.trim(),
        content: noteContent.trim(),
        unit_id: unitId,
        topic_id: topicId,
        file_url: fileUrl,
        tags: ['ocr', 'image-to-text']
      });

      toast.success('Note saved successfully!');
      onSaveComplete();
      onOpenChange(false);
    } catch (error) {
      console.error('Save error:', error);
      toast.error('Failed to save note. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setSelectedUnit('');
    setSelectedTopic('');
    setNoteTitle('');
    setNoteContent(extractedText);
    setUnitMode('existing');
    setTopicMode('existing');
    setNewUnitName('');
    setNewUnitDescription('');
    setNewTopicName('');
    setNewTopicDescription('');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-white/95 backdrop-blur-sm">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold flex items-center gap-2">
            <Save className="w-5 h-5 text-green-600" />
            Save to Notes Folder
          </DialogTitle>
          <DialogDescription>
            Choose where to save your extracted text as a PDF document
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Note Details */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="note-title">Note Title *</Label>
              <Input
                id="note-title"
                value={noteTitle}
                onChange={(e) => setNoteTitle(e.target.value)}
                placeholder="Enter a title for your note"
                required
              />
            </div>

            <div>
              <Label htmlFor="note-content">Note Content</Label>
              <Textarea
                id="note-content"
                value={noteContent}
                onChange={(e) => setNoteContent(e.target.value)}
                placeholder="Edit the extracted text if needed"
                rows={6}
                className="font-mono text-sm"
              />
            </div>
          </div>

          {/* Folder Selection */}
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <FolderOpen className="w-5 h-5" />
                Choose Folder Location
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={generateSuggestions}
                className="text-xs"
              >
                ✨ Auto-suggest
              </Button>
            </div>

            {/* Unit Selection */}
            <div className="space-y-3">
              <Label className="text-base font-medium">Unit/Subject</Label>

              {/* Unit Mode Toggle */}
              <Tabs value={unitMode} onValueChange={(value) => {
                setUnitMode(value as 'existing' | 'new');
                // Reset topic selection when switching unit modes
                setSelectedTopic('');
                setTopicMode('existing');
                if (value === 'new') {
                  setSelectedUnit('');
                } else {
                  setNewUnitName('');
                  setNewUnitDescription('');
                  setNewTopicName('');
                  setNewTopicDescription('');
                }
              }}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="existing">Select Existing</TabsTrigger>
                  <TabsTrigger value="new">Create New</TabsTrigger>
                </TabsList>

                <TabsContent value="existing" className="mt-3">
                  <Select
                    value={selectedUnit}
                    onValueChange={(value) => {
                      setSelectedUnit(value);
                      setSelectedTopic(''); // Reset topic when unit changes
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a unit" />
                    </SelectTrigger>
                    <SelectContent>
                      {units.map((unit) => (
                        <SelectItem key={unit.id} value={unit.id}>
                          <div className="flex items-center gap-2">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: unit.color }}
                            />
                            {unit.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {units.length === 0 && (
                    <p className="text-sm text-gray-500 mt-2">
                      No units found. Create a new one instead.
                    </p>
                  )}
                </TabsContent>

                <TabsContent value="new" className="mt-3 space-y-3">
                  <Input
                    value={newUnitName}
                    onChange={(e) => setNewUnitName(e.target.value)}
                    placeholder="Enter unit name (e.g., Mathematics, Biology)"
                  />
                  <Textarea
                    value={newUnitDescription}
                    onChange={(e) => setNewUnitDescription(e.target.value)}
                    placeholder="Enter unit description (optional)"
                    rows={2}
                  />
                </TabsContent>
              </Tabs>
            </div>

            {/* Topic Selection */}
            <div className="space-y-3">
              <Label className="text-base font-medium">Topic</Label>

              {/* Topic Mode Toggle */}
              <Tabs value={topicMode} onValueChange={(value) => {
                setTopicMode(value as 'existing' | 'new');
                if (value === 'new') {
                  setSelectedTopic('');
                } else {
                  setNewTopicName('');
                  setNewTopicDescription('');
                }
              }}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger
                    value="existing"
                    disabled={unitMode === 'existing' && !selectedUnit}
                  >
                    Select Existing
                  </TabsTrigger>
                  <TabsTrigger value="new">Create New</TabsTrigger>
                </TabsList>

                <TabsContent value="existing" className="mt-3">
                  <Select
                    value={selectedTopic}
                    onValueChange={setSelectedTopic}
                    disabled={unitMode === 'existing' && !selectedUnit}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a topic" />
                    </SelectTrigger>
                    <SelectContent>
                      {topics.map((topic) => (
                        <SelectItem key={topic.id} value={topic.id}>
                          {topic.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {unitMode === 'existing' && !selectedUnit && (
                    <p className="text-sm text-gray-500 mt-2">
                      Please select a unit first.
                    </p>
                  )}
                  {unitMode === 'existing' && selectedUnit && topics.length === 0 && (
                    <p className="text-sm text-gray-500 mt-2">
                      No topics found in this unit. Create a new one instead.
                    </p>
                  )}
                </TabsContent>

                <TabsContent value="new" className="mt-3 space-y-3">
                  <Input
                    value={newTopicName}
                    onChange={(e) => setNewTopicName(e.target.value)}
                    placeholder="Enter topic name (e.g., Cell Division, Algebra)"
                  />
                  <Textarea
                    value={newTopicDescription}
                    onChange={(e) => setNewTopicDescription(e.target.value)}
                    placeholder="Enter topic description (optional)"
                    rows={2}
                  />
                </TabsContent>
              </Tabs>
            </div>
          </div>

          {/* Save Format Info */}
          <div className="bg-blue-50 p-3 rounded-md border border-blue-200">
            <div className="flex items-center gap-2 mb-2">
              <FileText className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">PDF Document</span>
            </div>
            <p className="text-sm text-blue-700">
              Your note will be saved as a PDF document containing both the extracted text and the original image for reference.
            </p>
          </div>

          {/* Save Preview */}
          {noteTitle && (unitMode === 'new' ? newUnitName : selectedUnit) && (topicMode === 'new' ? newTopicName : selectedTopic) && (
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <h4 className="font-medium text-blue-800 mb-2">📋 Save Preview:</h4>
              <div className="text-sm text-blue-700 space-y-1">
                <p><strong>Note:</strong> {noteTitle}</p>
                <p><strong>Unit:</strong> {unitMode === 'new' ? `${newUnitName} (new)` : units.find(u => u.id === selectedUnit)?.name}</p>
                <p><strong>Topic:</strong> {topicMode === 'new' ? `${newTopicName} (new)` : topics.find(t => t.id === selectedTopic)?.name}</p>
                <p><strong>Format:</strong> PDF Document with Text & Image</p>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              className="flex-1"
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={
                isLoading ||
                !noteTitle.trim() ||
                (unitMode === 'existing' && !selectedUnit) ||
                (unitMode === 'new' && !newUnitName.trim()) ||
                (topicMode === 'existing' && !selectedTopic) ||
                (topicMode === 'new' && !newTopicName.trim())
              }
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {unitMode === 'new' || topicMode === 'new' ? 'Creating & Saving...' : 'Saving...'}
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  {unitMode === 'new' || topicMode === 'new' ? 'Create & Save Note' : 'Save Note'}
                </>
              )}
            </Button>
          </div>

          {/* Tips */}
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-700 mb-2">💡 Quick Tips:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Create new units for different subjects (e.g., Math, Science)</li>
              <li>• Create new topics for specific chapters or themes</li>
              <li>• PDF format includes both text and original image</li>
              <li>• PDFs are great for sharing, printing, and archiving</li>
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SaveToFolderModal;
