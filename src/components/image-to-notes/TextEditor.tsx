import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Edit3, 
  Eye, 
  Download, 
  Save, 
  FileText,
  Copy,
  RotateCcw,
  Type,
  AlignLeft,
  List
} from 'lucide-react';
import { toast } from 'sonner';
import jsPDF from 'jspdf';
import { cleanMarkdownForDisplay } from '@/utils/markdownCleaner';

interface TextEditorProps {
  initialText: string;
  onTextChange: (text: string) => void;
  onSave: () => void;
}

const TextEditor: React.FC<TextEditorProps> = ({
  initialText,
  onTextChange,
  onSave
}) => {
  const [editedText, setEditedText] = useState(initialText);
  const [activeTab, setActiveTab] = useState<'edit' | 'preview'>('edit');
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);
  const [lineCount, setLineCount] = useState(0);

  // Use ref to track if we should notify parent (avoid infinite loops)
  const lastNotifiedText = useRef(initialText);

  // Sync with initialText changes from parent
  useEffect(() => {
    if (initialText !== editedText) {
      setEditedText(initialText);
      lastNotifiedText.current = initialText;
    }
  }, [initialText]);

  // Update stats when text changes
  useEffect(() => {
    const words = editedText.trim().split(/\s+/).filter(w => w.length > 0);
    const lines = editedText.split('\n');

    setWordCount(words.length);
    setCharCount(editedText.length);
    setLineCount(lines.length);

    // Only notify parent if text actually changed from last notification
    if (editedText !== lastNotifiedText.current) {
      lastNotifiedText.current = editedText;
      onTextChange(editedText);
    }
  }, [editedText, onTextChange]);

  const handleTextChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditedText(e.target.value);
  }, []);

  const copyToClipboard = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(editedText);
      toast.success('Text copied to clipboard!');
    } catch (error) {
      toast.error('Failed to copy text');
    }
  }, [editedText]);

  const downloadAsText = useCallback(() => {
    const blob = new Blob([editedText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'extracted-notes.txt';
    link.click();
    URL.revokeObjectURL(url);
    toast.success('Text file downloaded!');
  }, [editedText]);

  const downloadAsPDF = useCallback(() => {
    try {
      const pdf = new jsPDF();
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const margin = 20;
      const maxWidth = pageWidth - 2 * margin;

      // Set font
      pdf.setFont('helvetica', 'normal');
      pdf.setFontSize(12);

      // Split text into lines that fit the page width
      const lines = pdf.splitTextToSize(editedText, maxWidth);

      let y = margin;
      const lineHeight = 7;

      lines.forEach((line: string) => {
        if (y + lineHeight > pageHeight - margin) {
          pdf.addPage();
          y = margin;
        }
        pdf.text(line, margin, y);
        y += lineHeight;
      });

      pdf.save('extracted-notes.pdf');
      toast.success('PDF downloaded successfully!');
    } catch (error) {
      toast.error('Failed to generate PDF');
    }
  }, [editedText]);

  const resetText = useCallback(() => {
    setEditedText(initialText);
    toast.info('Text reset to original');
  }, [initialText]);

  const formatText = useCallback((type: 'uppercase' | 'lowercase' | 'title') => {
    let formatted = editedText;

    switch (type) {
      case 'uppercase':
        formatted = editedText.toUpperCase();
        break;
      case 'lowercase':
        formatted = editedText.toLowerCase();
        break;
      case 'title':
        formatted = editedText.replace(/\w\S*/g, (txt) =>
          txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
        );
        break;
    }

    setEditedText(formatted);
  }, [editedText]);

  const addBulletPoints = useCallback(() => {
    const lines = editedText.split('\n');
    const bulletLines = lines.map(line => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('•') && !trimmed.startsWith('-')) {
        return `• ${trimmed}`;
      }
      return line;
    });
    setEditedText(bulletLines.join('\n'));
  }, [editedText]);

  return (
    <Card className="p-4 md:p-6 bg-white/95 backdrop-blur-sm">
      <div className="text-center mb-4 md:mb-6">
        <Edit3 className="w-12 h-12 md:w-16 md:h-16 mx-auto text-green-500 mb-3 md:mb-4" />
        <h2 className="text-xl md:text-2xl font-bold text-gray-800 mb-2">Edit Extracted Text</h2>
        <p className="text-sm md:text-base text-gray-600">
          Review and edit the extracted text before saving to your notes
        </p>
      </div>

      {/* Stats Bar */}
      <div className="flex flex-wrap gap-2 md:gap-4 mb-4 md:mb-6 justify-center">
        <Badge variant="secondary" className="flex items-center gap-1 text-xs">
          <Type className="w-3 h-3" />
          <span className="hidden sm:inline">{charCount} characters</span>
          <span className="sm:hidden">{charCount} chars</span>
        </Badge>
        <Badge variant="secondary" className="flex items-center gap-1 text-xs">
          <FileText className="w-3 h-3" />
          {wordCount} words
        </Badge>
        <Badge variant="secondary" className="flex items-center gap-1 text-xs">
          <AlignLeft className="w-3 h-3" />
          {lineCount} lines
        </Badge>
      </div>

      {/* Text Formatting Tools */}
      <div className="mb-4 p-3 bg-gray-50 rounded-lg">
        <h4 className="text-xs md:text-sm font-medium text-gray-700 mb-2">Quick Formatting:</h4>
        <div className="grid grid-cols-2 md:flex md:flex-wrap gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => formatText('uppercase')}
            className="text-xs"
          >
            <span className="hidden sm:inline">UPPERCASE</span>
            <span className="sm:hidden">UPPER</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => formatText('lowercase')}
            className="text-xs"
          >
            <span className="hidden sm:inline">lowercase</span>
            <span className="sm:hidden">lower</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => formatText('title')}
            className="text-xs"
          >
            <span className="hidden sm:inline">Title Case</span>
            <span className="sm:hidden">Title</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={addBulletPoints}
            className="text-xs"
          >
            <List className="w-3 h-3 mr-1" />
            <span className="hidden sm:inline">Add Bullets</span>
            <span className="sm:hidden">Bullets</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={resetText}
            className="text-xs col-span-2 md:col-span-1"
          >
            <RotateCcw className="w-3 h-3 mr-1" />
            Reset
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'edit' | 'preview')}>
        <TabsList className="grid w-full grid-cols-2 mb-4">
          <TabsTrigger value="edit" className="flex items-center gap-2">
            <Edit3 className="w-4 h-4" />
            Edit Text
          </TabsTrigger>
          <TabsTrigger value="preview" className="flex items-center gap-2">
            <Eye className="w-4 h-4" />
            Preview
          </TabsTrigger>
        </TabsList>

        <TabsContent value="edit" className="space-y-4">
          <Textarea
            value={editedText}
            onChange={handleTextChange}
            placeholder="Edit your extracted text here..."
            className="min-h-[300px] md:min-h-[400px] font-mono text-xs md:text-sm"
          />
        </TabsContent>

        <TabsContent value="preview" className="space-y-4">
          <div className="min-h-[300px] md:min-h-[400px] p-3 md:p-4 bg-gray-50 rounded-lg border overflow-auto">
            <div
              className="text-xs md:text-sm text-gray-800"
              dangerouslySetInnerHTML={{
                __html: editedText ? cleanMarkdownForDisplay(editedText) : 'No text to preview'
              }}
            />
          </div>
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <div className="mt-4 md:mt-6 space-y-3">
        <Button onClick={onSave} className="w-full" size="lg">
          <Save className="w-4 h-4 mr-2" />
          Save to Notes Folder
        </Button>

        <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
          <Button variant="outline" onClick={copyToClipboard} className="text-xs md:text-sm">
            <Copy className="w-3 h-3 md:w-4 md:h-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Copy Text</span>
            <span className="sm:hidden">Copy</span>
          </Button>
          <Button variant="outline" onClick={downloadAsText} className="text-xs md:text-sm">
            <Download className="w-3 h-3 md:w-4 md:h-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Download TXT</span>
            <span className="sm:hidden">TXT</span>
          </Button>
          <Button variant="outline" onClick={downloadAsPDF} className="text-xs md:text-sm">
            <Download className="w-3 h-3 md:w-4 md:h-4 mr-1 md:mr-2" />
            <span className="hidden sm:inline">Download PDF</span>
            <span className="sm:hidden">PDF</span>
          </Button>
        </div>
      </div>

      {/* Tips */}
      <div className="mt-4 md:mt-6 p-3 md:p-4 bg-green-50 rounded-lg">
        <h4 className="font-medium text-green-800 mb-2 text-sm md:text-base">📝 Editing Tips:</h4>
        <ul className="text-xs md:text-sm text-green-700 space-y-1">
          <li>• Review the text for any OCR errors and correct them</li>
          <li>• Use formatting tools to clean up the text structure</li>
          <li>• Add bullet points for better organization</li>
          <li>• Preview your changes before saving</li>
        </ul>
      </div>
    </Card>
  );
};

export default TextEditor;
