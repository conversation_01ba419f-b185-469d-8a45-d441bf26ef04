import React, { useState, useEffect } from 'react';
import { useOffline } from '@/hooks/useOffline';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Users,
  WifiOff,
  Save,
  MessageSquare,
  Plus,
  Search,
  Clock,
  User,
  ArrowLeft,
  Lock,
  Globe
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { OfflineGroups as OfflineGroupsUtil, OfflineDiscussions, OfflineGroup, OfflineDiscussion } from '@/utils/offlineStorage';

interface OfflineGroupsProps {
  className?: string;
}

export const OfflineGroups: React.FC<OfflineGroupsProps> = ({ className }) => {
  const { status, saveForOffline, getOfflineData } = useOffline();
  const [groups, setGroups] = useState<OfflineGroup[]>([]);
  const [discussions, setDiscussions] = useState<OfflineDiscussion[]>([]);
  const [selectedGroup, setSelectedGroup] = useState<OfflineGroup | null>(null);
  const [isCreateDiscussionOpen, setIsCreateDiscussionOpen] = useState(false);
  const [isCreateGroupOpen, setIsCreateGroupOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('groups');

  // Form state for creating discussions
  const [discussionForm, setDiscussionForm] = useState({
    title: '',
    content: ''
  });

  const [groupForm, setGroupForm] = useState({
    name: '',
    description: '',
    subject: '',
    image_url: '',
    is_private: false
  });

  // Load groups and discussions on component mount
  useEffect(() => {
    // Add a small delay to ensure IndexedDB is ready
    const timer = setTimeout(() => {
      loadData();
    }, 200);

    return () => clearTimeout(timer);
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      const [groupsData, discussionsData] = await Promise.all([
        getOfflineData('groups'),
        getOfflineData('discussions')
      ]);
      setGroups(groupsData);
      setDiscussions(discussionsData);
    } catch (error) {
      console.error('Error loading groups data:', error);
      toast({
        title: "Error",
        description: "Failed to load groups data.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateDiscussion = async () => {
    if (!selectedGroup || !discussionForm.title.trim() || !discussionForm.content.trim()) {
      toast({
        title: "Validation Error",
        description: "Please provide both title and content for the discussion.",
        variant: "destructive",
      });
      return;
    }

    try {
      console.log('=== CREATING DISCUSSION ===');
      console.log('Group:', selectedGroup.id);
      console.log('Title:', discussionForm.title);
      console.log('Content:', discussionForm.content);

      // Create discussion data with ID
      const discussionId = `discussion_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const discussionData = {
        id: discussionId,
        group_id: selectedGroup.id,
        title: discussionForm.title.trim(),
        content: discussionForm.content.trim(),
        author_id: 'current_user',
        author_name: 'You',
        replies_count: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        synced: false
      };

      console.log('Saving discussion directly to IndexedDB:', discussionData);

      // Save directly to IndexedDB
      const { offlineStorage } = await import('@/utils/offlineStorage');
      await offlineStorage.put('group_discussions', discussionData);
      console.log('Discussion saved successfully!');

      // Reset form
      setDiscussionForm({
        title: '',
        content: ''
      });

      setIsCreateDiscussionOpen(false);

      // Reload discussions
      console.log('Reloading data...');
      await loadData();

      toast({
        title: "Discussion Posted!",
        description: "Your discussion has been posted successfully.",
      });
    } catch (error) {
      console.error('=== DISCUSSION CREATION FAILED ===');
      console.error('Error:', error);
      console.error('Error message:', error?.message);
      console.error('Error stack:', error?.stack);

      toast({
        title: "Failed to create post",
        description: error?.message || "Unknown error - check console for details",
        variant: "destructive",
      });
    }
  };

  const handleCreateGroup = async () => {
    if (!groupForm.name.trim()) {
      toast({
        title: "Validation Error",
        description: "Please provide a group name.",
        variant: "destructive",
      });
      return;
    }

    try {
      console.log('Creating group:', groupForm);

      const groupData = {
        name: groupForm.name.trim(),
        description: groupForm.description.trim(),
        subject: groupForm.subject.trim(),
        image_url: groupForm.image_url.trim(),
        is_private: groupForm.is_private,
        created_by: 'current_user', // This would come from auth context
        member_count: 1, // Creator is the first member
        is_member: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Save directly to IndexedDB
      const { offlineStorage } = await import('@/utils/offlineStorage');
      const groupId = `group_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const completeGroupData = {
        ...groupData,
        id: groupId,
        synced: false,
        offline_id: groupId
      };

      await offlineStorage.put('groups', completeGroupData);
      console.log('Group created successfully!');

      // Reset form
      setGroupForm({
        name: '',
        description: '',
        subject: '',
        image_url: '',
        is_private: false
      });

      setIsCreateGroupOpen(false);

      // Reload groups
      await loadData();

      toast({
        title: "Group Created!",
        description: `${groupForm.is_private ? 'Private' : 'Public'} group "${groupData.name}" has been created successfully.`,
      });
    } catch (error) {
      console.error('Error creating group:', error);
      toast({
        title: "Failed to create group",
        description: error?.message || "Unknown error - check console for details",
        variant: "destructive",
      });
    }
  };

  const getGroupDiscussions = (groupId: string) => {
    return discussions
      .filter(discussion => discussion.group_id === groupId)
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
  };

  const filteredGroups = groups.filter(group =>
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (group.subject && group.subject.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <Users className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-gray-500">Loading groups...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Users className="h-6 w-6" />
            Study Groups
            {status.isOffline && (
              <Badge variant="secondary" className="ml-2">
                <WifiOff className="h-3 w-3 mr-1" />
                Offline
              </Badge>
            )}
          </h2>
          <p className="text-gray-600 mt-1">
            {status.isOffline
              ? "View groups and participate in discussions offline. Posts will sync when you're back online."
              : "Join study groups and participate in discussions."
            }
          </p>
        </div>

        <div className="flex gap-2">
          <Dialog open={isCreateGroupOpen} onOpenChange={setIsCreateGroupOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Group
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-lg">
              <DialogHeader>
                <DialogTitle>Create New Group</DialogTitle>
                <DialogDescription>
                  Create a {groupForm.is_private ? 'private' : 'public'} study group for collaboration and discussions.
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="groupName">Group Name *</Label>
                  <Input
                    id="groupName"
                    placeholder="Enter group name..."
                    value={groupForm.name}
                    onChange={(e) => setGroupForm(prev => ({ ...prev, name: e.target.value }))}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="groupDescription">Description</Label>
                  <Textarea
                    id="groupDescription"
                    placeholder="Describe what this group is about..."
                    value={groupForm.description}
                    onChange={(e) => setGroupForm(prev => ({ ...prev, description: e.target.value }))}
                    className="min-h-[80px]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="groupSubject">Subject</Label>
                  <Input
                    id="groupSubject"
                    placeholder="e.g., Mathematics, Biology, Computer Science..."
                    value={groupForm.subject}
                    onChange={(e) => setGroupForm(prev => ({ ...prev, subject: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="groupImage">Image URL (optional)</Label>
                  <Input
                    id="groupImage"
                    placeholder="https://example.com/image.jpg"
                    value={groupForm.image_url}
                    onChange={(e) => setGroupForm(prev => ({ ...prev, image_url: e.target.value }))}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isPrivate"
                    checked={groupForm.is_private}
                    onChange={(e) => setGroupForm(prev => ({ ...prev, is_private: e.target.checked }))}
                    className="rounded"
                  />
                  <Label htmlFor="isPrivate" className="flex items-center gap-2">
                    {groupForm.is_private ? <Lock className="h-4 w-4" /> : <Globe className="h-4 w-4" />}
                    Make this group private
                  </Label>
                </div>

                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsCreateGroupOpen(false)}>
                    Cancel
                  </Button>
                  <Button
                    onClick={handleCreateGroup}
                    disabled={!groupForm.name.trim()}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Create Group
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="groups">Groups</TabsTrigger>
          <TabsTrigger value="discussions">Discussions</TabsTrigger>
        </TabsList>

        <TabsContent value="groups" className="space-y-4">
          {/* Search */}
          {groups.length > 0 && (
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search groups..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          )}

          {/* Groups List */}
          {filteredGroups.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Users className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium mb-2">
                  {groups.length === 0 ? 'No groups available' : 'No matching groups'}
                </h3>
                <p className="text-gray-500">
                  {groups.length === 0 
                    ? 'Groups will appear here when available.'
                    : 'Try adjusting your search terms.'
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredGroups.map((group) => (
                <Card 
                  key={group.id} 
                  className={`hover:shadow-md transition-shadow cursor-pointer ${
                    selectedGroup?.id === group.id ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => {
                    setSelectedGroup(group);
                    setActiveTab('discussions');
                  }}
                >
                  {/* Group Image */}
                  {group.image_url && (
                    <div className="aspect-video w-full overflow-hidden rounded-t-lg">
                      <img
                        src={group.image_url}
                        alt={group.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // Hide image if it fails to load
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    </div>
                  )}

                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <CardTitle className="text-lg line-clamp-2 flex items-center gap-2">
                        {group.name}
                        {group.is_private && (
                          <Lock className="h-4 w-4 text-gray-500" />
                        )}
                      </CardTitle>
                      {group.is_member && (
                        <Badge variant="default" className="ml-2 shrink-0">
                          Member
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2 flex-wrap">
                      {group.subject && (
                        <Badge variant="secondary" className="w-fit">
                          {group.subject}
                        </Badge>
                      )}
                      {group.is_private && (
                        <Badge variant="outline" className="w-fit">
                          <Lock className="h-3 w-3 mr-1" />
                          Private
                        </Badge>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    {group.description && (
                      <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                        {group.description}
                      </p>
                    )}
                    
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        {group.member_count} members
                      </div>
                      <div className="flex items-center gap-1">
                        <MessageSquare className="h-3 w-3" />
                        {getGroupDiscussions(group.id).length} discussions
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="discussions" className="space-y-4">
          {selectedGroup ? (
            <div className="space-y-4">
              {/* Selected Group Header */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedGroup(null);
                          setActiveTab('groups');
                        }}
                      >
                        <ArrowLeft className="h-4 w-4" />
                      </Button>
                      <div>
                        <CardTitle className="text-lg">{selectedGroup.name}</CardTitle>
                        <CardDescription>{selectedGroup.description}</CardDescription>
                      </div>
                    </div>
                    
                    {selectedGroup.is_member && (
                      <Dialog open={isCreateDiscussionOpen} onOpenChange={setIsCreateDiscussionOpen}>
                        <DialogTrigger asChild>
                          <Button>
                            <Plus className="h-4 w-4 mr-2" />
                            New Discussion
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-lg">
                          <DialogHeader>
                            <DialogTitle>Start New Discussion</DialogTitle>
                            <DialogDescription>
                              {status.isOffline
                                ? "This discussion will be saved offline and posted when you're back online."
                                : "Start a new discussion in this group."
                              }
                            </DialogDescription>
                          </DialogHeader>

                          <div className="space-y-4">
                            <div className="space-y-2">
                              <Label htmlFor="title">Discussion Title</Label>
                              <Input
                                id="title"
                                placeholder="Enter discussion title..."
                                value={discussionForm.title}
                                onChange={(e) => setDiscussionForm(prev => ({ ...prev, title: e.target.value }))}
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="content">Content</Label>
                              <Textarea
                                id="content"
                                placeholder="What would you like to discuss?"
                                value={discussionForm.content}
                                onChange={(e) => setDiscussionForm(prev => ({ ...prev, content: e.target.value }))}
                                className="min-h-[120px]"
                              />
                            </div>

                            <div className="flex justify-end gap-2">
                              <Button variant="outline" onClick={() => setIsCreateDiscussionOpen(false)}>
                                Cancel
                              </Button>
                              <Button
                                onClick={handleCreateDiscussion}
                                disabled={!discussionForm.title.trim() || !discussionForm.content.trim()}
                              >
                                <Save className="h-4 w-4 mr-2" />
                                Post Discussion
                              </Button>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                    )}
                  </div>
                </CardHeader>
              </Card>

              {/* Discussions List */}
              {getGroupDiscussions(selectedGroup.id).length === 0 ? (
                <Card>
                  <CardContent className="text-center py-8">
                    <MessageSquare className="h-8 w-8 mx-auto mb-3 text-gray-400" />
                    <h3 className="text-lg font-medium mb-2">No discussions yet</h3>
                    <p className="text-gray-500 mb-4">
                      Be the first to start a discussion in this group.
                    </p>
                    {selectedGroup.is_member && (
                      <Button onClick={() => setIsCreateDiscussionOpen(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        Start Discussion
                      </Button>
                    )}
                  </CardContent>
                </Card>
              ) : (
                <div className="space-y-4">
                  {getGroupDiscussions(selectedGroup.id).map((discussion) => (
                    <Card key={discussion.id} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <CardTitle className="text-lg line-clamp-2">{discussion.title}</CardTitle>
                          {!discussion.synced && (
                            <Badge variant="outline" className="ml-2 shrink-0">
                              <WifiOff className="h-3 w-3 mr-1" />
                              Offline
                            </Badge>
                          )}
                        </div>
                        <CardDescription className="flex items-center gap-4 text-xs">
                          <span>By {discussion.author_name}</span>
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {formatDate(discussion.created_at)}
                          </span>
                          <span>{discussion.replies_count} replies</span>
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <p className="text-sm text-gray-600 line-clamp-3">
                          {discussion.content}
                        </p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <Users className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium mb-2">Select a group</h3>
                <p className="text-gray-500">
                  Choose a group from the Groups tab to view its discussions.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default OfflineGroups;
