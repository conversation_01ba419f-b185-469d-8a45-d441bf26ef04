import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useIsAuthenticated } from '@/hooks/useAuth';
import { Loader2 } from 'lucide-react';

interface AuthRedirectProps {
  children: React.ReactNode;
  redirectTo?: string;
  allowedPaths?: string[];
}

/**
 * Component that redirects authenticated users away from public pages
 * and unauthenticated users to login page from protected pages
 */
const AuthRedirect: React.FC<AuthRedirectProps> = ({ 
  children, 
  redirectTo = '/dashboard',
  allowedPaths = []
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, isLoading } = useIsAuthenticated();

  useEffect(() => {
    if (isLoading) return; // Wait for auth state to be determined

    const currentPath = location.pathname;

    // Check localStorage for persistent auth state
    const storedAuthState = localStorage.getItem('studyfam_auth_state');
    const hasStoredAuth = storedAuthState === 'authenticated';

    // Public paths that authenticated users should be redirected away from
    const publicPaths = [
      '/',
      '/login',
      '/register',
      '/contact-us',
      '/privacy-policy',
      '/about-us',
      ...allowedPaths
    ];

    // If user is authenticated (either from hook or localStorage) and on a public page, redirect to dashboard
    if ((isAuthenticated || hasStoredAuth) && publicPaths.includes(currentPath)) {
      console.log('Authenticated user on public page, redirecting to:', redirectTo);
      navigate(redirectTo, { replace: true });
      return;
    }

    // If user is not authenticated and on a protected page, redirect to login
    const protectedPaths = [
      '/dashboard',
      '/upload-files',
      '/sort-notes',
      '/document',
      '/revision-planner',
      '/ask-ai-tutor',
      '/quiz-generator',
      '/image-to-notes',
      '/study-groups',
      '/past-papers',
      '/take-notes',
      '/library',
      '/progress',
      '/search',
      '/profile',
      '/discover',
      '/messages',
      '/reading-timetable',
      '/notifications'
    ];

    const isProtectedPath = protectedPaths.some(path => currentPath.startsWith(path));
    
    if (!isAuthenticated && isProtectedPath) {
      console.log('Unauthenticated user on protected page, redirecting to login');
      navigate('/login', { 
        replace: true,
        state: { from: currentPath } // Save the intended destination
      });
      return;
    }
  }, [isAuthenticated, isLoading, location.pathname, navigate, redirectTo, allowedPaths]);

  // Show loading spinner while determining auth state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-violet-900 via-purple-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-white mb-4 mx-auto" />
          <p className="text-white/80">Loading...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default AuthRedirect;
