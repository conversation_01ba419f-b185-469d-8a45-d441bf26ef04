import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Bell,
  Check,
  CheckCheck,
  Clock,
  Users,
  MessageSquare,
  BookOpen,
  FileText,
  Settings,
  Loader2,
  X
} from 'lucide-react';
import {
  useNotifications,
  useUnreadNotificationCount,
  useMarkNotificationRead,
  useMarkAllNotificationsRead,
  useNotificationColor,
  Notification
} from '@/hooks/useNotifications';
import { formatDistanceToNow } from 'date-fns';

interface NotificationPanelProps {
  onClose?: () => void;
}

const NotificationPanel: React.FC<NotificationPanelProps> = ({ onClose }) => {
  const [showUnreadOnly, setShowUnreadOnly] = useState(false);
  
  const { data: notifications = [], isLoading, refetch } = useNotifications(50, 0, showUnreadOnly);
  const { data: unreadCount = 0 } = useUnreadNotificationCount();
  const markAsReadMutation = useMarkNotificationRead();
  const markAllAsReadMutation = useMarkAllNotificationsRead();

  const handleMarkAsRead = async (notificationId: string, actionUrl?: string) => {
    try {
      await markAsReadMutation.mutateAsync(notificationId);
      
      if (actionUrl) {
        if (onClose) onClose();
        // Navigate to the action URL
        window.location.href = actionUrl;
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsReadMutation.mutateAsync();
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const getNotificationIcon = (type: Notification['type']) => {
    const iconMap = {
      friend_request: Users,
      friend_accepted: Check,
      message: MessageSquare,
      study_group_invite: BookOpen,
      study_group_post: FileText,
      note_shared: FileText,
      system: Settings,
    };

    const IconComponent = iconMap[type] || Bell;
    return <IconComponent className="w-5 h-5" />;
  };

  const getNotificationColor = (type: Notification['type'], read: boolean) => {
    if (read) return 'text-gray-500 bg-gray-50';
    
    const colorMap = {
      friend_request: 'text-blue-600 bg-blue-50',
      friend_accepted: 'text-green-600 bg-green-50',
      message: 'text-purple-600 bg-purple-50',
      study_group_invite: 'text-orange-600 bg-orange-50',
      study_group_post: 'text-indigo-600 bg-indigo-50',
      note_shared: 'text-cyan-600 bg-cyan-50',
      system: 'text-gray-600 bg-gray-50',
    };

    return colorMap[type] || 'text-gray-600 bg-gray-50';
  };

  return (
    <div className="w-96 max-w-[90vw] bg-white border border-gray-200 rounded-lg shadow-xl">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Bell className="w-5 h-5 text-gray-600" />
          <h3 className="font-semibold text-gray-900">Notifications</h3>
          {unreadCount > 0 && (
            <Badge variant="destructive" className="text-xs">
              {unreadCount}
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleMarkAllAsRead}
              disabled={markAllAsReadMutation.isPending}
              className="text-xs"
            >
              {markAllAsReadMutation.isPending ? (
                <Loader2 className="w-3 h-3 animate-spin" />
              ) : (
                <CheckCheck className="w-3 h-3" />
              )}
              Mark all read
            </Button>
          )}
          
          {onClose && (
            <Button variant="ghost" size="icon" onClick={onClose} className="h-8 w-8">
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Filter Toggle */}
      <div className="p-3 border-b bg-gray-50">
        <div className="flex items-center gap-2">
          <Button
            variant={showUnreadOnly ? "outline" : "default"}
            size="sm"
            onClick={() => setShowUnreadOnly(false)}
            className="text-xs"
          >
            All
          </Button>
          <Button
            variant={showUnreadOnly ? "default" : "outline"}
            size="sm"
            onClick={() => setShowUnreadOnly(true)}
            className="text-xs"
          >
            Unread ({unreadCount})
          </Button>
        </div>
      </div>

      {/* Notifications List */}
      <ScrollArea className="h-96">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
            <span className="ml-2 text-gray-500">Loading notifications...</span>
          </div>
        ) : notifications.length === 0 ? (
          <div className="text-center py-8">
            <Bell className="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500 text-sm">
              {showUnreadOnly ? 'No unread notifications' : 'No notifications yet'}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 hover:bg-gray-50 transition-colors cursor-pointer ${
                  !notification.read ? 'bg-blue-50/30' : ''
                }`}
                onClick={() => handleMarkAsRead(notification.id, notification.action_url)}
              >
                <div className="flex items-start gap-3">
                  {/* Icon */}
                  <div className={`p-2 rounded-full ${getNotificationColor(notification.type, notification.read)}`}>
                    {getNotificationIcon(notification.type)}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className={`text-sm font-medium ${
                          notification.read ? 'text-gray-700' : 'text-gray-900'
                        }`}>
                          {notification.title}
                        </h4>
                        <p className={`text-sm mt-1 ${
                          notification.read ? 'text-gray-500' : 'text-gray-700'
                        }`}>
                          {notification.message}
                        </p>
                      </div>
                      
                      {!notification.read && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1 flex-shrink-0" />
                      )}
                    </div>

                    {/* Timestamp */}
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-xs text-gray-400 flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                      </span>
                      
                      {notification.action_url && (
                        <span className="text-xs text-blue-600 hover:text-blue-800">
                          Click to view →
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </ScrollArea>

      {/* Footer */}
      {notifications.length > 0 && (
        <div className="p-3 border-t bg-gray-50">
          <Link
            to="/notifications"
            className="text-sm text-blue-600 hover:text-blue-800 font-medium block text-center"
            onClick={onClose}
          >
            View all notifications
          </Link>
        </div>
      )}
    </div>
  );
};

export default NotificationPanel;
