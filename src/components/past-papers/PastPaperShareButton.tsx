
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { DocumentShareModal } from '@/components/sharing/DocumentShareModal';
import { Share2 } from 'lucide-react';

interface PastPaperShareButtonProps {
  pastPaper: {
    id: string;
    title: string;
    file_url?: string;
    subject: string;
    year?: number;
  };
  size?: 'sm' | 'default' | 'lg';
  variant?: 'default' | 'outline' | 'ghost';
}

export const PastPaperShareButton: React.FC<PastPaperShareButtonProps> = ({
  pastPaper,
  size = 'sm',
  variant = 'outline',
}) => {
  const [showShareModal, setShowShareModal] = useState(false);

  // Only show share button if past paper has a file
  if (!pastPaper.file_url) {
    return null;
  }

  const fileName = `${pastPaper.subject}_${pastPaper.year || 'unknown'}_${pastPaper.title}.pdf`;

  return (
    <>
      <Button
        size={size}
        variant={variant}
        onClick={() => setShowShareModal(true)}
        className="flex items-center gap-2"
      >
        <Share2 className="w-4 h-4" />
        Share
      </Button>

      <DocumentShareModal
        open={showShareModal}
        onOpenChange={setShowShareModal}
        document={{
          url: pastPaper.file_url,
          name: fileName,
          title: pastPaper.title,
        }}
      />
    </>
  );
};
