import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChevronLeft, ChevronRight, Clock, CheckCircle, Circle, Edit, Trash2, MoreVertical } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { ReadingSession, ReadingSubject, ReadingProgress } from '@/hooks/useReadingTimetable';

interface WeeklyViewProps {
  sessions: ReadingSession[];
  subjects: ReadingSubject[];
  progress: ReadingProgress[];
  selectedDate: Date;
  onDateChange: (date: Date) => void;
  onMarkComplete: (data: { sessionId: string; sessionDate: string; completionPercentage?: number }) => Promise<void>;
  onEditSession?: (session: ReadingSession) => void;
  onDeleteSession?: (sessionId: string) => void;
}

const DAYS_OF_WEEK = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

export const WeeklyView: React.FC<WeeklyViewProps> = ({
  sessions,
  subjects,
  progress,
  selectedDate,
  onDateChange,
  onMarkComplete,
  onEditSession,
  onDeleteSession,
}) => {
  // Get the start of the week (Sunday)
  const getWeekStart = (date: Date) => {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day;
    return new Date(d.setDate(diff));
  };

  const weekStart = getWeekStart(selectedDate);
  
  // Generate week dates
  const weekDates = Array.from({ length: 7 }, (_, i) => {
    const date = new Date(weekStart);
    date.setDate(weekStart.getDate() + i);
    return date;
  });

  const navigateWeek = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    newDate.setDate(selectedDate.getDate() + (direction === 'next' ? 7 : -7));
    onDateChange(newDate);
  };

  const formatTime = (time: string) => {
    return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const getSessionsForDay = (dayOfWeek: number) => {
    return sessions.filter(session => session.day_of_week === dayOfWeek)
      .sort((a, b) => a.start_time.localeCompare(b.start_time));
  };

  const getProgressForSession = (sessionId: string, date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    return progress.find(p => p.session_id === sessionId && p.session_date === dateStr);
  };

  const handleMarkComplete = async (session: ReadingSession, date: Date) => {
    try {
      await onMarkComplete({
        sessionId: session.id,
        sessionDate: date.toISOString().split('T')[0],
        completionPercentage: 100,
      });
    } catch (error) {
      console.error('Error marking session complete:', error);
    }
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isPastDate = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const compareDate = new Date(date);
    compareDate.setHours(0, 0, 0, 0);
    return compareDate < today;
  };

  return (
    <div className="space-y-6">
      {/* Week Navigation */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-bold text-white drop-shadow-sm">Weekly Schedule</h3>
          <p className="text-sm text-gray-100 font-medium">
            {weekDates[0].toLocaleDateString('en-US', { month: 'long', day: 'numeric' })} - {' '}
            {weekDates[6].toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateWeek('prev')}
            className="border-slate-400 text-white hover:bg-slate-700 hover:border-slate-300 bg-slate-800/60 font-medium"
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onDateChange(new Date())}
            className="border-slate-400 text-white hover:bg-slate-700 hover:border-slate-300 bg-slate-800/60 px-3 font-medium"
          >
            Today
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateWeek('next')}
            className="border-slate-400 text-white hover:bg-slate-700 hover:border-slate-300 bg-slate-800/60 font-medium"
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Weekly Calendar Grid */}
      <div className="grid grid-cols-1 md:grid-cols-7 gap-4">
        {weekDates.map((date, dayIndex) => {
          const daySessions = getSessionsForDay(dayIndex);
          const isCurrentDay = isToday(date);
          const isPast = isPastDate(date);

          return (
            <Card
              key={dayIndex}
              className={`${isCurrentDay ? 'ring-2 ring-purple-400 bg-purple-900/30' : 'bg-slate-800/60'} min-h-[300px] border-slate-600/50 backdrop-blur-sm`}
            >
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium text-center">
                  <div className={`${isCurrentDay ? 'text-purple-300 font-semibold' : 'text-gray-200'}`}>
                    {DAYS_OF_WEEK[dayIndex]}
                  </div>
                  <div className={`text-lg ${isCurrentDay ? 'text-purple-300 font-bold' : 'text-white'}`}>
                    {date.getDate()}
                  </div>
                </CardTitle>
              </CardHeader>
              
              <CardContent className="pt-0 space-y-2">
                {daySessions.length === 0 ? (
                  <div className="text-center text-slate-400 text-sm py-4">
                    No sessions
                  </div>
                ) : (
                  daySessions.map((session) => {
                    const sessionProgress = getProgressForSession(session.id, date);
                    const isCompleted = sessionProgress?.status === 'completed';
                    // Use session color, fallback to subject color, then default
                    const sessionColor = session.color || session.subject?.color || '#6366f1';

                    return (
                      <div
                        key={session.id}
                        className={`p-3 rounded-lg border-l-4 relative overflow-hidden ${
                          isCompleted ? 'bg-green-600/20' : 'bg-slate-700/60'
                        } shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm`}
                        style={{
                          borderLeftColor: sessionColor,
                          background: isCompleted
                            ? `linear-gradient(135deg, ${sessionColor}15, #16a34a20)`
                            : `linear-gradient(135deg, ${sessionColor}10, rgba(51, 65, 85, 0.6))`
                        }}
                      >
                        {/* Color accent bar */}
                        <div
                          className="absolute top-0 right-0 w-1 h-full opacity-60"
                          style={{ backgroundColor: sessionColor }}
                        />
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <h4 className="text-sm font-medium text-white truncate drop-shadow-sm flex-1 mr-2">
                              {session.title}
                            </h4>

                            <div className="flex items-center gap-1">
                              {isCompleted ? (
                                <CheckCircle className="w-4 h-4 text-green-400 shrink-0" />
                              ) : (
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0 hover:bg-green-600/20"
                                  onClick={() => handleMarkComplete(session, date)}
                                  disabled={!isPast && !isCurrentDay}
                                >
                                  <Circle className="w-4 h-4 text-slate-300 hover:text-green-400" />
                                </Button>
                              )}

                              {/* Edit/Delete Menu */}
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    className="h-6 w-6 p-0 hover:bg-slate-600/40"
                                  >
                                    <MoreVertical className="w-3 h-3 text-slate-300" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="bg-slate-700 border-slate-600">
                                  {onEditSession && (
                                    <DropdownMenuItem
                                      onClick={() => onEditSession(session)}
                                      className="text-white hover:bg-slate-600 cursor-pointer"
                                    >
                                      <Edit className="w-4 h-4 mr-2" />
                                      Edit Session
                                    </DropdownMenuItem>
                                  )}
                                  {onDeleteSession && (
                                    <DropdownMenuItem
                                      onClick={() => onDeleteSession(session.id)}
                                      className="text-red-400 hover:bg-red-600/20 cursor-pointer"
                                    >
                                      <Trash2 className="w-4 h-4 mr-2" />
                                      Delete Session
                                    </DropdownMenuItem>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>

                          <div className="flex items-center gap-1 text-xs text-gray-200">
                            <Clock className="w-3 h-3" />
                            <span>
                              {formatTime(session.start_time)} - {formatTime(session.end_time)}
                            </span>
                          </div>

                          <div className="flex items-center gap-1">
                            <div
                              className="w-2 h-2 rounded-full shadow-sm"
                              style={{ backgroundColor: sessionColor }}
                            />
                            <span className="text-xs text-gray-200 truncate">
                              {session.subject?.name || 'Personal Session'}
                            </span>
                          </div>

                          {session.goals && (
                            <p className="text-xs text-gray-300 line-clamp-2">
                              {session.goals}
                            </p>
                          )}

                          {sessionProgress && sessionProgress.completion_percentage < 100 && (
                            <div className="space-y-1">
                              <div className="flex justify-between text-xs">
                                <span className="text-gray-300">Progress</span>
                                <span className="text-gray-100 font-medium">{sessionProgress.completion_percentage}%</span>
                              </div>
                              <div className="w-full bg-slate-600 rounded-full h-1">
                                <div
                                  className="bg-purple-500 h-1 rounded-full transition-all duration-300"
                                  style={{ width: `${sessionProgress.completion_percentage}%` }}
                                />
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Legend */}
      {subjects.length > 0 && (
        <Card className="bg-slate-800/60 border-slate-600/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-sm text-white font-medium">Subject Legend</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-3">
              {subjects.map((subject) => (
                <div key={subject.id} className="flex items-center gap-2">
                  <div
                    className="w-4 h-4 rounded-full shadow-sm"
                    style={{ backgroundColor: subject.color }}
                  />
                  <span className="text-sm text-gray-200 font-medium">{subject.name}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
