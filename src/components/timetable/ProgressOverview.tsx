import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { TrendingUp, Target, Calendar } from 'lucide-react';
import { ReadingSession, ReadingSubject, ReadingProgress, ReadingStreak } from '@/hooks/useReadingTimetable';

interface ProgressOverviewProps {
  sessions: ReadingSession[];
  subjects: ReadingSubject[];
  progress: ReadingProgress[];
  streaks: ReadingStreak[];
}

export const ProgressOverview: React.FC<ProgressOverviewProps> = ({
  sessions,
  subjects,
  progress,
  streaks,
}) => {
  // Calculate overall statistics
  const totalSessions = sessions.length;
  const completedSessions = progress.filter(p => p.status === 'completed').length;
  const overallCompletionRate = totalSessions > 0 ? (completedSessions / totalSessions) * 100 : 0;

  // Calculate this week's progress
  const getWeekStart = (date: Date) => {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day;
    return new Date(d.setDate(diff));
  };

  const thisWeekStart = getWeekStart(new Date());
  const thisWeekEnd = new Date(thisWeekStart);
  thisWeekEnd.setDate(thisWeekStart.getDate() + 6);

  const thisWeekProgress = progress.filter(p => {
    const progressDate = new Date(p.session_date);
    return progressDate >= thisWeekStart && progressDate <= thisWeekEnd;
  });

  const thisWeekCompleted = thisWeekProgress.filter(p => p.status === 'completed').length;
  const thisWeekTotal = thisWeekProgress.length;
  const thisWeekCompletionRate = thisWeekTotal > 0 ? (thisWeekCompleted / thisWeekTotal) * 100 : 0;



  // Recent activity (last 7 days)
  const last7Days = Array.from({ length: 7 }, (_, i) => {
    const date = new Date();
    date.setDate(date.getDate() - i);
    return date;
  }).reverse();

  const recentActivity = last7Days.map(date => {
    const dateStr = date.toISOString().split('T')[0];
    const dayProgress = progress.filter(p => p.session_date === dateStr);
    const completed = dayProgress.filter(p => p.status === 'completed').length;
    const total = dayProgress.length;
    
    return {
      date,
      completed,
      total,
      completionRate: total > 0 ? (completed / total) * 100 : 0,
    };
  });

  return (
    <div className="space-y-6">
      {/* Overall Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Target className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Overall Progress</p>
                <p className="text-xl font-bold text-gray-900">
                  {Math.round(overallCompletionRate)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Calendar className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">This Week</p>
                <p className="text-xl font-bold text-gray-900">
                  {thisWeekCompleted}/{thisWeekTotal}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>


      </div>

      {/* Recent Activity Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-blue-600" />
            Recent Activity (Last 7 Days)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentActivity.map((day, index) => (
              <div key={index} className="flex items-center gap-4">
                <div className="w-20 text-sm text-gray-600">
                  {day.date.toLocaleDateString('en-US', { 
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric'
                  })}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm text-gray-700">
                      {day.completed}/{day.total} sessions
                    </span>
                    <span className="text-sm font-medium text-gray-900">
                      {Math.round(day.completionRate)}%
                    </span>
                  </div>
                  <Progress value={day.completionRate} className="h-2" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>



      {/* Achievements */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="w-5 h-5 text-yellow-600" />
            Achievements
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* First Session */}
            <div className={`p-4 rounded-lg border ${
              completedSessions > 0 ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'
            }`}>
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-lg ${
                  completedSessions > 0 ? 'bg-green-100' : 'bg-gray-100'
                }`}>
                  <BookOpen className={`w-5 h-5 ${
                    completedSessions > 0 ? 'text-green-600' : 'text-gray-400'
                  }`} />
                </div>
                <div>
                  <p className="font-medium text-gray-900">First Steps</p>
                  <p className="text-sm text-gray-600">Complete your first session</p>
                </div>
              </div>
            </div>

            {/* Week Warrior */}
            <div className={`p-4 rounded-lg border ${
              thisWeekCompletionRate >= 80 ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'
            }`}>
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-lg ${
                  thisWeekCompletionRate >= 80 ? 'bg-blue-100' : 'bg-gray-100'
                }`}>
                  <Calendar className={`w-5 h-5 ${
                    thisWeekCompletionRate >= 80 ? 'text-blue-600' : 'text-gray-400'
                  }`} />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Week Warrior</p>
                  <p className="text-sm text-gray-600">80%+ completion this week</p>
                </div>
              </div>
            </div>

            {/* Streak Master */}
            <div className={`p-4 rounded-lg border ${
              currentStreak >= 7 ? 'bg-orange-50 border-orange-200' : 'bg-gray-50 border-gray-200'
            }`}>
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-lg ${
                  currentStreak >= 7 ? 'bg-orange-100' : 'bg-gray-100'
                }`}>
                  <Flame className={`w-5 h-5 ${
                    currentStreak >= 7 ? 'text-orange-600' : 'text-gray-400'
                  }`} />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Streak Master</p>
                  <p className="text-sm text-gray-600">7+ day reading streak</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
