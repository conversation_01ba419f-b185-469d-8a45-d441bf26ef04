import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChevronLeft, ChevronRight, Calendar } from 'lucide-react';
import { ReadingSession, ReadingProgress } from '@/hooks/useReadingTimetable';

interface MonthlyViewProps {
  sessions: ReadingSession[];
  progress: ReadingProgress[];
  selectedDate: Date;
  onDateChange: (date: Date) => void;
}

export const MonthlyView: React.FC<MonthlyViewProps> = ({
  sessions,
  progress,
  selectedDate,
  onDateChange,
}) => {
  const currentMonth = selectedDate.getMonth();
  const currentYear = selectedDate.getFullYear();

  // Get first day of month and how many days in month
  const firstDayOfMonth = new Date(currentYear, currentMonth, 1);
  const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0);
  const daysInMonth = lastDayOfMonth.getDate();
  const startingDayOfWeek = firstDayOfMonth.getDay();

  // Generate calendar days
  const calendarDays = [];
  
  // Add empty cells for days before month starts
  for (let i = 0; i < startingDayOfWeek; i++) {
    calendarDays.push(null);
  }
  
  // Add days of the month
  for (let day = 1; day <= daysInMonth; day++) {
    calendarDays.push(new Date(currentYear, currentMonth, day));
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    newDate.setMonth(selectedDate.getMonth() + (direction === 'next' ? 1 : -1));
    onDateChange(newDate);
  };

  const getSessionsForDate = (date: Date) => {
    const dayOfWeek = date.getDay();
    return sessions.filter(session => session.day_of_week === dayOfWeek);
  };

  const getProgressForDate = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    return progress.filter(p => p.session_date === dateStr);
  };

  const getCompletionStats = (date: Date) => {
    const daySessions = getSessionsForDate(date);
    const dayProgress = getProgressForDate(date);
    
    const completed = dayProgress.filter(p => p.status === 'completed').length;
    const total = daySessions.length;
    
    return { completed, total };
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <div className="space-y-6">
      {/* Month Navigation */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-bold text-white drop-shadow-sm">Monthly Overview</h3>
          <p className="text-sm text-gray-100 font-medium">
            {monthNames[currentMonth]} {currentYear}
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateMonth('prev')}
            className="border-slate-400 text-white hover:bg-slate-700 hover:border-slate-300 bg-slate-800/60 font-medium"
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onDateChange(new Date())}
            className="border-slate-400 text-white hover:bg-slate-700 hover:border-slate-300 bg-slate-800/60 px-3 font-medium"
          >
            Today
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateMonth('next')}
            className="border-slate-400 text-white hover:bg-slate-700 hover:border-slate-300 bg-slate-800/60 font-medium"
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Calendar Grid */}
      <Card className="bg-slate-800/60 border-slate-600/50 backdrop-blur-sm">
        <CardContent className="p-6">
          {/* Day Headers */}
          <div className="grid grid-cols-7 gap-2 mb-4">
            {dayNames.map((day) => (
              <div key={day} className="text-center text-sm font-semibold text-gray-200 py-2">
                {day}
              </div>
            ))}
          </div>

          {/* Calendar Days */}
          <div className="grid grid-cols-7 gap-2">
            {calendarDays.map((date, index) => {
              if (!date) {
                return <div key={index} className="h-24" />;
              }

              const { completed, total } = getCompletionStats(date);
              const isCurrentDay = isToday(date);
              const hasActivity = total > 0;

              return (
                <div
                  key={index}
                  className={`h-24 p-2 border rounded-lg cursor-pointer transition-all hover:shadow-lg ${
                    isCurrentDay
                      ? 'bg-purple-600/30 border-purple-400 ring-2 ring-purple-400'
                      : hasActivity
                        ? 'bg-slate-700/60 border-slate-500 hover:bg-slate-700/80'
                        : 'border-slate-600 hover:bg-slate-700/40'
                  }`}
                  onClick={() => onDateChange(date)}
                >
                  <div className="flex flex-col h-full">
                    <div className={`text-sm font-medium ${
                      isCurrentDay ? 'text-purple-200' : 'text-white'
                    }`}>
                      {date.getDate()}
                    </div>
                    
                    {hasActivity && (
                      <div className="flex-1 flex flex-col justify-between">
                        {/* Session color dots */}
                        <div className="flex flex-wrap gap-1 justify-center mb-1">
                          {sessions
                            .filter(session => session.day_of_week === date.getDay() && session.is_active)
                            .slice(0, 4) // Show max 4 dots
                            .map((session, idx) => (
                              <div
                                key={session.id}
                                className="w-2 h-2 rounded-full shadow-sm"
                                style={{
                                  backgroundColor: session.color || session.subject?.color || '#6366f1'
                                }}
                                title={session.title}
                              />
                            ))}
                          {sessions.filter(session => session.day_of_week === date.getDay() && session.is_active).length > 4 && (
                            <div className="w-2 h-2 rounded-full bg-slate-400 text-xs flex items-center justify-center">
                              +
                            </div>
                          )}
                        </div>

                        <div className="text-center">
                          <div className="text-xs text-gray-200 mb-1 font-medium">
                            {total} session{total !== 1 ? 's' : ''}
                          </div>

                          {completed > 0 && (
                            <Badge
                              variant={completed === total ? 'default' : 'secondary'}
                              className={`text-xs px-1 py-0 ${
                                completed === total
                                  ? 'bg-green-600 text-white'
                                  : 'bg-yellow-600 text-white'
                              }`}
                            >
                              {completed}/{total}
                            </Badge>
                          )}

                          {/* Progress bar */}
                          {total > 0 && (
                            <div className="w-full bg-slate-600 rounded-full h-1 mt-1">
                              <div
                                className={`h-1 rounded-full transition-all duration-300 ${
                                  completed === total ? 'bg-green-500' : 'bg-purple-500'
                                }`}
                                style={{ width: `${(completed / total) * 100}%` }}
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Monthly Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-slate-800/60 border-slate-600/50 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-600/30 rounded-lg border border-blue-500/40">
                <Calendar className="w-5 h-5 text-blue-300" />
              </div>
              <div>
                <p className="text-sm text-gray-200 font-medium">Total Sessions</p>
                <p className="text-xl font-bold text-white">
                  {Math.round(sessions.length * daysInMonth / 7)} {/* Rough estimate */}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/60 border-slate-600/50 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-600/30 rounded-lg border border-green-500/40">
                <Calendar className="w-5 h-5 text-green-300" />
              </div>
              <div>
                <p className="text-sm text-gray-200 font-medium">Completed</p>
                <p className="text-xl font-bold text-white">
                  {progress.filter(p =>
                    p.status === 'completed' &&
                    new Date(p.session_date).getMonth() === currentMonth &&
                    new Date(p.session_date).getFullYear() === currentYear
                  ).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/60 border-slate-600/50 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-600/30 rounded-lg border border-purple-500/40">
                <Calendar className="w-5 h-5 text-purple-300" />
              </div>
              <div>
                <p className="text-sm text-gray-200 font-medium">Completion Rate</p>
                <p className="text-xl font-bold text-white">
                  {(() => {
                    const monthProgress = progress.filter(p =>
                      new Date(p.session_date).getMonth() === currentMonth &&
                      new Date(p.session_date).getFullYear() === currentYear
                    );
                    const completed = monthProgress.filter(p => p.status === 'completed').length;
                    const total = monthProgress.length;
                    return total > 0 ? Math.round((completed / total) * 100) : 0;
                  })()}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
