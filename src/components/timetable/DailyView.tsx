import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Clock, CheckCircle, Circle, Target, BookOpen, Edit } from 'lucide-react';
import { ReadingSession, ReadingProgress } from '@/hooks/useReadingTimetable';

interface DailyViewProps {
  sessions: ReadingSession[];
  progress?: ReadingProgress[];
  onMarkComplete: (data: { sessionId: string; sessionDate: string; completionPercentage?: number }) => Promise<void>;
  onEditSession: (session: ReadingSession) => void;
}

export const DailyView: React.FC<DailyViewProps> = ({
  sessions,
  progress = [],
  onMarkComplete,
  onEditSession,
}) => {
  const today = new Date().toISOString().split('T')[0];
  const currentTime = new Date();

  // Get progress for today
  const getTodayProgress = (sessionId: string) => {
    return progress.find(p => p.session_id === sessionId && p.session_date === today);
  };

  // Sort sessions by start time
  const sortedSessions = [...sessions].sort((a, b) => {
    return a.start_time.localeCompare(b.start_time);
  });

  const formatTime = (time: string) => {
    return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  };

  const isSessionActive = (session: ReadingSession) => {
    const sessionStart = new Date(`2000-01-01T${session.start_time}`);
    const sessionEnd = new Date(`2000-01-01T${session.end_time}`);
    const now = new Date(`2000-01-01T${currentTime.toTimeString().split(' ')[0]}`);
    
    return now >= sessionStart && now <= sessionEnd;
  };

  const isSessionUpcoming = (session: ReadingSession) => {
    const sessionStart = new Date(`2000-01-01T${session.start_time}`);
    const now = new Date(`2000-01-01T${currentTime.toTimeString().split(' ')[0]}`);
    
    return now < sessionStart;
  };

  const handleMarkComplete = async (session: ReadingSession) => {
    try {
      await onMarkComplete({
        sessionId: session.id,
        sessionDate: today,
        completionPercentage: 100,
      });
    } catch (error) {
      console.error('Error marking session complete:', error);
    }
  };

  if (sortedSessions.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No sessions scheduled for today</h3>
          <p className="text-gray-500 mb-4">
            You don't have any reading sessions scheduled for today. Create a new session to get started!
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          Today's Reading Schedule
        </h3>
        <Badge variant="outline" className="text-sm">
          {new Date().toLocaleDateString('en-US', { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          })}
        </Badge>
      </div>

      <div className="space-y-3">
        {sortedSessions.map((session) => {
          const sessionProgress = getTodayProgress(session.id);
          const isCompleted = sessionProgress?.status === 'completed';
          const isActive = isSessionActive(session);
          const isUpcoming = isSessionUpcoming(session);

          return (
            <Card 
              key={session.id} 
              className={`transition-all duration-200 ${
                isActive ? 'ring-2 ring-blue-500 bg-blue-50' : 
                isCompleted ? 'bg-green-50 border-green-200' : 
                'hover:shadow-md'
              }`}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-gray-500" />
                        <span className="text-sm font-medium text-gray-700">
                          {formatTime(session.start_time)} - {formatTime(session.end_time)}
                        </span>
                      </div>
                      
                      {isActive && (
                        <Badge className="bg-blue-600 text-white">
                          Active Now
                        </Badge>
                      )}
                      
                      {isUpcoming && (
                        <Badge variant="outline" className="text-blue-600 border-blue-600">
                          Upcoming
                        </Badge>
                      )}
                    </div>

                    <h4 className="text-lg font-semibold text-gray-900 mb-1">
                      {session.title}
                    </h4>

                    {session.description && (
                      <p className="text-gray-600 text-sm mb-2">
                        {session.description}
                      </p>
                    )}

                    {session.subject && (
                      <div className="flex items-center gap-2 mb-2">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: session.subject.color }}
                        />
                        <span className="text-sm text-gray-600">
                          {session.subject.name}
                        </span>
                      </div>
                    )}

                    {session.goals && (
                      <div className="flex items-start gap-2 mb-2">
                        <Target className="w-4 h-4 text-orange-500 mt-0.5 shrink-0" />
                        <p className="text-sm text-gray-600">
                          {session.goals}
                        </p>
                      </div>
                    )}

                    {session.notes && (
                      <p className="text-sm text-gray-500 italic">
                        {session.notes}
                      </p>
                    )}
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEditSession(session)}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>

                    {isCompleted ? (
                      <div className="flex items-center gap-2 text-green-600">
                        <CheckCircle className="w-5 h-5" />
                        <span className="text-sm font-medium">Completed</span>
                      </div>
                    ) : (
                      <Button
                        onClick={() => handleMarkComplete(session)}
                        size="sm"
                        className="bg-green-600 hover:bg-green-700 text-white"
                      >
                        <Circle className="w-4 h-4 mr-1" />
                        Mark Complete
                      </Button>
                    )}
                  </div>
                </div>

                {sessionProgress && sessionProgress.completion_percentage < 100 && (
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Progress</span>
                      <span className="font-medium">{sessionProgress.completion_percentage}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${sessionProgress.completion_percentage}%` }}
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};
