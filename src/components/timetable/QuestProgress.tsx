import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Trophy, 
  Crown, 
  Star, 
  Zap, 
  Target, 
  Flame, 
  Shield, 
  Sword,
  BookOpen,
  Calendar,
  TrendingUp,
  Award
} from 'lucide-react';
import { ReadingSession, ReadingSubject, ReadingProgress, ReadingStreak } from '@/hooks/useReadingTimetable';

interface QuestProgressProps {
  sessions: ReadingSession[];
  subjects: ReadingSubject[];
  progress: ReadingProgress[];
  streaks: ReadingStreak[];
}

const achievements = [
  {
    id: 'first_quest',
    name: 'First Steps',
    description: 'Complete your first quest',
    icon: Sword,
    color: 'from-green-500 to-emerald-500',
    requirement: 1,
    type: 'completed_quests'
  },
  {
    id: 'quest_master',
    name: 'Quest Master',
    description: 'Complete 50 quests',
    icon: Crown,
    color: 'from-yellow-500 to-orange-500',
    requirement: 50,
    type: 'completed_quests'
  },
  {
    id: 'legendary_scholar',
    name: 'Legendary Scholar',
    description: 'Complete 200 quests',
    icon: Star,
    color: 'from-purple-500 to-pink-500',
    requirement: 200,
    type: 'completed_quests'
  },
  {
    id: 'streak_warrior',
    name: 'Streak Warrior',
    description: 'Maintain a 7-day streak',
    icon: Flame,
    color: 'from-red-500 to-orange-500',
    requirement: 7,
    type: 'streak'
  },
  {
    id: 'consistency_champion',
    name: 'Consistency Champion',
    description: 'Maintain a 30-day streak',
    icon: Shield,
    color: 'from-blue-500 to-cyan-500',
    requirement: 30,
    type: 'streak'
  },
  {
    id: 'knowledge_seeker',
    name: 'Knowledge Seeker',
    description: 'Study 5 different subjects',
    icon: BookOpen,
    color: 'from-indigo-500 to-purple-500',
    requirement: 5,
    type: 'subjects'
  },
  {
    id: 'time_master',
    name: 'Time Master',
    description: 'Study for 100 hours total',
    icon: Target,
    color: 'from-teal-500 to-green-500',
    requirement: 6000, // 100 hours in minutes
    type: 'total_time'
  }
];

export const QuestProgress: React.FC<QuestProgressProps> = ({
  sessions,
  subjects,
  progress,
  streaks
}) => {
  // Calculate statistics
  const completedQuests = progress.filter(p => p.status === 'completed').length;
  const totalXP = completedQuests * 100; // Assuming 100 XP per quest
  const currentLevel = Math.floor(totalXP / 1000) + 1;
  const xpToNextLevel = (currentLevel * 1000) - totalXP;
  const levelProgress = ((totalXP % 1000) / 1000) * 100;
  
  const currentStreak = Math.max(...(streaks.map(s => s.current_streak) || [0]));
  const longestStreak = Math.max(...(streaks.map(s => s.longest_streak) || [0]));
  
  const totalStudyTime = progress
    .filter(p => p.status === 'completed')
    .reduce((total, p) => {
      const session = sessions.find(s => s.id === p.session_id);
      return total + (session?.duration_minutes || 0);
    }, 0);

  const uniqueSubjects = new Set(
    progress
      .filter(p => p.status === 'completed')
      .map(p => sessions.find(s => s.id === p.session_id)?.subject_id)
      .filter(Boolean)
  ).size;

  // Check achievement progress
  const getAchievementProgress = (achievement: any) => {
    let current = 0;
    
    switch (achievement.type) {
      case 'completed_quests':
        current = completedQuests;
        break;
      case 'streak':
        current = longestStreak;
        break;
      case 'subjects':
        current = uniqueSubjects;
        break;
      case 'total_time':
        current = totalStudyTime;
        break;
    }
    
    const isUnlocked = current >= achievement.requirement;
    const progressPercentage = Math.min((current / achievement.requirement) * 100, 100);
    
    return { current, isUnlocked, progressPercentage };
  };

  // Recent activity (last 7 days)
  const getRecentActivity = () => {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    return progress
      .filter(p => new Date(p.session_date) >= sevenDaysAgo)
      .sort((a, b) => new Date(b.session_date).getTime() - new Date(a.session_date).getTime())
      .slice(0, 10);
  };

  const recentActivity = getRecentActivity();

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Scholar Profile */}
      <Card className="bg-gradient-to-r from-purple-800/60 to-blue-800/60 border-purple-400/40 shadow-xl">
        <CardContent className="p-4 sm:p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 lg:gap-6">
            <div className="flex items-center gap-3 sm:gap-4">
              <div className="relative flex-shrink-0">
                <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
                  <Crown className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
                </div>
                <Badge className="absolute -bottom-1 -right-1 bg-blue-600 text-white text-xs font-bold border-2 border-white">
                  Lv.{currentLevel}
                </Badge>
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="text-xl sm:text-2xl font-bold text-white">Scholar Adventurer</h3>
                <p className="text-purple-100 text-sm sm:text-base">Total XP: {totalXP.toLocaleString()}</p>
                <div className="flex items-center gap-2 mt-1 sm:mt-2">
                  <Progress value={levelProgress} className="w-32 sm:w-40 h-2 sm:h-3" />
                  <span className="text-xs sm:text-sm text-purple-200 whitespace-nowrap">{xpToNextLevel} XP to next level</span>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-3 sm:gap-4 lg:gap-6 text-center">
              <div className="bg-yellow-500/20 rounded-lg p-2 sm:p-3 border border-yellow-400/30">
                <div className="text-2xl sm:text-3xl font-bold text-yellow-400">{currentStreak}</div>
                <div className="text-xs sm:text-sm text-yellow-200">Current Streak</div>
              </div>
              <div className="bg-green-500/20 rounded-lg p-2 sm:p-3 border border-green-400/30">
                <div className="text-2xl sm:text-3xl font-bold text-green-400">{completedQuests}</div>
                <div className="text-xs sm:text-sm text-green-200">Quests Completed</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
        <Card className="bg-slate-800/60 border-slate-600/60 hover:bg-slate-800/80 transition-all duration-200">
          <CardContent className="p-3 sm:p-4 text-center">
            <Flame className="w-6 h-6 sm:w-8 sm:h-8 text-orange-400 mx-auto mb-1 sm:mb-2" />
            <div className="text-lg sm:text-2xl font-bold text-white">{longestStreak}</div>
            <div className="text-xs sm:text-sm text-slate-300">Longest Streak</div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/60 border-slate-600/60 hover:bg-slate-800/80 transition-all duration-200">
          <CardContent className="p-3 sm:p-4 text-center">
            <BookOpen className="w-6 h-6 sm:w-8 sm:h-8 text-purple-400 mx-auto mb-1 sm:mb-2" />
            <div className="text-lg sm:text-2xl font-bold text-white">{uniqueSubjects}</div>
            <div className="text-xs sm:text-sm text-slate-300">Subjects Mastered</div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/60 border-slate-600/60 hover:bg-slate-800/80 transition-all duration-200">
          <CardContent className="p-3 sm:p-4 text-center">
            <Target className="w-6 h-6 sm:w-8 sm:h-8 text-green-400 mx-auto mb-1 sm:mb-2" />
            <div className="text-lg sm:text-2xl font-bold text-white">{formatTime(totalStudyTime)}</div>
            <div className="text-xs sm:text-sm text-slate-300">Total Study Time</div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/60 border-slate-600/60 hover:bg-slate-800/80 transition-all duration-200">
          <CardContent className="p-3 sm:p-4 text-center">
            <TrendingUp className="w-6 h-6 sm:w-8 sm:h-8 text-blue-400 mx-auto mb-1 sm:mb-2" />
            <div className="text-lg sm:text-2xl font-bold text-white">{sessions.filter(s => s.is_active).length}</div>
            <div className="text-xs sm:text-sm text-slate-300">Active Quests</div>
          </CardContent>
        </Card>
      </div>

      {/* Achievements */}
      <Card className="bg-slate-800/40 border-slate-600/40">
        <CardHeader className="pb-4">
          <CardTitle className="text-white flex items-center gap-2 text-lg sm:text-xl">
            <Award className="w-5 h-5 text-yellow-400" />
            Achievement Hall
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-3 sm:gap-4">
            {achievements.map((achievement) => {
              const { current, isUnlocked, progressPercentage } = getAchievementProgress(achievement);
              const Icon = achievement.icon;
              
              return (
                <Card 
                  key={achievement.id}
                  className={`transition-all ${
                    isUnlocked 
                      ? `bg-gradient-to-br ${achievement.color} text-white shadow-lg` 
                      : 'bg-slate-700/50 border-slate-600/50'
                  }`}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div className={`p-2 rounded-lg ${
                        isUnlocked ? 'bg-white/20' : 'bg-slate-600/50'
                      }`}>
                        <Icon className={`w-6 h-6 ${
                          isUnlocked ? 'text-white' : 'text-slate-400'
                        }`} />
                      </div>
                      <div className="flex-1">
                        <h4 className={`font-bold ${
                          isUnlocked ? 'text-white' : 'text-slate-300'
                        }`}>
                          {achievement.name}
                        </h4>
                        <p className={`text-sm ${
                          isUnlocked ? 'text-white/80' : 'text-slate-400'
                        }`}>
                          {achievement.description}
                        </p>
                        <div className="mt-2">
                          <div className="flex items-center justify-between text-sm mb-1">
                            <span className={isUnlocked ? 'text-white/80' : 'text-slate-400'}>
                              {current} / {achievement.requirement}
                            </span>
                            <span className={isUnlocked ? 'text-white/80' : 'text-slate-400'}>
                              {Math.round(progressPercentage)}%
                            </span>
                          </div>
                          <Progress 
                            value={progressPercentage} 
                            className={`h-2 ${isUnlocked ? 'bg-white/20' : 'bg-slate-600'}`}
                          />
                        </div>
                        {isUnlocked && (
                          <Badge className="mt-2 bg-white/20 text-white">
                            <Trophy className="w-3 h-3 mr-1" />
                            Unlocked
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card className="bg-slate-800/30 border-slate-600/30">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Calendar className="w-5 h-5 text-blue-400" />
            Recent Quest Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentActivity.length > 0 ? (
              recentActivity.map((activity, index) => {
                const session = sessions.find(s => s.id === activity.session_id);
                const subject = subjects.find(s => s.id === session?.subject_id);
                
                return (
                  <div 
                    key={index}
                    className="flex items-center gap-3 p-3 bg-slate-700/30 rounded-lg"
                  >
                    <div className={`p-2 rounded-lg ${
                      activity.status === 'completed' 
                        ? 'bg-green-500/20' 
                        : 'bg-slate-600/50'
                    }`}>
                      {activity.status === 'completed' ? (
                        <Trophy className="w-4 h-4 text-green-400" />
                      ) : (
                        <Target className="w-4 h-4 text-slate-400" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-white">{session?.title}</div>
                      <div className="text-sm text-slate-400">
                        {subject?.name} • {new Date(activity.session_date).toLocaleDateString()}
                      </div>
                    </div>
                    <Badge className={
                      activity.status === 'completed' 
                        ? 'bg-green-600 text-white' 
                        : 'bg-slate-600 text-slate-300'
                    }>
                      {activity.status}
                    </Badge>
                  </div>
                );
              })
            ) : (
              <div className="text-center py-8 text-slate-400">
                <Calendar className="w-12 h-12 mx-auto mb-3 opacity-50" />
                <p>No recent activity. Start your first quest!</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
