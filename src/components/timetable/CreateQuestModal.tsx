import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Plus, Clock, Calendar, BookOpen, Target, Sword, Shield, Zap, Crown, Star } from 'lucide-react';
import { ReadingSubject } from '@/hooks/useReadingTimetable';
import { toast } from 'sonner';

interface CreateQuestModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  subjects: ReadingSubject[];
  onCreateSession: (session: any) => Promise<void>;
  onCreateSubject: (subject: any) => Promise<void>;
}

const questTypes = [
  { id: 'daily', name: 'Daily Quest', icon: Sword, color: 'from-red-500 to-orange-500', xp: 100 },
  { id: 'weekly', name: 'Weekly Challenge', icon: Shield, color: 'from-blue-500 to-purple-500', xp: 500 },
  { id: 'epic', name: 'Epic Campaign', icon: Crown, color: 'from-purple-500 to-pink-500', xp: 1000 },
  { id: 'legendary', name: 'Legendary Quest', icon: Star, color: 'from-yellow-500 to-orange-500', xp: 2000 }
];

const difficultyLevels = [
  { id: 'novice', name: 'Novice', multiplier: 1, color: 'text-green-400' },
  { id: 'adept', name: 'Adept', multiplier: 1.5, color: 'text-blue-400' },
  { id: 'expert', name: 'Expert', multiplier: 2, color: 'text-purple-400' },
  { id: 'master', name: 'Master', multiplier: 3, color: 'text-yellow-400' }
];

export const CreateQuestModal: React.FC<CreateQuestModalProps> = ({
  open,
  onOpenChange,
  subjects,
  onCreateSession,
  onCreateSubject
}) => {
  const [questName, setQuestName] = useState('');
  const [description, setDescription] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('');
  const [questType, setQuestType] = useState('daily');
  const [difficulty, setDifficulty] = useState('novice');
  const [duration, setDuration] = useState(30);
  const [selectedDays, setSelectedDays] = useState<number[]>([]);
  const [startTime, setStartTime] = useState('09:00');
  const [isCreatingSubject, setIsCreatingSubject] = useState(false);
  const [newSubjectName, setNewSubjectName] = useState('');
  const [newSubjectColor, setNewSubjectColor] = useState('#3B82F6');

  const days = [
    { id: 0, name: 'Sunday', short: 'Sun' },
    { id: 1, name: 'Monday', short: 'Mon' },
    { id: 2, name: 'Tuesday', short: 'Tue' },
    { id: 3, name: 'Wednesday', short: 'Wed' },
    { id: 4, name: 'Thursday', short: 'Thu' },
    { id: 5, name: 'Friday', short: 'Fri' },
    { id: 6, name: 'Saturday', short: 'Sat' }
  ];

  const selectedQuestType = questTypes.find(q => q.id === questType);
  const selectedDifficulty = difficultyLevels.find(d => d.id === difficulty);
  const totalXP = Math.round((selectedQuestType?.xp || 100) * (selectedDifficulty?.multiplier || 1));

  const handleCreateSubject = async () => {
    if (!newSubjectName.trim()) {
      toast.error('Please enter a subject name');
      return;
    }

    try {
      await onCreateSubject({
        name: newSubjectName,
        color: newSubjectColor,
        description: `Knowledge realm: ${newSubjectName}`
      });
      
      setNewSubjectName('');
      setNewSubjectColor('#3B82F6');
      setIsCreatingSubject(false);
      toast.success('New knowledge realm created!');
    } catch (error) {
      toast.error('Failed to create subject');
    }
  };

  const handleCreateQuest = async () => {
    if (!questName.trim()) {
      toast.error('Please enter a quest name');
      return;
    }

    if (!selectedSubject) {
      toast.error('Please select a knowledge realm');
      return;
    }

    if (selectedDays.length === 0) {
      toast.error('Please select at least one day');
      return;
    }

    try {
      // Create a session for each selected day
      for (const dayOfWeek of selectedDays) {
        await onCreateSession({
          subject_id: selectedSubject,
          title: questName,
          description: description || `${selectedQuestType?.name} - ${questName}`,
          day_of_week: dayOfWeek,
          start_time: startTime,
          duration_minutes: duration,
          is_active: true,
          quest_type: questType,
          difficulty: difficulty,
          xp_reward: totalXP
        });
      }

      // Reset form
      setQuestName('');
      setDescription('');
      setSelectedSubject('');
      setQuestType('daily');
      setDifficulty('novice');
      setDuration(30);
      setSelectedDays([]);
      setStartTime('09:00');
      
      onOpenChange(false);
      toast.success(`Epic quest "${questName}" has been created! 🎉`);
    } catch (error) {
      toast.error('Failed to create quest');
    }
  };

  const toggleDay = (dayId: number) => {
    setSelectedDays(prev => 
      prev.includes(dayId) 
        ? prev.filter(d => d !== dayId)
        : [...prev, dayId]
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95vw] max-w-2xl max-h-[90vh] overflow-y-auto bg-gradient-to-br from-slate-900 to-slate-800 border-slate-600 text-white">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-xl sm:text-2xl font-bold flex items-center gap-2">
            <Sword className="w-5 h-5 sm:w-6 sm:h-6 text-yellow-400" />
            Create New Quest
          </DialogTitle>
          <DialogDescription className="text-slate-200 text-sm sm:text-base">
            Design an epic learning adventure and embark on your knowledge journey!
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 sm:space-y-6">
          {/* Quest Type Selection */}
          <div>
            <Label className="text-white mb-2 sm:mb-3 block text-sm sm:text-base font-medium">Quest Type</Label>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
              {questTypes.map((type) => {
                const Icon = type.icon;
                return (
                  <Card
                    key={type.id}
                    className={`cursor-pointer transition-all ${
                      questType === type.id
                        ? 'ring-2 ring-yellow-400 bg-gradient-to-r ' + type.color + ' text-white shadow-lg'
                        : 'bg-slate-700/80 hover:bg-slate-600/80 border-slate-600'
                    }`}
                    onClick={() => setQuestType(type.id)}
                  >
                    <CardContent className="p-2 sm:p-3 flex items-center gap-2">
                      <Icon className="w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <div className="font-medium text-sm sm:text-base">{type.name}</div>
                        <div className="text-xs sm:text-sm opacity-75">{type.xp} XP</div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Quest Details */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
            <div>
              <Label htmlFor="questName" className="text-white text-sm sm:text-base font-medium">Quest Name</Label>
              <Input
                id="questName"
                value={questName}
                onChange={(e) => setQuestName(e.target.value)}
                placeholder="Enter your quest name..."
                className="bg-slate-700/80 border-slate-600 text-white placeholder:text-slate-400 mt-1"
              />
            </div>
            <div>
              <Label htmlFor="difficulty" className="text-white text-sm sm:text-base font-medium">Difficulty Level</Label>
              <Select value={difficulty} onValueChange={setDifficulty}>
                <SelectTrigger className="bg-slate-700/80 border-slate-600 text-white mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-slate-700 border-slate-600">
                  {difficultyLevels.map((level) => (
                    <SelectItem key={level.id} value={level.id} className="text-white">
                      <span className={level.color}>{level.name}</span>
                      <span className="ml-2 text-sm opacity-75">({level.multiplier}x XP)</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="description" className="text-white text-sm sm:text-base font-medium">Quest Description (Optional)</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe your learning adventure..."
              className="bg-slate-700/80 border-slate-600 text-white placeholder:text-slate-400 mt-1"
              rows={3}
            />
          </div>

          {/* Subject Selection */}
          <div>
            <Label className="text-white mb-2 block">Knowledge Realm</Label>
            {!isCreatingSubject ? (
              <div className="flex gap-2">
                <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                    <SelectValue placeholder="Select a knowledge realm..." />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-700 border-slate-600">
                    {subjects.map((subject) => (
                      <SelectItem key={subject.id} value={subject.id} className="text-white">
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: subject.color }}
                          />
                          {subject.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreatingSubject(true)}
                  className="border-slate-600 text-white hover:bg-slate-700"
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            ) : (
              <div className="space-y-3 p-4 bg-slate-800 rounded-lg border border-slate-600">
                <div className="flex items-center gap-2">
                  <BookOpen className="w-5 h-5 text-purple-400" />
                  <span className="font-medium">Create New Knowledge Realm</span>
                </div>
                <div className="grid grid-cols-3 gap-3">
                  <div className="col-span-2">
                    <Input
                      value={newSubjectName}
                      onChange={(e) => setNewSubjectName(e.target.value)}
                      placeholder="Realm name..."
                      className="bg-slate-700 border-slate-600 text-white"
                    />
                  </div>
                  <div>
                    <Input
                      type="color"
                      value={newSubjectColor}
                      onChange={(e) => setNewSubjectColor(e.target.value)}
                      className="bg-slate-700 border-slate-600 h-10"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    onClick={handleCreateSubject}
                    className="bg-purple-600 hover:bg-purple-700"
                  >
                    Create Realm
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsCreatingSubject(false)}
                    className="border-slate-600 text-white hover:bg-slate-700"
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Schedule Settings */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="duration" className="text-white">Duration (minutes)</Label>
              <Input
                id="duration"
                type="number"
                value={duration}
                onChange={(e) => setDuration(Number(e.target.value))}
                min="15"
                max="240"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            <div>
              <Label htmlFor="startTime" className="text-white">Start Time</Label>
              <Input
                id="startTime"
                type="time"
                value={startTime}
                onChange={(e) => setStartTime(e.target.value)}
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
          </div>

          {/* Days Selection */}
          <div>
            <Label className="text-white mb-2 sm:mb-3 block text-sm sm:text-base font-medium">Quest Days</Label>
            <div className="flex flex-wrap gap-1.5 sm:gap-2">
              {days.map((day) => (
                <Badge
                  key={day.id}
                  variant={selectedDays.includes(day.id) ? "default" : "outline"}
                  className={`cursor-pointer px-2 sm:px-3 py-1.5 sm:py-2 text-xs sm:text-sm transition-all duration-200 ${
                    selectedDays.includes(day.id)
                      ? 'bg-purple-600 text-white hover:bg-purple-700 border-purple-500 shadow-lg'
                      : 'border-slate-500 text-slate-200 hover:bg-slate-700 hover:border-slate-400'
                  }`}
                  onClick={() => toggleDay(day.id)}
                >
                  <span className="hidden sm:inline">{day.short}</span>
                  <span className="sm:hidden">{day.short.slice(0, 1)}</span>
                </Badge>
              ))}
            </div>
          </div>

          {/* XP Preview */}
          <Card className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border-yellow-500/30">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Zap className="w-5 h-5 text-yellow-400" />
                  <span className="font-medium text-white">Quest Reward</span>
                </div>
                <div className="text-2xl font-bold text-yellow-400">
                  {totalXP} XP
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 pt-4">
            <Button
              onClick={handleCreateQuest}
              className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold shadow-lg border border-purple-500/30 hover:border-purple-400/50 transition-all duration-200"
            >
              <Sword className="w-4 h-4 mr-2" />
              Create Quest
            </Button>
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="border-slate-500 text-white hover:bg-slate-700 hover:border-slate-400 bg-slate-800/50"
            >
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
