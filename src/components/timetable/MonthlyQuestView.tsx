import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Calendar, 
  ChevronLeft, 
  ChevronRight, 
  Trophy, 
  Target,
  Flame,
  Crown,
  Star
} from 'lucide-react';
import { ReadingSession, ReadingProgress } from '@/hooks/useReadingTimetable';

interface MonthlyQuestViewProps {
  sessions: ReadingSession[];
  progress: ReadingProgress[];
  selectedDate: Date;
  onDateChange: (date: Date) => void;
}

export const MonthlyQuestView: React.FC<MonthlyQuestViewProps> = ({
  sessions,
  progress,
  selectedDate,
  onDateChange
}) => {
  const getMonthDates = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    
    // Get first day of month and adjust to start from Sunday
    const firstDay = new Date(year, month, 1);
    const startDate = new Date(firstDay);
    startDate.setDate(firstDay.getDate() - firstDay.getDay());
    
    // Generate 42 days (6 weeks) to fill the calendar grid
    const dates = [];
    for (let i = 0; i < 42; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      dates.push(date);
    }
    
    return dates;
  };

  const monthDates = getMonthDates(selectedDate);
  const currentMonth = selectedDate.getMonth();
  const currentYear = selectedDate.getFullYear();

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    newDate.setMonth(selectedDate.getMonth() + (direction === 'next' ? 1 : -1));
    onDateChange(newDate);
  };

  const getSessionsForDay = (date: Date) => {
    const dayOfWeek = date.getDay();
    return sessions.filter(session => session.day_of_week === dayOfWeek && session.is_active);
  };

  const getProgressForDate = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    const daySessions = getSessionsForDay(date);
    const dayProgress = progress.filter(p => p.session_date === dateStr);
    
    const completed = dayProgress.filter(p => p.status === 'completed').length;
    const total = daySessions.length;
    
    return {
      completed,
      total,
      percentage: total > 0 ? (completed / total) * 100 : 0,
      hasQuests: total > 0
    };
  };

  const getMonthStats = () => {
    const monthStart = new Date(currentYear, currentMonth, 1);
    const monthEnd = new Date(currentYear, currentMonth + 1, 0);
    
    let totalQuests = 0;
    let completedQuests = 0;
    let perfectDays = 0;
    let activeStreak = 0;
    let currentStreak = 0;
    
    // Calculate stats for each day of the month
    for (let d = new Date(monthStart); d <= monthEnd; d.setDate(d.getDate() + 1)) {
      const dayProgress = getProgressForDate(new Date(d));
      
      if (dayProgress.hasQuests) {
        totalQuests += dayProgress.total;
        completedQuests += dayProgress.completed;
        
        if (dayProgress.percentage === 100) {
          perfectDays++;
          currentStreak++;
          activeStreak = Math.max(activeStreak, currentStreak);
        } else {
          currentStreak = 0;
        }
      }
    }
    
    return {
      totalQuests,
      completedQuests,
      perfectDays,
      activeStreak,
      completionRate: totalQuests > 0 ? (completedQuests / totalQuests) * 100 : 0
    };
  };

  const monthStats = getMonthStats();
  const monthName = selectedDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  const getDayIntensity = (percentage: number) => {
    if (percentage === 0) return 'bg-slate-700/30';
    if (percentage < 25) return 'bg-red-500/30';
    if (percentage < 50) return 'bg-orange-500/30';
    if (percentage < 75) return 'bg-yellow-500/30';
    if (percentage < 100) return 'bg-blue-500/30';
    return 'bg-green-500/50';
  };

  const getAchievementLevel = () => {
    const { completionRate, perfectDays } = monthStats;
    
    if (completionRate >= 90 && perfectDays >= 20) return { title: 'Legendary Scholar', icon: Crown, color: 'text-yellow-400' };
    if (completionRate >= 75 && perfectDays >= 15) return { title: 'Master Adventurer', icon: Star, color: 'text-purple-400' };
    if (completionRate >= 60 && perfectDays >= 10) return { title: 'Quest Champion', icon: Trophy, color: 'text-blue-400' };
    if (completionRate >= 40 && perfectDays >= 5) return { title: 'Rising Hero', icon: Target, color: 'text-green-400' };
    return { title: 'Novice Explorer', icon: Target, color: 'text-slate-400' };
  };

  const achievement = getAchievementLevel();
  const AchievementIcon = achievement.icon;

  return (
    <div className="space-y-6">
      {/* Month Navigation */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
        <div className="text-center sm:text-left">
          <h3 className="text-xl sm:text-2xl font-bold text-white">{monthName} Campaign</h3>
          <p className="text-slate-200 text-sm sm:text-base">Your monthly learning adventure overview</p>
        </div>
        <div className="flex items-center justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateMonth('prev')}
            className="border-slate-500 text-white hover:bg-slate-700 hover:border-slate-400 bg-slate-800/50"
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onDateChange(new Date())}
            className="border-slate-500 text-white hover:bg-slate-700 hover:border-slate-400 bg-slate-800/50 px-3"
          >
            Today
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateMonth('next')}
            className="border-slate-500 text-white hover:bg-slate-700 hover:border-slate-400 bg-slate-800/50"
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Monthly Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-br from-blue-500/20 to-cyan-500/20 border-blue-500/30">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Trophy className="w-8 h-8 text-blue-400" />
              <div>
                <div className="text-2xl font-bold text-white">{monthStats.completedQuests}</div>
                <div className="text-sm text-blue-200">Quests Completed</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-500/20 to-emerald-500/20 border-green-500/30">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Target className="w-8 h-8 text-green-400" />
              <div>
                <div className="text-2xl font-bold text-white">{monthStats.perfectDays}</div>
                <div className="text-sm text-green-200">Perfect Days</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-500/20 to-red-500/20 border-orange-500/30">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Flame className="w-8 h-8 text-orange-400" />
              <div>
                <div className="text-2xl font-bold text-white">{monthStats.activeStreak}</div>
                <div className="text-sm text-orange-200">Best Streak</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-500/20 to-pink-500/20 border-purple-500/30">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <AchievementIcon className={`w-8 h-8 ${achievement.color}`} />
              <div>
                <div className="text-lg font-bold text-white">{achievement.title}</div>
                <div className="text-sm text-purple-200">{Math.round(monthStats.completionRate)}% Complete</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Progress */}
      <Card className="bg-slate-800/30 border-slate-600/30">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Calendar className="w-5 h-5 text-purple-400" />
            Monthly Quest Calendar
          </CardTitle>
          <div className="flex items-center gap-4">
            <Progress value={monthStats.completionRate} className="flex-1" />
            <span className="text-sm text-slate-300">
              {Math.round(monthStats.completionRate)}% Complete
            </span>
          </div>
        </CardHeader>
        <CardContent>
          {/* Calendar Header */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {dayNames.map((day) => (
              <div key={day} className="p-2 text-center text-sm font-medium text-slate-400">
                {day}
              </div>
            ))}
          </div>

          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1">
            {monthDates.map((date, index) => {
              const isCurrentMonth = date.getMonth() === currentMonth;
              const isToday = date.toDateString() === new Date().toDateString();
              const dayProgress = getProgressForDate(date);
              
              return (
                <div
                  key={index}
                  className={`
                    relative p-2 h-16 border border-slate-600/30 rounded-lg transition-all
                    ${getDayIntensity(dayProgress.percentage)}
                    ${isCurrentMonth ? 'opacity-100' : 'opacity-30'}
                    ${isToday ? 'ring-2 ring-purple-500' : ''}
                    ${dayProgress.hasQuests ? 'cursor-pointer hover:scale-105' : ''}
                  `}
                >
                  <div className={`text-sm font-medium ${
                    isToday ? 'text-purple-400' : 
                    isCurrentMonth ? 'text-white' : 'text-slate-500'
                  }`}>
                    {date.getDate()}
                  </div>
                  
                  {dayProgress.hasQuests && (
                    <div className="absolute bottom-1 left-1 right-1">
                      <div className="text-xs text-center text-white">
                        {dayProgress.completed}/{dayProgress.total}
                      </div>
                      {dayProgress.percentage === 100 && (
                        <div className="absolute -top-1 -right-1">
                          <div className="w-3 h-3 bg-yellow-400 rounded-full flex items-center justify-center">
                            <Star className="w-2 h-2 text-yellow-900" />
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* Legend */}
          <div className="flex items-center justify-center gap-6 mt-4 text-sm text-slate-400">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-slate-700/30 rounded"></div>
              <span>No Quests</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500/30 rounded"></div>
              <span>0-25%</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-orange-500/30 rounded"></div>
              <span>25-50%</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-yellow-500/30 rounded"></div>
              <span>50-75%</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500/30 rounded"></div>
              <span>75-99%</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500/50 rounded"></div>
              <span>100%</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
