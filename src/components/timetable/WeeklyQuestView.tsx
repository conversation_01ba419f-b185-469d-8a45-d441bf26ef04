import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Calendar, 
  Clock, 
  CheckCircle, 
  Circle, 
  Sword, 
  Shield, 
  Crown, 
  Star, 
  Zap,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { ReadingSession, ReadingSubject, ReadingProgress } from '@/hooks/useReadingTimetable';

interface WeeklyQuestViewProps {
  sessions: ReadingSession[];
  subjects: ReadingSubject[];
  progress: ReadingProgress[];
  selectedDate: Date;
  onDateChange: (date: Date) => void;
  onMarkComplete: (sessionId: string, date: string) => Promise<void>;
}

const questTypeIcons = {
  daily: Sword,
  weekly: Shield,
  epic: Crown,
  legendary: Star
};

const difficultyColors = {
  novice: 'text-green-400 bg-green-400/20',
  adept: 'text-blue-400 bg-blue-400/20',
  expert: 'text-purple-400 bg-purple-400/20',
  master: 'text-yellow-400 bg-yellow-400/20'
};

export const WeeklyQuestView: React.FC<WeeklyQuestViewProps> = ({
  sessions,
  subjects,
  progress,
  selectedDate,
  onDateChange,
  onMarkComplete
}) => {
  const getWeekDates = (date: Date) => {
    const week = [];
    const startOfWeek = new Date(date);
    startOfWeek.setDate(date.getDate() - date.getDay());
    
    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      week.push(day);
    }
    return week;
  };

  const weekDates = getWeekDates(selectedDate);
  const weekStart = weekDates[0];
  const weekEnd = weekDates[6];

  const navigateWeek = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    newDate.setDate(selectedDate.getDate() + (direction === 'next' ? 7 : -7));
    onDateChange(newDate);
  };

  const getSessionsForDay = (dayOfWeek: number) => {
    return sessions.filter(session => session.day_of_week === dayOfWeek && session.is_active);
  };

  const getProgressForSession = (sessionId: string, date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    return progress.find(p => p.session_id === sessionId && p.session_date === dateStr);
  };

  const getSubjectForSession = (subjectId: string) => {
    return subjects.find(s => s.id === subjectId);
  };

  const getDayProgress = (date: Date) => {
    const dayOfWeek = date.getDay();
    const daySessions = getSessionsForDay(dayOfWeek);
    const completed = daySessions.filter(session => {
      const sessionProgress = getProgressForSession(session.id, date);
      return sessionProgress?.status === 'completed';
    }).length;
    
    return {
      completed,
      total: daySessions.length,
      percentage: daySessions.length > 0 ? (completed / daySessions.length) * 100 : 0
    };
  };

  const formatTime = (timeString: string) => {
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const handleMarkComplete = async (sessionId: string, date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    await onMarkComplete(sessionId, dateStr);
  };

  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Week Navigation */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
        <div className="text-center sm:text-left">
          <h3 className="text-lg sm:text-xl font-bold text-white">Weekly Quest Board</h3>
          <p className="text-slate-200 text-sm sm:text-base">
            {weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - {' '}
            {weekEnd.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}
          </p>
        </div>
        <div className="flex items-center justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateWeek('prev')}
            className="border-slate-500 text-white hover:bg-slate-700 hover:border-slate-400 bg-slate-800/50"
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onDateChange(new Date())}
            className="border-slate-500 text-white hover:bg-slate-700 hover:border-slate-400 bg-slate-800/50 px-3"
          >
            Today
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateWeek('next')}
            className="border-slate-500 text-white hover:bg-slate-700 hover:border-slate-400 bg-slate-800/50"
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Weekly Overview */}
      <div className="grid grid-cols-7 gap-1 sm:gap-2 lg:gap-3">
        {weekDates.map((date, index) => {
          const dayProgress = getDayProgress(date);
          const isToday = date.toDateString() === new Date().toDateString();

          return (
            <Card
              key={index}
              className={`bg-slate-800/60 border-slate-600/60 hover:bg-slate-800/80 transition-all duration-200 ${
                isToday ? 'ring-2 ring-purple-500 bg-purple-900/30' : ''
              }`}
            >
              <CardContent className="p-2 sm:p-3">
                <div className="text-center">
                  <div className="text-xs sm:text-sm text-slate-300 font-medium">{dayNames[date.getDay()].slice(0, 3)}</div>
                  <div className={`text-sm sm:text-lg font-bold ${isToday ? 'text-purple-300' : 'text-white'}`}>
                    {date.getDate()}
                  </div>
                  <div className="mt-1 sm:mt-2">
                    <Progress
                      value={dayProgress.percentage}
                      className="h-1.5 sm:h-2"
                    />
                    <div className="text-xs text-slate-400 mt-1">
                      {dayProgress.completed}/{dayProgress.total}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Daily Quest Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-3 sm:gap-4">
        {weekDates.map((date, dayIndex) => {
          const dayOfWeek = date.getDay();
          const daySessions = getSessionsForDay(dayOfWeek);
          const isToday = date.toDateString() === new Date().toDateString();

          if (daySessions.length === 0) return null;

          return (
            <Card
              key={dayIndex}
              className={`bg-slate-800/40 border-slate-600/40 hover:bg-slate-800/60 transition-all duration-200 ${
                isToday ? 'ring-1 ring-purple-500/50 bg-purple-900/20' : ''
              }`}
            >
              <CardHeader className="pb-2 sm:pb-3">
                <CardTitle className="text-base sm:text-lg text-white flex items-center gap-2">
                  <Calendar className="w-4 h-4 sm:w-5 sm:h-5 text-purple-400" />
                  <span className="hidden sm:inline">{dayNames[dayOfWeek]}</span>
                  <span className="sm:hidden">{dayNames[dayOfWeek].slice(0, 3)}</span>
                  {isToday && <Badge className="bg-purple-600 text-white text-xs">Today</Badge>}
                </CardTitle>
                <div className="text-xs sm:text-sm text-slate-300">
                  {date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                {daySessions.map((session) => {
                  const subject = getSubjectForSession(session.subject_id);
                  const sessionProgress = getProgressForSession(session.id, date);
                  const isCompleted = sessionProgress?.status === 'completed';
                  const QuestIcon = questTypeIcons[session.quest_type as keyof typeof questTypeIcons] || Sword;
                  
                  return (
                    <div
                      key={session.id}
                      className={`p-3 rounded-lg border transition-all ${
                        isCompleted 
                          ? 'bg-green-500/20 border-green-500/30' 
                          : 'bg-slate-700/50 border-slate-600/50 hover:bg-slate-700/70'
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <QuestIcon className="w-4 h-4 text-yellow-400" />
                            <span className="font-medium text-white">{session.title}</span>
                            {session.difficulty && (
                              <Badge 
                                className={`text-xs ${
                                  difficultyColors[session.difficulty as keyof typeof difficultyColors]
                                }`}
                              >
                                {session.difficulty}
                              </Badge>
                            )}
                          </div>
                          
                          {subject && (
                            <div className="flex items-center gap-2 mb-2">
                              <div 
                                className="w-3 h-3 rounded-full" 
                                style={{ backgroundColor: subject.color }}
                              />
                              <span className="text-sm text-slate-300">{subject.name}</span>
                            </div>
                          )}
                          
                          <div className="flex items-center gap-4 text-sm text-slate-400">
                            <div className="flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              {formatTime(session.start_time)}
                            </div>
                            <div>{session.duration_minutes} min</div>
                            {session.xp_reward && (
                              <div className="flex items-center gap-1 text-yellow-400">
                                <Zap className="w-3 h-3" />
                                {session.xp_reward} XP
                              </div>
                            )}
                          </div>
                        </div>
                        
                        <Button
                          size="sm"
                          variant={isCompleted ? "default" : "outline"}
                          onClick={() => handleMarkComplete(session.id, date)}
                          className={
                            isCompleted
                              ? "bg-green-600 hover:bg-green-700 text-white border-green-500 shadow-lg"
                              : "border-slate-500 text-white hover:bg-slate-700 hover:border-slate-400 bg-slate-800/50"
                          }
                        >
                          {isCompleted ? (
                            <CheckCircle className="w-4 h-4" />
                          ) : (
                            <Circle className="w-4 h-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};
