import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Plus, Clock, Calendar, BookOpen, Target } from 'lucide-react';
import { ReadingSubject } from '@/hooks/useReadingTimetable';
import { toast } from 'sonner';

interface CreateSessionModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  subjects: ReadingSubject[];
  onCreateSession: (sessionData: any) => Promise<void>;
  onCreateSubject: (subjectData: any) => Promise<void>;
  editingSession?: any;
}

const DAYS_OF_WEEK = [
  { value: 0, label: 'Sunday' },
  { value: 1, label: 'Monday' },
  { value: 2, label: 'Tuesday' },
  { value: 3, label: 'Wednesday' },
  { value: 4, label: 'Thursday' },
  { value: 5, label: 'Friday' },
  { value: 6, label: 'Saturday' },
];

const SUBJECT_COLORS = [
  '#3B82F6', '#EF4444', '#10B981', '#F59E0B', 
  '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16'
];

export const CreateSessionModal: React.FC<CreateSessionModalProps> = ({
  open,
  onOpenChange,
  subjects,
  onCreateSession,
  onCreateSubject,
  editingSession,
}) => {
  const [step, setStep] = useState<'session' | 'subject'>('session');
  const [isLoading, setIsLoading] = useState(false);

  // Session form state
  const [sessionForm, setSessionForm] = useState({
    title: '',
    description: '',
    subject_id: '',
    day_of_week: '',
    start_time: '',
    end_time: '',
    notes: '',
    goals: '',
    reading_text: '',
    is_recurring: true,
    color: '#6366f1', // Default indigo color
  });

  // Subject form state
  const [subjectForm, setSubjectForm] = useState({
    name: '',
    description: '',
    color: SUBJECT_COLORS[0],
  });

  const resetForms = () => {
    setSessionForm({
      title: '',
      description: '',
      subject_id: '',
      day_of_week: '',
      start_time: '',
      end_time: '',
      notes: '',
      goals: '',
      reading_text: '',
      is_recurring: true,
      color: '#6366f1',
    });
    setSubjectForm({
      name: '',
      description: '',
      color: SUBJECT_COLORS[0],
    });
    setStep('session');
  };

  // Effect to populate form when editing
  useEffect(() => {
    if (editingSession && open) {
      setSessionForm({
        title: editingSession.title || '',
        description: editingSession.description || '',
        subject_id: editingSession.subject_id || '',
        day_of_week: editingSession.day_of_week?.toString() || '',
        start_time: editingSession.start_time || '',
        end_time: editingSession.end_time || '',
        notes: editingSession.notes || '',
        goals: editingSession.goals || '',
        reading_text: editingSession.reading_text || '',
        is_recurring: editingSession.is_recurring ?? true,
        color: editingSession.color || editingSession.subject?.color || '#6366f1',
      });
      setStep('session');
    } else if (!editingSession && open) {
      resetForms();
    }
  }, [editingSession, open]);

  // Effect to update session color when subject changes
  useEffect(() => {
    if (sessionForm.subject_id && !editingSession) {
      const selectedSubject = subjects.find(s => s.id === sessionForm.subject_id);
      if (selectedSubject) {
        setSessionForm(prev => ({ ...prev, color: selectedSubject.color }));
      }
    }
  }, [sessionForm.subject_id, subjects, editingSession]);

  const handleClose = () => {
    resetForms();
    onOpenChange(false);
  };

  const handleCreateSubject = async () => {
    if (!subjectForm.name.trim()) {
      toast.error('Subject name is required');
      return;
    }

    setIsLoading(true);
    try {
      await onCreateSubject(subjectForm);
      setStep('session');
      setSubjectForm({ name: '', description: '', color: SUBJECT_COLORS[0] });
    } catch (error) {
      console.error('Error creating subject:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateSession = async () => {
    if (!sessionForm.title.trim()) {
      toast.error('Session title is required');
      return;
    }
    if (!sessionForm.day_of_week) {
      toast.error('Please select a day of the week');
      return;
    }
    if (!sessionForm.start_time || !sessionForm.end_time) {
      toast.error('Please set start and end times');
      return;
    }

    // Validate time order
    if (sessionForm.start_time >= sessionForm.end_time) {
      toast.error('End time must be after start time');
      return;
    }

    setIsLoading(true);
    try {
      const sessionData = {
        ...sessionForm,
        day_of_week: parseInt(sessionForm.day_of_week),
        subject_id: sessionForm.subject_id || null,
      };

      if (editingSession) {
        // Update existing session
        sessionData.id = editingSession.id;
      }

      await onCreateSession(sessionData);
      handleClose();
    } catch (error) {
      console.error('Error saving session:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="w-[95vw] max-w-2xl max-h-[95vh] overflow-y-auto bg-gradient-to-br from-slate-900 to-slate-800 border-slate-600 text-white mx-4">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl font-bold">
            {step === 'session' ? (
              <>
                <BookOpen className="w-5 h-5 text-yellow-400" />
                {editingSession ? 'Edit Reading Session' : 'Create Reading Session'}
              </>
            ) : (
              <>
                <Plus className="w-5 h-5 text-green-400" />
                Create New Subject
              </>
            )}
          </DialogTitle>
          <DialogDescription className="text-slate-200">
            {step === 'session'
              ? editingSession
                ? 'Update your reading session details'
                : 'Set up a new reading session for your learning adventure'
              : 'Create a new subject to organize your reading sessions'
            }
          </DialogDescription>
        </DialogHeader>

        {step === 'session' ? (
          <div className="space-y-4 sm:space-y-6 px-1">
            {/* Basic Information */}
            <div className="space-y-3 sm:space-y-4">
              <div>
                <Label htmlFor="title" className="text-white font-medium text-sm sm:text-base">Session Title *</Label>
                <Input
                  id="title"
                  placeholder="e.g., Mathematics Chapter 5"
                  value={sessionForm.title}
                  onChange={(e) => setSessionForm(prev => ({ ...prev, title: e.target.value }))}
                  className="bg-slate-700/90 border-slate-500 text-white placeholder:text-slate-300 mt-1 focus:border-purple-400 focus:ring-purple-400/20 font-medium text-sm sm:text-base h-10 sm:h-11"
                />
              </div>

              <div>
                <Label htmlFor="description" className="text-white font-medium text-sm sm:text-base">Description</Label>
                <Input
                  id="description"
                  placeholder="Brief description of the reading session"
                  value={sessionForm.description}
                  onChange={(e) => setSessionForm(prev => ({ ...prev, description: e.target.value }))}
                  className="bg-slate-700/90 border-slate-500 text-white placeholder:text-slate-300 mt-1 focus:border-purple-400 focus:ring-purple-400/20 font-medium text-sm sm:text-base h-10 sm:h-11"
                />
              </div>
            </div>

            {/* Subject Selection */}
            <div>
              <Label className="text-white font-medium text-sm sm:text-base">Subject</Label>
              <div className="flex gap-2 mt-1">
                <Select
                  value={sessionForm.subject_id}
                  onValueChange={(value) => setSessionForm(prev => ({ ...prev, subject_id: value }))}
                >
                  <SelectTrigger className="flex-1 bg-slate-700/90 border-slate-500 text-white focus:border-purple-400 focus:ring-purple-400/20 h-10 sm:h-11 text-sm sm:text-base">
                    <SelectValue placeholder="Select a subject (optional)" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-700/95 border-slate-500 backdrop-blur-sm">
                    {subjects.map((subject) => (
                      <SelectItem key={subject.id} value={subject.id} className="text-white text-sm sm:text-base">
                        <div className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: subject.color }}
                          />
                          {subject.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setStep('subject')}
                  className="shrink-0 border-slate-500 text-white hover:bg-slate-700 hover:border-slate-400 bg-slate-800/50 h-10 sm:h-11 w-10 sm:w-11"
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Session Color */}
            <div>
              <Label className="text-white font-medium text-sm sm:text-base">Session Color</Label>
              <div className="flex flex-col sm:flex-row gap-3 mt-2">
                {/* Predefined Colors */}
                <div className="flex flex-wrap gap-2">
                  {[
                    '#6366f1', // Indigo
                    '#8b5cf6', // Purple
                    '#06b6d4', // Cyan
                    '#10b981', // Emerald
                    '#f59e0b', // Amber
                    '#ef4444', // Red
                    '#ec4899', // Pink
                    '#84cc16', // Lime
                  ].map((color) => (
                    <button
                      key={color}
                      type="button"
                      className={`w-8 h-8 sm:w-9 sm:h-9 rounded-full border-2 transition-all duration-200 hover:scale-110 ${
                        sessionForm.color === color ? 'border-white shadow-lg ring-2 ring-white/30' : 'border-slate-400 hover:border-slate-300'
                      }`}
                      style={{ backgroundColor: color }}
                      onClick={() => setSessionForm(prev => ({ ...prev, color }))}
                    />
                  ))}
                </div>

                {/* Custom Color Picker */}
                <div className="flex items-center gap-2 mt-2 sm:mt-0">
                  <input
                    type="color"
                    value={sessionForm.color}
                    onChange={(e) => setSessionForm(prev => ({ ...prev, color: e.target.value }))}
                    className="w-8 h-8 sm:w-9 sm:h-9 rounded border border-slate-500 bg-slate-700 cursor-pointer"
                  />
                  <span className="text-xs sm:text-sm text-gray-300 font-mono">{sessionForm.color}</span>
                </div>
              </div>
            </div>

            {/* Schedule */}
            <div className="space-y-3 sm:space-y-0 sm:grid sm:grid-cols-3 sm:gap-4">
              <div>
                <Label className="text-white font-medium text-sm sm:text-base">Day of Week *</Label>
                <Select
                  value={sessionForm.day_of_week}
                  onValueChange={(value) => setSessionForm(prev => ({ ...prev, day_of_week: value }))}
                >
                  <SelectTrigger className="bg-slate-700/90 border-slate-500 text-white mt-1 focus:border-purple-400 focus:ring-purple-400/20 h-10 sm:h-11 text-sm sm:text-base">
                    <SelectValue placeholder="Select day" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-700/95 border-slate-500 backdrop-blur-sm">
                    {DAYS_OF_WEEK.map((day) => (
                      <SelectItem key={day.value} value={day.value.toString()} className="text-white text-sm sm:text-base">
                        {day.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="start_time" className="text-white font-medium text-sm sm:text-base">Start Time *</Label>
                <Input
                  id="start_time"
                  type="time"
                  value={sessionForm.start_time}
                  onChange={(e) => setSessionForm(prev => ({ ...prev, start_time: e.target.value }))}
                  className="bg-slate-700/90 border-slate-500 text-white mt-1 focus:border-purple-400 focus:ring-purple-400/20 font-medium h-10 sm:h-11 text-sm sm:text-base"
                />
              </div>

              <div>
                <Label htmlFor="end_time" className="text-white font-medium text-sm sm:text-base">End Time *</Label>
                <Input
                  id="end_time"
                  type="time"
                  value={sessionForm.end_time}
                  onChange={(e) => setSessionForm(prev => ({ ...prev, end_time: e.target.value }))}
                  className="bg-slate-700/90 border-slate-500 text-white mt-1 focus:border-purple-400 focus:ring-purple-400/20 font-medium h-10 sm:h-11 text-sm sm:text-base"
                />
              </div>
            </div>

            {/* Notes and Goals */}
            <div className="space-y-3 sm:space-y-0 sm:grid sm:grid-cols-2 sm:gap-4">
              <div>
                <Label htmlFor="notes" className="text-white font-medium text-sm sm:text-base">Notes</Label>
                <Textarea
                  id="notes"
                  placeholder="Additional notes about this session"
                  value={sessionForm.notes}
                  onChange={(e) => setSessionForm(prev => ({ ...prev, notes: e.target.value }))}
                  rows={3}
                  className="bg-slate-700/90 border-slate-500 text-white placeholder:text-slate-300 mt-1 focus:border-purple-400 focus:ring-purple-400/20 font-medium text-sm sm:text-base"
                />
              </div>

              <div>
                <Label htmlFor="goals" className="text-white font-medium text-sm sm:text-base">Goals</Label>
                <Textarea
                  id="goals"
                  placeholder="What do you want to achieve? e.g., Read chapter 4, summarize key points"
                  value={sessionForm.goals}
                  onChange={(e) => setSessionForm(prev => ({ ...prev, goals: e.target.value }))}
                  rows={3}
                  className="bg-slate-700/90 border-slate-500 text-white placeholder:text-slate-300 mt-1 focus:border-purple-400 focus:ring-purple-400/20 font-medium text-sm sm:text-base"
                />
              </div>
            </div>

            {/* Reading Text */}
            <div>
              <Label htmlFor="reading_text" className="text-white font-medium text-sm sm:text-base">Reading Material (Optional)</Label>
              <Textarea
                id="reading_text"
                placeholder="Paste reading text here or leave empty to link files later"
                value={sessionForm.reading_text}
                onChange={(e) => setSessionForm(prev => ({ ...prev, reading_text: e.target.value }))}
                rows={4}
                className="bg-slate-700/90 border-slate-500 text-white placeholder:text-slate-300 mt-1 focus:border-purple-400 focus:ring-purple-400/20 font-medium text-sm sm:text-base"
              />
            </div>

            {/* Actions */}
            <div className="flex flex-col sm:flex-row justify-end gap-3 pt-2">
              <Button
                variant="outline"
                onClick={handleClose}
                className="border-slate-500 text-white hover:bg-slate-700 hover:border-slate-400 bg-slate-800/50 h-10 sm:h-11 text-sm sm:text-base order-2 sm:order-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateSession}
                disabled={isLoading}
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold shadow-lg border border-purple-500/30 hover:border-purple-400/50 h-10 sm:h-11 text-sm sm:text-base order-1 sm:order-2"
              >
                {isLoading
                  ? (editingSession ? 'Updating...' : 'Creating...')
                  : (editingSession ? 'Update Session' : 'Create Session')
                }
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4 sm:space-y-6 px-1">
            {/* Subject Form */}
            <div>
              <Label htmlFor="subject_name" className="text-white font-medium text-sm sm:text-base">Subject Name *</Label>
              <Input
                id="subject_name"
                placeholder="e.g., Mathematics, History, Biology"
                value={subjectForm.name}
                onChange={(e) => setSubjectForm(prev => ({ ...prev, name: e.target.value }))}
                className="bg-slate-700/90 border-slate-500 text-white placeholder:text-slate-300 mt-1 focus:border-purple-400 focus:ring-purple-400/20 font-medium h-10 sm:h-11 text-sm sm:text-base"
              />
            </div>

            <div>
              <Label htmlFor="subject_description" className="text-white font-medium text-sm sm:text-base">Description</Label>
              <Textarea
                id="subject_description"
                placeholder="Brief description of this subject"
                value={subjectForm.description}
                onChange={(e) => setSubjectForm(prev => ({ ...prev, description: e.target.value }))}
                rows={2}
                className="bg-slate-700/90 border-slate-500 text-white placeholder:text-slate-300 mt-1 focus:border-purple-400 focus:ring-purple-400/20 font-medium text-sm sm:text-base"
              />
            </div>

            <div>
              <Label className="text-white font-medium text-sm sm:text-base">Color</Label>
              <div className="flex flex-wrap gap-2 mt-2">
                {SUBJECT_COLORS.map((color) => (
                  <button
                    key={color}
                    type="button"
                    className={`w-8 h-8 sm:w-9 sm:h-9 rounded-full border-2 transition-all duration-200 hover:scale-110 ${
                      subjectForm.color === color ? 'border-white shadow-lg' : 'border-slate-400 hover:border-slate-300'
                    }`}
                    style={{ backgroundColor: color }}
                    onClick={() => setSubjectForm(prev => ({ ...prev, color }))}
                  />
                ))}
              </div>
            </div>

            {/* Actions */}
            <div className="flex flex-col sm:flex-row justify-end gap-3 pt-2">
              <Button
                variant="outline"
                onClick={() => setStep('session')}
                className="border-slate-500 text-white hover:bg-slate-700 hover:border-slate-400 bg-slate-800/50 h-10 sm:h-11 text-sm sm:text-base order-2 sm:order-1"
              >
                Back
              </Button>
              <Button
                onClick={handleCreateSubject}
                disabled={isLoading}
                className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-bold shadow-lg border border-green-500/30 hover:border-green-400/50 h-10 sm:h-11 text-sm sm:text-base order-1 sm:order-2"
              >
                {isLoading ? 'Creating...' : 'Create Subject'}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
