import React, { useState, useEffect } from 'react';
import { useOffline } from '@/hooks/useOffline';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowLeft,
  FileText, 
  WifiOff, 
  Search,
  FolderOpen,
  BookOpen,
  Plus
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { OfflineUnits, OfflineTopics, OfflineNotes as OfflineNotesUtil, OfflineUnit, OfflineTopic, OfflineNote } from '@/utils/offlineStorage';

interface OfflineNotesBrowserProps {
  className?: string;
}

type NavigationLevel =
  | { level: "root" }
  | { level: "unit"; unitId: string }
  | { level: "topic"; unitId: string; topicId: string };

export const OfflineNotesBrowser: React.FC<OfflineNotesBrowserProps> = ({ className }) => {
  const { status } = useOffline();
  const [nav, setNav] = useState<NavigationLevel>({ level: "root" });
  const [search, setSearch] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  // Data states
  const [units, setUnits] = useState<OfflineUnit[]>([]);
  const [topics, setTopics] = useState<OfflineTopic[]>([]);
  const [notes, setNotes] = useState<OfflineNote[]>([]);

  // Load data based on navigation level
  useEffect(() => {
    const timer = setTimeout(() => {
      loadData();
    }, 200);
    
    return () => clearTimeout(timer);
  }, [nav]);

  const loadData = async () => {
    try {
      setIsLoading(true);
      
      if (nav.level === "root") {
        const unitsData = await OfflineUnits.getAll();
        setUnits(unitsData);
      } else if (nav.level === "unit") {
        const topicsData = await OfflineTopics.getByUnit(nav.unitId);
        setTopics(topicsData);
      } else if (nav.level === "topic") {
        const notesData = await OfflineNotesUtil.getByTopic(nav.topicId);
        setNotes(notesData);
      }
    } catch (error) {
      console.error('Error loading data:', error);
      toast({
        title: "Error",
        description: "Failed to load data.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Get current unit and topic for breadcrumbs
  const currentUnit = nav.level === "unit" || nav.level === "topic"
    ? units.find(u => u.id === nav.unitId)
    : null;
  const currentTopic = nav.level === "topic"
    ? topics.find(t => t.id === nav.topicId)
    : null;

  // Filter data based on search
  const getFilteredData = () => {
    if (nav.level === "root") {
      return units.filter(unit =>
        search === "" || unit.name.toLowerCase().includes(search.toLowerCase())
      );
    } else if (nav.level === "unit") {
      return topics.filter(topic =>
        search === "" || topic.name.toLowerCase().includes(search.toLowerCase())
      );
    } else if (nav.level === "topic") {
      return notes.filter(note =>
        search === "" || note.title.toLowerCase().includes(search.toLowerCase())
      );
    }
    return [];
  };

  const filteredData = getFilteredData();

  const handleBack = () => {
    if (nav.level === "topic") {
      setNav({ level: "unit", unitId: nav.unitId });
    } else if (nav.level === "unit") {
      setNav({ level: "root" });
    }
  };

  const getBreadcrumbs = () => {
    const breadcrumbs = ["Notes"];
    
    if (currentUnit) {
      breadcrumbs.push(currentUnit.name);
    }
    
    if (currentTopic) {
      breadcrumbs.push(currentTopic.name);
    }
    
    return breadcrumbs.join(" > ");
  };

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-gray-500">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <div className="flex items-center gap-2 mb-2">
            {nav.level !== "root" && (
              <Button variant="ghost" size="sm" onClick={handleBack}>
                <ArrowLeft className="h-4 w-4" />
              </Button>
            )}
            <h2 className="text-2xl font-bold flex items-center gap-2">
              <FileText className="h-6 w-6" />
              {getBreadcrumbs()}
              {status.isOffline && (
                <Badge variant="secondary" className="ml-2">
                  <WifiOff className="h-3 w-3 mr-1" />
                  Offline
                </Badge>
              )}
            </h2>
          </div>
          <p className="text-gray-600">
            {nav.level === "root" && "Browse your study units"}
            {nav.level === "unit" && `Topics in ${currentUnit?.name}`}
            {nav.level === "topic" && `Notes in ${currentTopic?.name}`}
          </p>
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          placeholder={`Search ${nav.level === "root" ? "units" : nav.level === "unit" ? "topics" : "notes"}...`}
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Content Grid */}
      {filteredData.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            {nav.level === "root" && <FolderOpen className="h-12 w-12 mx-auto mb-4 text-gray-400" />}
            {nav.level === "unit" && <BookOpen className="h-12 w-12 mx-auto mb-4 text-gray-400" />}
            {nav.level === "topic" && <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />}
            <h3 className="text-lg font-medium mb-2">
              {search ? `No matching ${nav.level === "root" ? "units" : nav.level === "unit" ? "topics" : "notes"}` : 
               `No ${nav.level === "root" ? "units" : nav.level === "unit" ? "topics" : "notes"} yet`}
            </h3>
            <p className="text-gray-500">
              {search ? "Try adjusting your search terms." : 
               `${nav.level === "root" ? "Units" : nav.level === "unit" ? "Topics" : "Notes"} will appear here when available.`}
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredData.map((item: any) => (
            <Card 
              key={item.id} 
              className="hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => {
                if (nav.level === "root") {
                  setNav({ level: "unit", unitId: item.id });
                } else if (nav.level === "unit") {
                  setNav({ level: "topic", unitId: nav.unitId, topicId: item.id });
                }
              }}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <CardTitle className="text-lg line-clamp-2 flex items-center gap-2">
                    {nav.level === "root" && <FolderOpen className="h-5 w-5" style={{ color: item.color }} />}
                    {nav.level === "unit" && <BookOpen className="h-5 w-5 text-blue-600" />}
                    {nav.level === "topic" && <FileText className="h-5 w-5 text-gray-600" />}
                    {nav.level === "root" ? item.name : nav.level === "unit" ? item.name : item.title}
                  </CardTitle>
                  {!item.synced && (
                    <Badge variant="outline" className="ml-2 shrink-0">
                      <WifiOff className="h-3 w-3 mr-1" />
                      Offline
                    </Badge>
                  )}
                </div>
                {item.description && (
                  <CardDescription className="line-clamp-2">
                    {item.description}
                  </CardDescription>
                )}
              </CardHeader>
              <CardContent className="pt-0">
                {nav.level === "root" && (
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>{item.topic_count} topics</span>
                    <span>{item.note_count} notes</span>
                  </div>
                )}
                {nav.level === "unit" && (
                  <div className="text-sm text-gray-500">
                    {item.note_count} notes
                  </div>
                )}
                {nav.level === "topic" && item.content && (
                  <p className="text-sm text-gray-600 line-clamp-3">
                    {item.content.substring(0, 150)}...
                  </p>
                )}
                {nav.level === "topic" && item.tags && item.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {item.tags.slice(0, 3).map((tag: string, index: number) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {item.tags.length > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{item.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default OfflineNotesBrowser;
