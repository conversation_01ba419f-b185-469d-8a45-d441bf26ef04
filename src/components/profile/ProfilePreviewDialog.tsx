import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { UserPlus, UserCheck, Clock, MapPin, GraduationCap, Building, MessageCircle } from "lucide-react";
import { useProfileById } from "@/hooks/useProfile";
import { useSendFriendRequest, useFriends, useFriendshipStatus } from "@/hooks/useFriends";
import { useUser } from "@/hooks/useAuth";
import { useGetOrCreateConversation } from "@/hooks/useMessages";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

interface ProfilePreviewDialogProps {
  userId: string | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const ProfilePreviewDialog: React.FC<ProfilePreviewDialogProps> = ({
  userId,
  open,
  onOpenChange
}) => {
  const navigate = useNavigate();
  const { data: profile, isLoading, error } = useProfileById(userId || '');
  const { data: friendshipStatus } = useFriendshipStatus(userId || '');
  const { data: currentUser } = useUser();
  const sendFriendRequestMutation = useSendFriendRequest();
  const getOrCreateConversationMutation = useGetOrCreateConversation();

  const handleSendFriendRequest = async () => {
    if (!userId) return;

    // Check if user is trying to send request to themselves
    if (currentUser?.id === userId) {
      toast.error("You cannot send a friend request to yourself");
      return;
    }

    try {
      await sendFriendRequestMutation.mutateAsync(userId);
      toast.success("Friend request sent!");
    } catch (error) {
      console.error('Failed to send friend request:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
      toast.error(`Failed to send friend request: ${error.message || 'Unknown error'}`);
    }
  };

  const handleStartConversation = async () => {
    if (!userId) return;

    try {
      const result = await getOrCreateConversationMutation.mutateAsync({ otherUserId: userId });
      toast.success("Conversation started!");
      onOpenChange(false);
      navigate(`/messages?conversation=${result.conversationId}`);
    } catch (error) {
      console.error('Failed to start conversation:', error);
      toast.error('Failed to start conversation');
    }
  };





  if (!userId) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md w-full bg-gradient-to-br from-violet-900/95 to-purple-900/95 backdrop-blur-2xl border border-white/20 shadow-2xl rounded-3xl text-white">
        <DialogHeader>
          <DialogTitle className="text-center text-xl font-bold bg-gradient-to-r from-pink-300 via-violet-300 to-cyan-300 bg-clip-text text-transparent">
            Profile Preview
          </DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            <span className="ml-3 text-white/70">Loading profile...</span>
          </div>
        ) : profile ? (
          <div className="space-y-6">
            {/* Profile Header */}
            <div className="flex flex-col items-center text-center space-y-4">
              {/* Avatar with Status */}
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-pink-400 via-violet-500 to-cyan-400 rounded-full p-1">
                  <div className="w-full h-full bg-gradient-to-r from-purple-900/50 to-indigo-900/50 rounded-full p-1">
                    <div className="w-full h-full bg-transparent rounded-full"></div>
                  </div>
                </div>
                
                <Avatar className="w-24 h-24 shadow-2xl border-4 border-white/30 relative z-10">
                  <AvatarImage src={profile.avatar_url} alt={profile.full_name} className="object-cover" />
                  <AvatarFallback className="text-2xl font-bold bg-gradient-to-br from-pink-500 via-violet-500 to-cyan-500 text-white">
                    {profile.full_name?.split(" ").map((n) => n[0]).join("").slice(0, 2).toUpperCase() || "U"}
                  </AvatarFallback>
                </Avatar>


              </div>

              {/* Name and Status */}
              <div>
                <h3 className="text-2xl font-bold text-white mb-1">
                  {profile.full_name || 'Unknown User'}
                </h3>

              </div>
            </div>

            {/* Profile Details */}
            <div className="space-y-3">
              {profile.country && (
                <div className="flex items-center gap-3 p-3 bg-white/10 rounded-xl">
                  <MapPin size={18} className="text-pink-400" />
                  <span className="text-white/90">{profile.country}</span>
                </div>
              )}
              
              {profile.course && (
                <div className="flex items-center gap-3 p-3 bg-white/10 rounded-xl">
                  <GraduationCap size={18} className="text-cyan-400" />
                  <span className="text-white/90">{profile.course}</span>
                </div>
              )}
              
              {profile.institute && (
                <div className="flex items-center gap-3 p-3 bg-white/10 rounded-xl">
                  <Building size={18} className="text-violet-400" />
                  <span className="text-white/90">{profile.institute}</span>
                </div>
              )}

              {profile.bio && (
                <div className="p-3 bg-white/10 rounded-xl">
                  <p className="text-white/90 text-sm leading-relaxed">{profile.bio}</p>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              {/* Message Button - Always available */}
              <Button
                onClick={handleStartConversation}
                disabled={getOrCreateConversationMutation.isPending || currentUser?.id === userId}
                className="w-full bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white shadow-xl transition-all duration-300 rounded-full"
              >
                <MessageCircle size={18} className="mr-2" />
                {getOrCreateConversationMutation.isPending ? 'Starting...' : 'Send Message'}
              </Button>

              {/* Friend Status/Action */}
              <div className="flex gap-3">
                {friendshipStatus?.isFriend ? (
                  <Badge variant="secondary" className="flex-1 px-4 py-2 bg-emerald-500/20 text-emerald-300 border-emerald-500/30 justify-center">
                    <UserCheck size={16} className="mr-1" />
                    Friend
                  </Badge>
                ) : friendshipStatus?.requestSent ? (
                  <Badge variant="secondary" className="flex-1 px-4 py-2 bg-yellow-500/20 text-yellow-300 border-yellow-500/30 justify-center">
                    <Clock size={16} className="mr-1" />
                    Request Sent
                  </Badge>
                ) : friendshipStatus?.requestReceived ? (
                  <Badge variant="secondary" className="flex-1 px-4 py-2 bg-blue-500/20 text-blue-300 border-blue-500/30 justify-center">
                    <UserPlus size={16} className="mr-1" />
                    Request Received
                  </Badge>
                ) : currentUser?.id !== userId ? (
                  <Button
                    onClick={handleSendFriendRequest}
                    disabled={sendFriendRequestMutation.isPending}
                    className="flex-1 bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white shadow-xl transition-all duration-300 rounded-full"
                  >
                    <UserPlus size={18} className="mr-2" />
                    {sendFriendRequestMutation.isPending ? 'Sending...' : 'Add Friend'}
                  </Button>
                ) : null}
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-white/70">Profile not found</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ProfilePreviewDialog;
