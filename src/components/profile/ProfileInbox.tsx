
import React from "react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { useQueryClient } from "@tanstack/react-query";

type InboxMsg = {
  from: string;
  image: string;
  preview: string;
  time: string;
  conversation_id: string;
  other_user_id?: string;
  unread_count?: number;
};

type ProfileInboxProps = {
  inboxMessages: InboxMsg[];
  setOpenConversation: (msg: InboxMsg | null) => void;
  onProfilePreview?: (userId: string) => void;
};

const ProfileInbox: React.FC<ProfileInboxProps> = ({
  inboxMessages,
  setOpenConversation,
  onProfilePreview,
}) => {
  const queryClient = useQueryClient();

  const handleRefresh = async () => {
    await queryClient.invalidateQueries({ queryKey: ['conversations'] });
  };

  // Calculate total unread messages
  const totalUnreadCount = inboxMessages.reduce((total, msg) => total + (msg.unread_count || 0), 0);
  return (
    <div className="bg-white/10 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/20 p-4 sm:p-6">
      <div className="flex items-center gap-3 mb-4 sm:mb-6">
        <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full flex items-center justify-center shadow-lg">
          <span className="text-white text-base sm:text-lg font-bold">📧</span>
        </div>
        <h2 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-cyan-300 via-blue-300 to-indigo-300 bg-clip-text text-transparent flex items-center gap-2">
          Inbox
          {totalUnreadCount > 0 && (
            <span className="bg-gradient-to-r from-red-400 to-pink-500 text-white text-xs sm:text-sm font-bold px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full min-w-[20px] sm:min-w-[24px] h-5 sm:h-6 flex items-center justify-center">
              {totalUnreadCount > 99 ? '99+' : totalUnreadCount}
            </span>
          )}
        </h2>
        <div className="ml-auto flex items-center gap-2 sm:gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            className="bg-white/10 border-white/20 text-white hover:bg-white/20 text-xs sm:text-sm px-2 sm:px-3 py-1 sm:py-2"
          >
            <RefreshCw size={14} className="mr-1 sm:mr-2 sm:w-4 sm:h-4" />
            <span className="hidden sm:inline">Refresh</span>
            <span className="sm:hidden">↻</span>
          </Button>
          {inboxMessages.length > 0 && (
            <span className="bg-gradient-to-r from-cyan-500/20 to-blue-500/20 backdrop-blur-sm text-white border border-white/20 px-2 sm:px-4 py-1 sm:py-2 rounded-full text-xs sm:text-sm font-medium">
              {inboxMessages.length} {inboxMessages.length === 1 ? 'message' : 'messages'}
            </span>
          )}
        </div>
      </div>

      <div className="space-y-3 sm:space-y-4">
        {inboxMessages.length === 0 && (
          <div className="text-center py-8 sm:py-12">
            <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 border border-white/20">
              <span className="text-2xl sm:text-3xl">📬</span>
            </div>
            <p className="text-white/80 text-base sm:text-lg font-medium">No messages yet</p>
            <p className="text-white/50 text-xs sm:text-sm mt-1">Your conversations will appear here</p>
          </div>
        )}

        {inboxMessages.map((msg, idx) => {
          const hasUnread = msg.unread_count && msg.unread_count > 0;
          return (
            <div
              key={msg.from + idx}
              className={`group flex items-center gap-3 sm:gap-4 p-3 sm:p-4 hover:bg-white/10 rounded-2xl transition-all duration-300 hover:shadow-xl hover:scale-[1.02] border border-transparent hover:border-white/20 cursor-pointer ${
                hasUnread ? 'bg-white/5 border-white/10' : ''
              }`}
              onClick={() => setOpenConversation(msg)}
            >

            <div className="relative">
              <Avatar
                className="w-12 h-12 sm:w-14 sm:h-14 border-3 border-white/30 shadow-2xl cursor-pointer hover:scale-110 transition-transform duration-200"
                onClick={(e) => {
                  e.stopPropagation();
                  if (msg.other_user_id && onProfilePreview) {
                    onProfilePreview(msg.other_user_id);
                  }
                }}
              >
                <AvatarImage src={msg.image} alt={msg.from} className="object-cover" />
                <AvatarFallback className="bg-gradient-to-br from-cyan-400 to-blue-400 text-white font-bold text-sm sm:text-base">
                  {msg.from
                    .split(" ")
                    .map((n) => n[0])
                    .join("")
                    .slice(0, 2)
                    .toUpperCase()}
                </AvatarFallback>
              </Avatar>


            </div>

            <div
              className="flex-1 min-w-0 cursor-pointer"
              onClick={() => setOpenConversation(msg)}
              role="button"
              tabIndex={0}
              aria-label={`Open conversation with ${msg.from}`}
            >
              <div className="flex items-center justify-between mb-1">
                <div className="font-bold text-white truncate text-sm sm:text-base">{msg.from}</div>
                <div className="flex items-center gap-2 flex-shrink-0 ml-2">
                  <div className="text-xs text-white/60 font-medium">{msg.time}</div>
                  {/* Unread message counter next to time */}
                  {hasUnread && (
                    <div className="bg-gradient-to-r from-emerald-400 to-green-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg border border-white/20 backdrop-blur-sm min-w-[20px] h-5 flex items-center justify-center">
                      {msg.unread_count > 99 ? '99+' : msg.unread_count}
                    </div>
                  )}
                </div>
              </div>
              <div className={`text-xs sm:text-sm truncate group-hover:text-white/90 transition-colors ${
                hasUnread ? 'text-white font-semibold' : 'text-white/70'
              }`}>
                {msg.preview}
              </div>
            </div>

            {/* Arrow indicator */}
            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex-shrink-0">
              <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>
          );
        })}
      </div>
    </div>
  );
};

export default ProfileInbox;
