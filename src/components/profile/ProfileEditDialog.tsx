
import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Edit, Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { useState } from "react";
import { COUNTRIES } from "@/data/countries";

type ProfileEditDialogProps = {
  user: any;
  open: boolean;
  setOpen: (v: boolean) => void;
  onSave: (values: any) => Promise<void>;
  courses: string[];
  institutes: string[];
  countries: string[];
  isLoading?: boolean;
};

// Custom ComboBox component for editable dropdowns
const EditableComboBox: React.FC<{
  label: string;
  value: string;
  onChange: (value: string) => void;
  options: string[];
  placeholder: string;
}> = ({ label, value, onChange, options, placeholder }) => {
  const [isO<PERSON>, setIsO<PERSON>] = useState(false);
  const [inputValue, setInputValue] = useState(value || '');

  React.useEffect(() => {
    setInputValue(value || '');
  }, [value]);

  const filteredOptions = options.filter(option =>
    option.toLowerCase().includes(inputValue.toLowerCase())
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    onChange(newValue);
    setIsOpen(true);
  };

  const handleOptionSelect = (option: string) => {
    setInputValue(option);
    onChange(option);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <label className="block text-sm font-semibold text-white/90 mb-2">
        {label}
      </label>
      <div className="relative">
        <Input
          value={inputValue}
          onChange={handleInputChange}
          onFocus={() => setIsOpen(true)}
          onBlur={() => setTimeout(() => setIsOpen(false), 200)}
          placeholder={placeholder}
          className="rounded-xl border-2 border-white/20 focus:border-pink-400 focus:ring-pink-400 bg-white/10 backdrop-blur-sm text-white placeholder:text-white/50"
        />
        {isOpen && filteredOptions.length > 0 && (
          <div className="absolute z-50 w-full mt-1 bg-violet-900/95 backdrop-blur-xl border border-white/20 rounded-xl shadow-2xl max-h-48 overflow-y-auto">
            {filteredOptions.slice(0, 10).map((option) => (
              <button
                key={option}
                type="button"
                className="w-full text-left px-4 py-2 text-white hover:bg-white/10 focus:bg-white/10 first:rounded-t-xl last:rounded-b-xl"
                onMouseDown={() => handleOptionSelect(option)}
              >
                {option}
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export const ProfileEditDialog: React.FC<ProfileEditDialogProps> = ({
  user, open, setOpen, onSave, courses, institutes, countries, isLoading = false
}) => {
  const [isSaving, setIsSaving] = useState(false);

  const { register, handleSubmit, reset, setValue, watch } = useForm({
    defaultValues: {
      name: user.name,
      email: user.email,
      course: user.course,
      institute: user.institute,
      country: user.country,
    },
  });

  const handleFormSubmit = async (values: any) => {
    setIsSaving(true);
    try {
      await onSave(values);
      setOpen(false);
    } catch (error) {
      console.error('Failed to save profile:', error);
    } finally {
      setIsSaving(false);
    }
  };

  React.useEffect(() => {
    reset({
      name: user.name,
      email: user.email,
      course: user.course,
      institute: user.institute,
      country: user.country,
    });
  }, [user, open, reset]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          aria-label="Edit profile"
          className="bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white border border-white/30 rounded-full shadow-2xl hover:shadow-white/25 transition-all duration-300 hover:scale-110"
        >
          <Edit size={20} />
        </Button>
      </DialogTrigger>
      <DialogContent className="bg-gradient-to-br from-violet-900/95 to-purple-900/95 backdrop-blur-2xl border border-white/20 shadow-2xl rounded-3xl text-white">
        <form
          onSubmit={handleSubmit(handleFormSubmit)}
          className="space-y-6"
        >
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-pink-300 via-violet-300 to-cyan-300 bg-clip-text text-transparent">
              Edit Profile
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            <div>
              <label className="block text-sm font-semibold text-white/90 mb-2" htmlFor="name">
                Full Name
              </label>
              <Input
                id="name"
                {...register("name", { required: true })}
                className="rounded-xl border-2 border-white/20 focus:border-pink-400 focus:ring-pink-400 bg-white/10 backdrop-blur-sm text-white placeholder:text-white/50"
              />
            </div>

            <div>
              <label className="block text-sm font-semibold text-white/90 mb-2" htmlFor="email">
                Email Address
              </label>
              <Input
                id="email"
                type="email"
                {...register("email", { required: true })}
                className="rounded-xl border-2 border-white/20 focus:border-pink-400 focus:ring-pink-400 bg-white/10 backdrop-blur-sm text-white placeholder:text-white/50"
              />
            </div>

            <EditableComboBox
              label="Course"
              value={watch("course") || ''}
              onChange={(val) => setValue("course", val)}
              options={courses}
              placeholder="Type or select your course"
            />

            <EditableComboBox
              label="Institute"
              value={watch("institute") || ''}
              onChange={(val) => setValue("institute", val)}
              options={institutes}
              placeholder="Type or select your institute"
            />

            <EditableComboBox
              label="Country"
              value={watch("country") || ''}
              onChange={(val) => setValue("country", val)}
              options={COUNTRIES.map(c => c.label)}
              placeholder="Type or select your country"
            />
          </div>

          <DialogFooter className="gap-4">
            <DialogClose asChild>
              <Button
                type="button"
                variant="outline"
                className="rounded-full border-2 border-white/30 hover:border-white/50 text-white hover:bg-white/10 px-8 backdrop-blur-sm"
              >
                Cancel
              </Button>
            </DialogClose>
            <Button
              type="submit"
              disabled={isSaving}
              className="bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white rounded-full px-8 shadow-2xl hover:shadow-pink-500/25 transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {isSaving && <Loader2 className="w-4 h-4 animate-spin" />}
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
