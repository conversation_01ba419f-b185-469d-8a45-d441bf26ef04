
import React, { useRef, useState } from "react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

type ProfileAvatarProps = {
  name: string;
  image?: string | null;
  onPhotoChange: (file: File) => void;
  className?: string;
};

const ProfileAvatar: React.FC<ProfileAvatarProps> = ({ name, image, onPhotoChange, className }) => {
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const handleAvatarEditClick = () => {
    fileInputRef.current?.click();
  };

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const url = URL.createObjectURL(file);
      setAvatarPreview(url);
      onPhotoChange(file);
    }
  };

  return (
    <div className="relative group">
      {/* Main Avatar with Glow Effect */}
      <div className="relative">
        {/* Glow Ring */}
        <div className="absolute inset-0 bg-gradient-to-r from-pink-400 via-violet-500 to-cyan-400 rounded-full p-1">
          <div className="w-full h-full bg-gradient-to-r from-purple-900/50 to-indigo-900/50 rounded-full p-2">
            <div className="w-full h-full bg-transparent rounded-full"></div>
          </div>
        </div>

        {/* Avatar */}
        <Avatar className={cn("w-40 h-40 lg:w-48 lg:h-48 shadow-2xl border-4 border-white/30", className)}>
          <AvatarImage src={avatarPreview || image} alt={name} className="object-cover" />
          <AvatarFallback className="text-3xl lg:text-4xl font-bold bg-gradient-to-br from-pink-500 via-violet-500 to-cyan-500 text-white">
            {name.split(" ").map((n) => n[0]).join("").slice(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>
      </div>

      {/* Edit Button - Bottom Right */}
      <button
        type="button"
        aria-label="Edit profile photo"
        className="absolute -bottom-2 -right-2 bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 border-3 border-white group-hover:scale-110"
        onClick={handleAvatarEditClick}
        tabIndex={0}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
          <path strokeLinecap="round" strokeLinejoin="round" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
        </svg>
      </button>

      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        className="hidden"
        onChange={handleAvatarChange}
      />
    </div>
  );
};

export default ProfileAvatar;
