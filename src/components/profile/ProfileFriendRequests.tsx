import React from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON>r<PERSON><PERSON><PERSON>, UserX, Loader2 } from "lucide-react";
import { useAcceptFriendRequest, useDeclineFriendRequest } from "@/hooks/useFriends";
import { toast } from "sonner";

type FriendRequest = {
  id: string;
  requester_id: string;
  addressee_id: string;
  status: string;
  created_at: string;
  requester: {
    id: string;
    full_name: string;
    avatar_url: string;
  };
};

type ProfileFriendRequestsProps = {
  friendRequests: FriendRequest[];
};

const ProfileFriendRequests: React.FC<ProfileFriendRequestsProps> = ({
  friendRequests,
}) => {
  const acceptRequestMutation = useAcceptFriendRequest();
  const declineRequestMutation = useDeclineFriendRequest();

  const handleAcceptRequest = async (requestId: string, requesterName: string) => {
    try {
      await acceptRequestMutation.mutateAsync(requestId);
      toast.success(`Accepted friend request from ${requesterName}!`);
    } catch (error) {
      toast.error("Failed to accept friend request");
    }
  };

  const handleDeclineRequest = async (requestId: string, requesterName: string) => {
    try {
      await declineRequestMutation.mutateAsync(requestId);
      toast.success(`Declined friend request from ${requesterName}`);
    } catch (error) {
      toast.error("Failed to decline friend request");
    }
  };

  return (
    <div className="bg-white/10 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/20 p-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-10 h-10 bg-gradient-to-r from-violet-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg">
          <UserCheck className="text-white" size={20} />
        </div>
        <h2 className="text-2xl font-bold bg-gradient-to-r from-violet-300 via-purple-300 to-pink-300 bg-clip-text text-transparent">
          Friend Requests
        </h2>
        {friendRequests && friendRequests.length > 0 && (
          <Badge variant="secondary" className="ml-2">
            {friendRequests.length}
          </Badge>
        )}
      </div>

      {!friendRequests || friendRequests.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gradient-to-r from-violet-500/20 to-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <UserCheck className="text-violet-300" size={32} />
          </div>
          <p className="text-white/60 text-lg">No friend requests</p>
          <p className="text-white/40 text-sm mt-2">
            When someone sends you a friend request, it will appear here.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {friendRequests.map((request) => (
            <Card
              key={request.id}
              className="bg-white/5 border-white/10 backdrop-blur-sm p-4 hover:bg-white/10 transition-all duration-300"
            >
              <div className="flex items-center gap-4">
                <Avatar className="w-12 h-12 border-2 border-white/20">
                  <AvatarImage
                    src={request.requester.avatar_url || ''}
                    alt={request.requester.full_name || 'User'}
                  />
                  <AvatarFallback className="bg-gradient-to-br from-violet-500 to-purple-600 text-white font-semibold">
                    {(request.requester.full_name || 'U')
                      .split(' ')
                      .map(n => n[0])
                      .join('')
                      .slice(0, 2)
                      .toUpperCase()}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1">
                  <h3 className="text-white font-semibold text-lg">
                    {request.requester.full_name || 'Unknown User'}
                  </h3>
                  <p className="text-white/60 text-sm">
                    Sent {new Date(request.created_at).toLocaleDateString()}
                  </p>
                </div>

                <div className="flex gap-2">
                  <Button
                    size="sm"
                    className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white"
                    onClick={() => handleAcceptRequest(request.id, request.requester.full_name)}
                    disabled={acceptRequestMutation.isPending}
                  >
                    {acceptRequestMutation.isPending ? (
                      <Loader2 size={16} className="animate-spin" />
                    ) : (
                      <UserCheck size={16} />
                    )}
                    Accept
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="border-red-500/50 text-red-400 hover:bg-red-500/10 hover:border-red-500"
                    onClick={() => handleDeclineRequest(request.id, request.requester.full_name)}
                    disabled={declineRequestMutation.isPending}
                  >
                    {declineRequestMutation.isPending ? (
                      <Loader2 size={16} className="animate-spin" />
                    ) : (
                      <UserX size={16} />
                    )}
                    Decline
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProfileFriendRequests;
