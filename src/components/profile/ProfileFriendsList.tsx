
import React from "react";
import { Card } from "@/components/ui/card";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { toast } from "sonner";

type Friend = {
  id: string;
  name: string;
  image: string;
  online: boolean;
  course?: string;
  country?: string;
};

type ProfileFriendsListProps = {
  friends: Friend[];
  onStartConversation?: (conversationId: string, friendName: string, friendAvatar: string) => void;
  onProfilePreview?: (userId: string) => void;
};

const ProfileFriendsList: React.FC<ProfileFriendsListProps> = ({
  friends,
  onStartConversation,
  onProfilePreview
}) => {

  const handleStartConversation = async (friendId: string, friendName: string, friendAvatar: string) => {
    try {
      // For now, just show a toast - the actual messaging functionality would be implemented here
      toast.success(`Starting conversation with ${friendName}...`);
      if (onStartConversation) {
        onStartConversation(friendId, friendName, friendAvatar);
      }
    } catch (error) {
      toast.error("Failed to start conversation");
    }
  };

  return (
  <div className="bg-white/10 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/20 p-6">
    <div className="flex items-center gap-3 mb-6">
      <div className="w-10 h-10 bg-gradient-to-r from-pink-500 to-violet-500 rounded-full flex items-center justify-center shadow-lg">
        <span className="text-white text-lg font-bold">👥</span>
      </div>
      <h2 className="text-2xl font-bold bg-gradient-to-r from-pink-300 via-violet-300 to-cyan-300 bg-clip-text text-transparent">
        Friends
      </h2>
      <span className="ml-auto bg-gradient-to-r from-pink-500/20 to-violet-500/20 backdrop-blur-sm text-white border border-white/20 px-4 py-2 rounded-full text-sm font-medium">
        {friends.length} {friends.length === 1 ? 'friend' : 'friends'}
      </span>
    </div>

    <div className="space-y-4">
      {friends.length === 0 && (
        <div className="text-center py-12">
          <div className="w-20 h-20 bg-gradient-to-r from-white/10 to-white/5 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 border border-white/20">
            <span className="text-3xl">👋</span>
          </div>
          <p className="text-white/80 text-lg font-medium">No friends yet</p>
          <p className="text-white/50 text-sm mt-1">Start connecting with other students!</p>
        </div>
      )}

      {friends.map((friend, idx) => (
        <div
          key={friend.name + idx}
          className="group flex items-center gap-4 p-4 hover:bg-white/10 rounded-2xl cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-[1.02] border border-transparent hover:border-white/20"
        >
          <div className="relative">
            <Avatar
              className="w-16 h-16 border-3 border-white/30 shadow-2xl cursor-pointer hover:scale-110 transition-transform duration-200"
              onClick={(e) => {
                e.stopPropagation();
                if (onProfilePreview) {
                  onProfilePreview(friend.id);
                }
              }}
            >
              <AvatarImage src={friend.image} alt={friend.name} className="object-cover" />
              <AvatarFallback className="bg-gradient-to-br from-pink-400 to-violet-400 text-white font-bold text-lg">
                {friend.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")
                  .slice(0, 2)
                  .toUpperCase()}
              </AvatarFallback>
            </Avatar>
          </div>

          <div className="flex-1">
            <div className="text-lg font-bold text-white">
              {friend.name}
            </div>
            {friend.course && (
              <div className="text-sm text-white/70">
                {friend.course}
              </div>
            )}
            {friend.country && (
              <div className="text-xs text-white/60">
                {friend.country}
              </div>
            )}
          </div>

          {/* Action buttons - always visible */}
          <div className="flex gap-3">
            <button
              className="p-3 bg-gradient-to-r from-pink-500 to-violet-500 text-white rounded-full hover:shadow-2xl hover:shadow-pink-500/25 transition-all duration-300 hover:scale-110 border border-white/20"
              onClick={() => handleStartConversation(friend.id, friend.name, friend.image)}
              title="Start conversation"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </button>
          </div>
        </div>
      ))}
    </div>
  </div>
  );
};

export default ProfileFriendsList;
