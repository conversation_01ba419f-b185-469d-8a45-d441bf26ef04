import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, FileText, Upload, X } from 'lucide-react';
import { useUploadFile, useCreateNote, useTopics } from '@/hooks/useNotes';
import { toast } from 'sonner';

interface CreateNoteModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  unitId: string;
  topicId?: string; // Optional - if not provided, user can select
  unitName?: string;
  topicName?: string;
}

const CreateNoteModal: React.FC<CreateNoteModalProps> = ({
  open,
  onOpenChange,
  unitId,
  topicId,
  unitName,
  topicName
}) => {
  const [noteTitle, setNoteTitle] = useState('');
  const [noteContent, setNoteContent] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedTopicId, setSelectedTopicId] = useState(topicId || '');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const uploadFileMutation = useUploadFile();
  const createNoteMutation = useCreateNote();
  const { data: topics = [] } = useTopics(unitId);

  // Update selectedTopicId when topicId prop changes
  React.useEffect(() => {
    setSelectedTopicId(topicId || '');
  }, [topicId]);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Check file size (25MB limit)
      if (file.size > 25 * 1024 * 1024) {
        toast.error('File size must be less than 25MB');
        return;
      }

      // Check file type - allow common document types
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain',
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp'
      ];

      if (!allowedTypes.includes(file.type)) {
        toast.error('Please select a supported file type (PDF, Word, PowerPoint, Excel, Text, or Image)');
        return;
      }

      setSelectedFile(file);

      // Auto-fill title from filename if empty
      if (!noteTitle) {
        const fileName = file.name.replace(/\.[^/.]+$/, ''); // Remove extension
        setNoteTitle(fileName);
      }
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!noteTitle.trim()) {
      toast.error('Please enter a note title');
      return;
    }

    if (!unitId) {
      toast.error('Unit is required');
      return;
    }

    if (!selectedTopicId) {
      toast.error('Please select a topic');
      return;
    }

    try {
      let fileUrl: string | undefined;
      
      // Upload file if selected
      if (selectedFile) {
        const uploadResult = await uploadFileMutation.mutateAsync(selectedFile);
        fileUrl = uploadResult.url;
      }

      // Create note
      await createNoteMutation.mutateAsync({
        title: noteTitle.trim(),
        content: noteContent.trim() || undefined,
        unit_id: unitId,
        topic_id: selectedTopicId,
        file_url: fileUrl,
      });
      
      // Reset form
      setNoteTitle('');
      setNoteContent('');
      setSelectedFile(null);
      setSelectedTopicId(topicId || '');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      onOpenChange(false);
    } catch (error) {
      // Error handled by mutations
    }
  };

  const handleClose = () => {
    setNoteTitle('');
    setNoteContent('');
    setSelectedFile(null);
    setSelectedTopicId(topicId || '');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    onOpenChange(false);
  };

  const isLoading = uploadFileMutation.isPending || createNoteMutation.isPending;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-md bg-white/95 backdrop-blur-2xl border border-white/20 shadow-2xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent flex items-center gap-2">
            <FileText className="w-5 h-5 text-green-600" />
            Create New Note
          </DialogTitle>
          <DialogDescription>
            {unitName && topicName ? (
              <>Add a new note to <strong>{topicName}</strong> in <strong>{unitName}</strong>.</>
            ) : (
              'Create a new note with optional PDF attachment.'
            )}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="note-title">Note Title *</Label>
            <Input
              id="note-title"
              value={noteTitle}
              onChange={(e) => setNoteTitle(e.target.value)}
              placeholder="e.g., Chapter 1 Notes, Assignment Solutions"
              required
            />
          </div>

          {/* Topic Selection - only show if topicId is not provided */}
          {!topicId && (
            <div>
              <Label htmlFor="topic-select">Topic *</Label>
              <Select value={selectedTopicId} onValueChange={setSelectedTopicId}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a topic" />
                </SelectTrigger>
                <SelectContent>
                  {topics.map((topic) => (
                    <SelectItem key={topic.id} value={topic.id}>
                      {topic.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {topics.length === 0 && (
                <p className="text-sm text-gray-500 mt-1">
                  No topics available. Create a topic first.
                </p>
              )}
            </div>
          )}

          <div>
            <Label htmlFor="note-content">Content (Optional)</Label>
            <Textarea
              id="note-content"
              value={noteContent}
              onChange={(e) => setNoteContent(e.target.value)}
              placeholder="Add any additional notes or description"
              rows={3}
            />
          </div>

          <div>
            <Label>Document File (Optional)</Label>
            <div className="space-y-2">
              {!selectedFile ? (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                  <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600 mb-2">
                    Upload PDF, Word, PowerPoint, Excel, Text, or Image files
                  </p>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    Choose File
                  </Button>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.jpg,.jpeg,.png,.gif,.webp"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                </div>
              ) : (
                <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
                  <FileText className="w-5 h-5 text-red-500" />
                  <span className="flex-1 text-sm truncate">{selectedFile.name}</span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handleRemoveFile}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              )}
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              className="flex-1"
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !noteTitle.trim() || !selectedTopicId}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {uploadFileMutation.isPending ? 'Uploading...' : 'Creating...'}
                </>
              ) : (
                'Create Note'
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateNoteModal;
