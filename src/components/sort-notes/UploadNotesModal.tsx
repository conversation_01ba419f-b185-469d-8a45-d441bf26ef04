import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Upload, FileText, Folder, Plus, X, Loader2 } from 'lucide-react';
import { useUnits, useTopics, useCreateUnit, useCreateTopic, useUploadFile, useCreateNote } from '@/hooks/useNotes';
import { toast } from 'sonner';

interface UploadNotesModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

type UploadStep = 'file' | 'unit' | 'topic' | 'confirm';

const UploadNotesModal: React.FC<UploadNotesModalProps> = ({ open, onOpenChange }) => {
  const [step, setStep] = useState<UploadStep>('file');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedUnitId, setSelectedUnitId] = useState<string>('');
  const [selectedTopicId, setSelectedTopicId] = useState<string>('');
  const [noteTitle, setNoteTitle] = useState<string>('');
  const [noteDescription, setNoteDescription] = useState<string>('');
  
  // New unit/topic creation states
  const [showNewUnit, setShowNewUnit] = useState(false);
  const [showNewTopic, setShowNewTopic] = useState(false);
  const [newUnitName, setNewUnitName] = useState('');
  const [newUnitDescription, setNewUnitDescription] = useState('');
  const [newUnitColor, setNewUnitColor] = useState('#6366f1');
  const [newTopicName, setNewTopicName] = useState('');
  const [newTopicDescription, setNewTopicDescription] = useState('');

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Hooks
  const { data: units = [], isLoading: unitsLoading } = useUnits();
  const { data: topics = [], isLoading: topicsLoading } = useTopics(selectedUnitId);
  const createUnitMutation = useCreateUnit();
  const createTopicMutation = useCreateTopic();
  const uploadFileMutation = useUploadFile();
  const createNoteMutation = useCreateNote();

  const selectedUnit = units.find(u => u.id === selectedUnitId);
  const selectedTopic = topics.find(t => t.id === selectedTopicId);

  const resetModal = () => {
    setStep('file');
    setSelectedFile(null);
    setSelectedUnitId('');
    setSelectedTopicId('');
    setNoteTitle('');
    setNoteDescription('');
    setShowNewUnit(false);
    setShowNewTopic(false);
    setNewUnitName('');
    setNewUnitDescription('');
    setNewUnitColor('#6366f1');
    setNewTopicName('');
    setNewTopicDescription('');
  };

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
    setNoteTitle(file.name.replace(/\.[^/.]+$/, '')); // Remove file extension
    setStep('unit');
  };

  const handleCreateUnit = async () => {
    if (!newUnitName.trim()) {
      toast.error('Please enter a unit name');
      return;
    }

    try {
      const result = await createUnitMutation.mutateAsync({
        name: newUnitName,
        description: newUnitDescription,
        color: newUnitColor,
      });
      setSelectedUnitId(result.id);
      setShowNewUnit(false);
      setNewUnitName('');
      setNewUnitDescription('');
      setNewUnitColor('#6366f1');
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleCreateTopic = async () => {
    if (!newTopicName.trim()) {
      toast.error('Please enter a topic name');
      return;
    }

    try {
      const result = await createTopicMutation.mutateAsync({
        name: newTopicName,
        description: newTopicDescription,
        unit_id: selectedUnitId,
      });
      setSelectedTopicId(result.id);
      setShowNewTopic(false);
      setNewTopicName('');
      setNewTopicDescription('');
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleFinalUpload = async () => {
    if (!selectedFile || !selectedUnitId || !selectedTopicId) {
      toast.error('Please complete all steps');
      return;
    }

    try {
      // Upload file first
      const fileResult = await uploadFileMutation.mutateAsync(selectedFile);
      
      // Create note with file reference
      await createNoteMutation.mutateAsync({
        title: noteTitle,
        content: noteDescription,
        unit_id: selectedUnitId,
        topic_id: selectedTopicId,
        file_url: fileResult.url,
      });

      onOpenChange(false);
      resetModal();
    } catch (error) {
      // Errors handled by mutations
    }
  };

  const isUploading = uploadFileMutation.isPending || createNoteMutation.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-white/95 backdrop-blur-2xl border border-white/20 shadow-2xl">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">
            Upload Notes
          </DialogTitle>
          <DialogDescription>
            Follow the steps to organize and upload your notes into units and topics.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Progress Steps */}
          <div className="flex items-center justify-between">
            {(['file', 'unit', 'topic', 'confirm'] as UploadStep[]).map((stepName, index) => (
              <div key={stepName} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step === stepName ? 'bg-violet-600 text-white' : 
                  ['file', 'unit', 'topic', 'confirm'].indexOf(step) > index ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600'
                }`}>
                  {index + 1}
                </div>
                {index < 3 && <div className="w-12 h-0.5 bg-gray-200 mx-2" />}
              </div>
            ))}
          </div>

          {/* Step 1: File Selection */}
          {step === 'file' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Select File</h3>
              <div 
                className="border-2 border-dashed border-violet-300 rounded-xl p-8 text-center cursor-pointer hover:border-violet-500 transition-colors"
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="w-12 h-12 text-violet-500 mx-auto mb-4" />
                <p className="text-lg font-medium text-violet-700 mb-2">
                  Click to select a file
                </p>
                <p className="text-sm text-gray-600">
                  Supported: PDF, DOCX, TXT, and more
                </p>
              </div>
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                accept=".pdf,.doc,.docx,.txt,.ppt,.pptx"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) handleFileSelect(file);
                }}
              />
              {selectedFile && (
                <div className="flex items-center gap-3 p-3 bg-violet-50 rounded-lg">
                  <FileText className="w-6 h-6 text-violet-600" />
                  <div className="flex-1">
                    <p className="font-medium">{selectedFile.name}</p>
                    <p className="text-sm text-gray-600">
                      {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedFile(null)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              )}
            </div>
          )}

          {/* Step 2: Unit Selection */}
          {step === 'unit' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Choose Unit (Subject)</h3>

              {!showNewUnit ? (
                <div className="space-y-3">
                  <Select value={selectedUnitId} onValueChange={setSelectedUnitId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a unit..." />
                    </SelectTrigger>
                    <SelectContent>
                      {units.map((unit) => (
                        <SelectItem key={unit.id} value={unit.id}>
                          <div className="flex items-center gap-2">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: unit.color }}
                            />
                            {unit.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Button
                    variant="outline"
                    onClick={() => setShowNewUnit(true)}
                    className="w-full"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Create New Unit
                  </Button>
                </div>
              ) : (
                <div className="space-y-3 p-4 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Create New Unit</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowNewUnit(false)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="unit-name">Unit Name</Label>
                      <Input
                        id="unit-name"
                        value={newUnitName}
                        onChange={(e) => setNewUnitName(e.target.value)}
                        placeholder="e.g., Biology, Mathematics"
                      />
                    </div>

                    <div>
                      <Label htmlFor="unit-description">Description (Optional)</Label>
                      <Textarea
                        id="unit-description"
                        value={newUnitDescription}
                        onChange={(e) => setNewUnitDescription(e.target.value)}
                        placeholder="Brief description of this unit"
                        rows={2}
                      />
                    </div>

                    <div>
                      <Label htmlFor="unit-color">Color</Label>
                      <div className="flex items-center gap-2">
                        <input
                          type="color"
                          id="unit-color"
                          value={newUnitColor}
                          onChange={(e) => setNewUnitColor(e.target.value)}
                          className="w-12 h-8 rounded border"
                        />
                        <span className="text-sm text-gray-600">{newUnitColor}</span>
                      </div>
                    </div>

                    <Button
                      onClick={handleCreateUnit}
                      disabled={createUnitMutation.isPending}
                      className="w-full"
                    >
                      {createUnitMutation.isPending ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Creating...
                        </>
                      ) : (
                        'Create Unit'
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Step 3: Topic Selection */}
          {step === 'topic' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Choose Topic</h3>
              <p className="text-sm text-gray-600">
                Unit: <span className="font-medium">{selectedUnit?.name}</span>
              </p>

              {!showNewTopic ? (
                <div className="space-y-3">
                  <Select value={selectedTopicId} onValueChange={setSelectedTopicId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a topic..." />
                    </SelectTrigger>
                    <SelectContent>
                      {topics.map((topic) => (
                        <SelectItem key={topic.id} value={topic.id}>
                          <div className="flex items-center gap-2">
                            <Folder className="w-4 h-4 text-blue-500" />
                            {topic.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Button
                    variant="outline"
                    onClick={() => setShowNewTopic(true)}
                    className="w-full"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Create New Topic
                  </Button>
                </div>
              ) : (
                <div className="space-y-3 p-4 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Create New Topic</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowNewTopic(false)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <Label htmlFor="topic-name">Topic Name</Label>
                      <Input
                        id="topic-name"
                        value={newTopicName}
                        onChange={(e) => setNewTopicName(e.target.value)}
                        placeholder="e.g., Cell Division, Algebra Basics"
                      />
                    </div>

                    <div>
                      <Label htmlFor="topic-description">Description (Optional)</Label>
                      <Textarea
                        id="topic-description"
                        value={newTopicDescription}
                        onChange={(e) => setNewTopicDescription(e.target.value)}
                        placeholder="Brief description of this topic"
                        rows={2}
                      />
                    </div>

                    <Button
                      onClick={handleCreateTopic}
                      disabled={createTopicMutation.isPending}
                      className="w-full"
                    >
                      {createTopicMutation.isPending ? (
                        <>
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          Creating...
                        </>
                      ) : (
                        'Create Topic'
                      )}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Step 4: Confirmation */}
          {step === 'confirm' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Confirm Upload</h3>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="note-title">Note Title</Label>
                  <Input
                    id="note-title"
                    value={noteTitle}
                    onChange={(e) => setNoteTitle(e.target.value)}
                    placeholder="Enter a title for your note"
                  />
                </div>

                <div>
                  <Label htmlFor="note-description">Description (Optional)</Label>
                  <Textarea
                    id="note-description"
                    value={noteDescription}
                    onChange={(e) => setNoteDescription(e.target.value)}
                    placeholder="Add a description or summary"
                    rows={3}
                  />
                </div>

                <div className="p-4 bg-gray-50 rounded-lg space-y-2">
                  <h4 className="font-medium">Upload Summary</h4>
                  <div className="text-sm space-y-1">
                    <p><span className="font-medium">File:</span> {selectedFile?.name}</p>
                    <p><span className="font-medium">Unit:</span> {selectedUnit?.name}</p>
                    <p><span className="font-medium">Topic:</span> {selectedTopic?.name}</p>
                    <p><span className="font-medium">Size:</span> {selectedFile ? (selectedFile.size / 1024 / 1024).toFixed(2) : '0'} MB</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-4">
            <Button
              variant="outline"
              onClick={() => {
                if (step === 'file') {
                  onOpenChange(false);
                  resetModal();
                } else if (step === 'unit') {
                  setStep('file');
                } else if (step === 'topic') {
                  setStep('unit');
                } else if (step === 'confirm') {
                  setStep('topic');
                }
              }}
              disabled={isUploading}
            >
              {step === 'file' ? 'Cancel' : 'Back'}
            </Button>

            <Button
              onClick={() => {
                if (step === 'file' && selectedFile) {
                  setStep('unit');
                } else if (step === 'unit' && selectedUnitId) {
                  setStep('topic');
                } else if (step === 'topic' && selectedTopicId) {
                  setStep('confirm');
                } else if (step === 'confirm') {
                  handleFinalUpload();
                }
              }}
              disabled={
                (step === 'file' && !selectedFile) ||
                (step === 'unit' && !selectedUnitId) ||
                (step === 'topic' && !selectedTopicId) ||
                isUploading
              }
            >
              {step === 'confirm' ? (
                isUploading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  'Upload Note'
                )
              ) : (
                'Next'
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default UploadNotesModal;
