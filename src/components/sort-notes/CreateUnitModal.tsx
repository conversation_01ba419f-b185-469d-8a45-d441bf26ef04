import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Folder } from 'lucide-react';
import { useCreateUnit } from '@/hooks/useNotes';
import { toast } from 'sonner';

interface CreateUnitModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const CreateUnitModal: React.FC<CreateUnitModalProps> = ({ open, onOpenChange }) => {
  const [unitName, setUnitName] = useState('');
  const [unitDescription, setUnitDescription] = useState('');
  const [unitColor, setUnitColor] = useState('#6366f1');

  const createUnitMutation = useCreateUnit();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!unitName.trim()) {
      toast.error('Please enter a unit name');
      return;
    }

    try {
      await createUnitMutation.mutateAsync({
        name: unitName.trim(),
        description: unitDescription.trim() || undefined,
        color: unitColor,
      });
      
      // Reset form
      setUnitName('');
      setUnitDescription('');
      setUnitColor('#6366f1');
      onOpenChange(false);
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleClose = () => {
    setUnitName('');
    setUnitDescription('');
    setUnitColor('#6366f1');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-md bg-white/95 backdrop-blur-2xl border border-white/20 shadow-2xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent flex items-center gap-2">
            <Folder className="w-5 h-5 text-violet-600" />
            Create New Unit
          </DialogTitle>
          <DialogDescription>
            Create a new unit to organize your topics and notes.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="unit-name">Unit Name *</Label>
            <Input
              id="unit-name"
              value={unitName}
              onChange={(e) => setUnitName(e.target.value)}
              placeholder="e.g., Mathematics, Biology, History"
              required
            />
          </div>

          <div>
            <Label htmlFor="unit-description">Description (Optional)</Label>
            <Textarea
              id="unit-description"
              value={unitDescription}
              onChange={(e) => setUnitDescription(e.target.value)}
              placeholder="Brief description of this unit"
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="unit-color">Color</Label>
            <div className="flex items-center gap-3">
              <input
                type="color"
                id="unit-color"
                value={unitColor}
                onChange={(e) => setUnitColor(e.target.value)}
                className="w-12 h-10 rounded border border-gray-300 cursor-pointer"
              />
              <span className="text-sm text-gray-600 font-mono">{unitColor}</span>
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={createUnitMutation.isPending || !unitName.trim()}
              className="flex-1"
            >
              {createUnitMutation.isPending ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Unit'
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateUnitModal;
