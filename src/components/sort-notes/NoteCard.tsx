
import React from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Folder, FileText, Plus, Share2 } from "lucide-react";

export type NoteCardType = "subject" | "unit" | "file";
type NoteCardProps = {
  type: NoteCardType;
  name: string;
  color?: string;
  unitsCount?: number;
  filesCount?: number;
  subtitle?: string;
  onClick?: () => void;
  onAdd?: (e: React.MouseEvent) => void;
  onShare?: (e: React.MouseEvent) => void;
  children?: React.ReactNode;
  fileUrl?: string; // For file type cards
  hasFile?: boolean; // Whether the note has an attached file
};

const NoteCard: React.FC<NoteCardProps> = ({
  type,
  name,
  color,
  unitsCount,
  filesCount,
  onClick,
  onAdd,
  onShare,
  hasFile,
}) => {
  if (type === "subject") {
    return (
      <Card
        className="p-0 rounded-xl border border-slate-100 bg-white overflow-hidden shadow-sm flex flex-col min-h-[100px] cursor-pointer transition hover:shadow"
        onClick={onClick}
        tabIndex={0}
      >
        <div className="p-6 flex flex-col gap-2 items-start">
          <div className={`rounded-md p-3 mb-1 ${color} bg-opacity-30`}>
            <Folder size={28} />
          </div>
          <span className="text-lg font-semibold text-slate-800">{name}</span>
          <span className="text-xs text-slate-500">{unitsCount} topics</span>
        </div>
      </Card>
    );
  }
  if (type === "unit") {
    return (
      <Card
        className="p-0 rounded-xl border border-slate-100 bg-white overflow-hidden shadow-sm flex flex-col min-h-[100px] cursor-pointer transition hover:shadow"
        onClick={onClick}
        tabIndex={0}
      >
        <div className="p-6 flex flex-col gap-2 items-start">
          <div className={`rounded px-2 py-1 text-xs font-semibold mb-2 ${color} bg-opacity-20`}>
            {name}
          </div>
          <span className="text-xs text-slate-500">
            {filesCount} PDF {filesCount === 1 ? "note" : "notes"}
          </span>
        </div>
        <button
          className="flex items-center gap-1 text-violet-600 mt-auto ml-4 mb-4 px-2 py-1 rounded hover:bg-violet-50 text-xs font-medium"
          onClick={onAdd}
        >
          <Plus size={16} />
          Add Note
        </button>
      </Card>
    );
  }
  if (type === "file") {
    return (
      <Card
        className={`p-0 rounded-xl border border-slate-100 bg-white overflow-hidden shadow-sm flex flex-col min-h-[60px] ${
          onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''
        }`}
      >
        <div className="p-4 flex items-center gap-3" onClick={onClick}>
          <div className="relative">
            <FileText className="text-violet-400" size={22} />
            {hasFile && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white" />
            )}
          </div>
          <div className="flex-1">
            <span className="truncate text-slate-700 text-base block">{name}</span>
            {hasFile && (
              <span className="text-xs text-green-600 font-medium">Has attachment</span>
            )}
          </div>
          {onShare && (
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onShare(e);
              }}
              className="h-8 w-8 p-0 hover:bg-blue-50"
            >
              <Share2 className="h-4 w-4 text-blue-600" />
            </Button>
          )}
        </div>
      </Card>
    );
  }
  return null;
};

export default NoteCard;
