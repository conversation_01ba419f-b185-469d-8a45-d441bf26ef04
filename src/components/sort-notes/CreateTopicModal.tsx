import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, FolderOpen } from 'lucide-react';
import { useCreateTopic } from '@/hooks/useNotes';
import { toast } from 'sonner';

interface CreateTopicModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  unitId: string;
  unitName?: string;
}

const CreateTopicModal: React.FC<CreateTopicModalProps> = ({ 
  open, 
  onOpenChange, 
  unitId, 
  unitName 
}) => {
  const [topicName, setTopicName] = useState('');
  const [topicDescription, setTopicDescription] = useState('');

  const createTopicMutation = useCreateTopic();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!topicName.trim()) {
      toast.error('Please enter a topic name');
      return;
    }

    if (!unitId) {
      toast.error('Unit ID is required');
      return;
    }

    try {
      await createTopicMutation.mutateAsync({
        name: topicName.trim(),
        description: topicDescription.trim() || undefined,
        unit_id: unitId,
      });
      
      // Reset form
      setTopicName('');
      setTopicDescription('');
      onOpenChange(false);
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleClose = () => {
    setTopicName('');
    setTopicDescription('');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-md bg-white/95 backdrop-blur-2xl border border-white/20 shadow-2xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent flex items-center gap-2">
            <FolderOpen className="w-5 h-5 text-blue-600" />
            Create New Topic
          </DialogTitle>
          <DialogDescription>
            {unitName ? (
              <>Create a new topic in <strong>{unitName}</strong> unit.</>
            ) : (
              'Create a new topic to organize your notes.'
            )}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="topic-name">Topic Name *</Label>
            <Input
              id="topic-name"
              value={topicName}
              onChange={(e) => setTopicName(e.target.value)}
              placeholder="e.g., Cell Division, Algebra Basics, World War II"
              required
            />
          </div>

          <div>
            <Label htmlFor="topic-description">Description (Optional)</Label>
            <Textarea
              id="topic-description"
              value={topicDescription}
              onChange={(e) => setTopicDescription(e.target.value)}
              placeholder="Brief description of this topic"
              rows={3}
            />
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={createTopicMutation.isPending || !topicName.trim()}
              className="flex-1"
            >
              {createTopicMutation.isPending ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Topic'
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateTopicModal;
