import React, { useState } from 'react';
import { useOffline } from '@/hooks/useOffline';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import {
  Wifi,
  WifiOff,
  RefreshCw,
  Database,
  Clock,
  HardDrive,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';

export const OfflineIndicator: React.FC = () => {
  const { status, forcSync, getStorageUsage, clearOfflineData } = useOffline();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [storageInfo, setStorageInfo] = useState<{ used: number; quota: number } | null>(null);

  const handleSync = async () => {
    if (!status.isOnline) {
      toast({
        title: "Cannot Sync",
        description: "You need an internet connection to sync offline data.",
        variant: "destructive",
      });
      return;
    }

    setIsSyncing(true);
    try {
      await forcSync();
      toast({
        title: "Sync Complete",
        description: "Your offline data has been synchronized.",
      });
    } catch (error) {
      toast({
        title: "Sync Failed",
        description: "Failed to sync offline data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSyncing(false);
    }
  };

  const handleClearOfflineData = async () => {
    try {
      await clearOfflineData();
      toast({
        title: "Offline Data Cleared",
        description: "All offline data has been removed from your device.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to clear offline data.",
        variant: "destructive",
      });
    }
  };

  const loadStorageInfo = async () => {
    const info = await getStorageUsage();
    setStorageInfo(info);
  };

  React.useEffect(() => {
    if (isDialogOpen) {
      loadStorageInfo();
    }
  }, [isDialogOpen]);

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusColor = () => {
    if (status.isOnline) {
      return status.pendingSyncCount > 0 ? 'bg-yellow-500' : 'bg-green-500';
    }
    return 'bg-red-500';
  };

  const getStatusText = () => {
    if (status.isOnline) {
      return status.pendingSyncCount > 0 ? 'Online (Sync Pending)' : 'Online';
    }
    return 'Offline';
  };

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="relative p-2 h-auto"
          title={getStatusText()}
        >
          {status.isOnline ? (
            <Wifi className="h-4 w-4" />
          ) : (
            <WifiOff className="h-4 w-4" />
          )}
          
          {/* Status indicator dot */}
          <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full ${getStatusColor()}`} />
          
          {/* Pending sync badge */}
          {status.pendingSyncCount > 0 && (
            <Badge 
              variant="secondary" 
              className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs flex items-center justify-center"
            >
              {status.pendingSyncCount}
            </Badge>
          )}
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {status.isOnline ? (
              <Wifi className="h-5 w-5 text-green-500" />
            ) : (
              <WifiOff className="h-5 w-5 text-red-500" />
            )}
            Connection Status
          </DialogTitle>
          <DialogDescription>
            Manage your offline data and synchronization settings
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Connection Status */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                {status.isOnline ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-red-500" />
                )}
                {getStatusText()}
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2 text-sm text-gray-600">
                {status.isOnline ? (
                  <p>You're connected to the internet. All features are available.</p>
                ) : (
                  <p>You're offline. You can still view and create content that will sync when you're back online.</p>
                )}
                
                {status.lastSyncTime && (
                  <div className="flex items-center gap-1 text-xs">
                    <Clock className="h-3 w-3" />
                    Last sync: {new Date(status.lastSyncTime).toLocaleString()}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Offline Data Status */}
          {(status.hasOfflineData || status.pendingSyncCount > 0) && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  Offline Data
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0 space-y-3">
                {status.pendingSyncCount > 0 && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Pending sync items:</span>
                    <Badge variant="outline">{status.pendingSyncCount}</Badge>
                  </div>
                )}

                {status.hasOfflineData && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Offline content available</span>
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </div>
                )}

                {/* Sync Button */}
                {status.isOnline && status.pendingSyncCount > 0 && (
                  <Button
                    onClick={handleSync}
                    disabled={isSyncing}
                    className="w-full"
                    size="sm"
                  >
                    {isSyncing ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Syncing...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Sync Now
                      </>
                    )}
                  </Button>
                )}
              </CardContent>
            </Card>
          )}

          {/* Storage Usage */}
          {storageInfo && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <HardDrive className="h-4 w-4" />
                  Storage Usage
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0 space-y-3">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Used:</span>
                    <span>{formatBytes(storageInfo.used)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Available:</span>
                    <span>{formatBytes(storageInfo.quota)}</span>
                  </div>
                  
                  {storageInfo.quota > 0 && (
                    <Progress 
                      value={(storageInfo.used / storageInfo.quota) * 100} 
                      className="h-2"
                    />
                  )}
                </div>

                {status.hasOfflineData && (
                  <Button
                    onClick={handleClearOfflineData}
                    variant="outline"
                    size="sm"
                    className="w-full text-red-600 hover:text-red-700"
                  >
                    Clear Offline Data
                  </Button>
                )}
              </CardContent>
            </Card>
          )}

          {/* Offline Capabilities */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Available Offline</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  View Notes
                </div>
                <div className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  Create Notes
                </div>
                <div className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  View Timetable
                </div>
                <div className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  Add Timetable
                </div>
                <div className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  Send Messages
                </div>
                <div className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  View Groups
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default OfflineIndicator;
