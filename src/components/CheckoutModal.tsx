import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2, X, ExternalLink, RefreshCw, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';
import { useUser } from '@/hooks/useAuth';
import { woocommerce } from '@/services/woocommerce';
import { supabase } from '@/integrations/supabase/client';

interface CheckoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export const CheckoutModal: React.FC<CheckoutModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const { data: user } = useUser();
  const [isLoading, setIsLoading] = useState(true);
  const [checkoutUrl, setCheckoutUrl] = useState<string>('');
  const [paymentStatus, setPaymentStatus] = useState<'checkout' | 'success'>('checkout');
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    if (isOpen && user?.id) {
      initializeCheckout();
    }
  }, [isOpen, user?.id]);

  const initializeCheckout = () => {
    if (!user?.id) return;

    setIsLoading(true);
    setPaymentStatus('checkout');
    
    // Get the WooCommerce product URL
    const url = woocommerce.getProductUrl(user.id);
    console.log('🛒 Opening checkout URL:', url);
    setCheckoutUrl(url);
  };

  const handleIframeLoad = () => {
    setIsLoading(false);
    
    // Start monitoring for success
    const checkInterval = setInterval(() => {
      try {
        const iframe = iframeRef.current;
        if (iframe?.contentWindow) {
          const currentUrl = iframe.contentWindow.location.href;
          
          if (woocommerce.isSuccessUrl(currentUrl)) {
            console.log('✅ Payment success detected:', currentUrl);
            clearInterval(checkInterval);
            handlePaymentSuccess(currentUrl);
          }
        }
      } catch (error) {
        // Cross-origin restrictions are normal
      }
    }, 2000);

    // Listen for messages from iframe
    const handleMessage = (event: MessageEvent) => {
      if (!event.origin.includes('studyfam.co.ke')) return;

      if (event.data.type === 'payment_success') {
        clearInterval(checkInterval);
        handlePaymentSuccess(event.data.url);
      }
    };

    window.addEventListener('message', handleMessage);

    // Cleanup
    return () => {
      clearInterval(checkInterval);
      window.removeEventListener('message', handleMessage);
    };
  };

  const handlePaymentSuccess = async (successUrl: string) => {
    if (!user?.id) return;

    try {
      setPaymentStatus('success');
      
      const orderId = woocommerce.extractOrderId(successUrl);
      const orderKey = woocommerce.extractOrderKey(successUrl);

      console.log('🎉 Processing payment success:', { orderId, orderKey });

      // Activate subscription in database
      await activateSubscription(orderId, orderKey);
      
      toast.success('Payment successful! Welcome to StudyFam Premium! 🎉');
      
      // Call success callback after a short delay
      setTimeout(() => {
        onSuccess();
        onClose();
      }, 2000);

    } catch (error) {
      console.error('❌ Error processing payment success:', error);
      toast.error('Payment successful, but failed to activate subscription. Please contact support.');
    }
  };

  const activateSubscription = async (orderId?: string | null, orderKey?: string | null) => {
    if (!user?.id) return;

    try {
      // Get the active subscription plan
      const { data: plan } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .single();

      if (!plan) {
        throw new Error('No active subscription plan found');
      }

      // Calculate subscription period (1 month from now)
      const now = new Date();
      const periodEnd = new Date(now);
      periodEnd.setMonth(periodEnd.getMonth() + 1);

      // Create or update user subscription
      const { error: subscriptionError } = await supabase
        .from('user_subscriptions')
        .upsert({
          user_id: user.id,
          plan_id: plan.id,
          status: 'active',
          current_period_start: now.toISOString(),
          current_period_end: periodEnd.toISOString(),
          payment_provider: 'woocommerce',
          payment_reference: orderId || `wc_${Date.now()}`,
          last_payment_date: now.toISOString(),
          is_trial: false,
        }, {
          onConflict: 'user_id',
        });

      if (subscriptionError) {
        throw subscriptionError;
      }

      // Record the payment transaction
      if (orderId) {
        await supabase
          .from('payment_transactions')
          .insert({
            user_id: user.id,
            amount_cents: plan.price_cents,
            currency: plan.currency,
            status: 'completed',
            transaction_type: 'subscription',
            payment_provider: 'woocommerce',
            payment_reference: orderId,
            metadata: {
              order_id: orderId,
              order_key: orderKey,
              plan_name: plan.name,
            },
            completed_at: now.toISOString(),
          });
      }

      console.log('✅ Subscription activated successfully');

    } catch (error) {
      console.error('❌ Error activating subscription:', error);
      throw error;
    }
  };

  const handleRefresh = () => {
    if (iframeRef.current) {
      iframeRef.current.src = iframeRef.current.src;
      setIsLoading(true);
    }
  };

  const handleOpenExternal = () => {
    if (checkoutUrl) {
      window.open(checkoutUrl, '_blank');
    }
  };

  if (paymentStatus === 'success') {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-md">
          <div className="text-center py-8">
            <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-green-800 mb-2">
              Payment Successful!
            </h3>
            <p className="text-gray-600 mb-4">
              Your StudyFam Premium subscription is now active.
            </p>
            <Button onClick={onClose} className="w-full">
              Continue to StudyFam
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0">
        <DialogHeader className="p-6 pb-0">
          <div className="flex items-center justify-between">
            <DialogTitle>Subscribe to StudyFam Premium</DialogTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>
        
        <div className="p-6 pt-0">
          <div className="relative">
            {/* Loading overlay */}
            {isLoading && (
              <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10">
                <div className="text-center">
                  <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
                  <p className="text-sm text-gray-600">Loading checkout...</p>
                </div>
              </div>
            )}

            {/* Iframe container */}
            <div className="bg-white rounded-lg overflow-hidden border" style={{ height: '600px' }}>
              {checkoutUrl && (
                <iframe
                  ref={iframeRef}
                  src={checkoutUrl}
                  className="w-full h-full"
                  onLoad={handleIframeLoad}
                  title="WooCommerce Checkout"
                />
              )}
            </div>

            {/* Controls */}
            <div className="flex justify-between items-center mt-4 pt-4 border-t">
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" onClick={handleRefresh}>
                  <RefreshCw className="w-4 h-4 mr-1" />
                  Refresh
                </Button>
                <Button variant="outline" size="sm" onClick={handleOpenExternal}>
                  <ExternalLink className="w-4 h-4 mr-1" />
                  Open in New Tab
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
