
import React, { useState } from "react";
import { Home, BookOpen, Users, Plus, NotebookPen, Brain, FileText, Calendar, Camera, X, PenTool } from "lucide-react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";

// Update: Notes (SortNotes) and AI Tutor replace Library and Progress
const navs = [
  { to: "/", icon: Home, label: "Home" },
  { to: "/sort-notes", icon: NotebookPen, label: "Notes" },
  { to: "/study-groups", icon: Users, label: "Study Groups" },
  { to: "/ask-ai-tutor", icon: Brain, label: "AI Tutor" },
];

const MobileBottomNav = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [showMenu, setShowMenu] = useState(false);

  const quickActions = [
    {
      label: "Take Notes",
      icon: NotebookPen,
      path: "/take-notes",
      color: "from-blue-500 to-cyan-500",
      description: "Create new notes"
    },
    {
      label: "Sort Notes",
      icon: FileText,
      path: "/sort-notes",
      color: "from-green-500 to-emerald-500",
      description: "Organize your notes"
    },
    {
      label: "Past Papers",
      icon: BookOpen,
      path: "/past-papers",
      color: "from-purple-500 to-violet-500",
      description: "Access past papers"
    },
    {
      label: "Image to Notes",
      icon: Camera,
      path: "/image-to-notes",
      color: "from-pink-500 to-rose-500",
      description: "Convert images to notes"
    },
    {
      label: "Timetable",
      icon: Calendar,
      path: "/reading-timetable",
      color: "from-orange-500 to-amber-500",
      description: "Manage your schedule"
    },
    {
      label: "Revision Planner",
      icon: PenTool,
      path: "/revision-planner",
      color: "from-indigo-500 to-blue-500",
      description: "Plan your revision"
    }
  ];

  const handleActionClick = (path: string) => {
    setShowMenu(false);
    navigate(path);
  };

  return (
    <>
      {/* Overlay */}
      {showMenu && (
        <div
          className="fixed inset-0 bg-black/20 z-40 md:hidden"
          onClick={() => setShowMenu(false)}
        />
      )}

      {/* Quick Actions Menu */}
      {showMenu && (
        <div className="fixed bottom-20 left-4 right-4 z-50 md:hidden">
          <div className="bg-white rounded-2xl shadow-2xl border border-gray-200 p-4 max-h-96 overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setShowMenu(false)}
                className="h-8 w-8"
              >
                <X size={16} />
              </Button>
            </div>

            <div className="grid grid-cols-2 gap-3">
              {quickActions.map((action) => (
                <button
                  key={action.path}
                  onClick={() => handleActionClick(action.path)}
                  className="flex flex-col items-center p-4 rounded-xl border border-gray-100 hover:border-gray-200 hover:bg-gray-50 transition-all duration-200 group"
                >
                  <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${action.color} flex items-center justify-center mb-2 group-hover:scale-110 transition-transform duration-200`}>
                    <action.icon size={24} className="text-white" />
                  </div>
                  <span className="text-sm font-medium text-gray-900 text-center leading-tight">
                    {action.label}
                  </span>
                  <span className="text-xs text-gray-500 text-center mt-1">
                    {action.description}
                  </span>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Bottom Navigation */}
      <nav className="fixed z-30 bottom-0 left-0 right-0 md:hidden bg-white border-t flex justify-around items-center h-16">
        {navs.slice(0, 2).map((nav) => (
          <Link
            key={nav.to}
            to={nav.to}
            className={`flex flex-col items-center ${
              location.pathname === nav.to
                ? "text-violet-600"
                : "text-slate-400"
            }`}
          >
            <nav.icon size={24} />
            <span className="text-xs">{nav.label}</span>
          </Link>
        ))}

        {/* Plus Button with Menu */}
        <button
          onClick={() => setShowMenu(!showMenu)}
          className={`flex items-center justify-center w-12 h-12 rounded-full text-white -mt-9 shadow-lg transition-all duration-200 ${
            showMenu
              ? "bg-gray-600 rotate-45"
              : "bg-violet-600 hover:bg-violet-700"
          }`}
        >
          {showMenu ? <X size={32} /> : <Plus size={32} />}
        </button>

        {navs.slice(2).map((nav) => (
          <Link
            key={nav.to}
            to={nav.to}
            className={`flex flex-col items-center ${
              location.pathname === nav.to
                ? "text-violet-600"
                : "text-slate-400"
            }`}
          >
            <nav.icon size={24} />
            <span className="text-xs">{nav.label}</span>
          </Link>
        ))}
      </nav>
    </>
  );
};

export default MobileBottomNav;

