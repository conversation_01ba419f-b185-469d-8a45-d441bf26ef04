
import React from "react";
import { Link, useLocation } from "react-router-dom";
import { Navigation } from "./Navigation";
import { Search, Bell, Menu, Settings, MessageCircle, Users } from "lucide-react";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { useProfile } from "@/hooks/useProfile";
import { useFriendshipRequests } from "@/hooks/useFriends";
import { useUnreadNotificationCount, useNotificationSubscription, usePushNotifications } from "@/hooks/useNotifications";
import { useUnreadCount } from "@/hooks/useMessaging";
import NotificationPanel from "@/components/notifications/NotificationPanel";
import OfflineIndicator from "./OfflineIndicator";

// Dashboard-only menu items
const dashboardLinks = [
  { to: "/dashboard", label: "Dashboard" },
  { to: "/offline", label: "Offline Mode" },
  { to: "/sort-notes", label: "Sort Notes" },
  { to: "/study-groups", label: "Study Groups" },
  { to: "/reading-timetable", label: "Timetable" },
  { to: "/ask-ai-tutor", label: "AI Tutor" },
  { to: "/quiz-generator", label: "Quiz Generator" },
  { to: "/image-to-notes", label: "Image to Notes" },
  { to: "/ai-notes", label: "AI Notes" },
  { to: "/revision-planner", label: "Revision Planner" },
  { to: "/past-papers", label: "Past Papers" },
  { to: "/take-notes", label: "Take Notes" },
  { to: "/messages", label: "Messages" },
  { to: "/discover", label: "Discover" },
];

const Header = () => {
  const location = useLocation();
  const [notificationsOpen, setNotificationsOpen] = React.useState(false);
  const [navOpen, setNavOpen] = React.useState(false);
  const { data: profile } = useProfile();
  const { data: friendRequests } = useFriendshipRequests();
  const { data: unreadCount = 0 } = useUnreadNotificationCount();
  const { data: unreadMessages = 0 } = useUnreadCount();
  const { requestPermission } = usePushNotifications();

  // Set up real-time notifications
  useNotificationSubscription();

  // Request notification permission on first load
  React.useEffect(() => {
    const hasRequestedPermission = localStorage.getItem('notification-permission-requested');
    if (!hasRequestedPermission && 'Notification' in window) {
      requestPermission().then(() => {
        localStorage.setItem('notification-permission-requested', 'true');
      });
    }
  }, [requestPermission]);

  return (
    <>
      {/* Desktop Header - Hidden on Mobile */}
      <header className="hidden md:flex items-center justify-between px-4 py-3 border-b bg-white sticky top-0 z-40 w-full">
        <div className="flex items-center gap-2">
          {/* Hamburger menu icon */}
          <button
            className="p-2 text-violet-700 hover:bg-violet-50 rounded-lg transition"
            aria-label="Open menu"
            onClick={() => setNavOpen(true)}
            type="button"
          >
            <Menu size={28} />
          </button>
          {/* Navigation Drawer with dashboard links */}
          <Navigation open={navOpen} setOpen={setNavOpen} links={dashboardLinks} showAuthButtons={false} />
          {/* Logo: links to dashboard */}
          <Link to="/dashboard" className="text-2xl font-extrabold text-[#635bff] tracking-tight">
            StudyFam
          </Link>
        </div>
        <div className="flex gap-4 items-center">
          {/* Search icon links to new search page */}
          <Link
            to="/search"
            title="Search"
            aria-label="Search"
            className="p-2 text-gray-400 hover:text-violet-700 transition-colors"
          >
            <Search size={22} />
          </Link>

          {/* Offline Indicator */}
          <OfflineIndicator />

          {/* Messages icon */}
          <Link
            to="/messages"
            title="Messages"
            aria-label="Messages"
            className="p-2 text-gray-400 hover:text-violet-700 transition-colors relative"
          >
            <MessageCircle size={22} />
            {unreadMessages > 0 && (
              <Badge
                variant="destructive"
                className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
              >
                {unreadMessages > 99 ? '99+' : unreadMessages}
              </Badge>
            )}
          </Link>
          {/* Enhanced Notification with both popover and page link */}
          <div className="flex items-center gap-2">
            {/* Quick notification popover */}
            <Popover open={notificationsOpen} onOpenChange={setNotificationsOpen}>
              <PopoverTrigger asChild>
                <button
                  className="p-2 text-gray-400 hover:text-violet-700 transition-colors relative"
                  title="Quick Notifications"
                  aria-label="Quick Notifications"
                  onClick={() => setNotificationsOpen(!notificationsOpen)}
                  type="button"
                >
                  <Bell size={22} />
                  {unreadCount > 0 && (
                    <Badge
                      variant="destructive"
                      className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs"
                    >
                      {unreadCount > 99 ? '99+' : unreadCount}
                    </Badge>
                  )}
                </button>
              </PopoverTrigger>
              <PopoverContent align="end" sideOffset={8} className="p-0 w-auto">
                <NotificationPanel onClose={() => setNotificationsOpen(false)} />
              </PopoverContent>
            </Popover>

            {/* Link to full notifications page - now visible on mobile too */}
            <Link
              to="/notifications"
              className="p-2 text-gray-400 hover:text-violet-700 transition-colors relative"
              title="Notification Settings"
              aria-label="Notification Settings"
            >
              <Settings size={20} />
            </Link>
          </div>
          <Link to="/profile" aria-label="Profile">
            <Avatar className="w-8 h-8 border-2 border-violet-200">
              <AvatarImage
                src={profile?.avatar_url || ''}
                alt={profile?.full_name || 'Profile'}
                className="object-cover"
              />
              <AvatarFallback className="bg-gradient-to-br from-violet-500 to-purple-600 text-white text-sm font-semibold">
                {profile?.full_name
                  ? profile.full_name.split(' ').map(n => n[0]).join('').slice(0, 2).toUpperCase()
                  : 'U'
                }
              </AvatarFallback>
            </Avatar>
          </Link>
        </div>
      </header>

      {/* Mobile Two-Layer Header - Visible only on Mobile */}
      <header className="md:hidden bg-white sticky top-0 z-40 w-full border-b">
        {/* Top Layer: Navigation Icon, Logo, Profile */}
        <div className="flex items-center justify-between px-3 py-2.5 border-b border-gray-100">
          {/* Left: Navigation Icon */}
          <button
            className="p-2 text-violet-700 hover:bg-violet-50 rounded-lg transition"
            aria-label="Open menu"
            onClick={() => setNavOpen(true)}
            type="button"
          >
            <Menu size={24} />
          </button>

          {/* Center: Logo */}
          <Link to="/dashboard" className="text-2xl font-extrabold text-[#635bff] tracking-tight">
            StudyFam
          </Link>

          {/* Right: Profile */}
          <Link to="/profile" aria-label="Profile">
            <Avatar className="w-8 h-8 border-2 border-violet-200">
              <AvatarImage
                src={profile?.avatar_url || ''}
                alt={profile?.full_name || 'Profile'}
                className="object-cover"
              />
              <AvatarFallback className="bg-gradient-to-br from-violet-500 to-purple-600 text-white text-sm font-semibold">
                {profile?.full_name
                  ? profile.full_name.split(' ').map(n => n[0]).join('').slice(0, 2).toUpperCase()
                  : 'U'
                }
              </AvatarFallback>
            </Avatar>
          </Link>
        </div>

        {/* Bottom Layer: Notifications, Settings, Messages, Discover */}
        <div className="flex items-center justify-around px-2 py-2.5 bg-gradient-to-r from-violet-50 via-purple-50 to-pink-50 shadow-sm">
          {/* Notifications */}
          <Popover open={notificationsOpen} onOpenChange={setNotificationsOpen}>
            <PopoverTrigger asChild>
              <button
                className="flex flex-col items-center p-2 text-gray-700 hover:text-violet-700 transition-colors relative"
                title="Notifications"
                aria-label="Notifications"
                onClick={() => setNotificationsOpen(!notificationsOpen)}
                type="button"
              >
                <Bell size={20} />
                <span className="text-xs mt-0.5">Alerts</span>
                {unreadCount > 0 && (
                  <Badge
                    variant="destructive"
                    className="absolute -top-0.5 -right-0.5 h-4 w-4 flex items-center justify-center p-0 text-[10px]"
                  >
                    {unreadCount > 9 ? '9+' : unreadCount}
                  </Badge>
                )}
              </button>
            </PopoverTrigger>
            <PopoverContent align="center" sideOffset={8} className="p-0 w-auto">
              <NotificationPanel onClose={() => setNotificationsOpen(false)} />
            </PopoverContent>
          </Popover>

          {/* Notification Settings */}
          <Link
            to="/notifications"
            className="flex flex-col items-center p-2 text-gray-700 hover:text-violet-700 transition-colors"
            title="Settings"
            aria-label="Notification Settings"
          >
            <Settings size={20} />
            <span className="text-xs mt-0.5">Settings</span>
          </Link>

          {/* Messages */}
          <Link
            to="/messages"
            className="flex flex-col items-center p-2 text-gray-700 hover:text-violet-700 transition-colors relative"
            title="Messages"
            aria-label="Messages"
          >
            <MessageCircle size={20} />
            <span className="text-xs mt-0.5">Messages</span>
            {unreadMessages > 0 && (
              <Badge
                variant="destructive"
                className="absolute -top-0.5 -right-0.5 h-4 w-4 flex items-center justify-center p-0 text-[10px]"
              >
                {unreadMessages > 9 ? '9+' : unreadMessages}
              </Badge>
            )}
          </Link>

          {/* Discover */}
          <Link
            to="/discover"
            className="flex flex-col items-center p-2 text-gray-700 hover:text-violet-700 transition-colors"
            title="Discover"
            aria-label="Discover"
          >
            <Users size={20} />
            <span className="text-xs mt-0.5">Discover</span>
          </Link>
        </div>

        {/* Navigation Drawer */}
        <Navigation open={navOpen} setOpen={setNavOpen} links={dashboardLinks} showAuthButtons={false} />
      </header>
    </>
  );
};

export default Header;
