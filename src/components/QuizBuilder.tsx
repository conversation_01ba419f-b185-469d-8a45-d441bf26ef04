
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { User, FileText, Plus, Share, Play } from 'lucide-react';

interface QuizBuilderProps {
  selectedCourse: any;
}

export const QuizBuilder = ({ selectedCourse }: QuizBuilderProps) => {
  const [activeTab, setActiveTab] = useState('create');
  const [questions, setQuestions] = useState([
    {
      id: 1,
      question: 'What is the time complexity of binary search?',
      type: 'multiple-choice',
      options: ['O(n)', 'O(log n)', 'O(n²)', 'O(1)'],
      correctAnswer: 1,
    }
  ]);

  const existingQuizzes = [
    {
      id: 1,
      title: 'Data Structures Basics',
      questions: 15,
      created: '2 days ago',
      type: 'Multiple Choice',
      shared: true,
    },
    {
      id: 2,
      title: 'Algorithm Complexity',
      questions: 10,
      created: '1 week ago',
      type: 'Mixed',
      shared: false,
    },
    {
      id: 3,
      title: 'Database Design Principles',
      questions: 20,
      created: '2 weeks ago',
      type: 'True/False',
      shared: true,
    },
  ];

  const addQuestion = () => {
    const newQuestion = {
      id: questions.length + 1,
      question: '',
      type: 'multiple-choice',
      options: ['', '', '', ''],
      correctAnswer: 0,
    };
    setQuestions([...questions, newQuestion]);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-foreground">Quiz Builder</h2>
          <p className="text-muted-foreground">
            Create custom quizzes from your notes and study materials
          </p>
        </div>
        <Button className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700">
          <Plus className="w-4 h-4 mr-2" />
          New Quiz
        </Button>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
        <button
          onClick={() => setActiveTab('create')}
          className={`px-4 py-2 rounded-md font-medium transition-colors ${
            activeTab === 'create'
              ? 'bg-white text-orange-600 shadow-sm'
              : 'text-muted-foreground hover:text-foreground'
          }`}
        >
          Create Quiz
        </button>
        <button
          onClick={() => setActiveTab('my-quizzes')}
          className={`px-4 py-2 rounded-md font-medium transition-colors ${
            activeTab === 'my-quizzes'
              ? 'bg-white text-orange-600 shadow-sm'
              : 'text-muted-foreground hover:text-foreground'
          }`}
        >
          My Quizzes
        </button>
        <button
          onClick={() => setActiveTab('auto-generate')}
          className={`px-4 py-2 rounded-md font-medium transition-colors ${
            activeTab === 'auto-generate'
              ? 'bg-white text-orange-600 shadow-sm'
              : 'text-muted-foreground hover:text-foreground'
          }`}
        >
          Auto Generate
        </button>
      </div>

      {activeTab === 'create' && (
        <div className="grid lg:grid-cols-2 gap-6">
          {/* Quiz Settings */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-foreground mb-4">Quiz Settings</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Quiz Title
                </label>
                <input
                  type="text"
                  placeholder="Enter quiz title..."
                  className="w-full p-3 border border-input rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Description
                </label>
                <textarea
                  placeholder="Brief description of the quiz..."
                  className="w-full p-3 border border-input rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Time Limit
                  </label>
                  <select className="w-full p-3 border border-input rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                    <option>No limit</option>
                    <option>10 minutes</option>
                    <option>20 minutes</option>
                    <option>30 minutes</option>
                    <option>1 hour</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Difficulty
                  </label>
                  <select className="w-full p-3 border border-input rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                    <option>Easy</option>
                    <option>Medium</option>
                    <option>Hard</option>
                    <option>Mixed</option>
                  </select>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input type="checkbox" id="randomize" className="rounded" />
                <label htmlFor="randomize" className="text-sm text-foreground">
                  Randomize question order
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <input type="checkbox" id="show-results" className="rounded" />
                <label htmlFor="show-results" className="text-sm text-foreground">
                  Show results immediately
                </label>
              </div>
            </div>
          </Card>

          {/* Question Builder */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-foreground">Questions</h3>
              <Button onClick={addQuestion} size="sm" variant="outline">
                <Plus className="w-4 h-4 mr-1" />
                Add Question
              </Button>
            </div>
            
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {questions.map((question, index) => (
                <div key={question.id} className="border border-input rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-foreground">
                      Question {index + 1}
                    </span>
                    <select className="text-xs border border-input rounded px-2 py-1">
                      <option>Multiple Choice</option>
                      <option>True/False</option>
                      <option>Short Answer</option>
                    </select>
                  </div>
                  
                  <input
                    type="text"
                    placeholder="Enter your question..."
                    value={question.question}
                    className="w-full p-2 border border-input rounded mb-2 text-sm"
                  />
                  
                  {question.type === 'multiple-choice' && (
                    <div className="space-y-1">
                      {question.options.map((option, optionIndex) => (
                        <div key={optionIndex} className="flex items-center space-x-2">
                          <input
                            type="radio"
                            name={`question-${question.id}`}
                            checked={question.correctAnswer === optionIndex}
                          />
                          <input
                            type="text"
                            placeholder={`Option ${optionIndex + 1}`}
                            value={option}
                            className="flex-1 p-1 border border-input rounded text-xs"
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>

            <div className="mt-4 flex space-x-2">
              <Button className="flex-1 bg-orange-600 hover:bg-orange-700">
                Save Quiz
              </Button>
              <Button variant="outline" className="flex-1">
                Preview
              </Button>
            </div>
          </Card>
        </div>
      )}

      {activeTab === 'my-quizzes' && (
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {existingQuizzes.map((quiz) => (
            <Card key={quiz.id} className="p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <User className="w-8 h-8 text-orange-600" />
                <span className={`text-xs px-2 py-1 rounded-full ${
                  quiz.shared 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {quiz.shared ? 'Shared' : 'Private'}
                </span>
              </div>
              
              <h3 className="font-semibold text-foreground mb-2">{quiz.title}</h3>
              
              <div className="space-y-1 mb-4">
                <p className="text-sm text-muted-foreground">
                  {quiz.questions} questions • {quiz.type}
                </p>
                <p className="text-sm text-muted-foreground">
                  Created {quiz.created}
                </p>
              </div>

              <div className="flex space-x-2">
                <Button size="sm" className="flex-1">
                  <Play className="w-3 h-3 mr-1" />
                  Take Quiz
                </Button>
                <Button variant="outline" size="sm">
                  <Share className="w-3 h-3" />
                </Button>
              </div>
            </Card>
          ))}
        </div>
      )}

      {activeTab === 'auto-generate' && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4">Auto-Generate Quiz</h3>
          
          <div className="space-y-4 max-w-2xl">
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Source Material
              </label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground mb-2">
                  Upload your notes, PDFs, or text files
                </p>
                <Button variant="outline">
                  Choose Files
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Number of Questions
                </label>
                <select className="w-full p-3 border border-input rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                  <option>5 questions</option>
                  <option>10 questions</option>
                  <option>15 questions</option>
                  <option>20 questions</option>
                  <option>25 questions</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Question Types
                </label>
                <select className="w-full p-3 border border-input rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                  <option>Multiple Choice only</option>
                  <option>True/False only</option>
                  <option>Mixed types</option>
                  <option>Short Answer only</option>
                </select>
              </div>
            </div>

            <Button className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700">
              Generate Quiz with AI
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
};
