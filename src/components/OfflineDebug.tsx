import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useOffline } from '@/hooks/useOffline';
import { offlineStorage, STORES } from '@/utils/offlineStorage';
import { toast } from '@/hooks/use-toast';

export const OfflineDebug: React.FC = () => {
  const { status, saveForOffline, getOfflineData } = useOffline();
  const [testData, setTestData] = useState({
    noteTitle: 'Test Note',
    noteContent: 'This is a test note content',
    timetableTitle: 'Test Class',
    groupName: 'Test Group'
  });
  const [debugInfo, setDebugInfo] = useState<any>({});

  const refreshDebugInfo = async () => {
    try {
      const [notes, timetable, groups, discussions, papers] = await Promise.all([
        offlineStorage.getAll(STORES.NOTES),
        offlineStorage.getAll(STORES.TIMETABLE),
        offlineStorage.getAll(STORES.GROUPS),
        offlineStorage.getAll(STORES.GROUP_DISCUSSIONS),
        offlineStorage.getAll(STORES.PAST_PAPERS)
      ]);

      setDebugInfo({
        notes: notes.length,
        timetable: timetable.length,
        groups: groups.length,
        discussions: discussions.length,
        papers: papers.length,
        notesData: notes,
        timetableData: timetable,
        groupsData: groups
      });
    } catch (error) {
      console.error('Error getting debug info:', error);
      setDebugInfo({ error: error.message });
    }
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      refreshDebugInfo();
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  const testSaveNote = async () => {
    try {
      const noteData = {
        title: testData.noteTitle,
        content: testData.noteContent,
        tags: ['test'],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      await saveForOffline('note', noteData);
      toast({
        title: "Test Note Saved",
        description: "Test note saved successfully",
      });
      
      setTimeout(refreshDebugInfo, 100);
    } catch (error) {
      console.error('Error saving test note:', error);
      toast({
        title: "Error",
        description: `Failed to save test note: ${error.message}`,
        variant: "destructive",
      });
    }
  };

  const testSaveTimetable = async () => {
    try {
      const timetableData = {
        title: testData.timetableTitle,
        description: 'Test timetable entry',
        start_time: '09:00',
        end_time: '10:00',
        day_of_week: 1,
        color: '#3B82F6',
        created_at: new Date().toISOString()
      };

      await saveForOffline('timetable', timetableData);
      toast({
        title: "Test Timetable Saved",
        description: "Test timetable entry saved successfully",
      });

      setTimeout(refreshDebugInfo, 100);
    } catch (error) {
      console.error('Error saving test timetable:', error);
      toast({
        title: "Error",
        description: `Failed to save test timetable: ${error.message}`,
        variant: "destructive",
      });
    }
  };

  const testSaveDiscussion = async () => {
    try {
      console.log('=== TESTING DISCUSSION SAVE ===');

      // Step 1: Check database status
      console.log('Step 1: Checking database...');
      const isReady = offlineStorage.isReady();
      console.log('Database ready:', isReady);

      if (!isReady) {
        console.log('Database not ready, initializing...');
        await offlineStorage.init();
        console.log('Database initialized');
      }

      // Step 2: Test simple data save
      console.log('Step 2: Testing simple save...');
      const testData = {
        id: 'test_discussion_' + Date.now(),
        group_id: 'sample_group_1',
        title: 'Simple Test Discussion',
        content: 'Testing basic functionality',
        author_id: 'test_user',
        author_name: 'Test User',
        replies_count: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        synced: false
      };

      console.log('Saving test data:', testData);

      // Direct IndexedDB save
      await offlineStorage.put('group_discussions', testData);
      console.log('Data saved successfully!');

      // Verify save
      const saved = await offlineStorage.get('group_discussions', testData.id);
      console.log('Retrieved data:', saved);

      toast({
        title: "Discussion Posted Successfully!",
        description: `Test discussion saved with ID: ${testData.id}`,
      });

      setTimeout(refreshDebugInfo, 100);
    } catch (error) {
      console.error('=== DISCUSSION SAVE FAILED ===');
      console.error('Error details:', error);
      console.error('Error message:', error?.message);
      console.error('Error stack:', error?.stack);

      toast({
        title: "Failed to create post",
        description: error?.message || "Unknown error - check console",
        variant: "destructive",
      });
    }
  };

  const testDatabaseReady = async () => {
    try {
      const isReady = offlineStorage.isReady();
      console.log('Database ready status:', isReady);

      // Check specific stores
      const stores = ['units', 'topics', 'notes', 'groups', 'group_discussions', 'timetable', 'past_papers'];
      const storeStatus = {};

      for (const store of stores) {
        try {
          const data = await offlineStorage.getAll(store);
          storeStatus[store] = `${data.length} items`;
          console.log(`Store ${store}:`, data.length, 'items');
        } catch (error) {
          storeStatus[store] = `Error: ${error.message}`;
          console.error(`Store ${store} error:`, error);
        }
      }

      console.log('Store status:', storeStatus);

      toast({
        title: "Database Status",
        description: `Ready: ${isReady}. Check console for store details.`,
      });
    } catch (error) {
      console.error('Database readiness test failed:', error);
      toast({
        title: "Database Error",
        description: error?.message || "Database not ready",
        variant: "destructive",
      });
    }
  };

  const clearAllData = async () => {
    try {
      await Promise.all([
        offlineStorage.clear(STORES.NOTES),
        offlineStorage.clear(STORES.TIMETABLE),
        offlineStorage.clear(STORES.GROUPS),
        offlineStorage.clear(STORES.GROUP_DISCUSSIONS),
        offlineStorage.clear(STORES.PAST_PAPERS)
      ]);
      
      toast({
        title: "Data Cleared",
        description: "All offline data has been cleared",
      });
      
      setTimeout(refreshDebugInfo, 100);
    } catch (error) {
      console.error('Error clearing data:', error);
      toast({
        title: "Error",
        description: `Failed to clear data: ${error.message}`,
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6 p-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Offline Debug Panel</h2>
        <p className="text-gray-600">Test and debug offline functionality</p>
      </div>

      {/* Status */}
      <Card>
        <CardHeader>
          <CardTitle>Connection Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Badge variant={status.isOnline ? "default" : "secondary"}>
                {status.isOnline ? "Online" : "Offline"}
              </Badge>
              <span>Connection Status</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">{status.pendingSyncCount}</Badge>
              <span>Pending Sync Items</span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={status.hasOfflineData ? "default" : "secondary"}>
                {status.hasOfflineData ? "Yes" : "No"}
              </Badge>
              <span>Has Offline Data</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Counts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Data Counts
            <Button onClick={refreshDebugInfo} size="sm">Refresh</Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {debugInfo.error ? (
            <div className="text-red-600">Error: {debugInfo.error}</div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{debugInfo.notes || 0}</div>
                <div className="text-sm text-gray-600">Notes</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{debugInfo.timetable || 0}</div>
                <div className="text-sm text-gray-600">Timetable</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{debugInfo.groups || 0}</div>
                <div className="text-sm text-gray-600">Groups</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{debugInfo.discussions || 0}</div>
                <div className="text-sm text-gray-600">Discussions</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{debugInfo.papers || 0}</div>
                <div className="text-sm text-gray-600">Papers</div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Test Actions</CardTitle>
          <CardDescription>Test saving different types of data</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="noteTitle">Note Title</Label>
              <Input
                id="noteTitle"
                value={testData.noteTitle}
                onChange={(e) => setTestData(prev => ({ ...prev, noteTitle: e.target.value }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="timetableTitle">Timetable Title</Label>
              <Input
                id="timetableTitle"
                value={testData.timetableTitle}
                onChange={(e) => setTestData(prev => ({ ...prev, timetableTitle: e.target.value }))}
              />
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            <Button onClick={testDatabaseReady}>Test Database</Button>
            <Button onClick={testSaveNote}>Save Test Note</Button>
            <Button onClick={testSaveTimetable}>Save Test Timetable</Button>
            <Button onClick={testSaveDiscussion}>Save Test Discussion</Button>
            <Button onClick={() => offlineStorage.reset().then(() => toast({ title: "Database Reset", description: "Database has been reset" }))}>Reset Database</Button>
            <Button onClick={clearAllData} variant="destructive">Clear All Data</Button>
          </div>
        </CardContent>
      </Card>

      {/* Raw Data */}
      {debugInfo.notesData && debugInfo.notesData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Sample Notes Data</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
              {JSON.stringify(debugInfo.notesData.slice(0, 2), null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default OfflineDebug;
