/* Preloader Animations */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.8), 0 0 60px rgba(147, 51, 234, 0.4);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) skewX(-15deg);
  }
  100% {
    transform: translateX(300%) skewX(-15deg);
  }
}

@keyframes rotate-border {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes bounce-in {
  0% {
    transform: scale(0) rotate(-180deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.1) rotate(-90deg);
    opacity: 0.8;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

@keyframes text-glow {
  0%, 100% {
    text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
  }
  50% {
    text-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(147, 51, 234, 0.6);
  }
}

@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-20px) translateX(10px);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-10px) translateX(-10px);
    opacity: 1;
  }
  75% {
    transform: translateY(-30px) translateX(5px);
    opacity: 0.5;
  }
}

/* Utility Classes */
.animate-gradient {
  animation: gradient 3s ease infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 1.5s ease-in-out infinite;
}

.animate-rotate-border {
  animation: rotate-border 3s linear infinite;
}

.animate-bounce-in {
  animation: bounce-in 1s ease-out;
}

.animate-text-glow {
  animation: text-glow 2s ease-in-out infinite;
}

.animate-particle-float {
  animation: particle-float 4s ease-in-out infinite;
}

/* Background Gradient */
.bg-300% {
  background-size: 300% 100%;
}

/* Glass Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Neon Glow */
.neon-glow {
  box-shadow: 
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor,
    0 0 20px currentColor;
}

/* Progress Bar Styles */
.progress-shimmer {
  position: relative;
  overflow: hidden;
}

.progress-shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .preloader-title {
    font-size: 2.5rem;
  }
  
  .preloader-logo {
    width: 80px;
    height: 80px;
  }
  
  .preloader-progress {
    width: 200px;
  }
}

@media (max-width: 480px) {
  .preloader-title {
    font-size: 2rem;
  }
  
  .preloader-logo {
    width: 60px;
    height: 60px;
  }
  
  .preloader-progress {
    width: 160px;
  }
}
