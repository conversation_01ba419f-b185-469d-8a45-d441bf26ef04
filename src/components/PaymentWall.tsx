import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Clock, Crown, Zap, CreditCard, CheckCircle, Loader2 } from 'lucide-react';
import { useSubscription } from '@/hooks/useSubscription';
import { useUser } from '@/hooks/useAuth';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

interface PaymentWallProps {
  onStartTrial: () => void;
}

export const PaymentWall: React.FC<PaymentWallProps> = ({ onStartTrial }) => {
  const { subscriptionStatus, isLoading, refetchStatus } = useSubscription();
  const { data: user } = useUser();
  const [paymentStep, setPaymentStep] = useState<'subscribe' | 'confirm' | 'processing'>('subscribe');

  // Debug logging
  console.log('🎯 PaymentWall render:', {
    isLoading,
    subscriptionStatus,
    userId: user?.id,
    paymentStep
  });

  // Check if user just returned from payment
  React.useEffect(() => {
    const checkReturnFromPayment = () => {
      const paymentUserId = localStorage.getItem('studyfam_payment_user');
      const paymentInitiated = localStorage.getItem('studyfam_payment_initiated');

      if (paymentUserId === user?.id && paymentInitiated) {
        const initiatedTime = parseInt(paymentInitiated);
        const now = Date.now();

        // If payment was initiated within last 30 minutes, show confirm button
        if (now - initiatedTime < 30 * 60 * 1000) {
          console.log('🔄 User returned from payment, showing confirm button');
          setPaymentStep('confirm');
          toast.info('Welcome back! Click "Confirm Payment" if you completed your purchase.');
        }
      }
    };

    if (user?.id) {
      checkReturnFromPayment();
    }
  }, [user?.id]);

  const handleSubscribeClick = () => {
    console.log('🚀 Subscribe button clicked');

    if (!user?.id) {
      console.log('❌ No user ID found');
      toast.error('Please log in to subscribe');
      return;
    }

    console.log('✅ User ID found:', user.id);

    // Store user info in localStorage for when they return
    localStorage.setItem('studyfam_payment_user', user.id);
    localStorage.setItem('studyfam_payment_initiated', Date.now().toString());

    // Direct redirect to WooCommerce product page
    const productUrl = `https://studyfam.co.ke/product/studyfam-subscripton/?ref=studyfam&user_id=${user.id}`;
    console.log('🛒 Redirecting to product URL:', productUrl);

    // Show confirmation message
    toast.success('Redirecting to payment page...');

    // Change button state
    setPaymentStep('confirm');

    // Redirect after short delay
    setTimeout(() => {
      window.location.href = productUrl;
    }, 1000);
  };

  const handleConfirmPayment = async () => {
    console.log('🎯 Confirming payment...');
    setPaymentStep('processing');

    try {
      // Activate subscription in database
      await activateSubscription();

      // Clear payment tracking from localStorage
      localStorage.removeItem('studyfam_payment_user');
      localStorage.removeItem('studyfam_payment_initiated');

      toast.success('Payment confirmed! Welcome to StudyFam Premium! 🎉');

      // Refresh subscription status to hide payment wall
      await refetchStatus();

    } catch (error) {
      console.error('❌ Error confirming payment:', error);
      toast.error('Failed to activate subscription. Please contact support.');
      setPaymentStep('confirm');
    }
  };

  const activateSubscription = async () => {
    if (!user?.id) return;

    try {
      // Get the active subscription plan
      const { data: plan } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .single();

      if (!plan) {
        throw new Error('No active subscription plan found');
      }

      // Calculate subscription period (1 month from now)
      const now = new Date();
      const periodEnd = new Date(now);
      periodEnd.setMonth(periodEnd.getMonth() + 1);

      // Create or update user subscription
      const { error: subscriptionError } = await supabase
        .from('user_subscriptions')
        .upsert({
          user_id: user.id,
          plan_id: plan.id,
          status: 'active',
          current_period_start: now.toISOString(),
          current_period_end: periodEnd.toISOString(),
          payment_provider: 'woocommerce',
          payment_reference: `manual_${Date.now()}`,
          last_payment_date: now.toISOString(),
          is_trial: false,
        }, {
          onConflict: 'user_id',
        });

      if (subscriptionError) {
        throw subscriptionError;
      }

      console.log('✅ Subscription activated successfully');

    } catch (error) {
      console.error('❌ Error activating subscription:', error);
      throw error;
    }
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <Card className="w-96 max-w-[90vw]">
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4">Checking subscription status...</p>
            {/* Add a skip button for development */}
            {process.env.NODE_ENV === 'development' && (
              <Button
                variant="outline"
                size="sm"
                className="mt-4"
                onClick={() => window.location.reload()}
              >
                Refresh Page
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-96 max-w-[90vw]">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-gradient-to-br from-primary to-purple-600 rounded-full flex items-center justify-center mb-4">
            <Crown className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl">Unlock Premium Features</CardTitle>
          <CardDescription>
            Access all StudyFam features with a premium subscription
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <Zap className="w-5 h-5 text-primary" />
              <span>Unlimited AI Notes & Tutoring</span>
            </div>
            <div className="flex items-center space-x-3">
              <Clock className="w-5 h-5 text-primary" />
              <span>Advanced Study Planning</span>
            </div>
            <div className="flex items-center space-x-3">
              <Crown className="w-5 h-5 text-primary" />
              <span>Premium Study Groups</span>
            </div>
          </div>

          <div className="pt-4 space-y-3">
            {!subscriptionStatus?.isTrial && (
              <Button 
                onClick={onStartTrial}
                variant="outline"
                className="w-full"
              >
                Start 1-Minute Free Trial
              </Button>
            )}
            
            {paymentStep === 'subscribe' && (
              <Button
                onClick={handleSubscribeClick}
                className="w-full bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90"
              >
                <CreditCard className="w-4 h-4 mr-2" />
                Subscribe Now
              </Button>
            )}

            {paymentStep === 'confirm' && (
              <div className="space-y-3">
                <Button
                  onClick={handleConfirmPayment}
                  className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Confirm Payment
                </Button>

                <Button
                  onClick={handleSubscribeClick}
                  variant="outline"
                  className="w-full"
                >
                  <CreditCard className="w-4 h-4 mr-2" />
                  Back to Payment Page
                </Button>
              </div>
            )}

            {paymentStep === 'processing' && (
              <Button
                disabled
                className="w-full bg-gradient-to-r from-blue-500 to-blue-600"
              >
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Processing...
              </Button>
            )}
          </div>

          {paymentStep === 'subscribe' && (
            <p className="text-xs text-muted-foreground text-center">
              Cancel anytime. Secure payment processing.
            </p>
          )}

          {paymentStep === 'confirm' && (
            <div className="text-xs text-muted-foreground text-center space-y-2">
              <p className="text-green-600 font-medium">
                ✓ Ready to confirm payment
              </p>
              <p>
                If you completed your payment on the StudyFam website, click "Confirm Payment" above.
                If you need to make the payment, click "Back to Payment Page".
              </p>
            </div>
          )}

          {paymentStep === 'processing' && (
            <p className="text-xs text-blue-600 text-center">
              Activating your subscription...
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};