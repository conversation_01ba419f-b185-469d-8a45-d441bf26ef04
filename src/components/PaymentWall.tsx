import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Clock, Crown, Zap, CreditCard } from 'lucide-react';
import { useSubscription } from '@/hooks/useSubscription';
import { useUser } from '@/hooks/useAuth';
import { toast } from 'sonner';
import { CheckoutModal } from '@/components/subscription/CheckoutModal';
import { woocommerceService } from '@/services/woocommerceService';

interface PaymentWallProps {
  onStartTrial: () => void;
}

export const PaymentWall: React.FC<PaymentWallProps> = ({ onStartTrial }) => {
  const { subscriptionStatus, isLoading, refetchStatus } = useSubscription();
  const { data: user } = useUser();
  const [isCheckoutOpen, setIsCheckoutOpen] = useState(false);
  const [isLoadingProduct, setIsLoadingProduct] = useState(false);

  // Debug logging
  console.log('🎯 PaymentWall render:', {
    isLoading,
    subscriptionStatus,
    userId: user?.id,
    isCheckoutOpen
  });

  const handleDirectSubscribe = async () => {
    if (!user?.id) {
      toast.error('Please log in to subscribe');
      return;
    }

    setIsLoadingProduct(true);
    try {
      // Skip product validation for now and directly open checkout
      // The checkout modal will handle loading the product page
      toast.success('Opening checkout...');
      setIsCheckoutOpen(true);
    } catch (error) {
      console.error('Failed to open checkout:', error);
      toast.error('Failed to open checkout. Please try again.');
    } finally {
      setIsLoadingProduct(false);
    }
  };

  const handlePaymentSuccess = async (orderData: any) => {
    try {
      // Refresh subscription status
      await refetchStatus();

      toast.success('Welcome to StudyFam Premium! 🎉');
      setIsCheckoutOpen(false);
    } catch (error) {
      console.error('Error updating subscription status:', error);
      toast.error('Payment successful, but failed to update subscription status. Please refresh the page.');
    }
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <Card className="w-96 max-w-[90vw]">
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4">Checking subscription status...</p>
            {/* Add a skip button for development */}
            {process.env.NODE_ENV === 'development' && (
              <Button
                variant="outline"
                size="sm"
                className="mt-4"
                onClick={() => window.location.reload()}
              >
                Refresh Page
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-96 max-w-[90vw]">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-gradient-to-br from-primary to-purple-600 rounded-full flex items-center justify-center mb-4">
            <Crown className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl">Unlock Premium Features</CardTitle>
          <CardDescription>
            Access all StudyFam features with a premium subscription
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <Zap className="w-5 h-5 text-primary" />
              <span>Unlimited AI Notes & Tutoring</span>
            </div>
            <div className="flex items-center space-x-3">
              <Clock className="w-5 h-5 text-primary" />
              <span>Advanced Study Planning</span>
            </div>
            <div className="flex items-center space-x-3">
              <Crown className="w-5 h-5 text-primary" />
              <span>Premium Study Groups</span>
            </div>
          </div>

          <div className="pt-4 space-y-3">
            {!subscriptionStatus?.is_trial && (
              <Button 
                onClick={onStartTrial}
                variant="outline"
                className="w-full"
              >
                Start 1-Minute Free Trial
              </Button>
            )}
            
            <Button
              onClick={handleDirectSubscribe}
              disabled={isLoadingProduct}
              className="w-full bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90"
            >
              {isLoadingProduct ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Loading...
                </>
              ) : (
                <>
                  <CreditCard className="w-4 h-4 mr-2" />
                  Subscribe Now
                </>
              )}
            </Button>
          </div>

          <p className="text-xs text-muted-foreground text-center">
            Cancel anytime. Secure payment processing.
          </p>
        </CardContent>
      </Card>

      {/* Checkout Modal */}
      <CheckoutModal
        isOpen={isCheckoutOpen}
        onClose={() => setIsCheckoutOpen(false)}
        onPaymentSuccess={handlePaymentSuccess}
        planName="StudyFam Premium Subscription"
      />
    </div>
  );
};