import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Clock, Crown, Zap } from 'lucide-react';
import { useSubscription } from '@/hooks/useSubscription';
import { paystackService } from '@/services/paystackService';
import { useUser } from '@/hooks/useAuth';
import { toast } from 'sonner';

interface PaymentWallProps {
  onStartTrial: () => void;
}

export const PaymentWall: React.FC<PaymentWallProps> = ({ onStartTrial }) => {
  const { subscriptionStatus, isLoading } = useSubscription();
  const { data: user } = useUser();

  const handleDirectSubscribe = async () => {
    if (!user?.id) return;
    
    try {
      // For now, navigate to subscription page until Paystack functions are ready
      const amount = 1000; // $0.10 = 1000 kobo for NGN
      toast.success('Redirecting to payment...');
      // TODO: Implement actual Paystack payment once functions are deployed
      console.log('Payment creation requested for user:', user.id, 'amount:', amount);
      // window.location.href = paymentUrl;
    } catch (error) {
      console.error('Payment creation failed:', error);
      toast.error('Failed to create payment. Please try again.');
    }
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <Card className="w-96 max-w-[90vw]">
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4">Checking subscription status...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-96 max-w-[90vw]">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-gradient-to-br from-primary to-purple-600 rounded-full flex items-center justify-center mb-4">
            <Crown className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl">Unlock Premium Features</CardTitle>
          <CardDescription>
            Access all StudyFam features with a premium subscription
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <Zap className="w-5 h-5 text-primary" />
              <span>Unlimited AI Notes & Tutoring</span>
            </div>
            <div className="flex items-center space-x-3">
              <Clock className="w-5 h-5 text-primary" />
              <span>Advanced Study Planning</span>
            </div>
            <div className="flex items-center space-x-3">
              <Crown className="w-5 h-5 text-primary" />
              <span>Premium Study Groups</span>
            </div>
          </div>

          <div className="pt-4 space-y-3">
            {!subscriptionStatus?.is_trial && (
              <Button 
                onClick={onStartTrial}
                variant="outline"
                className="w-full"
              >
                Start 1-Minute Free Trial
              </Button>
            )}
            
            <Button 
              onClick={handleDirectSubscribe}
              className="w-full bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90"
            >
              Subscribe for $0.10/day
            </Button>
          </div>

          <p className="text-xs text-muted-foreground text-center">
            Cancel anytime. Secure payment with Paystack.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};