import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Clock, Crown, Zap, CreditCard, CheckCircle, Loader2, RefreshCw, ExternalLink } from 'lucide-react';
import { useSubscription } from '@/hooks/useSubscription';
import { useUser } from '@/hooks/useAuth';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

interface PaymentWallProps {
  onStartTrial: () => void;
}

export const PaymentWall: React.FC<PaymentWallProps> = ({ onStartTrial }) => {
  const { subscriptionStatus, isLoading, refetchStatus } = useSubscription();
  const { data: user } = useUser();
  const [paymentStep, setPaymentStep] = useState<'subscribe' | 'creating' | 'checkout' | 'processing' | 'success'>('subscribe');
  const [currentOrder, setCurrentOrder] = useState<any>(null);
  const [checkingPayment, setCheckingPayment] = useState(false);

  // Debug logging
  console.log('🎯 PaymentWall render:', {
    isLoading,
    subscriptionStatus,
    userId: user?.id,
    paymentStep
  });

  // Check if user has pending payment or just returned, and auto-start trial
  useEffect(() => {
    const checkPendingPayment = async () => {
      if (!user?.id) return;

      const paymentUser = localStorage.getItem('studyfam_payment_user');
      const trackingId = localStorage.getItem('studyfam_tracking_id');
      const urlParams = new URLSearchParams(window.location.search);
      const returnFromPayment = urlParams.has('return') || urlParams.has('order-received') || urlParams.has('key');

      if (paymentUser === user.id && trackingId) {
        setCurrentOrder({
          id: trackingId,
          tracking_id: trackingId,
          user_id: user.id
        });
        setPaymentStep('checkout');

        if (returnFromPayment) {
          toast.info('Welcome back! Click "Activate Subscription" to complete your premium upgrade.');

          // Clean up URL parameters
          const url = new URL(window.location.href);
          url.search = '';
          window.history.replaceState({}, document.title, url.toString());
        } else {
          toast.info('Complete your payment or activate your subscription below.');
        }
      } else {
        // Auto-start trial if user doesn't have one and hasn't started payment
        if (!subscriptionStatus?.isTrial && onStartTrial) {
          console.log('🎯 Auto-starting free trial for user:', user.id);
          onStartTrial();
          toast.success('🎉 Free trial started! You have 1 minute to explore premium features.');
        }
      }
    };

    checkPendingPayment();
  }, [user?.id, subscriptionStatus?.isTrial, onStartTrial]);

  const handleCreateOrder = async () => {
    if (!user?.id || !user?.email) {
      toast.error('Please log in to subscribe');
      return;
    }

    setPaymentStep('creating');

    try {
      // Store user info for tracking
      localStorage.setItem('studyfam_payment_user', user.id);
      localStorage.setItem('studyfam_payment_email', user.email);
      localStorage.setItem('studyfam_payment_time', Date.now().toString());

      // Create a simple tracking ID
      const trackingId = `sf_${Date.now()}_${user.id.slice(-8)}`;
      localStorage.setItem('studyfam_tracking_id', trackingId);

      // Get product URL with tracking parameters and return URL
      const returnUrl = encodeURIComponent(`${window.location.origin}/`);
      const productUrl = `https://studyfam.co.ke/product/studyfam-subscripton/?user_id=${user.id}&tracking_id=${trackingId}&email=${encodeURIComponent(user.email)}&return_url=${returnUrl}`;

      console.log('🛒 Redirecting to product page:', productUrl);

      toast.success('Redirecting to payment page...');
      setPaymentStep('checkout');

      // Set current order info for tracking
      setCurrentOrder({
        id: trackingId,
        tracking_id: trackingId,
        user_id: user.id
      });

      // Redirect to WooCommerce product page
      setTimeout(() => {
        window.location.href = productUrl;
      }, 1500);

    } catch (error) {
      console.error('❌ Error preparing checkout:', error);
      toast.error('Failed to prepare checkout. Please try again.');
      setPaymentStep('subscribe');
    }
  };

  const handleCheckPaymentStatus = async () => {
    if (!user?.id) return;

    setCheckingPayment(true);

    try {
      console.log('🔍 Checking payment status for user:', user.id);

      // Check if user now has an active subscription
      const { data: subscription, error: subscriptionError } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      if (subscriptionError && subscriptionError.code !== 'PGRST116') {
        // PGRST116 is "no rows returned" which is expected if no subscription
        console.error('❌ Error checking subscription:', subscriptionError);
        throw subscriptionError;
      }

      console.log('📊 Subscription check result:', subscription);

      if (subscription) {
        toast.success('Payment confirmed! Welcome to StudyFam Premium! 🎉');
        setPaymentStep('processing');

        // Clear tracking data
        localStorage.removeItem('studyfam_payment_user');
        localStorage.removeItem('studyfam_payment_email');
        localStorage.removeItem('studyfam_payment_time');
        localStorage.removeItem('studyfam_tracking_id');

        // Refresh subscription status to hide payment wall
        await refetchStatus();

      } else {
        console.log('ℹ️ No active subscription found');
        toast.info('Payment not yet confirmed. Please complete your payment first, then try again.');
      }
    } catch (error) {
      console.error('❌ Error checking subscription status:', error);
      toast.error(`Failed to check payment status: ${error.message}`);
    } finally {
      setCheckingPayment(false);
    }
  };

  const handleManualActivation = async () => {
    if (!user?.id) return;

    setPaymentStep('processing');

    try {
      console.log('🔄 Starting manual activation for user:', user.id);

      // Get the active subscription plan
      const { data: plan, error: planError } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .single();

      if (planError) {
        console.error('❌ Error fetching plan:', planError);
        throw new Error(`Failed to fetch subscription plan: ${planError.message}`);
      }

      if (!plan) {
        throw new Error('No active subscription plan found');
      }

      console.log('✅ Found active plan:', plan);

      // Calculate subscription period based on plan interval
      const now = new Date();
      const periodEnd = new Date(now);

      if (plan.interval_type === 'daily') {
        periodEnd.setDate(periodEnd.getDate() + (plan.interval_count || 1));
      } else if (plan.interval_type === 'monthly') {
        periodEnd.setMonth(periodEnd.getMonth() + (plan.interval_count || 1));
      } else if (plan.interval_type === 'yearly') {
        periodEnd.setFullYear(periodEnd.getFullYear() + (plan.interval_count || 1));
      } else {
        // Default to 1 month
        periodEnd.setMonth(periodEnd.getMonth() + 1);
      }

      console.log('📅 Subscription period:', now.toISOString(), 'to', periodEnd.toISOString());

      // Create or update user subscription
      const subscriptionData = {
        user_id: user.id,
        plan_id: plan.id,
        status: 'active',
        current_period_start: now.toISOString(),
        current_period_end: periodEnd.toISOString(),
        payment_provider: 'woocommerce',
        payment_reference: localStorage.getItem('studyfam_tracking_id') || `manual_${Date.now()}`,
        last_payment_date: now.toISOString(),
        is_trial: false,
      };

      console.log('💾 Creating subscription with data:', subscriptionData);

      // Now we can use upsert with the unique constraint
      const { error: subscriptionError, data: subscriptionResult } = await supabase
        .from('user_subscriptions')
        .upsert(subscriptionData, {
          onConflict: 'user_id',
        })
        .select();

      if (subscriptionError) {
        console.error('❌ Subscription error:', subscriptionError);
        throw new Error(`Failed to create subscription: ${subscriptionError.message}`);
      }

      console.log('✅ Subscription created/updated:', subscriptionResult);

      // Clear tracking data
      localStorage.removeItem('studyfam_payment_user');
      localStorage.removeItem('studyfam_payment_email');
      localStorage.removeItem('studyfam_payment_time');
      localStorage.removeItem('studyfam_tracking_id');

      toast.success('Subscription activated successfully! 🎉');

      // Refresh subscription status to hide payment wall
      await refetchStatus();

    } catch (error) {
      console.error('❌ Error activating subscription:', error);
      toast.error(`Failed to activate subscription: ${error.message}`);
      setPaymentStep('checkout');
    }
  };

  // Removed old activateSubscription function - now using handleManualActivation

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <Card className="w-96 max-w-[90vw]">
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4">Checking subscription status...</p>
            {/* Add a skip button for development */}
            {process.env.NODE_ENV === 'development' && (
              <Button
                variant="outline"
                size="sm"
                className="mt-4"
                onClick={() => window.location.reload()}
              >
                Refresh Page
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 overflow-y-auto">
      <div className="w-full max-w-md my-8">
        <Card className="w-full bg-white shadow-2xl">
          <CardHeader className="text-center pb-4">
            <div className="mx-auto w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-primary to-purple-600 rounded-full flex items-center justify-center mb-4">
              <Crown className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
            </div>
            <CardTitle className="text-xl sm:text-2xl font-bold">Unlock Premium Features</CardTitle>
            <CardDescription className="text-sm sm:text-base">
              Access all StudyFam features with a premium subscription
            </CardDescription>
          </CardHeader>
        <CardContent className="space-y-4 px-4 sm:px-6">
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <Zap className="w-4 h-4 sm:w-5 sm:h-5 text-primary flex-shrink-0" />
              <span className="text-sm sm:text-base">Unlimited AI Notes & Tutoring</span>
            </div>
            <div className="flex items-center space-x-3">
              <Clock className="w-4 h-4 sm:w-5 sm:h-5 text-primary flex-shrink-0" />
              <span className="text-sm sm:text-base">Advanced Study Planning</span>
            </div>
            <div className="flex items-center space-x-3">
              <Crown className="w-4 h-4 sm:w-5 sm:h-5 text-primary flex-shrink-0" />
              <span className="text-sm sm:text-base">Premium Study Groups</span>
            </div>
          </div>

          <div className="pt-4 space-y-3">
            {paymentStep === 'subscribe' && (
              <Button
                onClick={handleCreateOrder}
                className="w-full bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90"
              >
                <CreditCard className="w-4 h-4 mr-2" />
                Subscribe Now
              </Button>
            )}

            {paymentStep === 'creating' && (
              <Button
                disabled
                className="w-full bg-gradient-to-r from-blue-500 to-blue-600"
              >
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Preparing Payment...
              </Button>
            )}

            {paymentStep === 'checkout' && (
              <div className="space-y-3">
                <Button
                  onClick={handleManualActivation}
                  className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Activate Subscription
                </Button>

                <Button
                  onClick={() => {
                    const productUrl = `https://studyfam.co.ke/product/studyfam-subscripton/?user_id=${user?.id}`;
                    window.open(productUrl, '_blank');
                  }}
                  variant="outline"
                  className="w-full"
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Complete Payment
                </Button>
              </div>
            )}

            {paymentStep === 'processing' && (
              <Button
                disabled
                className="w-full bg-gradient-to-r from-blue-500 to-blue-600"
              >
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Activating Subscription...
              </Button>
            )}

            {paymentStep === 'success' && (
              <Button
                disabled
                className="w-full bg-gradient-to-r from-green-500 to-green-600"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                Welcome to Premium!
              </Button>
            )}
          </div>

          {paymentStep === 'subscribe' && (
            <p className="text-xs text-muted-foreground text-center">
              Cancel anytime. Secure payment processing.
            </p>
          )}

          {paymentStep === 'creating' && (
            <p className="text-xs text-blue-600 text-center">
              Setting up your payment...
            </p>
          )}

          {paymentStep === 'checkout' && (
            <div className="text-xs text-muted-foreground text-center space-y-2">
              <p className="text-green-600 font-medium">
                ✓ Payment page ready
              </p>
              <p>
                Complete your payment on the StudyFam website, then click "Activate Subscription" to confirm.
              </p>
            </div>
          )}

          {paymentStep === 'processing' && (
            <p className="text-xs text-blue-600 text-center">
              Activating your subscription...
            </p>
          )}

          {paymentStep === 'success' && (
            <p className="text-xs text-green-600 text-center">
              🎉 Welcome to StudyFam Premium!
            </p>
          )}
        </CardContent>
      </Card>
      </div>
    </div>
  );
};