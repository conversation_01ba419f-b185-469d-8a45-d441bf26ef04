import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Checkbox } from '@/components/ui/checkbox';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Users,
  UserPlus,
  Send,
  Loader2,
  FileText,
  BookOpen,
  File,
  Download
} from 'lucide-react';
import { useFriends } from '@/hooks/useFriends';
import { useMyStudyGroups } from '@/hooks/useStudyGroups';
import { useShareItem, ShareData } from '@/hooks/useSharing';

interface ShareModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  item: {
    id: string;
    title: string;
    type: 'note' | 'past_paper';
    content?: string;
    file_url?: string;
  };
}

export const ShareModal: React.FC<ShareModalProps> = ({
  open,
  onOpenChange,
  item,
}) => {
  const [selectedFriends, setSelectedFriends] = useState<string[]>([]);
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<'friends' | 'groups'>('friends');

  // Hooks
  const { data: friends = [], isLoading: friendsLoading } = useFriends();
  const { data: studyGroups = [], isLoading: groupsLoading } = useMyStudyGroups();
  const shareItemMutation = useShareItem();





  const handleShare = async () => {
    if (selectedFriends.length === 0 && selectedGroups.length === 0) {
      return;
    }

    const shareData: ShareData = {
      type: item.type,
      item_id: item.id,
      title: item.title,
      content: item.content,
      file_url: item.file_url,
      recipients: {
        friends: selectedFriends.length > 0 ? selectedFriends : undefined,
        study_groups: selectedGroups.length > 0 ? selectedGroups : undefined,
      },
    };

    try {
      await shareItemMutation.mutateAsync(shareData);
      onOpenChange(false);
      // Reset form
      setSelectedFriends([]);
      setSelectedGroups([]);
    } catch (error) {
      // Error handled by mutation
    }
  };

  const totalSelected = selectedFriends.length + selectedGroups.length;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-lg">
            {item.type === 'note' ? <FileText className="w-5 h-5" /> : <BookOpen className="w-5 h-5" />}
            Share File
          </DialogTitle>
          <DialogDescription>
            Share "{item.title}" with your friends and study groups
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* File Info */}
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                {item.file_url ? (
                  <File className="w-5 h-5 text-blue-600" />
                ) : item.type === 'note' ? (
                  <FileText className="w-5 h-5 text-blue-600" />
                ) : (
                  <BookOpen className="w-5 h-5 text-blue-600" />
                )}
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-gray-900 truncate">{item.title}</h4>
                <p className="text-sm text-gray-600">
                  {item.file_url ? (
                    <>
                      <File className="w-3 h-3 inline mr-1" />
                      Document file - will be shared as-is
                    </>
                  ) : (
                    <>
                      <FileText className="w-3 h-3 inline mr-1" />
                      Text content - will be converted to PDF
                    </>
                  )}
                </p>
              </div>
            </div>
          </div>

          {/* Recipients Selection Tabs */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium text-gray-900">Select Recipients</h3>
              {totalSelected > 0 && (
                <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                  {totalSelected} selected
                </Badge>
              )}
            </div>

            <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'friends' | 'groups')}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="friends" className="flex items-center gap-2">
                  <UserPlus className="w-4 h-4" />
                  <span>Friends</span>
                  {friends.length > 0 && (
                    <Badge variant="outline" className="ml-1 text-xs">
                      {friends.length}
                    </Badge>
                  )}
                  {selectedFriends.length > 0 && (
                    <Badge className="ml-1 text-xs bg-green-100 text-green-700">
                      {selectedFriends.length}
                    </Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger value="groups" className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  <span>Groups</span>
                  {studyGroups.length > 0 && (
                    <Badge variant="outline" className="ml-1 text-xs">
                      {studyGroups.length}
                    </Badge>
                  )}
                  {selectedGroups.length > 0 && (
                    <Badge className="ml-1 text-xs bg-green-100 text-green-700">
                      {selectedGroups.length}
                    </Badge>
                  )}
                </TabsTrigger>
              </TabsList>

              <TabsContent value="friends" className="mt-4">
                {friendsLoading ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="w-5 h-5 animate-spin text-gray-400" />
                  </div>
                ) : friends.length === 0 ? (
                  <div className="text-center py-8">
                    <UserPlus className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                    <p className="text-gray-500 text-sm">No friends available</p>
                    <p className="text-gray-400 text-xs mt-1">Add friends to share content with them</p>
                  </div>
                ) : (
                  <div className="max-h-48 overflow-y-auto space-y-2">
                    {friends.map((friend) => (
                      <label
                        key={friend.id}
                        className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer border border-transparent hover:border-gray-200 transition-all"
                      >
                        <Checkbox
                          checked={selectedFriends.includes(friend.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedFriends([...selectedFriends, friend.id]);
                            } else {
                              setSelectedFriends(selectedFriends.filter(id => id !== friend.id));
                            }
                          }}
                        />
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={friend.avatar_url} />
                          <AvatarFallback className="text-xs">
                            {friend.full_name.split(' ').map(n => n[0]).join('').toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <span className="text-sm font-medium block truncate">{friend.full_name}</span>
                          {friend.course && (
                            <span className="text-xs text-gray-500 block truncate">{friend.course}</span>
                          )}
                          {friend.country && (
                            <span className="text-xs text-gray-400 block truncate">{friend.country}</span>
                          )}
                        </div>
                      </label>
                    ))}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="groups" className="mt-4">
                {groupsLoading ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="w-5 h-5 animate-spin text-gray-400" />
                  </div>
                ) : studyGroups.length === 0 ? (
                  <div className="text-center py-8">
                    <Users className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                    <p className="text-gray-500 text-sm">No study groups available</p>
                    <p className="text-gray-400 text-xs mt-1">Join or create study groups to share content</p>
                  </div>
                ) : (
                  <div className="max-h-48 overflow-y-auto space-y-2">
                    {studyGroups.map((group) => (
                      <label
                        key={group.id}
                        className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer border border-transparent hover:border-gray-200 transition-all"
                      >
                        <Checkbox
                          checked={selectedGroups.includes(group.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedGroups([...selectedGroups, group.id]);
                            } else {
                              setSelectedGroups(selectedGroups.filter(id => id !== group.id));
                            }
                          }}
                        />
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={group.avatar_url} />
                          <AvatarFallback className="text-xs bg-blue-100 text-blue-600">
                            {group.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <span className="text-sm font-medium block truncate">{group.name}</span>
                          <span className="text-xs text-gray-500">{group.member_count} members</span>
                        </div>
                      </label>
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </div>
        </div>

        {/* Footer */}
        <div className="flex gap-3 pt-4 border-t">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="flex-1"
          >
            Cancel
          </Button>
          <Button
            onClick={handleShare}
            disabled={(selectedFriends.length === 0 && selectedGroups.length === 0) || shareItemMutation.isPending}
            className="flex-1"
          >
            {shareItemMutation.isPending ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Sharing...
              </>
            ) : (
              <>
                <Send className="w-4 h-4 mr-2" />
                Share
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
