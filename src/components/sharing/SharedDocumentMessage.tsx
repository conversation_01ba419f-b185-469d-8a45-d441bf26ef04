
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  FileText,
  BookOpen,
  Download,
  Save,
  Eye,
  Loader2,
  FolderPlus
} from 'lucide-react';
import FolderSelector from '@/components/notes/FolderSelector';
import { DocumentViewer } from './DocumentViewer';
import { useCreateNote } from '@/hooks/useNotes';
import { toast } from 'sonner';

interface SharedDocumentMessageProps {
  message: {
    id: string;
    content: string;
    file_url?: string;
    file_name?: string;
    message_type: string;
    created_at: string;
    sender: {
      id: string;
      full_name: string;
      avatar_url?: string;
    };
  };
}

export const SharedDocumentMessage: React.FC<SharedDocumentMessageProps> = ({ message }) => {
  const [showFolderSelector, setShowFolderSelector] = useState(false);
  const [showDocumentViewer, setShowDocumentViewer] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const createNoteMutation = useCreateNote();

  // Check if this is a file message (shared document)
  const isSharedDocument = message.message_type === 'file' && message.file_url;

  if (!isSharedDocument) {
    return null; // This component only handles file messages
  }

  // Extract title from message content or file name
  const extractTitle = () => {
    // Try to extract from content first (for shared items)
    const sharedMatch = message.content.match(/📎 Shared (?:note|past_paper|document|ai_notes): (.+)/);
    if (sharedMatch) {
      return sharedMatch[1];
    }

    // Fallback to file name without extension or default
    if (message.file_name) {
      const nameWithoutExt = message.file_name.replace(/\.[^/.]+$/, '');
      return nameWithoutExt;
    }

    return 'Shared Document';
  };

  const title = extractTitle();

  // Get document type and file extension
  const getDocumentInfo = () => {
    // Check if it's a shared document type from content
    if (message.content.includes('📎 Shared note:')) {
      return { type: 'Note', extension: 'PDF' };
    }
    if (message.content.includes('📎 Shared past_paper:')) {
      return { type: 'Past Paper', extension: 'PDF' };
    }
    if (message.content.includes('📎 Shared ai_notes:')) {
      return { type: 'AI Notes', extension: 'PDF' };
    }

    // Fallback to file extension
    if (message.file_name) {
      const extension = message.file_name.split('.').pop()?.toUpperCase();
      return { type: 'Document', extension: extension || 'FILE' };
    }

    return { type: 'Document', extension: 'FILE' };
  };

  const documentInfo = getDocumentInfo();

  const handleView = () => {
    if (message.file_url) {
      setShowDocumentViewer(true);
    }
  };

  const handleDownload = async () => {
    if (!message.file_url) return;

    setIsDownloading(true);
    try {
      const link = document.createElement('a');
      link.href = message.file_url;
      link.download = message.file_name || title;
      link.target = '_blank';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast.success('Download started!');
    } catch (error) {
      toast.error('Failed to download file');
    } finally {
      setIsDownloading(false);
    }
  };

  const handleSaveToNotes = async (unitId: string, topicId: string) => {
    try {
      const noteData = {
        title: title,
        content: `Shared by ${message.sender.full_name} on ${new Date(message.created_at).toLocaleDateString()}`,
        unit_id: unitId,
        topic_id: topicId,
        file_url: message.file_url,
        tags: ['shared', 'document', 'imported']
      };

      await createNoteMutation.mutateAsync(noteData);
      setShowFolderSelector(false);
      toast.success('Document saved to Sort Notes!');
    } catch (error) {
      toast.error('Failed to save document');
    }
  };

  return (
    <>
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 my-2">
        {/* Header */}
        <div className="flex items-start gap-3 mb-3">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <FileText className="w-5 h-5 text-blue-600" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-medium text-gray-900 truncate">{title}</h4>
              <Badge variant="secondary" className="text-xs">
                {documentInfo.type}
              </Badge>
            </div>
            <p className="text-sm text-gray-600">
              Shared by {message.sender.full_name}
            </p>
          </div>
        </div>

        {/* Message content (if there's a personal message) */}
        {(() => {
          const parts = message.content.split('\n\n');
          const personalMessage = parts[0];
          const sharedInfo = parts.find(part => part.includes('📎 Shared'));

          // Show personal message if it's different from the shared info
          if (personalMessage && !personalMessage.includes('📎 Shared') && personalMessage !== sharedInfo) {
            return (
              <div className="bg-white rounded-md p-3 mb-3 border border-blue-100">
                <p className="text-sm text-gray-700 italic">
                  "{personalMessage}"
                </p>
              </div>
            );
          }
          return null;
        })()}

        {/* File info */}
        {message.file_url && (
          <div className="bg-white rounded-md p-3 mb-3 border border-blue-100">
            <div className="flex items-center gap-2">
              <FileText className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">
                {message.file_name || title}
              </span>
              <Badge variant="outline" className="text-xs">
                {documentInfo.extension}
              </Badge>
            </div>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex flex-wrap gap-2">
          {message.file_url && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={handleView}
                className="flex-1 min-w-0 text-black hover:text-black border-gray-300 hover:bg-gray-50"
              >
                <Eye className="w-4 h-4 mr-2 text-black" />
                View
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload}
                disabled={isDownloading}
                className="flex-1 min-w-0 text-black hover:text-black border-gray-300 hover:bg-gray-50"
              >
                {isDownloading ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin text-black" />
                ) : (
                  <Download className="w-4 h-4 mr-2 text-black" />
                )}
                Download
              </Button>
            </>
          )}
          <Button
            size="sm"
            onClick={() => setShowFolderSelector(true)}
            disabled={createNoteMutation.isPending}
            className="flex-1 min-w-0 bg-blue-600 hover:bg-blue-700"
          >
            {createNoteMutation.isPending ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <FolderPlus className="w-4 h-4 mr-2" />
            )}
            Save
          </Button>
        </div>
      </div>

      {/* Folder Selector Modal */}
      <FolderSelector
        open={showFolderSelector}
        onOpenChange={setShowFolderSelector}
        onFolderSelected={handleSaveToNotes}
      />

      {/* Document Viewer Modal */}
      {message.file_url && (
        <DocumentViewer
          open={showDocumentViewer}
          onOpenChange={setShowDocumentViewer}
          document={{
            url: message.file_url,
            name: message.file_name || title,
            title: title,
            sharedBy: message.sender.full_name,
            sharedAt: message.created_at,
          }}
        />
      )}
    </>
  );
};
