
import React from 'react';
import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Download, 
  ExternalLink, 
  FileText, 
  Image as ImageIcon,
  File,
  X,
  Save
} from 'lucide-react';

interface DocumentViewerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  document: {
    url: string;
    name: string;
    title: string;
    sharedBy?: string;
    sharedAt?: string;
  };
  onSaveToNotes?: () => void;
}

export const DocumentViewer: React.FC<DocumentViewerProps> = ({
  open,
  onOpenChange,
  document,
  onSaveToNotes,
}) => {
  const getFileExtension = () => {
    const extension = document.name?.split('.').pop()?.toLowerCase();
    return extension || 'file';
  };

  const getFileIcon = () => {
    const extension = getFileExtension();
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    
    if (extension === 'pdf') return <FileText className="w-5 h-5" />;
    if (imageTypes.includes(extension)) return <ImageIcon className="w-5 h-5" />;
    return <File className="w-5 h-5" />;
  };

  const isViewableInline = () => {
    const extension = getFileExtension();
    const viewableTypes = ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    return viewableTypes.includes(extension);
  };

  const handleDownload = async () => {
    try {
      const response = await fetch(document.url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = window.document.createElement('a');
      link.href = url;
      link.download = document.name;
      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  const handleOpenExternal = () => {
    window.open(document.url, '_blank', 'noopener,noreferrer');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0">
        <DialogHeader className="p-6 pb-4 border-b">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                {getFileIcon()}
              </div>
              <div>
                <DialogTitle className="text-lg font-semibold mb-1">
                  {document.title}
                </DialogTitle>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <span>{document.name}</span>
                  <Badge variant="outline" className="text-xs">
                    {getFileExtension().toUpperCase()}
                  </Badge>
                </div>
                {document.sharedBy && (
                  <p className="text-sm text-gray-500 mt-1">
                    Shared by {document.sharedBy}
                    {document.sharedAt && ` on ${new Date(document.sharedAt).toLocaleDateString()}`}
                  </p>
                )}
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="flex-1 p-6">
          {isViewableInline() ? (
            <div className="w-full h-[60vh] border rounded-lg overflow-hidden">
              {getFileExtension() === 'pdf' ? (
                <iframe
                  src={document.url}
                  className="w-full h-full"
                  title={document.title}
                />
              ) : (
                <img
                  src={document.url}
                  alt={document.title}
                  className="w-full h-full object-contain bg-gray-50"
                />
              )}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                {getFileIcon()}
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Preview not available
              </h3>
              <p className="text-gray-600 mb-4">
                This file type cannot be previewed inline. You can download it or open it in a new tab.
              </p>
            </div>
          )}
        </div>

        <div className="p-6 pt-4 border-t bg-gray-50">
          <div className="flex gap-3">
            {onSaveToNotes && (
              <Button
                variant="outline"
                onClick={onSaveToNotes}
                className="flex-1"
              >
                <Save className="w-4 h-4 mr-2" />
                Save to Notes
              </Button>
            )}
            <Button
              variant="outline"
              onClick={handleOpenExternal}
              className="flex-1"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Open in New Tab
            </Button>
            <Button
              onClick={handleDownload}
              className="flex-1 bg-blue-600 hover:bg-blue-700"
            >
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
