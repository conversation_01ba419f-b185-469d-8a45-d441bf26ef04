
import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useUserFriends } from '@/hooks/useProfile';
import { useUserStudyGroups } from '@/hooks/useStudyGroups';
import { useShareDocument } from '@/hooks/useDocumentSharing';
import { Search, Users, MessageCircle, FileText } from 'lucide-react';

interface DocumentShareModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  document: {
    url: string;
    name: string;
    title: string;
  };
}

export const DocumentShareModal: React.FC<DocumentShareModalProps> = ({
  open,
  onOpenChange,
  document,
}) => {
  const [shareMessage, setShareMessage] = useState('');
  const [selectedFriends, setSelectedFriends] = useState<string[]>([]);
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  const { data: friends = [] } = useUserFriends();
  const { data: studyGroups = [] } = useUserStudyGroups();
  const shareDocumentMutation = useShareDocument();

  const filteredFriends = friends.filter(friend =>
    friend.full_name?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredGroups = studyGroups.filter(group =>
    group.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleShare = async () => {
    if (selectedFriends.length === 0 && selectedGroups.length === 0) {
      return;
    }

    await shareDocumentMutation.mutateAsync({
      documentUrl: document.url,
      documentName: document.name,
      documentTitle: document.title,
      shareMessage: shareMessage.trim() || undefined,
      recipients: {
        friends: selectedFriends.length > 0 ? selectedFriends : undefined,
        studyGroups: selectedGroups.length > 0 ? selectedGroups : undefined,
      },
    });

    // Reset form and close modal
    setShareMessage('');
    setSelectedFriends([]);
    setSelectedGroups([]);
    setSearchQuery('');
    onOpenChange(false);
  };

  const toggleFriend = (friendId: string) => {
    setSelectedFriends(prev =>
      prev.includes(friendId)
        ? prev.filter(id => id !== friendId)
        : [...prev, friendId]
    );
  };

  const toggleGroup = (groupId: string) => {
    setSelectedGroups(prev =>
      prev.includes(groupId)
        ? prev.filter(id => id !== groupId)
        : [...prev, groupId]
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Share Document: {document.title}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Share Message */}
          <div>
            <label className="text-sm font-medium mb-2 block">
              Add a message (optional)
            </label>
            <Textarea
              placeholder="Add a note about this document..."
              value={shareMessage}
              onChange={(e) => setShareMessage(e.target.value)}
              className="min-h-[80px]"
            />
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search friends and groups..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Recipients Tabs */}
          <Tabs defaultValue="friends" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="friends" className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                Friends ({selectedFriends.length})
              </TabsTrigger>
              <TabsTrigger value="groups" className="flex items-center gap-2">
                <MessageCircle className="w-4 h-4" />
                Groups ({selectedGroups.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="friends">
              <ScrollArea className="h-64 border rounded-md p-2">
                {filteredFriends.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    {searchQuery ? 'No friends found' : 'No friends available'}
                  </div>
                ) : (
                  <div className="space-y-2">
                    {filteredFriends.map((friend) => (
                      <div
                        key={friend.id}
                        className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg cursor-pointer"
                        onClick={() => toggleFriend(friend.id)}
                      >
                        <Checkbox
                          checked={selectedFriends.includes(friend.id)}
                          onChange={() => toggleFriend(friend.id)}
                        />
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={friend.avatar_url} />
                          <AvatarFallback>
                            {friend.full_name?.charAt(0)?.toUpperCase() || 'U'}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <p className="font-medium text-sm">{friend.full_name}</p>
                          <p className="text-xs text-gray-500">{friend.email}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </TabsContent>

            <TabsContent value="groups">
              <ScrollArea className="h-64 border rounded-md p-2">
                {filteredGroups.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    {searchQuery ? 'No groups found' : 'No study groups available'}
                  </div>
                ) : (
                  <div className="space-y-2">
                    {filteredGroups.map((group) => (
                      <div
                        key={group.id}
                        className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg cursor-pointer"
                        onClick={() => toggleGroup(group.id)}
                      >
                        <Checkbox
                          checked={selectedGroups.includes(group.id)}
                          onChange={() => toggleGroup(group.id)}
                        />
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={group.cover_image_url} />
                          <AvatarFallback>
                            {group.name.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <p className="font-medium text-sm">{group.name}</p>
                          <p className="text-xs text-gray-500">
                            {group.member_count} members
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </TabsContent>
          </Tabs>

          {/* Share Button */}
          <div className="flex justify-end gap-3 pt-4">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleShare}
              disabled={
                (selectedFriends.length === 0 && selectedGroups.length === 0) ||
                shareDocumentMutation.isPending
              }
            >
              {shareDocumentMutation.isPending ? 'Sharing...' : 'Share Document'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
