
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { 
  FileText, 
  BookOpen, 
  Download, 
  Save, 
  Eye, 
  Loader2,
  FolderPlus,
  MessageCircle,
  Heart,
  Share2,
  User
} from 'lucide-react';
import FolderSelector from '@/components/notes/FolderSelector';
import { useCreateNote } from '@/hooks/useNotes';
import { toast } from 'sonner';

interface SharedItemPostProps {
  post: {
    id: string;
    title?: string;
    content?: string;
    file_url?: string;
    file_name?: string;
    post_type: string;
    created_at: string;
    author: {
      id: string;
      full_name: string;
      avatar_url?: string;
    };
  };
}

export const SharedItemPost: React.FC<SharedItemPostProps> = ({ post }) => {
  const [showFolderSelector, setShowFolderSelector] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const createNoteMutation = useCreateNote();

  // Check if this is a shared item post
  const isSharedItem = post.post_type === 'shared_note' || post.post_type === 'shared_past_paper';
  
  if (!isSharedItem) {
    return null; // This component only handles shared items
  }

  const isNote = post.post_type === 'shared_note';
  const title = post.title || post.file_name || 'Shared Item';

  // Get file extension for badge
  const getFileExtension = () => {
    if (post.file_name) {
      const extension = post.file_name.split('.').pop()?.toUpperCase();
      return extension || 'FILE';
    }
    return 'FILE';
  };

  const handleView = () => {
    if (post.file_url) {
      window.open(post.file_url, '_blank', 'noopener,noreferrer');
    }
  };

  const handleDownload = async () => {
    if (!post.file_url) return;

    setIsDownloading(true);
    try {
      const link = document.createElement('a');
      link.href = post.file_url;
      link.download = post.file_name || title;
      link.target = '_blank';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast.success('Download started!');
    } catch (error) {
      toast.error('Failed to download file');
    } finally {
      setIsDownloading(false);
    }
  };

  const handleSaveToNotes = async (unitId: string, topicId: string) => {
    try {
      const noteData = {
        title: title,
        content: post.content || `Shared by ${post.author.full_name} on ${new Date(post.created_at).toLocaleDateString()}`,
        unit_id: unitId,
        topic_id: topicId,
        file_url: post.file_url,
        tags: ['shared', isNote ? 'note' : 'past-paper', 'imported']
      };

      await createNoteMutation.mutateAsync(noteData);
      setShowFolderSelector(false);
      toast.success('Item saved to Sort Notes!');
    } catch (error) {
      toast.error('Failed to save item');
    }
  };

  return (
    <>
      <Card className="p-4 mb-4 border border-blue-200 bg-blue-50">
        {/* Post Header */}
        <div className="flex items-start gap-3 mb-3">
          <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
            {post.author.avatar_url ? (
              <img 
                src={post.author.avatar_url} 
                alt={post.author.full_name}
                className="w-8 h-8 rounded-full object-cover"
              />
            ) : (
              <User className="w-5 h-5 text-gray-500" />
            )}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-medium text-gray-900">{post.author.full_name}</h4>
              <span className="text-sm text-gray-500">
                shared a {isNote ? 'note' : 'past paper'}
              </span>
            </div>
            <p className="text-sm text-gray-600">
              {new Date(post.created_at).toLocaleDateString()}
            </p>
          </div>
        </div>

        {/* Shared Item Content */}
        <div className="bg-white border border-blue-200 rounded-lg p-4 mb-3">
          <div className="flex items-start gap-3 mb-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              {isNote ? (
                <FileText className="w-5 h-5 text-blue-600" />
              ) : (
                <BookOpen className="w-5 h-5 text-blue-600" />
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="font-medium text-gray-900 truncate">{title}</h4>
                <Badge variant="secondary" className="text-xs">
                  {isNote ? 'Note' : 'Past Paper'}
                </Badge>
              </div>
              {post.content && (
                <p className="text-sm text-gray-600 line-clamp-2">
                  {post.content}
                </p>
              )}
            </div>
          </div>

          {/* File info */}
          {post.file_url && (
            <div className="bg-gray-50 rounded-md p-3 mb-3 border border-gray-200">
              <div className="flex items-center gap-2">
                <FileText className="w-4 h-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">
                  {post.file_name || title}
                </span>
                <Badge variant="outline" className="text-xs">
                  {getFileExtension()}
                </Badge>
              </div>
            </div>
          )}

          {/* Action buttons */}
          <div className="flex flex-wrap gap-2">
            {post.file_url && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleView}
                  className="flex-1 min-w-0 text-black hover:text-black border-gray-300 hover:bg-gray-50"
                >
                  <Eye className="w-4 h-4 mr-2 text-black" />
                  View
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDownload}
                  disabled={isDownloading}
                  className="flex-1 min-w-0 text-black hover:text-black border-gray-300 hover:bg-gray-50"
                >
                  {isDownloading ? (
                    <Loader2 className="w-4 h-4 mr-2 animate-spin text-black" />
                  ) : (
                    <Download className="w-4 h-4 mr-2 text-black" />
                  )}
                  Download
                </Button>
              </>
            )}
            <Button
              size="sm"
              onClick={() => setShowFolderSelector(true)}
              disabled={createNoteMutation.isPending}
              className="flex-1 min-w-0 bg-blue-600 hover:bg-blue-700"
            >
              {createNoteMutation.isPending ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <FolderPlus className="w-4 h-4 mr-2" />
              )}
              Save
            </Button>
          </div>
        </div>

        {/* Post interaction buttons */}
        <div className="flex items-center gap-4 pt-2">
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-red-600">
            <Heart className="w-4 h-4 mr-1" />
            Like
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-blue-600">
            <MessageCircle className="w-4 h-4 mr-1" />
            Comment
          </Button>
          <Button variant="ghost" size="sm" className="text-gray-600 hover:text-green-600">
            <Share2 className="w-4 h-4 mr-1" />
            Share
          </Button>
        </div>
      </Card>

      {/* Folder Selector Modal */}
      <FolderSelector
        open={showFolderSelector}
        onOpenChange={setShowFolderSelector}
        onFolderSelected={handleSaveToNotes}
      />
    </>
  );
};
