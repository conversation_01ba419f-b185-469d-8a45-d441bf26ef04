
import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Calendar } from "@/components/ui/calendar";
import { Slider } from "@/components/ui/slider";

export type Exam = {
  id: string;
  title: string;
  subject: string;
  date: Date;
  difficulty: number;
  description?: string;
};

type Props = {
  onAdd: (exam: Exam) => void;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  showTrigger?: boolean;
};

const ExamModal: React.FC<Props> = ({
  onAdd,
  open: externalOpen,
  onOpenChange: externalOnOpenChange,
  showTrigger = true
}) => {
  const [internalOpen, setInternalOpen] = useState(false);

  // Use external control if provided, otherwise use internal state
  const open = externalOpen !== undefined ? externalOpen : internalOpen;
  const setOpen = externalOnOpenChange || setInternalOpen;

  // Form state
  const [title, setTitle] = useState("");
  const [date, setDate] = useState<Date | undefined>();
  const [difficulty, setDifficulty] = useState(2);
  const [description, setDescription] = useState("");

  function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (!title || !date) return;

    onAdd({
      id: `exam-${Date.now()}`,
      title,
      subject: "General", // Default subject since we removed the field
      date,
      difficulty,
      description,
    });

    // Reset form
    setTitle("");
    setDate(undefined);
    setDifficulty(2);
    setDescription("");
    setOpen(false);
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {showTrigger && (
        <DialogTrigger asChild>
          <Button variant="default" size="sm">
            + Add Exam/Assignment
          </Button>
        </DialogTrigger>
      )}
      <DialogContent
        className="flex flex-col max-h-[90vh] sm:max-h-[90vh] overflow-hidden"
      >
        <DialogHeader>
          <DialogTitle>Add Exam or Assignment</DialogTitle>
          <DialogDescription>
            Fill out the details for your upcoming exam or assignment.
          </DialogDescription>
        </DialogHeader>
        {/* Scrollable FORM */}
        <div className="flex-1 min-h-0 overflow-y-auto px-1" style={{ WebkitOverflowScrolling: "touch" }}>
          <form
            onSubmit={handleSubmit}
            className="flex flex-col gap-5 mt-2 w-full"
          >
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="block text-xs mb-1">Title *</label>
                <Input
                  placeholder="e.g. Physics Midterm, Math Assignment, History Essay"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  required
                />
              </div>
              <div>
                <label className="block text-xs mb-1">Description (Optional)</label>
                <Input
                  placeholder="Additional details about the exam or assignment"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                />
              </div>
              <div className="flex flex-col gap-2">
                <label className="block text-xs mb-1">Date *</label>
                <Calendar
                  selected={date}
                  onSelect={setDate}
                  mode="single"
                  className="w-full max-w-xs"
                />
              </div>
              <div className="flex gap-4 items-center">
                <label className="block text-xs mb-1 min-w-[70px]">Difficulty</label>
                <Slider
                  min={1}
                  max={3}
                  step={1}
                  value={[difficulty]}
                  onValueChange={([v]) => setDifficulty(v)}
                  className="w-32"
                />
                <span className="text-xs">
                  {["Easy", "Medium", "Hard"][difficulty - 1]}
                </span>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="submit"
                className="w-full"
                disabled={!title || !date}
              >
                Add Exam/Assignment
              </Button>
            </DialogFooter>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ExamModal;

