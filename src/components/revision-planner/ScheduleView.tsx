import React, { useMemo } from "react";
import { format, differenceInCalendarDays, isToday, isPast, addDays } from "date-fns";
import { AlarmClock } from "lucide-react";
import { Button } from "@/components/ui/button";
import DailySchedule from "./DailySchedule";
import WeeklySchedule from "./WeeklySchedule";

export type ScheduledSession = {
  id: string;
  examId: string;
  date: Date;
  subject: string;
  topic: string;
  isDone: boolean;
  overdue?: boolean;
};

type Props = {
  schedule: ScheduledSession[];
  markDone: (id: string) => void;
  view: "daily" | "weekly";
  onReschedule: (id: string, newDate: Date) => void;
  exams: { id: string; title: string; date: Date }[];
};

// Helper to get countdown (days) until a target date
function getDaysTo(target: Date | undefined) {
  if (!target) return "";
  const daysLeft = differenceInCalendarDays(target, new Date());
  if (daysLeft > 0) {
    return `${daysLeft} day${daysLeft === 1 ? "" : "s"} left`;
  } else if (daysLeft === 0) {
    return "Today";
  } else {
    return "Passed";
  }
}

const ScheduleView: React.FC<Props> = ({
  schedule,
  markDone,
  view,
  onReschedule,
  exams,
}) => {
  // Group days/week for quick rendering

  const daysOrWeeks = React.useMemo(() => {
    if (view === "daily") {
      const out: { date: Date; sessions: ScheduledSession[] }[] = [];
      for (let i = 0; i < 14; i++) {
        const d = addDays(new Date(), i);
        const sessions = schedule.filter(
          (sess) =>
            format(sess.date, "yyyy-MM-dd") === format(d, "yyyy-MM-dd")
        );
        out.push({ date: d, sessions });
      }
      return out;
    } else {
      // Weekly
      const out: { week: string; sessions: ScheduledSession[] }[] = [];
      for (let w = 0; w < 4; w++) {
        const weekStart = addDays(new Date(), 7 * w);
        const weekEnd = addDays(weekStart, 6);
        const sessions = schedule.filter(
          (sess) => sess.date >= weekStart && sess.date <= weekEnd
        );
        out.push({
          week: `${format(weekStart, "MMM d")}-${format(weekEnd, "MMM d")}`,
          sessions,
        });
      }
      return out;
    }
  }, [schedule, view]);

  return (
    <div>
      {view === "daily" ? (
        <div className="flex flex-col gap-5">
          {(daysOrWeeks as { date: Date; sessions: ScheduledSession[] }[]).map((day) => (
            <DailySchedule
              key={format(day.date, "yyyy-MM-dd")}
              date={day.date}
              sessions={day.sessions}
              markDone={markDone}
              onReschedule={onReschedule}
              exams={exams}
            />
          ))}
        </div>
      ) : (
        // Weekly
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
          {(daysOrWeeks as { week: string; sessions: ScheduledSession[] }[]).map((weekObj) => (
            <WeeklySchedule
              key={weekObj.week}
              week={weekObj.week}
              sessions={weekObj.sessions}
              markDone={markDone}
              onReschedule={onReschedule}
              exams={exams}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default ScheduleView;
