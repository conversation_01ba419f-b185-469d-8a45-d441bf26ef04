
import React from "react";
import { Slider } from "@/components/ui/slider";

type Props = {
  hours: number;
  setHours: (h: number) => void;
};

const Preferences: React.FC<Props> = ({ hours, setHours }) => (
  <div className="flex items-center gap-4 p-4 bg-muted rounded-xl max-w-sm">
    <span className="text-sm font-semibold">Study hours per day:</span>
    <Slider min={1} max={12} value={[hours]} onValueChange={([v]) => setHours(v)} className="w-40" />
    <span className="text-sm">{hours}h</span>
  </div>
);

export default Preferences;
