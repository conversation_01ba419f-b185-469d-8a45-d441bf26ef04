
import React from "react";
import { AlarmClock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { format, differenceInCalendarDays, addDays } from "date-fns";

export type SessionCardProps = {
  session: {
    id: string;
    examId: string;
    date: Date;
    subject: string;
    topic: string;
    isDone: boolean;
    overdue?: boolean;
  };
  exam?: { id: string; title: string; date: Date };
  showCountdown?: boolean; // for clarity, can be toggled
  onMarkDone: (id: string) => void;
  onReschedule: (id: string, newDate: Date) => void;
  rescheduleDate?: Date; // use a custom date for rescheduling (daily/weekly difference)
};

// Helper to get days countdown
export function getDaysTo(target: Date | undefined) {
  if (!target) return "";
  const daysLeft = differenceInCalendarDays(target, new Date());
  if (daysLeft > 0) {
    return `${daysLeft} day${daysLeft === 1 ? "" : "s"} left`;
  } else if (daysLeft === 0) {
    return "Today";
  } else {
    return "Passed";
  }
}

const SessionCard: React.FC<SessionCardProps> = ({
  session,
  exam,
  showCountdown = true,
  onMarkDone,
  onReschedule,
  rescheduleDate,
}) => {
  // Responsive stack: if overdue (has red background on mobile), stack card vertically
  return (
    <div
      className={`flex flex-col md:flex-row items-stretch md:items-center gap-2 md:gap-3 p-3 rounded transition-all ${
        session.overdue ? "bg-red-100" : "hover:bg-accent"
      }`}
    >
      <div className="flex items-center mb-1 md:mb-0">
        <AlarmClock className="w-4 h-4 mr-1 text-muted-foreground" />
      </div>
      <div className="flex-1 flex flex-col gap-1">
        <div className="flex flex-col sm:flex-row sm:flex-wrap sm:gap-1 items-start sm:items-center">
          <span className="font-semibold">{session.subject}</span>
          <span className="hidden sm:inline"> – </span>
          <span
            className={
              session.overdue
                ? "text-red-600 font-bold ml-0 sm:ml-1"
                : "ml-0 sm:ml-1"
            }
          >
            {session.topic}
          </span>
          {showCountdown && exam && (
            <span className="mt-2 sm:mt-0 sm:ml-2 text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded block sm:inline-block">
              {getDaysTo(exam.date)}
            </span>
          )}
        </div>
        <small className="block text-xs text-gray-500 mt-1">
          {exam && (
            <>
              Exam: <b>{exam.title}</b>
            </>
          )}
        </small>
      </div>
      {/* Buttons: Full width stack on mobile, inline on desktop */}
      <div className="flex flex-col gap-2 w-full md:w-auto md:flex-row md:items-center md:justify-end">
        <Button
          size="sm"
          variant={session.isDone ? "secondary" : "outline"}
          className={`w-full md:w-auto ${session.isDone ? "opacity-70" : ""}`}
          onClick={() => onMarkDone(session.id)}
        >
          {session.isDone ? "Done" : "Mark Done"}
        </Button>
        {session.overdue && (
          <Button
            size="sm"
            variant="destructive"
            className="w-full md:w-auto"
            onClick={() =>
              onReschedule(session.id, rescheduleDate || addDays(session.date, 1))
            }
          >
            Reschedule
          </Button>
        )}
      </div>
    </div>
  );
};

export default SessionCard;

