
import React from "react";
import SessionCard from "./SessionCard";
import { ScheduledSession } from "./ScheduleView";

type WeeklyProps = {
  week: string;
  sessions: ScheduledSession[];
  markDone: (id: string) => void;
  onReschedule: (id: string, newDate: Date) => void;
  exams: { id: string; title: string; date: Date }[];
};

const WeeklySchedule: React.FC<WeeklyProps> = ({
  week,
  sessions,
  markDone,
  onReschedule,
  exams,
}) => {
  return (
    <div className="p-4 border rounded-xl bg-white shadow-sm">
      <div className="font-medium mb-2">{week}</div>
      <div className="flex flex-col gap-2 min-h-[28px]">
        {sessions.length === 0 && (
          <span className="text-xs text-muted-foreground">
            No sessions scheduled.
          </span>
        )}
        {sessions.map((session) => {
          const examObj = exams.find(e => e.id === session.examId);
          return (
            <SessionCard
              key={session.id}
              session={session}
              exam={examObj}
              onMarkDone={markDone}
              onReschedule={onReschedule}
              showCountdown={true}
            />
          );
        })}
      </div>
    </div>
  );
};

export default WeeklySchedule;
