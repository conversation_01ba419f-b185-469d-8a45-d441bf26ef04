
import React from "react";
import { format, isToday, isPast, addDays } from "date-fns";
import SessionCard from "./SessionCard";
import { ScheduledSession } from "./ScheduleView";

type DailyProps = {
  date: Date;
  sessions: ScheduledSession[];
  markDone: (id: string) => void;
  onReschedule: (id: string, newDate: Date) => void;
  exams: { id: string; title: string; date: Date }[];
};

const DailySchedule: React.FC<DailyProps> = ({
  date,
  sessions,
  markDone,
  onReschedule,
  exams,
}) => {
  return (
    <div className="p-4 border rounded-xl bg-white shadow-sm">
      <div className="flex justify-between mb-2">
        <span className="font-medium">
          {format(date, "EEE, MMM d")}
        </span>
        <span className="text-xs text-muted-foreground">
          {isToday(date) ? "Today" : ""}
          {!isToday(date) && isPast(date) ? "Past" : ""}
        </span>
      </div>
      <div className="flex flex-col gap-2 min-h-[28px]">
        {sessions.length === 0 && (
          <span className="text-xs text-muted-foreground">
            No sessions scheduled.
          </span>
        )}
        {sessions.map((session) => {
          const examObj = exams.find((e) => e.id === session.examId);
          return (
            <SessionCard
              key={session.id}
              session={session}
              exam={examObj}
              onMarkDone={markDone}
              onReschedule={onReschedule}
              rescheduleDate={addDays(date, 1)}
              showCountdown={true}
            />
          );
        })}
      </div>
    </div>
  );
};

export default DailySchedule;
