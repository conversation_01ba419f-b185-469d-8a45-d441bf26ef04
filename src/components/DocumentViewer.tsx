import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Download, 
  ZoomIn, 
  ZoomOut, 
  RotateCw, 
  Maximize2, 
  Minimize2,
  FileText,
  File,
  Image,
  Video,
  Music,
  Archive,
  X,
  ExternalLink,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import { getFileTypeFromExtension, canPreviewFile } from '@/utils/fileUtils';

interface DocumentViewerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  fileUrl: string;
  fileName: string;
  fileType?: string;
  title?: string;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({
  open,
  onOpenChange,
  fileUrl,
  fileName,
  fileType,
  title
}) => {
  const [zoom, setZoom] = useState(100);
  const [rotation, setRotation] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Detect file type from URL or filename
  const getFileType = () => {
    if (fileType) return fileType.toLowerCase();

    const extension = fileName.split('.').pop()?.toLowerCase() || '';
    return extension;
  };

  const detectedFileType = getFileType();
  const fileTypeInfo = getFileTypeFromExtension(fileName);

  // Get file icon based on type
  const getFileIcon = () => {
    const type = detectedFileType;
    
    if (type === 'pdf') return <FileText className="w-5 h-5 text-red-500" />;
    if (['doc', 'docx'].includes(type)) return <FileText className="w-5 h-5 text-blue-500" />;
    if (['ppt', 'pptx'].includes(type)) return <FileText className="w-5 h-5 text-orange-500" />;
    if (['xls', 'xlsx'].includes(type)) return <FileText className="w-5 h-5 text-green-500" />;
    if (['txt', 'md'].includes(type)) return <FileText className="w-5 h-5 text-gray-500" />;
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(type)) return <Image className="w-5 h-5 text-purple-500" />;
    if (['mp4', 'avi', 'mov', 'wmv', 'flv'].includes(type)) return <Video className="w-5 h-5 text-red-500" />;
    if (['mp3', 'wav', 'flac', 'aac'].includes(type)) return <Music className="w-5 h-5 text-green-500" />;
    if (['zip', 'rar', '7z', 'tar'].includes(type)) return <Archive className="w-5 h-5 text-yellow-500" />;
    
    return <File className="w-5 h-5 text-gray-500" />;
  };

  // Check if file can be viewed inline
  const canViewInline = () => {
    return canPreviewFile(fileName);
  };

  // Handle download
  const handleDownload = async () => {
    try {
      const response = await fetch(fileUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('File downloaded successfully');
    } catch (error) {
      toast.error('Failed to download file');
    }
  };

  // Handle zoom
  const handleZoomIn = () => setZoom(prev => Math.min(prev + 25, 300));
  const handleZoomOut = () => setZoom(prev => Math.max(prev - 25, 25));
  const handleRotate = () => setRotation(prev => (prev + 90) % 360);

  // Handle fullscreen
  const toggleFullscreen = () => setIsFullscreen(!isFullscreen);

  // Open in new tab
  const openInNewTab = () => {
    window.open(fileUrl, '_blank');
  };

  // Reset states when modal opens/closes
  useEffect(() => {
    if (open) {
      setZoom(100);
      setRotation(0);
      setIsFullscreen(false);
      setIsLoading(true);
      setError(null);
    }
  }, [open]);

  // Render content based on file type
  const renderContent = () => {
    const type = detectedFileType;

    if (!canViewInline()) {
      return (
        <div className="flex flex-col items-center justify-center h-96 text-center space-y-4">
          {getFileIcon()}
          <div>
            <h3 className="text-lg font-semibold text-gray-700">{fileName}</h3>
            <p className="text-sm text-gray-500 mt-2">
              This file type cannot be previewed inline.
            </p>
          </div>
          <div className="flex gap-2">
            <Button onClick={handleDownload} variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
            <Button onClick={openInNewTab} variant="outline">
              <ExternalLink className="w-4 h-4 mr-2" />
              Open in New Tab
            </Button>
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex flex-col items-center justify-center h-96 text-center space-y-4">
          <X className="w-12 h-12 text-red-500" />
          <div>
            <h3 className="text-lg font-semibold text-red-700">Error Loading File</h3>
            <p className="text-sm text-gray-500 mt-2">{error}</p>
          </div>
          <Button onClick={handleDownload} variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Download Instead
          </Button>
        </div>
      );
    }

    // PDF Viewer
    if (type === 'pdf') {
      return (
        <div className="relative w-full h-full">
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
              <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
            </div>
          )}
          <iframe
            src={`${fileUrl}#toolbar=1&navpanes=1&scrollbar=1&page=1&view=FitH`}
            className="w-full h-full border-0"
            style={{
              transform: `scale(${zoom / 100}) rotate(${rotation}deg)`,
              transformOrigin: 'center center'
            }}
            onLoad={() => setIsLoading(false)}
            onError={() => {
              setIsLoading(false);
              setError('Failed to load PDF file');
            }}
            title={fileName}
          />
        </div>
      );
    }

    // Image Viewer
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(type)) {
      return (
        <div className="flex items-center justify-center h-full">
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
              <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
            </div>
          )}
          <img
            src={fileUrl}
            alt={fileName}
            className="max-w-full max-h-full object-contain"
            style={{
              transform: `scale(${zoom / 100}) rotate(${rotation}deg)`,
              transformOrigin: 'center center'
            }}
            onLoad={() => setIsLoading(false)}
            onError={() => {
              setIsLoading(false);
              setError('Failed to load image');
            }}
          />
        </div>
      );
    }

    // Video Viewer
    if (['mp4', 'webm', 'ogg'].includes(type)) {
      return (
        <div className="flex items-center justify-center h-full">
          <video
            src={fileUrl}
            controls
            className="max-w-full max-h-full"
            onLoadStart={() => setIsLoading(false)}
            onError={() => {
              setIsLoading(false);
              setError('Failed to load video');
            }}
          >
            Your browser does not support the video tag.
          </video>
        </div>
      );
    }

    // Audio Viewer
    if (['mp3', 'wav', 'ogg'].includes(type)) {
      return (
        <div className="flex flex-col items-center justify-center h-96 space-y-4">
          <Music className="w-16 h-16 text-green-500" />
          <h3 className="text-lg font-semibold">{fileName}</h3>
          <audio
            src={fileUrl}
            controls
            className="w-full max-w-md"
            onLoadStart={() => setIsLoading(false)}
            onError={() => {
              setIsLoading(false);
              setError('Failed to load audio');
            }}
          >
            Your browser does not support the audio tag.
          </audio>
        </div>
      );
    }

    // Text Viewer
    if (type === 'txt') {
      return (
        <div className="w-full h-full">
          <iframe
            src={fileUrl}
            className="w-full h-full border-0 bg-white"
            onLoad={() => setIsLoading(false)}
            onError={() => {
              setIsLoading(false);
              setError('Failed to load text file');
            }}
            title={fileName}
          />
        </div>
      );
    }

    return null;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent 
        className={`${
          isFullscreen 
            ? 'max-w-[100vw] max-h-[100vh] w-[100vw] h-[100vh] p-0' 
            : 'max-w-6xl max-h-[90vh] w-[90vw] h-[80vh]'
        } bg-white`}
      >
        <DialogHeader className={`${isFullscreen ? 'p-4' : ''} border-b`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getFileIcon()}
              <div>
                <DialogTitle className="text-lg font-semibold">
                  {title || fileName}
                </DialogTitle>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="secondary" className="text-xs">
                    {detectedFileType.toUpperCase()}
                  </Badge>
                  <span className="text-xs text-gray-500">
                    {zoom}%
                  </span>
                </div>
              </div>
            </div>
            
            {/* Toolbar */}
            <div className="flex items-center gap-1">
              {canViewInline() && (
                <>
                  <Button variant="ghost" size="sm" onClick={handleZoomOut}>
                    <ZoomOut className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm" onClick={handleZoomIn}>
                    <ZoomIn className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm" onClick={handleRotate}>
                    <RotateCw className="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm" onClick={toggleFullscreen}>
                    {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
                  </Button>
                </>
              )}
              <Button variant="ghost" size="sm" onClick={openInNewTab}>
                <ExternalLink className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={handleDownload}>
                <Download className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className={`${isFullscreen ? 'flex-1 p-4' : 'flex-1 p-6'} overflow-hidden`}>
          <ScrollArea className="w-full h-full">
            {renderContent()}
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DocumentViewer;
