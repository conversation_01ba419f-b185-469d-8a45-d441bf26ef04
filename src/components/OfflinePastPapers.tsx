import React, { useState, useEffect } from 'react';
import { useOfflinePastPapers } from '@/hooks/useOffline';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  FileText, 
  WifiOff, 
  Download, 
  Search,
  Calendar,
  BookOpen,
  Eye,
  Filter
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { OfflinePastPapers as OfflinePastPapersUtil, OfflinePastPaper } from '@/utils/offlineStorage';

interface OfflinePastPapersProps {
  className?: string;
}

export const OfflinePastPapers: React.FC<OfflinePastPapersProps> = ({ className }) => {
  const { isOffline, getPapers } = useOfflinePastPapers();
  const [papers, setPapers] = useState<OfflinePastPaper[]>([]);
  const [filteredPapers, setFilteredPapers] = useState<OfflinePastPaper[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSubject, setSelectedSubject] = useState<string>('all');
  const [selectedYear, setSelectedYear] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(true);

  // Load past papers on component mount
  useEffect(() => {
    // Add a small delay to ensure IndexedDB is ready
    const timer = setTimeout(() => {
      loadPastPapers();
    }, 200);

    return () => clearTimeout(timer);
  }, []);

  // Filter papers when search term or filters change
  useEffect(() => {
    filterPapers();
  }, [papers, searchTerm, selectedSubject, selectedYear]);

  const loadPastPapers = async () => {
    try {
      setIsLoading(true);
      const papersData = await getPapers();
      setPapers(papersData);
    } catch (error) {
      console.error('Error loading past papers:', error);
      toast({
        title: "Error",
        description: "Failed to load past papers.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filterPapers = () => {
    let filtered = papers;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(paper =>
        paper.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        paper.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (paper.exam_board && paper.exam_board.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filter by subject
    if (selectedSubject !== 'all') {
      filtered = filtered.filter(paper => paper.subject === selectedSubject);
    }

    // Filter by year
    if (selectedYear !== 'all') {
      filtered = filtered.filter(paper => paper.year.toString() === selectedYear);
    }

    setFilteredPapers(filtered);
  };

  const handleViewPaper = (paper: OfflinePastPaper) => {
    if (paper.file_content) {
      // If we have offline content, display it
      const blob = new Blob([paper.file_content], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      window.open(url, '_blank');
    } else if (paper.file_url && !isOffline) {
      // If online and we have a URL, open it
      window.open(paper.file_url, '_blank');
    } else {
      toast({
        title: "Offline Content Not Available",
        description: "This paper is not available offline. Connect to the internet to view it.",
        variant: "destructive",
      });
    }
  };

  const handleDownloadPaper = (paper: OfflinePastPaper) => {
    if (paper.file_content) {
      // Download from offline content
      const blob = new Blob([paper.file_content], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${paper.title}.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } else if (paper.file_url && !isOffline) {
      // Download from online URL
      window.open(paper.file_url, '_blank');
    } else {
      toast({
        title: "Offline Content Not Available",
        description: "This paper is not available for offline download.",
        variant: "destructive",
      });
    }
  };

  // Get unique subjects and years for filters
  const subjects = Array.from(new Set(papers.map(paper => paper.subject))).sort();
  const years = Array.from(new Set(papers.map(paper => paper.year))).sort((a, b) => b - a);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-gray-500">Loading past papers...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <FileText className="h-6 w-6" />
            Past Papers
            {isOffline && (
              <Badge variant="secondary" className="ml-2">
                <WifiOff className="h-3 w-3 mr-1" />
                Offline
              </Badge>
            )}
          </h2>
          <p className="text-gray-600 mt-1">
            {isOffline
              ? "View downloaded past papers offline. Some papers may not be available without internet."
              : "Browse and download past examination papers."
            }
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Search */}
        <div className="relative md:col-span-2">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search papers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Subject Filter */}
        <Select value={selectedSubject} onValueChange={setSelectedSubject}>
          <SelectTrigger>
            <SelectValue placeholder="All Subjects" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Subjects</SelectItem>
            {subjects.map((subject) => (
              <SelectItem key={subject} value={subject}>
                {subject}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Year Filter */}
        <Select value={selectedYear} onValueChange={setSelectedYear}>
          <SelectTrigger>
            <SelectValue placeholder="All Years" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Years</SelectItem>
            {years.map((year) => (
              <SelectItem key={year} value={year.toString()}>
                {year}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Results Summary */}
      {papers.length > 0 && (
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <span>
            Showing {filteredPapers.length} of {papers.length} papers
          </span>
          {isOffline && (
            <Badge variant="outline" className="text-xs">
              <WifiOff className="h-3 w-3 mr-1" />
              {papers.filter(p => p.file_content).length} available offline
            </Badge>
          )}
        </div>
      )}

      {/* Papers List */}
      {filteredPapers.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">
              {papers.length === 0 ? 'No past papers available' : 'No matching papers'}
            </h3>
            <p className="text-gray-500">
              {papers.length === 0 
                ? 'Past papers will appear here when available.'
                : 'Try adjusting your search terms or filters.'
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredPapers.map((paper) => (
            <Card key={paper.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <CardTitle className="text-lg line-clamp-2">{paper.title}</CardTitle>
                  <div className="flex flex-col gap-1 ml-2 shrink-0">
                    <Badge variant="secondary" className="text-xs">
                      {paper.year}
                    </Badge>
                    {paper.file_content ? (
                      <Badge variant="default" className="text-xs">
                        <WifiOff className="h-3 w-3 mr-1" />
                        Offline
                      </Badge>
                    ) : (
                      isOffline && (
                        <Badge variant="outline" className="text-xs">
                          Online Only
                        </Badge>
                      )
                    )}
                  </div>
                </div>
                <CardDescription className="space-y-1">
                  <div className="flex items-center gap-1">
                    <BookOpen className="h-3 w-3" />
                    {paper.subject}
                  </div>
                  {paper.exam_board && (
                    <div className="text-xs text-gray-500">
                      {paper.exam_board}
                    </div>
                  )}
                  <div className="flex items-center gap-1 text-xs">
                    <Calendar className="h-3 w-3" />
                    Added {formatDate(paper.created_at)}
                  </div>
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                {paper.description && (
                  <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                    {paper.description}
                  </p>
                )}

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewPaper(paper)}
                    disabled={!paper.file_content && isOffline}
                    className="flex-1"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDownloadPaper(paper)}
                    disabled={!paper.file_content && isOffline}
                    className="flex-1"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>

                {!paper.file_content && isOffline && (
                  <p className="text-xs text-gray-500 mt-2 text-center">
                    Requires internet connection
                  </p>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default OfflinePastPapers;
