import React from 'react';
import { useUser } from '@/hooks/useAuth';
import { useProfile } from '@/hooks/useProfile';

const DebugAuth: React.FC = () => {
  const { data: user, isLoading: userLoading, error: userError } = useUser();
  const { data: profile, isLoading: profileLoading, error: profileError } = useProfile();

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs max-w-sm z-50">
      <h3 className="font-bold mb-2">Debug Info</h3>
      
      <div className="mb-2">
        <strong>User:</strong>
        {userLoading && <span className="text-yellow-400"> Loading...</span>}
        {userError && <span className="text-red-400"> Error: {userError.message}</span>}
        {user && <span className="text-green-400"> ✓ {user.email}</span>}
        {!user && !userLoading && <span className="text-red-400"> Not authenticated</span>}
      </div>

      <div className="mb-2">
        <strong>Profile:</strong>
        {profileLoading && <span className="text-yellow-400"> Loading...</span>}
        {profileError && <span className="text-red-400"> Error: {profileError.message}</span>}
        {profile && <span className="text-green-400"> ✓ {profile.full_name}</span>}
        {!profile && !profileLoading && <span className="text-red-400"> No profile</span>}
      </div>

      {user && (
        <div className="text-xs text-gray-300">
          User ID: {user.id.slice(0, 8)}...
        </div>
      )}
    </div>
  );
};

export default DebugAuth;
