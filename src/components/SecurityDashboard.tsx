import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Activity,
  Lock,
  Unlock,
  Eye,
  RefreshCw
} from 'lucide-react';
import { useSecurity } from '@/contexts/SecurityContext';
import { SECURITY_CONFIG } from '@/config/security';

interface SecurityLog {
  timestamp: string;
  userId: string;
  activity: string;
  details?: any;
  userAgent: string;
  url: string;
}

const SecurityDashboard: React.FC = () => {
  const security = useSecurity();
  const [securityLogs, setSecurityLogs] = useState<SecurityLog[]>([]);
  const [showLogs, setShowLogs] = useState(false);

  useEffect(() => {
    // Load security logs from localStorage
    const logs = JSON.parse(localStorage.getItem('security_logs') || '[]');
    setSecurityLogs(logs.slice(-20)); // Show last 20 logs
  }, []);

  const getSecurityScore = (): number => {
    let score = 100;
    
    if (security.securityStatus.isBlocked) score -= 50;
    if (security.securityStatus.failedAttempts > 0) score -= security.securityStatus.failedAttempts * 5;
    if (!security.securityStatus.sessionValid) score -= 20;
    
    return Math.max(0, score);
  };

  const getSecurityLevel = (score: number): { level: string; color: string; icon: React.ReactNode } => {
    if (score >= 90) return { 
      level: 'Excellent', 
      color: 'text-green-600', 
      icon: <CheckCircle className="w-5 h-5 text-green-600" /> 
    };
    if (score >= 70) return { 
      level: 'Good', 
      color: 'text-blue-600', 
      icon: <Shield className="w-5 h-5 text-blue-600" /> 
    };
    if (score >= 50) return { 
      level: 'Warning', 
      color: 'text-yellow-600', 
      icon: <AlertTriangle className="w-5 h-5 text-yellow-600" /> 
    };
    return { 
      level: 'Critical', 
      color: 'text-red-600', 
      icon: <XCircle className="w-5 h-5 text-red-600" /> 
    };
  };

  const clearSecurityLogs = () => {
    localStorage.removeItem('security_logs');
    setSecurityLogs([]);
  };

  const refreshSecurityStatus = () => {
    security.refreshSession();
    window.location.reload();
  };

  const securityScore = getSecurityScore();
  const securityLevel = getSecurityLevel(securityScore);

  return (
    <div className="space-y-6 p-6 max-w-6xl mx-auto">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Security Dashboard</h1>
        <Button onClick={refreshSecurityStatus} variant="outline">
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh Status
        </Button>
      </div>

      {/* Security Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Security Score */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              {securityLevel.icon}
              Security Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold mb-2">{securityScore}/100</div>
            <Badge variant={securityScore >= 70 ? 'default' : 'destructive'}>
              {securityLevel.level}
            </Badge>
          </CardContent>
        </Card>

        {/* Session Status */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <Clock className="w-5 h-5" />
              Session Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span>Valid:</span>
                {security.securityStatus.sessionValid ? (
                  <CheckCircle className="w-4 h-4 text-green-600" />
                ) : (
                  <XCircle className="w-4 h-4 text-red-600" />
                )}
              </div>
              <div className="flex items-center justify-between">
                <span>Last Activity:</span>
                <span className="text-sm text-gray-600">
                  {security.securityStatus.lastActivity?.toLocaleTimeString() || 'Unknown'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Account Status */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              {security.securityStatus.isBlocked ? (
                <Lock className="w-5 h-5 text-red-600" />
              ) : (
                <Unlock className="w-5 h-5 text-green-600" />
              )}
              Account Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Badge variant={security.securityStatus.isBlocked ? 'destructive' : 'default'}>
                {security.securityStatus.isBlocked ? 'Blocked' : 'Active'}
              </Badge>
              <div className="text-sm text-gray-600">
                Failed Attempts: {security.securityStatus.failedAttempts}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Security Alerts */}
      {security.securityStatus.isBlocked && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Your account has been temporarily blocked due to suspicious activity. 
            Contact support if you believe this is an error.
          </AlertDescription>
        </Alert>
      )}

      {!security.securityStatus.sessionValid && (
        <Alert variant="destructive">
          <Clock className="h-4 w-4" />
          <AlertDescription>
            Your session has expired. Please refresh the page and log in again.
          </AlertDescription>
        </Alert>
      )}

      {/* Security Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Security Configuration</CardTitle>
          <CardDescription>Current security settings and limits</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-semibold">Rate Limits</h4>
              <div className="text-sm space-y-1">
                <div>Login Attempts: {SECURITY_CONFIG.RATE_LIMITS.LOGIN_ATTEMPTS}/15min</div>
                <div>API Requests: {SECURITY_CONFIG.RATE_LIMITS.API_REQUESTS}/15min</div>
                <div>File Uploads: {SECURITY_CONFIG.RATE_LIMITS.FILE_UPLOADS}/15min</div>
              </div>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold">Session Settings</h4>
              <div className="text-sm space-y-1">
                <div>Max Age: {SECURITY_CONFIG.SESSION.MAX_AGE / (60 * 60 * 1000)}h</div>
                <div>Idle Timeout: {SECURITY_CONFIG.SESSION.IDLE_TIMEOUT / (60 * 60 * 1000)}h</div>
                <div>Max Sessions: {SECURITY_CONFIG.SESSION.CONCURRENT_SESSIONS}</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Security Logs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              Security Activity Log
            </span>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowLogs(!showLogs)}
              >
                <Eye className="w-4 h-4 mr-2" />
                {showLogs ? 'Hide' : 'Show'} Logs
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={clearSecurityLogs}
                disabled={securityLogs.length === 0}
              >
                Clear Logs
              </Button>
            </div>
          </CardTitle>
          <CardDescription>
            Recent security events and activities ({securityLogs.length} entries)
          </CardDescription>
        </CardHeader>
        {showLogs && (
          <CardContent>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {securityLogs.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  No security logs available
                </div>
              ) : (
                securityLogs.map((log, index) => (
                  <div
                    key={index}
                    className="flex items-start justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex-1">
                      <div className="font-medium text-sm">{log.activity}</div>
                      <div className="text-xs text-gray-600">
                        {new Date(log.timestamp).toLocaleString()}
                      </div>
                      {log.details && (
                        <div className="text-xs text-gray-500 mt-1">
                          {JSON.stringify(log.details, null, 2)}
                        </div>
                      )}
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {log.userId}
                    </Badge>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        )}
      </Card>

      {/* Security Actions */}
      {security.securityStatus.isBlocked && (
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">Account Recovery</CardTitle>
            <CardDescription>
              Your account is currently blocked. Contact support for assistance.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={security.unblockUser}
              variant="outline"
              className="mr-2"
            >
              Request Unblock
            </Button>
            <Button variant="outline">
              Contact Support
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default SecurityDashboard;
