
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronDown, BookOpen, GraduationCap } from 'lucide-react';

interface Course {
  university: string;
  course: string;
  year: string;
  unit: string;
}

interface CourseSelectorProps {
  onCourseSelect: (course: Course) => void;
}

export const CourseSelector = ({ onCourseSelect }: CourseSelectorProps) => {
  const [selectedUniversity, setSelectedUniversity] = useState('');
  const [selectedCourse, setSelectedCourse] = useState('');
  const [selectedYear, setSelectedYear] = useState('');
  const [selectedUnit, setSelectedUnit] = useState('');

  const universities = [
    'Harvard University',
    'Stanford University',
    'MIT',
    'University of Oxford',
    'University of Cambridge',
    'Yale University',
    'Princeton University',
    'Columbia University',
    'University of California, Berkeley',
    'University of Toronto',
  ];

  const courses = {
    'Computer Science': ['Introduction to Programming', 'Data Structures', 'Algorithms', 'Database Systems', 'Machine Learning', 'Software Engineering', 'Computer Networks'],
    'Business Administration': ['Marketing Fundamentals', 'Financial Accounting', 'Operations Management', 'Strategic Management', 'Business Analytics', 'International Business'],
    'Psychology': ['Introduction to Psychology', 'Cognitive Psychology', 'Social Psychology', 'Research Methods', 'Developmental Psychology', 'Abnormal Psychology'],
    'Biology': ['Cell Biology', 'Genetics', 'Ecology', 'Molecular Biology', 'Physiology', 'Evolution', 'Biochemistry'],
    'Mathematics': ['Calculus I', 'Linear Algebra', 'Statistics', 'Discrete Mathematics', 'Real Analysis', 'Differential Equations'],
    'Engineering': ['Engineering Mathematics', 'Thermodynamics', 'Materials Science', 'Control Systems', 'Fluid Mechanics'],
    'Medicine': ['Anatomy', 'Physiology', 'Pathology', 'Pharmacology', 'Clinical Medicine', 'Medical Ethics'],
  };

  const years = ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Masters', 'PhD'];

  const handleSubmit = () => {
    if (selectedUniversity && selectedCourse && selectedYear && selectedUnit) {
      onCourseSelect({
        university: selectedUniversity,
        course: selectedCourse,
        year: selectedYear,
        unit: selectedUnit,
      });
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <Card className="bg-white/80 backdrop-blur-sm border-white/50 shadow-2xl overflow-hidden">
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
          <div className="flex items-center space-x-3 mb-2">
            <BookOpen className="w-8 h-8" />
            <h2 className="text-2xl font-bold">Select Your Academic Path</h2>
          </div>
          <p className="text-blue-100">
            Choose your university, course, and unit to unlock personalized study resources
          </p>
        </div>

        <div className="p-8 space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label className="flex items-center text-sm font-semibold text-slate-700 mb-3">
                <GraduationCap className="w-4 h-4 mr-2" />
                University
              </label>
              <div className="relative">
                <select
                  value={selectedUniversity}
                  onChange={(e) => setSelectedUniversity(e.target.value)}
                  className="w-full p-4 bg-white border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none shadow-sm"
                >
                  <option value="">Select University</option>
                  {universities.map((uni) => (
                    <option key={uni} value={uni}>
                      {uni}
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 pointer-events-none" />
              </div>
            </div>

            <div className="space-y-2">
              <label className="flex items-center text-sm font-semibold text-slate-700 mb-3">
                <BookOpen className="w-4 h-4 mr-2" />
                Course
              </label>
              <div className="relative">
                <select
                  value={selectedCourse}
                  onChange={(e) => setSelectedCourse(e.target.value)}
                  className="w-full p-4 bg-white border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none shadow-sm"
                >
                  <option value="">Select Course</option>
                  {Object.keys(courses).map((course) => (
                    <option key={course} value={course}>
                      {course}
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 pointer-events-none" />
              </div>
            </div>

            <div className="space-y-2">
              <label className="flex items-center text-sm font-semibold text-slate-700 mb-3">
                Academic Year
              </label>
              <div className="relative">
                <select
                  value={selectedYear}
                  onChange={(e) => setSelectedYear(e.target.value)}
                  className="w-full p-4 bg-white border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none shadow-sm"
                >
                  <option value="">Select Year</option>
                  {years.map((year) => (
                    <option key={year} value={year}>
                      {year}
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 pointer-events-none" />
              </div>
            </div>

            <div className="space-y-2">
              <label className="flex items-center text-sm font-semibold text-slate-700 mb-3">
                Unit/Module
              </label>
              <div className="relative">
                <select
                  value={selectedUnit}
                  onChange={(e) => setSelectedUnit(e.target.value)}
                  className="w-full p-4 bg-white border border-slate-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none shadow-sm disabled:bg-slate-50 disabled:text-slate-400"
                  disabled={!selectedCourse}
                >
                  <option value="">Select Unit</option>
                  {selectedCourse && courses[selectedCourse as keyof typeof courses]?.map((unit) => (
                    <option key={unit} value={unit}>
                      {unit}
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 pointer-events-none" />
              </div>
            </div>
          </div>

          <div className="pt-6">
            <Button
              onClick={handleSubmit}
              disabled={!selectedUniversity || !selectedCourse || !selectedYear || !selectedUnit}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Start Your Academic Journey
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};
