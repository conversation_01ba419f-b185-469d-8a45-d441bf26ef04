import React from 'react';
import { format } from 'date-fns';
import { useCountdown, getUrgencyStyles } from '@/hooks/useCountdown';
import { Clock, AlertTriangle, CheckCircle } from 'lucide-react';

interface ExamCountdownCardProps {
  exam: {
    id: string;
    title: string;
    subject: string;
    exam_date: string;
    difficulty: 'easy' | 'medium' | 'hard';
    description?: string;
  };
  onClick?: () => void;
}

const ExamCountdownCard: React.FC<ExamCountdownCardProps> = ({ exam, onClick }) => {
  const examDate = new Date(exam.exam_date);
  const countdown = useCountdown(examDate);
  const styles = getUrgencyStyles(countdown.urgencyLevel);

  // Don't render if exam is past (except today)
  if (countdown.isPast) {
    return null;
  }

  const getDifficultyIcon = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return <CheckCircle className="w-3 h-3 text-green-600" />;
      case 'medium':
        return <Clock className="w-3 h-3 text-yellow-600" />;
      case 'hard':
        return <AlertTriangle className="w-3 h-3 text-red-600" />;
      default:
        return <Clock className="w-3 h-3 text-gray-600" />;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'text-green-600';
      case 'medium':
        return 'text-yellow-600';
      case 'hard':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div 
      className={`bg-white rounded-lg shadow-sm p-4 flex items-center border-l-4 transition-all hover:shadow-md cursor-pointer ${styles.border} ${styles.background}`}
      onClick={onClick}
    >
      <div className="flex-1">
        <div className="font-medium text-slate-800 mb-1">{exam.title}</div>
        <div className="flex items-center gap-2 text-xs text-slate-500">
          {exam.subject !== 'General' && (
            <>
              <span>{exam.subject}</span>
              <span>•</span>
            </>
          )}
          <div className="flex items-center gap-1">
            {getDifficultyIcon(exam.difficulty)}
            <span className={`capitalize ${getDifficultyColor(exam.difficulty)}`}>
              {exam.difficulty}
            </span>
          </div>
          {exam.description && (
            <>
              <span>•</span>
              <span className="truncate max-w-[100px]">{exam.description}</span>
            </>
          )}
        </div>
      </div>
      
      <div className="flex flex-col items-end ml-4">
        <span className="text-sm font-semibold text-slate-700 mb-1">
          {format(examDate, 'MMM d, yyyy')}
        </span>
        
        {/* Live countdown badge */}
        <div className={`text-xs font-medium px-2 py-1 rounded-full ${styles.badge}`}>
          {countdown.urgencyLevel === 'today' && countdown.hours > 0 ? (
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              <span>{countdown.displayText}</span>
            </div>
          ) : countdown.urgencyLevel === 'today' ? (
            <div className="flex items-center gap-1">
              <AlertTriangle className="w-3 h-3" />
              <span>📅 Today!</span>
            </div>
          ) : countdown.urgencyLevel === 'tomorrow' ? (
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              <span>⏰ Tomorrow</span>
            </div>
          ) : (
            countdown.displayText
          )}
        </div>
        
        {/* Additional time info for today's exams */}
        {countdown.isToday && countdown.hours > 0 && (
          <div className="text-xs text-slate-500 mt-1">
            {format(examDate, 'h:mm a')}
          </div>
        )}
      </div>
    </div>
  );
};

export default ExamCountdownCard;
