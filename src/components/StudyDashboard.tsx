
import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { BookOpen, FileText, Users, Brain, Search, Calendar, TrendingUp, Clock, Star } from 'lucide-react';
import { useProfile } from '@/hooks/useProfile';

interface StudyDashboardProps {
  selectedCourse: any;
}

export const StudyDashboard = ({ selectedCourse }: StudyDashboardProps) => {
  const { data: profile, isLoading: profileLoading } = useProfile();

  const quickActions = [
    {
      title: 'AI Note Generator',
      description: 'Generate comprehensive notes for your unit',
      icon: Search,
      color: 'from-blue-500 to-blue-600',
      bgColor: 'bg-blue-50',
      action: () => console.log('Generate notes'),
    },
    {
      title: 'Past Papers',
      description: 'Access filtered past papers with solutions',
      icon: FileText,
      color: 'from-green-500 to-green-600',
      bgColor: 'bg-green-50',
      action: () => console.log('View past papers'),
    },
    {
      title: 'Study Groups',
      description: 'Find and join study groups',
      icon: Users,
      color: 'from-purple-500 to-purple-600',
      bgColor: 'bg-purple-50',
      action: () => console.log('Join study groups'),
    },
    {
      title: 'Quiz Builder',
      description: 'Create quizzes from your notes',
      icon: Brain,
      color: 'from-orange-500 to-orange-600',
      bgColor: 'bg-orange-50',
      action: () => console.log('Build quiz'),
    },
  ];

  const recentActivity = [
    { type: 'note', title: 'Data Structures - Arrays and Lists', time: '2 hours ago', icon: BookOpen, color: 'text-blue-600' },
    { type: 'paper', title: '2023 Final Exam - Computer Science', time: '1 day ago', icon: FileText, color: 'text-green-600' },
    { type: 'quiz', title: 'Algorithms Practice Quiz', time: '2 days ago', icon: Brain, color: 'text-purple-600' },
    { type: 'group', title: 'Joined CS Study Group', time: '3 days ago', icon: Users, color: 'text-orange-600' },
  ];

  const upcomingDeadlines = [
    { title: 'Assignment 3 - Database Design', date: 'Nov 25, 2024', priority: 'high', daysLeft: 3 },
    { title: 'Midterm Exam - Data Structures', date: 'Dec 2, 2024', priority: 'high', daysLeft: 10 },
    { title: 'Project Proposal', date: 'Dec 10, 2024', priority: 'medium', daysLeft: 18 },
  ];

  const studyStats = [
    { label: 'Study Hours This Week', value: '24h', change: '+12%', icon: Clock, color: 'text-blue-600' },
    { label: 'Notes Generated', value: '12', change: '+25%', icon: BookOpen, color: 'text-green-600' },
    { label: 'Quiz Score Average', value: '87%', change: '+5%', icon: TrendingUp, color: 'text-purple-600' },
    { label: 'Study Streak', value: '7 days', change: 'New!', icon: Star, color: 'text-orange-600' },
  ];

  return (
    <div className="p-8 space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 rounded-2xl p-8 text-white relative overflow-hidden">
        <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -mr-32 -mt-32"></div>
        <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/10 rounded-full -ml-24 -mb-24"></div>
        <div className="relative">
          <h1 className="text-3xl font-bold mb-2">
            Welcome back{profileLoading ? '!' : `, ${profile?.full_name || 'Student'}!`}
          </h1>
          <p className="text-blue-100 text-lg mb-6">
            Ready to excel in {selectedCourse.unit}? Your study materials are waiting.
          </p>
          <div className="flex space-x-4">
            <Button className="bg-white/20 hover:bg-white/30 text-white border-white/30">
              Continue Learning
            </Button>
            <Button variant="outline" className="text-white border-white/30 hover:bg-white/10">
              View Progress
            </Button>
          </div>
        </div>
      </div>

      {/* Study Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {studyStats.map((stat, index) => (
          <Card key={index} className="p-4 bg-white/70 backdrop-blur-sm border-white/50">
            <div className="flex items-center justify-between mb-2">
              <stat.icon className={`w-5 h-5 ${stat.color}`} />
              <span className="text-xs text-green-600 font-medium">{stat.change}</span>
            </div>
            <p className="text-2xl font-bold text-slate-800">{stat.value}</p>
            <p className="text-sm text-slate-600">{stat.label}</p>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">Quick Actions</h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickActions.map((action, index) => (
            <Card key={index} className="p-6 bg-white/70 backdrop-blur-sm border-white/50 hover:shadow-xl transition-all duration-300 cursor-pointer group hover:-translate-y-1">
              <div className={`w-14 h-14 rounded-2xl bg-gradient-to-r ${action.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                <action.icon className="w-7 h-7 text-white" />
              </div>
              <h3 className="font-semibold text-slate-800 mb-2">{action.title}</h3>
              <p className="text-sm text-slate-600 mb-4 leading-relaxed">{action.description}</p>
              <Button variant="outline" size="sm" onClick={action.action} className="w-full">
                Get Started
              </Button>
            </Card>
          ))}
        </div>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Recent Activity */}
        <Card className="p-6 bg-white/70 backdrop-blur-sm border-white/50">
          <h3 className="text-xl font-bold text-slate-800 mb-4 flex items-center">
            <Clock className="w-5 h-5 mr-2" />
            Recent Activity
          </h3>
          <div className="space-y-4">
            {recentActivity.map((item, index) => (
              <div key={index} className="flex items-center space-x-4 p-3 rounded-xl hover:bg-white/50 transition-colors">
                <div className="w-10 h-10 bg-slate-100 rounded-xl flex items-center justify-center">
                  <item.icon className={`w-5 h-5 ${item.color}`} />
                </div>
                <div className="flex-1">
                  <p className="font-medium text-slate-800">{item.title}</p>
                  <p className="text-sm text-slate-600">{item.time}</p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Upcoming Deadlines */}
        <Card className="p-6 bg-white/70 backdrop-blur-sm border-white/50">
          <h3 className="text-xl font-bold text-slate-800 mb-4 flex items-center">
            <Calendar className="w-5 h-5 mr-2" />
            Upcoming Deadlines
          </h3>
          <div className="space-y-4">
            {upcomingDeadlines.map((deadline, index) => (
              <div key={index} className="p-4 rounded-xl border-l-4 border-l-orange-500 bg-gradient-to-r from-orange-50 to-transparent">
                <div className="flex items-center justify-between mb-2">
                  <p className="font-medium text-slate-800">{deadline.title}</p>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    deadline.priority === 'high' 
                      ? 'bg-red-100 text-red-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {deadline.priority}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <p className="text-sm text-slate-600 flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    {deadline.date}
                  </p>
                  <span className="text-sm font-medium text-orange-600">
                    {deadline.daysLeft} days left
                  </span>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
};
