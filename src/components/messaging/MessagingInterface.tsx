import React, { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, Send, MessageCircle, User, ArrowLeft } from 'lucide-react';
import { useSearchParams } from 'react-router-dom';
import { SharedDocumentMessage } from '@/components/sharing/SharedDocumentMessage';
import {
  useConversations,
  useMessages,
  useSendMessage,
  useGetOrCreateConversation,
  useMarkMessagesAsRead,
  useMessageSubscription,
  type Conversation,
  type Message
} from '@/hooks/useMessages';
import { toast } from 'sonner';

interface MessagingInterfaceProps {
  otherUserId?: string;
  otherUserName?: string;
  otherUserAvatar?: string;
}

const MessagingInterface: React.FC<MessagingInterfaceProps> = ({
  otherUserId,
  otherUserName,
  otherUserAvatar
}) => {
  const [searchParams] = useSearchParams();
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [messageInput, setMessageInput] = useState('');
  const conversationCreatedRef = useRef(false);

  // Hooks
  const { data: conversations = [], isLoading: conversationsLoading } = useConversations();
  const { data: messages = [], isLoading: messagesLoading } = useMessages(selectedConversation || '');
  const sendMessageMutation = useSendMessage();
  const getOrCreateConversationMutation = useGetOrCreateConversation();
  const markAsReadMutation = useMarkMessagesAsRead();

  // Real-time subscription for the selected conversation
  useMessageSubscription(selectedConversation || '');

  // Handle URL parameters for direct conversation access
  useEffect(() => {
    const conversationParam = searchParams.get('conversation');
    if (conversationParam && conversationParam !== selectedConversation) {
      setSelectedConversation(conversationParam);
    }
  }, [searchParams, selectedConversation]);

  // Auto-create conversation if otherUserId is provided
  useEffect(() => {
    if (otherUserId && !selectedConversation && !conversationCreatedRef.current) {
      conversationCreatedRef.current = true;
      getOrCreateConversationMutation.mutate(
        { otherUserId },
        {
          onSuccess: (data) => {
            setSelectedConversation(data.conversationId);
          },
          onError: () => {
            conversationCreatedRef.current = false; // Reset on error
          }
        }
      );
    }
  }, [otherUserId, selectedConversation]);

  const handleSendMessage = async () => {
    if (!messageInput.trim() || !selectedConversation) return;

    try {
      await sendMessageMutation.mutateAsync({
        conversationId: selectedConversation,
        content: messageInput.trim(),
      });
      setMessageInput('');
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleConversationSelect = (conversationId: string) => {
    setSelectedConversation(conversationId);
    // Mark messages as read
    markAsReadMutation.mutate({ conversationId });
  };

  const selectedConversationData = conversations.find(c => c.id === selectedConversation);

  if (conversationsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-6 h-6 animate-spin text-white" />
        <span className="ml-2 text-white">Loading conversations...</span>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 h-[600px]">
      {/* Conversations List */}
      <div className={`lg:col-span-1 ${selectedConversation ? 'hidden lg:block' : 'block'}`}>
        <Card className="bg-white/10 backdrop-blur-2xl border border-white/20 shadow-2xl h-full">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <MessageCircle className="w-5 h-5" />
              Conversations
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <ScrollArea className="h-[500px]">
              <div className="space-y-2 p-4">
                {conversations.length === 0 ? (
                  <div className="text-center py-8">
                    <MessageCircle className="w-12 h-12 text-white/30 mx-auto mb-4" />
                    <p className="text-white/60 text-sm">No conversations yet</p>
                    <p className="text-white/40 text-xs mt-1">
                      Start a conversation from someone's profile
                    </p>
                  </div>
                ) : (
                  conversations.map((conversation) => (
                    <div
                      key={conversation.id}
                      onClick={() => handleConversationSelect(conversation.id)}
                      className={`flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-colors ${
                        selectedConversation === conversation.id
                          ? 'bg-white/20'
                          : 'hover:bg-white/10'
                      }`}
                    >
                      <Avatar className="w-10 h-10">
                        <AvatarImage src={conversation.other_user_avatar || conversation.avatar_url} />
                        <AvatarFallback className="bg-gradient-to-r from-pink-500 to-violet-500 text-white">
                          {(conversation.other_user_name || conversation.name || 'U').charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium text-white truncate">
                            {conversation.other_user_name || conversation.name || 'Unknown'}
                          </h3>
                          {conversation.unread_count > 0 && (
                            <Badge className="bg-red-500 text-white text-xs">
                              {conversation.unread_count}
                            </Badge>
                          )}
                        </div>
                        {conversation.latest_message && (
                          <p className="text-sm text-white/70 truncate">
                            {conversation.latest_message}
                          </p>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>

      {/* Chat Area */}
      <div className={`lg:col-span-2 ${selectedConversation ? 'block' : 'hidden lg:block'}`}>
        {selectedConversation ? (
          <Card className="bg-white/10 backdrop-blur-2xl border border-white/20 shadow-2xl h-full flex flex-col">
            {/* Chat Header */}
            <CardHeader className="pb-4">
              <div className="flex items-center gap-3">
                <Button
                  size="icon"
                  variant="ghost"
                  className="lg:hidden text-white hover:bg-white/20"
                  onClick={() => setSelectedConversation(null)}
                >
                  <ArrowLeft className="w-5 h-5" />
                </Button>
                <Avatar className="w-8 h-8">
                  <AvatarImage src={selectedConversationData?.other_user_avatar || selectedConversationData?.avatar_url} />
                  <AvatarFallback className="bg-gradient-to-r from-pink-500 to-violet-500 text-white">
                    {(selectedConversationData?.other_user_name || selectedConversationData?.name || 'U').charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <h2 className="font-semibold text-white">
                  {selectedConversationData?.other_user_name || selectedConversationData?.name || 'Unknown'}
                </h2>
              </div>
            </CardHeader>

            {/* Messages */}
            <CardContent className="flex-1 p-0">
              <ScrollArea className="h-[400px] p-4">
                {messagesLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="w-6 h-6 animate-spin text-white" />
                  </div>
                ) : messages.length === 0 ? (
                  <div className="text-center py-8">
                    <MessageCircle className="w-12 h-12 text-white/30 mx-auto mb-4" />
                    <p className="text-white/60">No messages yet</p>
                    <p className="text-white/40 text-sm mt-1">Start the conversation!</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {messages.map((message) => {
                      // Check if this is a file message (shared document)
                      if (message.message_type === 'file' && message.file_url) {
                        return (
                          <div key={message.id} className="w-full">
                            <SharedDocumentMessage
                              message={{
                                id: message.id,
                                content: message.content,
                                file_url: message.file_url,
                                file_name: message.file_name,
                                message_type: message.message_type,
                                created_at: message.created_at,
                                sender: {
                                  id: message.sender_id,
                                  full_name: message.sender_name || 'Unknown User',
                                  avatar_url: message.sender_avatar
                                }
                              }}
                            />
                          </div>
                        );
                      }

                      // Regular text message
                      return (
                        <div
                          key={message.id}
                          className={`flex gap-3 ${message.is_own ? 'flex-row-reverse' : 'flex-row'}`}
                        >
                          {!message.is_own && (
                            <Avatar className="w-8 h-8">
                              <AvatarImage src={message.sender_avatar} />
                              <AvatarFallback className="bg-gradient-to-r from-pink-500 to-violet-500 text-white text-xs">
                                {(message.sender_name || 'U').charAt(0).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                          )}
                          <div className={`max-w-[70%] ${message.is_own ? 'text-right' : 'text-left'}`}>
                            <div
                              className={`inline-block p-3 rounded-lg ${
                                message.is_own
                                  ? 'bg-gradient-to-r from-violet-500 to-purple-600 text-white'
                                  : 'bg-white/20 text-white'
                              }`}
                            >
                              <p className="text-sm">{message.content}</p>
                            </div>
                            <p className="text-xs text-white/50 mt-1">
                              {new Date(message.created_at).toLocaleTimeString([], {
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </ScrollArea>
            </CardContent>

            {/* Message Input */}
            <div className="p-4 border-t border-white/20">
              <div className="flex gap-2">
                <Input
                  placeholder="Type a message..."
                  value={messageInput}
                  onChange={(e) => setMessageInput(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="flex-1 bg-white/10 border-white/20 text-white placeholder:text-white/50"
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!messageInput.trim() || sendMessageMutation.isPending}
                  className="bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700"
                >
                  {sendMessageMutation.isPending ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                </Button>
              </div>
            </div>
          </Card>
        ) : (
          <Card className="bg-white/10 backdrop-blur-2xl border border-white/20 shadow-2xl h-full flex items-center justify-center">
            <div className="text-center">
              <MessageCircle className="w-16 h-16 text-white/30 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">
                Select a conversation
              </h3>
              <p className="text-white/60">
                Choose a conversation from the list to start messaging
              </p>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};

export default MessagingInterface;
