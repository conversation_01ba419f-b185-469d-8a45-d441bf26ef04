import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Search, Plus, Send } from 'lucide-react';
import { useFriends } from '@/hooks/useFriends';
import { useCreateConversation } from '@/hooks/useMessaging';
import { toast } from 'sonner';

interface NewMessageDialogProps {
  children: React.ReactNode;
  onConversationCreated?: (conversationId: string) => void;
}

export const NewMessageDialog: React.FC<NewMessageDialogProps> = ({
  children,
  onConversationCreated,
}) => {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFriend, setSelectedFriend] = useState<any>(null);
  const [message, setMessage] = useState('');

  const { data: friends = [] } = useFriends();
  const createConversationMutation = useCreateConversation();

  const filteredFriends = friends.filter(friend =>
    friend.full_name?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSendMessage = async () => {
    if (!selectedFriend || !message.trim()) return;

    try {
      const result = await createConversationMutation.mutateAsync({
        participantId: selectedFriend.id,
        message: message.trim(),
      });

      setOpen(false);
      setSelectedFriend(null);
      setMessage('');
      setSearchQuery('');
      
      if (onConversationCreated) {
        onConversationCreated(result.conversationId);
      }
      
      toast.success('Message sent!');
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleClose = () => {
    setOpen(false);
    setSelectedFriend(null);
    setMessage('');
    setSearchQuery('');
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md bg-gradient-to-br from-white via-[#ecd7fa] to-[#b475ea] border-white/20">
        <DialogHeader>
          <DialogTitle className="text-gray-900">New Message</DialogTitle>
          <DialogDescription className="text-gray-700">
            Start a new conversation with one of your friends.
          </DialogDescription>
        </DialogHeader>

        {!selectedFriend ? (
          <div className="space-y-4">
            {/* Search Friends */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search friends..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 border-white/30 bg-white/80 backdrop-blur-sm"
              />
            </div>

            {/* Friends List */}
            <ScrollArea className="h-64">
              {filteredFriends.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  {searchQuery ? 'No friends found' : 'No friends yet. Add some friends to start messaging!'}
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredFriends.map((friend) => (
                    <button
                      key={friend.id}
                      onClick={() => setSelectedFriend(friend)}
                      className="w-full p-3 text-left hover:bg-white/20 rounded-lg transition-colors"
                    >
                      <div className="flex items-center space-x-3">
                        <Avatar className="w-10 h-10">
                          <AvatarImage src={friend.avatar_url} />
                          <AvatarFallback>
                            {friend.full_name?.charAt(0).toUpperCase() || 'U'}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{friend.full_name}</p>
                          <p className="text-sm text-gray-500">
                            {friend.is_online ? 'Online' : 'Offline'}
                          </p>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </ScrollArea>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Selected Friend */}
            <div className="flex items-center space-x-3 p-3 bg-white/50 backdrop-blur-sm rounded-lg border border-white/30">
              <Avatar className="w-10 h-10">
                <AvatarImage src={selectedFriend.avatar_url} />
                <AvatarFallback>
                  {selectedFriend.full_name?.charAt(0).toUpperCase() || 'U'}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <p className="font-medium">{selectedFriend.full_name}</p>
                <p className="text-sm text-gray-500">
                  {selectedFriend.is_online ? 'Online' : 'Offline'}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedFriend(null)}
              >
                Change
              </Button>
            </div>

            {/* Message Input */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Message</label>
              <Textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Type your message..."
                rows={4}
                className="border-white/30 bg-white/80 backdrop-blur-sm"
              />
            </div>

            {/* Actions */}
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={handleClose}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSendMessage}
                disabled={!message.trim() || createConversationMutation.isPending}
                className="flex-1 bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700"
              >
                <Send className="w-4 h-4 mr-2" />
                {createConversationMutation.isPending ? 'Sending...' : 'Send'}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
