
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Crown, Clock, CheckCircle } from 'lucide-react';
import { useSubscription } from '@/hooks/useSubscription';

export const SubscriptionCard = () => {
  const { subscriptionStatus, hasActiveSubscription, isLoading, startTrial } = useSubscription();

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!hasActiveSubscription) {
    return (
      <Card className="border-2 border-dashed border-gray-300">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-3">
            <Crown className="w-6 h-6 text-blue-600" />
          </div>
          <CardTitle>Unlock Premium Features</CardTitle>
          <CardDescription>
            Start your free trial to access all StudyFam premium features
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-900 mb-2">Premium Features Include:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Unlimited AI-generated notes</li>
              <li>• Advanced study planning</li>
              <li>• Priority support</li>
              <li>• Export to multiple formats</li>
            </ul>
          </div>
          <div className="space-y-2">
            <p className="text-sm text-gray-600">
              <strong>Free Trial:</strong> 1 minute • <strong>Then:</strong> $0.50/month
            </p>
            <Button onClick={startTrial} className="w-full" size="lg">
              Start Free Trial
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const isTrialActive = subscriptionStatus?.is_trial;
  const daysRemaining = subscriptionStatus?.days_remaining || 0;

  return (
    <Card className="border-green-200 bg-green-50">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Crown className="w-5 h-5 text-green-600" />
            <CardTitle className="text-green-800">
              {subscriptionStatus?.plan_name || 'Premium Plan'}
            </CardTitle>
          </div>
          <Badge variant={isTrialActive ? "secondary" : "default"}>
            {isTrialActive ? 'Trial' : 'Active'}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center space-x-2 text-green-700">
          <CheckCircle className="w-4 h-4" />
          <span className="text-sm font-medium">Premium features unlocked</span>
        </div>
        
        {isTrialActive && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-center space-x-2 text-yellow-800">
              <Clock className="w-4 h-4" />
              <span className="text-sm font-medium">
                Trial expires in {daysRemaining} day{daysRemaining !== 1 ? 's' : ''}
              </span>
            </div>
            <p className="text-xs text-yellow-700 mt-1">
              Subscribe now to continue enjoying premium features at $0.50/month
            </p>
          </div>
        )}

        {!isTrialActive && (
          <div className="text-sm text-green-700">
            <p>Next billing: {subscriptionStatus?.current_period_end ? 
              new Date(subscriptionStatus.current_period_end).toLocaleDateString() : 'N/A'}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
