import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2, X, ExternalLink, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { useUser } from '@/hooks/useAuth';
import { woocommerceService } from '@/services/woocommerceService';
import { pesapalService } from '@/services/pesapalService';

interface CheckoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPaymentSuccess: (orderData: any) => void;
  planName?: string;
}

export const CheckoutModal: React.FC<CheckoutModalProps> = ({
  isOpen,
  onClose,
  onPaymentSuccess,
  planName = 'StudyFam Subscription',
}) => {
  const { data: user } = useUser();
  const [isLoading, setIsLoading] = useState(true);
  const [checkoutUrl, setCheckoutUrl] = useState<string>('');
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentStep, setPaymentStep] = useState<'checkout' | 'payment' | 'success'>('checkout');
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    if (isOpen && user?.id) {
      initializeCheckout();
    }
  }, [isOpen, user?.id]);

  const initializeCheckout = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    try {
      // Get the checkout URL from WooCommerce
      const url = woocommerceService.getCheckoutUrl(user.id);
      setCheckoutUrl(url);
      setPaymentStep('checkout');
    } catch (error) {
      console.error('Error initializing checkout:', error);
      toast.error('Failed to initialize checkout');
    } finally {
      setIsLoading(false);
    }
  };

  const handleIframeLoad = () => {
    setIsLoading(false);
    
    // Listen for messages from the iframe
    const handleMessage = (event: MessageEvent) => {
      // Only accept messages from our WooCommerce domain
      if (!event.origin.includes('studyfam.co.ke')) return;

      if (event.data.type === 'woocommerce_checkout_complete') {
        handleCheckoutComplete(event.data);
      } else if (event.data.type === 'woocommerce_payment_redirect') {
        handlePaymentRedirect(event.data.url);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  };

  const handleCheckoutComplete = async (data: any) => {
    if (!user?.id || !user?.email) return;

    setIsProcessingPayment(true);
    setPaymentStep('payment');

    try {
      // Initialize Pesapal payment
      const pesapalResponse = await pesapalService.initializePayment(
        user.id,
        user.email,
        data.total || 50, // Default amount if not provided
        data.order_id || `order_${Date.now()}`,
        planName
      );

      if (pesapalResponse.redirect_url) {
        // Open Pesapal payment in the same iframe
        setCheckoutUrl(pesapalResponse.redirect_url);
        
        // Start polling for payment status
        pollPaymentStatus(pesapalResponse.order_tracking_id, data.order_id);
      } else {
        throw new Error('Failed to get payment URL');
      }
    } catch (error) {
      console.error('Payment initialization error:', error);
      toast.error('Failed to initialize payment');
      setPaymentStep('checkout');
    } finally {
      setIsProcessingPayment(false);
    }
  };

  const handlePaymentRedirect = (url: string) => {
    // Handle any payment redirects within the iframe
    setCheckoutUrl(url);
  };

  const pollPaymentStatus = async (orderTrackingId: string, woocommerceOrderId: string) => {
    const maxAttempts = 30; // Poll for 5 minutes (30 * 10 seconds)
    let attempts = 0;

    const poll = async () => {
      try {
        const isPaymentComplete = await pesapalService.verifyPayment(orderTrackingId);
        
        if (isPaymentComplete) {
          // Record the transaction
          await pesapalService.recordTransaction(
            user!.id,
            orderTrackingId,
            `studyfam_${Date.now()}_${user!.id}`,
            50, // Amount
            'success',
            parseInt(woocommerceOrderId)
          );

          setPaymentStep('success');
          toast.success('Payment successful! Your subscription is now active.');
          
          // Call success callback
          onPaymentSuccess({
            orderTrackingId,
            woocommerceOrderId,
            status: 'success',
          });

          return;
        }

        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 10000); // Poll every 10 seconds
        } else {
          throw new Error('Payment verification timeout');
        }
      } catch (error) {
        console.error('Payment verification error:', error);
        toast.error('Payment verification failed. Please contact support.');
        setPaymentStep('checkout');
      }
    };

    poll();
  };

  const handleRefresh = () => {
    if (iframeRef.current) {
      iframeRef.current.src = iframeRef.current.src;
      setIsLoading(true);
    }
  };

  const handleOpenExternal = () => {
    if (checkoutUrl) {
      window.open(checkoutUrl, '_blank');
    }
  };

  const renderContent = () => {
    if (paymentStep === 'success') {
      return (
        <div className="text-center py-8">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-green-800 mb-2">Payment Successful!</h3>
          <p className="text-gray-600 mb-4">Your subscription has been activated successfully.</p>
          <Button onClick={onClose} className="w-full">
            Continue to StudyFam
          </Button>
        </div>
      );
    }

    return (
      <div className="relative">
        {/* Loading overlay */}
        {isLoading && (
          <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
              <p className="text-sm text-gray-600">
                {paymentStep === 'payment' ? 'Processing payment...' : 'Loading checkout...'}
              </p>
            </div>
          </div>
        )}

        {/* Iframe container */}
        <div className="relative bg-white rounded-lg overflow-hidden" style={{ height: '600px' }}>
          {checkoutUrl && (
            <iframe
              ref={iframeRef}
              src={checkoutUrl}
              className="w-full h-full border-0"
              onLoad={handleIframeLoad}
              sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
              title="Checkout"
            />
          )}
        </div>

        {/* Controls */}
        <div className="flex justify-between items-center mt-4 pt-4 border-t">
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCw className="w-4 h-4 mr-1" />
              Refresh
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleOpenExternal}
              disabled={isLoading}
            >
              <ExternalLink className="w-4 h-4 mr-1" />
              Open in New Tab
            </Button>
          </div>
          
          {paymentStep === 'payment' && (
            <div className="flex items-center text-sm text-blue-600">
              <Loader2 className="w-4 h-4 animate-spin mr-2" />
              Waiting for payment confirmation...
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0">
        <DialogHeader className="p-6 pb-0">
          <div className="flex items-center justify-between">
            <DialogTitle>
              {paymentStep === 'success' ? 'Payment Complete' : 'Subscribe to StudyFam'}
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>
        
        <div className="p-6 pt-0">
          {renderContent()}
        </div>
      </DialogContent>
    </Dialog>
  );
};
