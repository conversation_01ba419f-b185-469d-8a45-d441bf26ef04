import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2, X, ExternalLink, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';
import { useUser } from '@/hooks/useAuth';
import { woocommerceService } from '@/services/woocommerceService';
import { supabase } from '@/integrations/supabase/client';

interface CheckoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPaymentSuccess: (orderData: any) => void;
  planName?: string;
}

export const CheckoutModal: React.FC<CheckoutModalProps> = ({
  isOpen,
  onClose,
  onPaymentSuccess,
  planName = 'StudyFam Subscription',
}) => {
  const { data: user } = useUser();
  const [isLoading, setIsLoading] = useState(true);
  const [checkoutUrl, setCheckoutUrl] = useState<string>('');
  const [paymentStep, setPaymentStep] = useState<'checkout' | 'success'>('checkout');
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    if (isOpen && user?.id) {
      initializeCheckout();
    }
  }, [isOpen, user?.id]);

  const initializeCheckout = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    try {
      // Get the checkout URL from WooCommerce
      const url = woocommerceService.getCheckoutUrl(user.id);
      setCheckoutUrl(url);
      setPaymentStep('checkout');
    } catch (error) {
      console.error('Error initializing checkout:', error);
      toast.error('Failed to initialize checkout');
    } finally {
      setIsLoading(false);
    }
  };

  const handleIframeLoad = () => {
    setIsLoading(false);

    // Try to inject our monitoring script into the iframe
    try {
      const iframe = iframeRef.current;
      if (iframe && iframe.contentDocument) {
        const script = iframe.contentDocument.createElement('script');
        script.textContent = `
          (function() {
            function sendMessageToParent(type, data = {}) {
              if (window.parent && window.parent !== window) {
                window.parent.postMessage({
                  type: type,
                  ...data,
                  timestamp: new Date().toISOString(),
                }, '*');
              }
            }

            function checkForOrderSuccess() {
              const url = window.location.href;
              const pathname = window.location.pathname;

              if (pathname.includes('/checkout/order-received/') ||
                  pathname.includes('/order-received/') ||
                  url.includes('order-received') ||
                  url.includes('thank-you') ||
                  document.querySelector('.woocommerce-order-received') ||
                  document.querySelector('.woocommerce-thankyou-order-received')) {

                const orderIdMatch = url.match(/order-received\\/(\\d+)/);
                const orderKeyMatch = url.match(/key=([^&]+)/);

                sendMessageToParent('woocommerce_payment_success', {
                  order_id: orderIdMatch ? orderIdMatch[1] : null,
                  order_key: orderKeyMatch ? orderKeyMatch[1] : null,
                  url: url,
                });
                return true;
              }
              return false;
            }

            // Check immediately and periodically
            checkForOrderSuccess();
            setInterval(checkForOrderSuccess, 3000);
          })();
        `;
        iframe.contentDocument.head.appendChild(script);
      }
    } catch (error) {
      // Cross-origin restrictions prevent script injection
      console.log('Cannot inject script due to cross-origin restrictions');
    }

    // Listen for messages from the iframe
    const handleMessage = (event: MessageEvent) => {
      // Only accept messages from our WooCommerce domain
      if (!event.origin.includes('studyfam.co.ke')) return;

      if (event.data.type === 'woocommerce_payment_success') {
        handlePaymentSuccess(event.data);
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  };

  const handlePaymentSuccess = async (data?: any) => {
    if (!user?.id) return;

    try {
      // Activate subscription in our database
      await activateSubscription();

      setPaymentStep('success');
      toast.success('Payment successful! Your subscription is now active.');

      // Call success callback
      onPaymentSuccess({
        status: 'success',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error activating subscription:', error);
      toast.error('Payment successful, but failed to activate subscription. Please contact support.');
    }
  };

  const activateSubscription = async () => {
    if (!user?.id) return;

    try {
      // Get or create subscription plan
      const { data: plans } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .limit(1)
        .single();

      if (!plans) {
        throw new Error('No active subscription plan found');
      }

      // Create or update user subscription
      const subscriptionEndDate = new Date();
      subscriptionEndDate.setMonth(subscriptionEndDate.getMonth() + 1); // 1 month subscription

      const { error: subscriptionError } = await supabase
        .from('user_subscriptions')
        .upsert({
          user_id: user.id,
          plan_id: plans.id,
          status: 'active',
          current_period_start: new Date().toISOString(),
          current_period_end: subscriptionEndDate.toISOString(),
          trial_ends_at: null, // Clear any trial
        }, {
          onConflict: 'user_id',
        });

      if (subscriptionError) {
        console.error('Error updating subscription:', subscriptionError);
        throw subscriptionError;
      }

      // Send welcome notification
      await supabase
        .from('notifications')
        .insert({
          user_id: user.id,
          title: 'Welcome to StudyFam Premium! 🎉',
          message: 'Your subscription is now active. Enjoy unlimited access to all premium features!',
          type: 'subscription',
          is_read: false,
        });

    } catch (error) {
      console.error('Error activating subscription:', error);
      throw error;
    }
  };

  const handleRefresh = () => {
    if (iframeRef.current) {
      iframeRef.current.src = iframeRef.current.src;
      setIsLoading(true);
    }
  };

  const handleOpenExternal = () => {
    if (checkoutUrl) {
      window.open(checkoutUrl, '_blank');
    }
  };

  const renderContent = () => {
    if (paymentStep === 'success') {
      return (
        <div className="text-center py-8">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-green-800 mb-2">Payment Successful!</h3>
          <p className="text-gray-600 mb-4">Your subscription has been activated successfully.</p>
          <Button onClick={onClose} className="w-full">
            Continue to StudyFam
          </Button>
        </div>
      );
    }

    return (
      <div className="relative">
        {/* Loading overlay */}
        {isLoading && (
          <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10">
            <div className="text-center">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-2" />
              <p className="text-sm text-gray-600">Loading checkout...</p>
            </div>
          </div>
        )}

        {/* Iframe container */}
        <div className="relative bg-white rounded-lg overflow-hidden" style={{ height: '600px' }}>
          {checkoutUrl && (
            <iframe
              ref={iframeRef}
              src={checkoutUrl}
              className="w-full h-full border-0"
              onLoad={handleIframeLoad}
              sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
              title="Checkout"
            />
          )}
        </div>

        {/* Controls */}
        <div className="flex justify-between items-center mt-4 pt-4 border-t">
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCw className="w-4 h-4 mr-1" />
              Refresh
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleOpenExternal}
              disabled={isLoading}
            >
              <ExternalLink className="w-4 h-4 mr-1" />
              Open in New Tab
            </Button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0">
        <DialogHeader className="p-6 pb-0">
          <div className="flex items-center justify-between">
            <DialogTitle>
              {paymentStep === 'success' ? 'Payment Complete' : 'Subscribe to StudyFam'}
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>
        
        <div className="p-6 pt-0">
          {renderContent()}
        </div>
      </DialogContent>
    </Dialog>
  );
};
