import React from "react";
import { Button } from "@/components/ui/button";
import { Globe, Lock, Filter } from "lucide-react";

type PrivacyFilterProps = {
  activeFilter: "all" | "public" | "private";
  onFilterChange: (filter: "all" | "public" | "private") => void;
  publicCount: number;
  privateCount: number;
  totalCount: number;
};

const PrivacyFilter: React.FC<PrivacyFilterProps> = ({
  activeFilter,
  onFilterChange,
  publicCount,
  privateCount,
  totalCount,
}) => {
  return (
    <div className="flex items-center gap-2 mb-4">
      <Filter className="h-4 w-4 text-white/60" />
      <span className="text-sm text-white/60 mr-2">Filter by privacy:</span>
      
      <div className="flex gap-1">
        <Button
          variant={activeFilter === "all" ? "default" : "ghost"}
          size="sm"
          onClick={() => onFilterChange("all")}
          className={`text-xs ${
            activeFilter === "all"
              ? "bg-white/20 text-white hover:bg-white/30"
              : "text-white/60 hover:text-white hover:bg-white/10"
          }`}
        >
          All ({totalCount})
        </Button>
        
        <Button
          variant={activeFilter === "public" ? "default" : "ghost"}
          size="sm"
          onClick={() => onFilterChange("public")}
          className={`text-xs flex items-center gap-1 ${
            activeFilter === "public"
              ? "bg-green-500/20 text-green-100 hover:bg-green-500/30"
              : "text-white/60 hover:text-white hover:bg-white/10"
          }`}
        >
          <Globe className="h-3 w-3" />
          Public ({publicCount})
        </Button>
        
        <Button
          variant={activeFilter === "private" ? "default" : "ghost"}
          size="sm"
          onClick={() => onFilterChange("private")}
          className={`text-xs flex items-center gap-1 ${
            activeFilter === "private"
              ? "bg-blue-500/20 text-blue-100 hover:bg-blue-500/30"
              : "text-white/60 hover:text-white hover:bg-white/10"
          }`}
        >
          <Lock className="h-3 w-3" />
          Private ({privateCount})
        </Button>
      </div>
    </div>
  );
};

export default PrivacyFilter;
