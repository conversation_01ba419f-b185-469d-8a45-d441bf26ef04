
import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, MessageSquare, FileText, Camera, X } from "lucide-react";
import { useCreateStudyGroupPost } from "@/hooks/useStudyGroups";
import { toast } from "sonner";

interface FloatingPostButtonProps {
  groupId: string;
}

// Floating action button with post type options
const FloatingPostButton: React.FC<FloatingPostButtonProps> = ({ groupId }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showTextDialog, setShowTextDialog] = useState(false);
  const [showPhotoDialog, setShowPhotoDialog] = useState(false);
  const [showDocumentDialog, setShowDocumentDialog] = useState(false);
  const [textContent, setTextContent] = useState("");
  const [photoCaption, setPhotoCaption] = useState("");
  const [documentCaption, setDocumentCaption] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);

  const createPostMutation = useCreateStudyGroupPost();

  // Handle text post creation (discussion)
  const handleTextPost = async () => {
    if (!textContent.trim()) {
      toast.error("Please enter some content");
      return;
    }

    try {
      await createPostMutation.mutateAsync({
        group_id: groupId,
        content: textContent.trim(),
        post_type: 'discussion',
      });

      setTextContent("");
      setShowTextDialog(false);
      setIsOpen(false);
      toast.success("Discussion post created successfully!");
    } catch (error) {
      console.error("Text post error:", error);
      toast.error(`Failed to create post: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Handle photo upload with caption
  const handlePhotoUpload = async () => {
    if (!selectedFile) {
      toast.error("Please select a photo");
      return;
    }

    try {
      const content = photoCaption.trim() || "📸 Shared a photo";

      await createPostMutation.mutateAsync({
        group_id: groupId,
        content: content,
        post_type: 'discussion',
        file: selectedFile,
      });

      setPhotoCaption("");
      setSelectedFile(null);
      setShowPhotoDialog(false);
      setIsOpen(false);
      toast.success("Photo uploaded successfully!");
    } catch (error) {
      console.error("Photo upload error:", error);
      toast.error(`Failed to upload photo: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Handle document upload with caption
  const handleDocumentUpload = async () => {
    if (!selectedFile) {
      toast.error("Please select a document");
      return;
    }

    try {
      const content = documentCaption.trim() || `📄 Shared: ${selectedFile.name}`;

      await createPostMutation.mutateAsync({
        group_id: groupId,
        content: content,
        post_type: 'note',
        file: selectedFile,
      });

      setDocumentCaption("");
      setSelectedFile(null);
      setShowDocumentDialog(false);
      setIsOpen(false);
      toast.success("Document uploaded successfully!");
    } catch (error) {
      console.error("Document upload error:", error);
      toast.error(`Failed to upload document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // File input handlers
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setShowDocumentDialog(true);
    }
    // Reset input
    if (e.target) e.target.value = '';
  };

  const handleImageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setShowPhotoDialog(true);
    }
    // Reset input
    if (e.target) e.target.value = '';
  };

  return (
    <>
      {/* Hidden file inputs */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf,.doc,.docx,.txt,.ppt,.pptx,.xls,.xlsx"
        onChange={handleFileInputChange}
        className="hidden"
      />
      <input
        ref={imageInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageInputChange}
        className="hidden"
      />

      {/* Text Post Dialog */}
      {showTextDialog && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Create Discussion</h3>
              <Button
                size="icon"
                variant="ghost"
                onClick={() => {
                  setShowTextDialog(false);
                  setTextContent("");
                }}
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  What's on your mind?
                </label>
                <textarea
                  value={textContent}
                  onChange={(e) => setTextContent(e.target.value)}
                  placeholder="Share your thoughts with the group..."
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-violet-500 resize-none"
                  autoFocus
                />
              </div>
            </div>

            <div className="flex gap-2 mt-6">
              <Button
                onClick={() => {
                  setShowTextDialog(false);
                  setTextContent("");
                }}
                variant="outline"
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleTextPost}
                disabled={!textContent.trim() || createPostMutation.isPending}
                className="flex-1 bg-gradient-to-r from-pink-500 to-violet-600 hover:from-pink-600 hover:to-violet-700"
              >
                {createPostMutation.isPending ? "Posting..." : "Post"}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Photo Upload Dialog */}
      {showPhotoDialog && selectedFile && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Share Photo</h3>
              <Button
                size="icon"
                variant="ghost"
                onClick={() => {
                  setShowPhotoDialog(false);
                  setPhotoCaption("");
                  setSelectedFile(null);
                }}
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            <div className="space-y-4">
              <div className="text-sm text-gray-600">
                <strong>Selected:</strong> {selectedFile.name}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Add a caption (optional)
                </label>
                <textarea
                  value={photoCaption}
                  onChange={(e) => setPhotoCaption(e.target.value)}
                  placeholder="Say something about this photo..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-violet-500 resize-none"
                  autoFocus
                />
              </div>
            </div>

            <div className="flex gap-2 mt-6">
              <Button
                onClick={() => {
                  setShowPhotoDialog(false);
                  setPhotoCaption("");
                  setSelectedFile(null);
                }}
                variant="outline"
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handlePhotoUpload}
                disabled={createPostMutation.isPending}
                className="flex-1 bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700"
              >
                {createPostMutation.isPending ? "Uploading..." : "Share Photo"}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Document Upload Dialog */}
      {showDocumentDialog && selectedFile && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Share Document</h3>
              <Button
                size="icon"
                variant="ghost"
                onClick={() => {
                  setShowDocumentDialog(false);
                  setDocumentCaption("");
                  setSelectedFile(null);
                }}
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            <div className="space-y-4">
              <div className="text-sm text-gray-600">
                <strong>Selected:</strong> {selectedFile.name}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Add a description (optional)
                </label>
                <textarea
                  value={documentCaption}
                  onChange={(e) => setDocumentCaption(e.target.value)}
                  placeholder="Describe this document or add notes..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-violet-500 resize-none"
                  autoFocus
                />
              </div>
            </div>

            <div className="flex gap-2 mt-6">
              <Button
                onClick={() => {
                  setShowDocumentDialog(false);
                  setDocumentCaption("");
                  setSelectedFile(null);
                }}
                variant="outline"
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleDocumentUpload}
                disabled={createPostMutation.isPending}
                className="flex-1 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
              >
                {createPostMutation.isPending ? "Uploading..." : "Share Document"}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Floating Action Button */}
      <div
        className="
          fixed z-40
          right-5
          bottom-20
          md:bottom-5
          pointer-events-auto
        "
      >
        {/* Action Menu */}
        {isOpen && (
          <div className="absolute bottom-16 right-0 mb-2 space-y-2">
            {/* Discussion Post */}
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-gray-700 bg-white px-3 py-1 rounded-full shadow-md">
                Discussion
              </span>
              <Button
                size="icon"
                className="
                  bg-blue-500 hover:bg-blue-600 text-white
                  shadow-lg w-12 h-12 rounded-full
                  border-2 border-white
                  transition-all duration-200
                "
                onClick={() => {
                  setShowTextDialog(true);
                  setIsOpen(false);
                }}
              >
                <MessageSquare className="w-5 h-5" />
              </Button>
            </div>

            {/* Share Document */}
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-gray-700 bg-white px-3 py-1 rounded-full shadow-md">
                Share Document
              </span>
              <Button
                size="icon"
                className="
                  bg-green-500 hover:bg-green-600 text-white
                  shadow-lg w-12 h-12 rounded-full
                  border-2 border-white
                  transition-all duration-200
                "
                onClick={() => {
                  fileInputRef.current?.click();
                }}
              >
                <FileText className="w-5 h-5" />
              </Button>
            </div>

            {/* Share Photo */}
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-gray-700 bg-white px-3 py-1 rounded-full shadow-md">
                Share Photo
              </span>
              <Button
                size="icon"
                className="
                  bg-purple-500 hover:bg-purple-600 text-white
                  shadow-lg w-12 h-12 rounded-full
                  border-2 border-white
                  transition-all duration-200
                "
                onClick={() => {
                  imageInputRef.current?.click();
                }}
              >
                <Camera className="w-5 h-5" />
              </Button>
            </div>
          </div>
        )}

        {/* Main FAB */}
        <Button
          size="icon"
          className="
            bg-gradient-to-r from-pink-500 to-violet-600 text-white
            shadow-lg
            w-14 h-14
            rounded-full
            border-4 border-white
            hover:scale-110
            transition-all duration-200
            focus-visible:ring-2 focus-visible:ring-violet-400
          "
          aria-label="New Post"
          onClick={() => setIsOpen(!isOpen)}
        >
          {isOpen ? (
            <X className="w-8 h-8" color="white" />
          ) : (
            <Plus className="w-8 h-8" color="white" />
          )}
        </Button>
      </div>
    </>
  );
};

export default FloatingPostButton;
