import React from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Users, Globe, Lock } from "lucide-react";
import { Link } from "react-router-dom";

// Group type (add members)
type Group = {
  id: string;
  name: string;
  description?: string;
  image: string;
  isPublic: boolean;
  isJoined: boolean;
  memberCount?: number;
  members: { name: string; avatar: string; id?: string }[];
};

interface GroupsGridProps {
  groups: Group[];
  emptyState: React.ReactNode;
  onJoinGroup: (groupId: string) => void;
  onLeaveGroup: (groupId: string) => void;
  onProfilePreview?: (userId: string) => void;
}

const GroupsGrid: React.FC<GroupsGridProps> = ({
  groups,
  emptyState,
  onJoinGroup,
  onLeaveGroup,
  onProfilePreview
}) => {
  if (groups.length === 0) {
    return <>{emptyState}</>;
  }
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
      {groups.map((group) => (
        <Card 
          key={group.id} 
          className="overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-white/5 hover:bg-white/10 border border-white/10 flex flex-col h-full group"
        >
          {/* Banner */}
          <div className="relative h-40 bg-gradient-to-br from-violet-500/20 to-purple-600/20">
            <img
              src={group.image}
              alt={group.name}
              className="w-full h-full object-cover opacity-90 group-hover:opacity-100 transition-opacity duration-300"
            />
            <div className="absolute top-3 right-3">
              {group.isPublic ? (
                <div className="bg-green-500/90 text-white text-xs px-3 py-1 rounded-full font-medium flex items-center gap-1 backdrop-blur-sm border border-white/20 shadow-lg">
                  <Globe className="w-3 h-3" />
                  Public
                </div>
              ) : (
                <div className="bg-blue-600/90 text-white text-xs px-3 py-1 rounded-full font-medium flex items-center gap-1 backdrop-blur-sm border border-white/20 shadow-lg">
                  <Lock className="w-3 h-3" />
                  Private
                </div>
              )}
            </div>
          </div>

          {/* Content */}
          <div className="flex flex-col flex-1 p-5">
            <h3 className="font-bold text-lg text-white mb-2">{group.name}</h3>
            <p className="text-white/70 text-sm mb-4 line-clamp-2">
              {group.description || "No description provided"}
            </p>

            {/* Member avatars */}
            {group.members && group.members.length > 0 && (
              <div className="flex items-center gap-2 mb-3">
                <div className="flex -space-x-2">
                  {group.members.slice(0, 4).map((member, index) => (
                    <img
                      key={index}
                      src={member.avatar}
                      alt={member.name}
                      className="w-8 h-8 rounded-full border-2 border-white/30 shadow-lg cursor-pointer hover:scale-110 transition-transform duration-200"
                      onClick={() => {
                        if (member.id && onProfilePreview) {
                          onProfilePreview(member.id);
                        }
                      }}
                      title={member.name}
                    />
                  ))}
                  {group.members.length > 4 && (
                    <div className="w-8 h-8 rounded-full border-2 border-white/30 bg-white/20 flex items-center justify-center text-xs text-white font-medium">
                      +{group.members.length - 4}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Member count */}
            <div className="flex items-center text-white/60 text-sm mb-5 mt-auto">
              <Users className="w-4 h-4 mr-1.5 text-white/70" />
              <span>{group.memberCount || 0} members</span>
            </div>

            {/* Actions */}
            <div className="flex gap-3">
              {group.isJoined ? (
                <Button
                  variant="outline"
                  className="flex-1 border-red-400/30 hover:border-red-400/50 text-red-300 hover:text-white hover:bg-red-500/20 transition-colors"
                  onClick={() => onLeaveGroup(group.id)}
                >
                  Leave
                </Button>
              ) : (
                <Button
                  variant="outline"
                  className="flex-1 border-green-400/30 hover:border-green-400/50 text-green-300 hover:text-white hover:bg-green-500/20 transition-colors"
                  onClick={() => onJoinGroup(group.id)}
                >
                  Join
                </Button>
              )}
              <Link
                to={`/study-groups/${group.id}`}
                className="flex-1"
              >
                <Button
                  className="w-full bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white border-0 shadow-md hover:shadow-violet-500/20 transition-all"
                >
                  View
                </Button>
              </Link>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

export default GroupsGrid;
