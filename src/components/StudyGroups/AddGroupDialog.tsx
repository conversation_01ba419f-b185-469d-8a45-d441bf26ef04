
import React, { useState, useRef } from "react";
import {
  <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>le, <PERSON>alogFooter, DialogDescription, DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@radix-ui/react-switch";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import clsx from "clsx";
import { Image, Lock, Globe, Users, Shield } from "lucide-react";

type AddGroupDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddGroup: (group: {
    name: string;
    description: string;
    isPublic: boolean;
    coverPhoto: File | null;
    coverPhotoURL: string | null;
  }) => void;
};

const AddGroupDialog: React.FC<AddGroupDialogProps> = ({
  open,
  onO<PERSON><PERSON>hang<PERSON>,
  onAddGroup,
}) => {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [isPublic, setIsPublic] = useState(true);
  const [coverPhoto, setCoverPhoto] = useState<File | null>(null);
  const [coverPhotoURL, setCoverPhotoURL] = useState<string | null>(null);

  // Reset form when closed
  React.useEffect(() => {
    if (!open) {
      setName("");
      setDescription("");
      setIsPublic(true);
      setCoverPhoto(null);
      setCoverPhotoURL(null);
    }
  }, [open]);

  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const onFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const f = e.target.files?.[0] || null;
    setCoverPhoto(f);
    if (f) {
      const url = URL.createObjectURL(f);
      setCoverPhotoURL(url);
    } else {
      setCoverPhotoURL(null);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (name.trim()) {
      onAddGroup({
        name: name.trim(),
        description: description.trim(),
        isPublic,
        coverPhoto,
        coverPhotoURL,
      });
      // Resets handled by effect after closing
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Create New Study Group
            </DialogTitle>
            <DialogDescription>
              Create a {isPublic ? 'public' : 'private'} study group for collaboration and learning together.
              {!isPublic && " Private groups are only visible to invited members."}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            {/* Cover photo upload */}
            <div>
              <Label htmlFor="group-cover-photo">Cover Photo</Label>
              <div className="flex items-center gap-3 mt-2">
                <Button
                  type="button"
                  variant="secondary"
                  className="flex items-center gap-2"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Image className="w-4 h-4" />
                  {coverPhoto ? "Change Photo" : "Upload Photo"}
                </Button>
                <input
                  ref={fileInputRef}
                  id="group-cover-photo"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={onFileChange}
                />
                {coverPhotoURL && (
                  <img
                    src={coverPhotoURL}
                    alt="Cover photo preview"
                    className="ml-4 w-14 h-14 object-cover rounded-lg border"
                  />
                )}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Optional. JPEG, PNG, or GIF under 2MB recommended.
              </p>
            </div>
            <div>
              <Label htmlFor="group-name">Group Name</Label>
              <Input
                id="group-name"
                required
                value={name}
                onChange={e => setName(e.target.value)}
                placeholder="e.g. Physics Study Group"
                autoFocus
              />
            </div>
            <div>
              <Label htmlFor="group-description">Description</Label>
              <Textarea
                id="group-description"
                value={description}
                onChange={e => setDescription(e.target.value)}
                placeholder="Describe what this group is about, what subjects you'll study, and what members can expect..."
                className="min-h-[80px]"
              />
            </div>
            <div className="space-y-3 pt-2">
              <Label className="text-sm font-medium">Group Privacy</Label>
              <div className="flex items-center gap-3">
                <Switch
                  checked={isPublic}
                  onCheckedChange={setIsPublic}
                  id="public-switch"
                />
                <Label
                  htmlFor="public-switch"
                  className={clsx(
                    "flex items-center gap-2 cursor-pointer",
                    isPublic ? "text-green-600" : "text-blue-600"
                  )}
                >
                  {isPublic ? (
                    <>
                      <Globe className="h-4 w-4" />
                      Public Group
                    </>
                  ) : (
                    <>
                      <Lock className="h-4 w-4" />
                      Private Group
                    </>
                  )}
                </Label>
              </div>
              <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-md">
                {isPublic ? (
                  <div className="flex items-start gap-2">
                    <Globe className="h-4 w-4 mt-0.5 text-green-500" />
                    <div>
                      <strong>Public groups</strong> are discoverable by all users and anyone can join them.
                      Perfect for open study communities and subject-specific discussions.
                    </div>
                  </div>
                ) : (
                  <div className="flex items-start gap-2">
                    <Shield className="h-4 w-4 mt-0.5 text-blue-500" />
                    <div>
                      <strong>Private groups</strong> are invitation-only and not visible in public discovery.
                      Ideal for close study circles, exam preparation, or exclusive collaborations.
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              type="submit"
              className="bg-gradient-to-r from-pink-500 to-violet-600 text-white"
            >
              Create Group
            </Button>
            <DialogClose asChild>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </DialogClose>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddGroupDialog;
