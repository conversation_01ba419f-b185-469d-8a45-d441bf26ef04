import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Settings, Edit3, Users, X, Upload, Check, Trash2, User<PERSON>inus, AlertTriangle } from "lucide-react";
import { useUpdateStudyGroup, useGetUserFriends, useSendGroupInvitations, useDeleteStudyGroup, useRemoveGroupMember } from "@/hooks/useStudyGroups";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";

interface GroupAdminPanelProps {
  group: any;
  isAdmin: boolean;
}

const GroupAdminPanel: React.FC<GroupAdminPanelProps> = ({ group, isAdmin }) => {
  const navigate = useNavigate();
  const [showEditModal, setShowEditModal] = useState(false);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showMembersModal, setShowMembersModal] = useState(false);
  const [editForm, setEditForm] = useState({
    name: group.name || '',
    description: group.description || '',
    cover_image: null as File | null,
  });
  const [selectedFriends, setSelectedFriends] = useState<string[]>([]);

  const updateGroupMutation = useUpdateStudyGroup();
  const { data: friends = [] } = useGetUserFriends();
  const sendInvitesMutation = useSendGroupInvitations();
  const deleteGroupMutation = useDeleteStudyGroup();
  const removeGroupMemberMutation = useRemoveGroupMember();

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      await updateGroupMutation.mutateAsync({
        id: group.id,
        name: editForm.name.trim() || undefined,
        description: editForm.description.trim() || undefined,
        cover_image: editForm.cover_image || undefined,
      });
      
      setShowEditModal(false);
      toast.success("Group updated successfully!");
    } catch (error) {
      toast.error("Failed to update group");
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setEditForm(prev => ({ ...prev, cover_image: file }));
    }
  };

  const handleInviteFriends = async () => {
    if (selectedFriends.length === 0) {
      toast.error("Please select friends to invite");
      return;
    }

    try {
      await sendInvitesMutation.mutateAsync({
        group_id: group.id,
        friend_ids: selectedFriends,
      });
      
      setSelectedFriends([]);
      setShowInviteModal(false);
      toast.success(`Invited ${selectedFriends.length} friends!`);
    } catch (error) {
      toast.error("Failed to send invitations");
    }
  };

  const toggleFriendSelection = (friendId: string) => {
    setSelectedFriends(prev => 
      prev.includes(friendId) 
        ? prev.filter(id => id !== friendId)
        : [...prev, friendId]
    );
  };

  const generateInviteLink = () => {
    const baseUrl = window.location.origin;
    return `${baseUrl}/invite/group/${group.id}`;
  };

  const copyInviteLink = () => {
    const link = generateInviteLink();
    navigator.clipboard.writeText(link);
    toast.success("Invite link copied to clipboard!");
  };

  const shareViaWhatsApp = () => {
    const link = generateInviteLink();
    const text = `Join our study group "${group.name}" on StudyHub!`;
    const url = `https://wa.me/?text=${encodeURIComponent(text + ' ' + link)}`;
    window.open(url, '_blank');
  };

  const shareViaFacebook = () => {
    const link = generateInviteLink();
    const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(link)}`;
    window.open(url, '_blank');
  };

  const shareViaTwitter = () => {
    const link = generateInviteLink();
    const text = `Join our study group "${group.name}" on StudyHub!`;
    const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(link)}`;
    window.open(url, '_blank');
  };

  const shareViaEmail = () => {
    const link = generateInviteLink();
    const subject = `Join our study group "${group.name}"`;
    const body = `Hi! I'd like to invite you to join our study group "${group.name}" on StudyHub. Click here to join: ${link}`;
    const url = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.open(url);
  };

  const handleDeleteGroup = async () => {
    try {
      await deleteGroupMutation.mutateAsync(group.id);
      toast.success("Group deleted successfully!");
      navigate("/study-groups");
    } catch (error: any) {
      toast.error(error.message || "Failed to delete group");
    }
  };

  const handleRemoveMember = async (memberId: string, memberName: string) => {
    try {
      await removeGroupMemberMutation.mutateAsync({
        groupId: group.id,
        memberId: memberId,
      });
      toast.success(`${memberName} has been removed from the group`);
      setShowMembersModal(false);
    } catch (error: any) {
      toast.error(error.message || "Failed to remove member");
    }
  };

  if (!isAdmin) return null;

  return (
    <>
      <div className="flex flex-wrap gap-2 mb-4">
        <Button
          onClick={() => setShowEditModal(true)}
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
        >
          <Edit3 size={16} />
          Edit Group
        </Button>
        <Button
          onClick={() => setShowInviteModal(true)}
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
        >
          <Users size={16} />
          Invite Friends
        </Button>
        <Button
          onClick={() => setShowMembersModal(true)}
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
        >
          <UserMinus size={16} />
          Manage Members
        </Button>
        <Button
          onClick={() => setShowDeleteModal(true)}
          variant="outline"
          size="sm"
          className="flex items-center gap-2 text-red-600 border-red-200 hover:bg-red-50"
        >
          <Trash2 size={16} />
          Delete Group
        </Button>
      </div>

      {/* Edit Group Modal */}
      {showEditModal && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Edit Group</h3>
              <Button
                size="icon"
                variant="ghost"
                onClick={() => setShowEditModal(false)}
              >
                <X className="w-5 h-5" />
              </Button>
            </div>
            
            <form onSubmit={handleEditSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Group Name
                </label>
                <input
                  type="text"
                  value={editForm.name}
                  onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-violet-500"
                  placeholder="Enter group name"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={editForm.description}
                  onChange={(e) => setEditForm(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-violet-500 resize-none"
                  placeholder="Enter group description"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Cover Photo
                </label>
                <div className="flex items-center gap-2">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                    id="cover-upload"
                  />
                  <label
                    htmlFor="cover-upload"
                    className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50"
                  >
                    <Upload size={16} />
                    {editForm.cover_image ? editForm.cover_image.name : "Choose file"}
                  </label>
                </div>
              </div>
              
              <div className="flex gap-2 mt-6">
                <Button
                  type="button"
                  onClick={() => setShowEditModal(false)}
                  variant="outline"
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={updateGroupMutation.isPending}
                  className="flex-1 bg-gradient-to-r from-violet-500 to-purple-600 hover:from-violet-600 hover:to-purple-700"
                >
                  {updateGroupMutation.isPending ? "Saving..." : "Save Changes"}
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Invite Friends Modal */}
      {showInviteModal && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Invite Friends</h3>
              <Button
                size="icon"
                variant="ghost"
                onClick={() => setShowInviteModal(false)}
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            <div className="p-6 space-y-6 overflow-y-auto max-h-96">
              {/* Platform Friends */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Invite Platform Friends</h4>
                {friends.length === 0 ? (
                  <p className="text-gray-500 text-sm">No friends found. Add friends to invite them!</p>
                ) : (
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {friends.map((friend: any) => (
                      <div key={friend.id} className="flex items-center gap-3 p-2 hover:bg-gray-50 rounded-lg">
                        <input
                          type="checkbox"
                          checked={selectedFriends.includes(friend.id)}
                          onChange={() => toggleFriendSelection(friend.id)}
                          className="w-4 h-4 text-violet-600 rounded focus:ring-violet-500"
                        />
                        <div className="w-8 h-8 bg-violet-200 rounded-full flex items-center justify-center text-sm font-semibold text-violet-800">
                          {friend.full_name[0].toUpperCase()}
                        </div>
                        <span className="text-sm font-medium text-gray-900">{friend.full_name}</span>
                      </div>
                    ))}
                  </div>
                )}
                
                {selectedFriends.length > 0 && (
                  <Button
                    onClick={handleInviteFriends}
                    disabled={sendInvitesMutation.isPending}
                    className="mt-3 bg-violet-500 hover:bg-violet-600"
                  >
                    {sendInvitesMutation.isPending ? "Sending..." : `Invite ${selectedFriends.length} Friends`}
                  </Button>
                )}
              </div>

              {/* Share Invite Link */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Share Group Invite Link</h4>
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={generateInviteLink()}
                    readOnly
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-sm"
                  />
                  <Button onClick={copyInviteLink} variant="outline" size="sm">
                    Copy
                  </Button>
                </div>
              </div>

              {/* Social Media Sharing */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Share via Social Media</h4>
                <div className="flex gap-3">
                  <Button onClick={shareViaWhatsApp} variant="outline" size="sm" className="flex items-center gap-2">
                    <span className="text-green-600">📱</span>
                    WhatsApp
                  </Button>
                  <Button onClick={shareViaFacebook} variant="outline" size="sm" className="flex items-center gap-2">
                    <span className="text-blue-600">📘</span>
                    Facebook
                  </Button>
                  <Button onClick={shareViaTwitter} variant="outline" size="sm" className="flex items-center gap-2">
                    <span className="text-blue-400">🐦</span>
                    Twitter
                  </Button>
                  <Button onClick={shareViaEmail} variant="outline" size="sm" className="flex items-center gap-2">
                    <span className="text-gray-600">✉️</span>
                    Email
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Group Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <AlertTriangle className="w-6 h-6 text-red-500" />
                <h3 className="text-lg font-semibold text-gray-900">Delete Group</h3>
              </div>
              <Button
                size="icon"
                variant="ghost"
                onClick={() => setShowDeleteModal(false)}
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            <div className="mb-6">
              <p className="text-gray-700 mb-2">
                Are you sure you want to delete <strong>"{group.name}"</strong>?
              </p>
              <p className="text-sm text-red-600">
                This action cannot be undone. All posts, comments, and group data will be permanently deleted.
              </p>
            </div>

            <div className="flex gap-2">
              <Button
                onClick={() => setShowDeleteModal(false)}
                variant="outline"
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleDeleteGroup}
                disabled={deleteGroupMutation.isPending}
                className="flex-1 bg-red-500 hover:bg-red-600 text-white"
              >
                {deleteGroupMutation.isPending ? "Deleting..." : "Delete Group"}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Manage Members Modal */}
      {showMembersModal && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Manage Members</h3>
              <Button
                size="icon"
                variant="ghost"
                onClick={() => setShowMembersModal(false)}
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            <div className="p-6 overflow-y-auto max-h-96">
              <div className="space-y-3">
                {group.members?.map((member: any) => (
                  <div key={member.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <img
                        src={member.avatar}
                        alt={member.name}
                        className="w-10 h-10 rounded-full border-2 border-gray-200 object-cover"
                      />
                      <div>
                        <div className="font-medium text-gray-900">{member.name}</div>
                        <div className="text-sm text-gray-500 capitalize">{member.role}</div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      {member.role === 'admin' && (
                        <span className="px-2 py-1 bg-violet-100 text-violet-700 text-xs font-medium rounded-full">
                          Admin
                        </span>
                      )}
                      {member.id === group.creator?.id && (
                        <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs font-medium rounded-full">
                          Creator
                        </span>
                      )}
                      {member.id !== group.creator?.id && (
                        <Button
                          onClick={() => handleRemoveMember(member.id, member.name)}
                          disabled={removeGroupMemberMutation.isPending}
                          variant="outline"
                          size="sm"
                          className="text-red-600 border-red-200 hover:bg-red-50"
                        >
                          <UserMinus size={14} className="mr-1" />
                          Remove
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {group.members?.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No members found.
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default GroupAdminPanel;
