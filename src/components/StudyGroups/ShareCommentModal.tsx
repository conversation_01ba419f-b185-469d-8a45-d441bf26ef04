import React, { useState } from 'react';
import { X, Copy, MessageCircle, Facebook, Twitter, Mail, Send, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useGetUserFriends } from '@/hooks/useStudyGroups';

interface ShareCommentModalProps {
  isOpen: boolean;
  onClose: () => void;
  commentId: string;
  commentContent: string;
  authorName: string;
  groupId: string;
  groupName: string;
}

const ShareCommentModal: React.FC<ShareCommentModalProps> = ({
  isOpen,
  onClose,
  commentId,
  commentContent,
  authorName,
  groupId,
  groupName
}) => {
  const [selectedFriends, setSelectedFriends] = useState<string[]>([]);
  const [personalMessage, setPersonalMessage] = useState('');
  const { data: friends = [] } = useGetUserFriends();

  if (!isOpen) return null;

  const baseUrl = window.location.origin;
  const commentUrl = `${baseUrl}/study-groups/${groupId}?comment=${commentId}`;
  const shareText = `Check out this comment by ${authorName} in "${groupName}": "${commentContent.substring(0, 100)}${commentContent.length > 100 ? '...' : ''}"`;

  const handleCopyLink = () => {
    navigator.clipboard.writeText(commentUrl);
    toast.success('Comment link copied to clipboard!');
    onClose();
  };

  const handleWhatsAppShare = () => {
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(shareText + ' ' + commentUrl)}`;
    window.open(whatsappUrl, '_blank');
    onClose();
  };

  const handleFacebookShare = () => {
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(commentUrl)}`;
    window.open(facebookUrl, '_blank');
    onClose();
  };

  const handleTwitterShare = () => {
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(commentUrl)}`;
    window.open(twitterUrl, '_blank');
    onClose();
  };

  const handleEmailShare = () => {
    const subject = `Check out this comment from StudyFam`;
    const body = `${shareText}\n\nView the full comment here: ${commentUrl}`;
    const emailUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.open(emailUrl);
    onClose();
  };

  const toggleFriendSelection = (friendId: string) => {
    setSelectedFriends(prev =>
      prev.includes(friendId)
        ? prev.filter(id => id !== friendId)
        : [...prev, friendId]
    );
  };

  const handleShareToFriends = async () => {
    if (selectedFriends.length === 0) {
      toast.error('Please select at least one friend to share with');
      return;
    }

    try {
      // Here you would implement the actual sharing to friends
      // This could be through direct messages, notifications, etc.
      // For now, we'll just show a success message
      toast.success(`Comment shared with ${selectedFriends.length} friend${selectedFriends.length > 1 ? 's' : ''}!`);
      onClose();
    } catch (error) {
      toast.error('Failed to share comment');
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Share Comment</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content Preview */}
        <div className="p-6 border-b border-gray-100">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm font-medium text-gray-800 mb-1">
              Comment by {authorName} in "{groupName}"
            </div>
            <div className="text-sm text-gray-600 line-clamp-3">
              {commentContent}
            </div>
          </div>
        </div>

        <div className="overflow-y-auto max-h-96">
          {/* Share to Friends */}
          <div className="p-6 border-b border-gray-100">
            <h4 className="font-medium text-gray-900 mb-3">Share with Friends</h4>
            
            {friends.length === 0 ? (
              <p className="text-gray-500 text-sm">No friends found. Add friends to share with them!</p>
            ) : (
              <>
                <div className="space-y-2 max-h-40 overflow-y-auto mb-4">
                  {friends.map((friend: any) => (
                    <div key={friend.id} className="flex items-center gap-3 p-2 hover:bg-gray-50 rounded-lg">
                      <input
                        type="checkbox"
                        checked={selectedFriends.includes(friend.id)}
                        onChange={() => toggleFriendSelection(friend.id)}
                        className="w-4 h-4 text-violet-600 rounded focus:ring-violet-500"
                      />
                      <div className="w-8 h-8 bg-violet-200 rounded-full flex items-center justify-center text-sm font-semibold text-violet-800">
                        {friend.full_name[0].toUpperCase()}
                      </div>
                      <span className="text-sm font-medium text-gray-900">{friend.full_name}</span>
                    </div>
                  ))}
                </div>

                {selectedFriends.length > 0 && (
                  <div className="space-y-3">
                    <textarea
                      value={personalMessage}
                      onChange={(e) => setPersonalMessage(e.target.value)}
                      placeholder="Add a personal message (optional)"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-violet-500 resize-none"
                      rows={2}
                    />
                    <Button
                      onClick={handleShareToFriends}
                      className="bg-violet-500 hover:bg-violet-600 flex items-center gap-2"
                    >
                      <Send size={16} />
                      Share with {selectedFriends.length} friend{selectedFriends.length > 1 ? 's' : ''}
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>

          {/* Share Options */}
          <div className="p-6 border-b border-gray-100">
            <h4 className="font-medium text-gray-900 mb-3">Share via</h4>
            <div className="grid grid-cols-2 gap-3">
              {/* Copy Link */}
              <Button
                variant="outline"
                className="flex items-center gap-2 justify-start h-12"
                onClick={handleCopyLink}
              >
                <Copy size={18} className="text-gray-600" />
                <span className="text-sm">Copy Link</span>
              </Button>

              {/* WhatsApp */}
              <Button
                variant="outline"
                className="flex items-center gap-2 justify-start h-12"
                onClick={handleWhatsAppShare}
              >
                <MessageCircle size={18} className="text-green-600" />
                <span className="text-sm">WhatsApp</span>
              </Button>

              {/* Facebook */}
              <Button
                variant="outline"
                className="flex items-center gap-2 justify-start h-12"
                onClick={handleFacebookShare}
              >
                <Facebook size={18} className="text-blue-600" />
                <span className="text-sm">Facebook</span>
              </Button>

              {/* Twitter */}
              <Button
                variant="outline"
                className="flex items-center gap-2 justify-start h-12"
                onClick={handleTwitterShare}
              >
                <Twitter size={18} className="text-blue-400" />
                <span className="text-sm">Twitter</span>
              </Button>

              {/* Email */}
              <Button
                variant="outline"
                className="flex items-center gap-2 justify-start h-12 col-span-2"
                onClick={handleEmailShare}
              >
                <Mail size={18} className="text-gray-600" />
                <span className="text-sm">Email</span>
              </Button>
            </div>
          </div>

          {/* Direct Link */}
          <div className="p-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Direct Link
            </label>
            <div className="flex items-center gap-2">
              <input
                type="text"
                value={commentUrl}
                readOnly
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm bg-gray-50 text-gray-600"
              />
              <Button
                size="sm"
                variant="outline"
                onClick={handleCopyLink}
                className="flex items-center gap-1"
              >
                <Copy size={14} />
                Copy
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShareCommentModal;
