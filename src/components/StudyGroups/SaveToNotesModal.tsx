import React, { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, FileText, Save, Plus } from 'lucide-react';
import { useUnits, useTopics, useCreateNote, useCreateUnit, useCreateTopic } from '@/hooks/useNotes';
import { toast } from 'sonner';

interface SaveToNotesModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  documentUrl: string;
  documentName: string;
  originalContent?: string;
  groupName?: string;
  authorName?: string;
}

const SaveToNotesModal: React.FC<SaveToNotesModalProps> = ({
  open,
  onOpenChange,
  documentUrl,
  documentName,
  originalContent,
  groupName,
  authorName
}) => {
  const [noteTitle, setNoteTitle] = useState(documentName || '');
  const [noteContent, setNoteContent] = useState(originalContent || '');
  const [selectedUnitId, setSelectedUnitId] = useState('');
  const [selectedTopicId, setSelectedTopicId] = useState('');
  const [showCreateUnit, setShowCreateUnit] = useState(false);
  const [showCreateTopic, setShowCreateTopic] = useState(false);
  const [newUnitName, setNewUnitName] = useState('');
  const [newTopicName, setNewTopicName] = useState('');

  const { data: units = [] } = useUnits();
  const { data: topics = [] } = useTopics(selectedUnitId);
  const createNoteMutation = useCreateNote();
  const createUnitMutation = useCreateUnit();
  const createTopicMutation = useCreateTopic();

  const handleCreateUnit = async () => {
    if (!newUnitName.trim()) {
      toast.error('Please enter a unit name');
      return;
    }

    try {
      const newUnit = await createUnitMutation.mutateAsync({
        name: newUnitName.trim(),
        color: '#8B5CF6' // Default purple color
      });
      setSelectedUnitId(newUnit.id);
      setNewUnitName('');
      setShowCreateUnit(false);
      toast.success('Unit created successfully!');
    } catch (error) {
      toast.error('Failed to create unit');
    }
  };

  const handleCreateTopic = async () => {
    if (!newTopicName.trim()) {
      toast.error('Please enter a topic name');
      return;
    }

    if (!selectedUnitId) {
      toast.error('Please select a unit first');
      return;
    }

    try {
      const newTopic = await createTopicMutation.mutateAsync({
        name: newTopicName.trim(),
        unit_id: selectedUnitId
      });
      setSelectedTopicId(newTopic.id);
      setNewTopicName('');
      setShowCreateTopic(false);
      toast.success('Topic created successfully!');
    } catch (error) {
      toast.error('Failed to create topic');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!noteTitle.trim()) {
      toast.error('Please enter a note title');
      return;
    }

    if (!selectedUnitId) {
      toast.error('Please select a unit');
      return;
    }

    if (!selectedTopicId) {
      toast.error('Please select a topic');
      return;
    }

    try {
      // Add context about where this document came from
      const contextualContent = noteContent + 
        (groupName || authorName ? `\n\n--- Document Source ---\n` : '') +
        (groupName ? `From study group: ${groupName}\n` : '') +
        (authorName ? `Shared by: ${authorName}\n` : '') +
        `Original document: ${documentName}`;

      await createNoteMutation.mutateAsync({
        title: noteTitle.trim(),
        content: contextualContent,
        unit_id: selectedUnitId,
        topic_id: selectedTopicId,
        file_url: documentUrl,
      });

      toast.success('Document saved to your notes!');
      handleClose();
    } catch (error) {
      toast.error('Failed to save document');
    }
  };

  const handleClose = () => {
    setNoteTitle(documentName || '');
    setNoteContent(originalContent || '');
    setSelectedUnitId('');
    setSelectedTopicId('');
    setShowCreateUnit(false);
    setShowCreateTopic(false);
    setNewUnitName('');
    setNewTopicName('');
    onOpenChange(false);
  };

  const isLoading = createNoteMutation.isPending || createUnitMutation.isPending || createTopicMutation.isPending;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-lg bg-white/95 backdrop-blur-2xl border border-white/20 shadow-2xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent flex items-center gap-2">
            <Save className="w-5 h-5 text-violet-600" />
            Save to Notes
          </DialogTitle>
          <DialogDescription>
            Save this document to your organized notes collection.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="note-title">Note Title *</Label>
            <Input
              id="note-title"
              value={noteTitle}
              onChange={(e) => setNoteTitle(e.target.value)}
              placeholder="Enter a title for this note"
              required
            />
          </div>

          <div>
            <Label htmlFor="unit-select">Unit *</Label>
            <div className="flex gap-2">
              <Select value={selectedUnitId} onValueChange={setSelectedUnitId}>
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="Select a unit" />
                </SelectTrigger>
                <SelectContent>
                  {units.map((unit) => (
                    <SelectItem key={unit.id} value={unit.id}>
                      {unit.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={() => setShowCreateUnit(true)}
                title="Create new unit"
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {showCreateUnit && (
            <div className="p-3 bg-gray-50 rounded-lg space-y-2">
              <Label htmlFor="new-unit">New Unit Name</Label>
              <div className="flex gap-2">
                <Input
                  id="new-unit"
                  value={newUnitName}
                  onChange={(e) => setNewUnitName(e.target.value)}
                  placeholder="Enter unit name"
                />
                <Button
                  type="button"
                  onClick={handleCreateUnit}
                  disabled={createUnitMutation.isPending}
                  size="sm"
                >
                  {createUnitMutation.isPending ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Create'}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowCreateUnit(false)}
                  size="sm"
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}

          <div>
            <Label htmlFor="topic-select">Topic *</Label>
            <div className="flex gap-2">
              <Select 
                value={selectedTopicId} 
                onValueChange={setSelectedTopicId}
                disabled={!selectedUnitId}
              >
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder={selectedUnitId ? "Select a topic" : "Select a unit first"} />
                </SelectTrigger>
                <SelectContent>
                  {topics.map((topic) => (
                    <SelectItem key={topic.id} value={topic.id}>
                      {topic.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={() => setShowCreateTopic(true)}
                disabled={!selectedUnitId}
                title="Create new topic"
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {showCreateTopic && (
            <div className="p-3 bg-gray-50 rounded-lg space-y-2">
              <Label htmlFor="new-topic">New Topic Name</Label>
              <div className="flex gap-2">
                <Input
                  id="new-topic"
                  value={newTopicName}
                  onChange={(e) => setNewTopicName(e.target.value)}
                  placeholder="Enter topic name"
                />
                <Button
                  type="button"
                  onClick={handleCreateTopic}
                  disabled={createTopicMutation.isPending}
                  size="sm"
                >
                  {createTopicMutation.isPending ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Create'}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowCreateTopic(false)}
                  size="sm"
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}

          <div>
            <Label htmlFor="note-content">Additional Notes (Optional)</Label>
            <Textarea
              id="note-content"
              value={noteContent}
              onChange={(e) => setNoteContent(e.target.value)}
              placeholder="Add any additional notes or context"
              rows={3}
            />
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              className="flex-1"
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !noteTitle.trim() || !selectedUnitId || !selectedTopicId}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Save to Notes
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default SaveToNotesModal;
