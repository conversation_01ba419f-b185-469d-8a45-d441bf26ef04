import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { UserPlus, Mail, Copy, Check, Lock } from "lucide-react";
import { toast } from "sonner";

interface InviteToGroupDialogProps {
  groupId: string;
  groupName: string;
  isPrivate: boolean;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const InviteToGroupDialog: React.FC<InviteToGroupDialogProps> = ({
  groupId,
  groupName,
  isPrivate,
  open,
  onOpenChange,
}) => {
  const [email, setEmail] = useState("");
  const [inviteLink, setInviteLink] = useState("");
  const [linkCopied, setLinkCopied] = useState(false);
  const [isGeneratingLink, setIsGeneratingLink] = useState(false);
  const [isSendingInvite, setIsSendingInvite] = useState(false);

  // Generate invite link
  const generateInviteLink = async () => {
    setIsGeneratingLink(true);
    try {
      // In a real app, this would call an API to generate a secure invite link
      const link = `${window.location.origin}/groups/join/${groupId}?token=${Date.now()}`;
      setInviteLink(link);
      toast.success("Invite link generated!");
    } catch (error) {
      toast.error("Failed to generate invite link");
    } finally {
      setIsGeneratingLink(false);
    }
  };

  // Copy invite link to clipboard
  const copyInviteLink = async () => {
    try {
      await navigator.clipboard.writeText(inviteLink);
      setLinkCopied(true);
      toast.success("Invite link copied to clipboard!");
      setTimeout(() => setLinkCopied(false), 2000);
    } catch (error) {
      toast.error("Failed to copy link");
    }
  };

  // Send email invitation
  const sendEmailInvite = async () => {
    if (!email.trim()) {
      toast.error("Please enter an email address");
      return;
    }

    setIsSendingInvite(true);
    try {
      // In a real app, this would call an API to send the email invitation
      console.log(`Sending invite to ${email} for group ${groupId}`);
      toast.success(`Invitation sent to ${email}!`);
      setEmail("");
    } catch (error) {
      toast.error("Failed to send invitation");
    } finally {
      setIsSendingInvite(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Invite to {groupName}
          </DialogTitle>
          <DialogDescription>
            {isPrivate ? (
              <div className="flex items-center gap-2">
                <Lock className="h-4 w-4 text-blue-500" />
                <span>This is a private group. Only invited users can join.</span>
              </div>
            ) : (
              "Invite others to join this public study group."
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Email Invitation */}
          <div className="space-y-3">
            <Label htmlFor="email">Send Email Invitation</Label>
            <div className="flex gap-2">
              <Input
                id="email"
                type="email"
                placeholder="Enter email address..."
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && sendEmailInvite()}
              />
              <Button
                onClick={sendEmailInvite}
                disabled={isSendingInvite || !email.trim()}
                size="sm"
              >
                {isSendingInvite ? (
                  "Sending..."
                ) : (
                  <>
                    <Mail className="h-4 w-4 mr-1" />
                    Send
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Invite Link */}
          <div className="space-y-3">
            <Label>Share Invite Link</Label>
            {!inviteLink ? (
              <Button
                onClick={generateInviteLink}
                disabled={isGeneratingLink}
                variant="outline"
                className="w-full"
              >
                {isGeneratingLink ? "Generating..." : "Generate Invite Link"}
              </Button>
            ) : (
              <div className="space-y-2">
                <div className="flex gap-2">
                  <Input
                    value={inviteLink}
                    readOnly
                    className="text-sm"
                  />
                  <Button
                    onClick={copyInviteLink}
                    size="sm"
                    variant="outline"
                  >
                    {linkCopied ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <p className="text-xs text-gray-500">
                  Share this link with people you want to invite to the group.
                  {isPrivate && " This link will expire in 7 days."}
                </p>
              </div>
            )}
          </div>

          {/* Group Info */}
          <div className="bg-gray-50 p-3 rounded-md">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Group Privacy:</span>
              <Badge variant={isPrivate ? "secondary" : "default"}>
                {isPrivate ? (
                  <>
                    <Lock className="h-3 w-3 mr-1" />
                    Private
                  </>
                ) : (
                  "Public"
                )}
              </Badge>
            </div>
            <p className="text-xs text-gray-600 mt-1">
              {isPrivate
                ? "Only invited members can see and join this group."
                : "Anyone can discover and join this group."}
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default InviteToGroupDialog;
