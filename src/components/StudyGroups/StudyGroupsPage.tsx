import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Users, Search, MoreHorizontal, Globe, Loader2, X, Save } from "lucide-react";
import ChatArea from "./ChatArea";
import ProfilePreviewDialog from "@/components/profile/ProfilePreviewDialog";
import SaveToNotesModal from "./SaveToNotesModal";
import clsx from "clsx";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { useStudyGroupPosts, useLeaveStudyGroupFromDetail, useGetUserFriends, useSendGroupInvitations } from "@/hooks/useStudyGroups";

import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import GroupAdminPanel from "./GroupAdminPanel";

// Export Message type for ChatArea import
export type Message = {
  id: string;
  user: string;
  user_id: string;
  user_avatar?: string;
  content: string;
  timestamp: string;
  channelId: string;
  kind?: "text" | "image" | "document";
  fileUrl?: string;
  fileName?: string;
  comments?: any[];
  likes?: number;
  isLiked?: boolean;
};

type StudyGroupsPageProps = {
  group: any; // Backend group data
  isMember?: boolean; // Whether user is a member of the group
  isAdmin?: boolean; // Whether user is admin of the group
  highlightPostId?: string | null; // Post ID to highlight (from shared links)
};

const TABS = [
  { id: "discussion", label: "Discussion" },
  { id: "notes", label: "Notes" },
  { id: "members", label: "Members" },
  { id: "search", label: "Search" }
];

function formatMemberCount(n: number) {
  if (n >= 1000000) return (n / 1000000).toFixed(1) + "M";
  if (n >= 1000) return (n / 1000).toFixed(1) + "K";
  return String(n);
}

export default function StudyGroupsPage({ group, isMember = true, isAdmin = false, highlightPostId }: StudyGroupsPageProps) {
  const navigate = useNavigate();

  // Backend hooks
  const { data: posts = [], isLoading: postsLoading } = useStudyGroupPosts(group.id);
  const leaveGroupMutation = useLeaveStudyGroupFromDetail();
  const { data: friends = [] } = useGetUserFriends();
  const sendInvitesMutation = useSendGroupInvitations();

  // Profile preview state
  const [profilePreviewUserId, setProfilePreviewUserId] = useState<string | null>(null);

  // Helper function to determine file type
  const getFileKind = (fileUrl: string | null, fileName: string | null): "text" | "image" | "document" => {
    if (!fileUrl) return 'text';

    // Check by file extension first (more reliable)
    if (fileName) {
      const extension = fileName.split('.').pop()?.toLowerCase();
      if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'].includes(extension || '')) {
        return 'image';
      }
      if (['pdf', 'doc', 'docx', 'txt', 'ppt', 'pptx', 'xls', 'xlsx'].includes(extension || '')) {
        return 'document';
      }
    }

    // Fallback to URL checking
    const lowerUrl = fileUrl.toLowerCase();
    if (lowerUrl.includes('image') || lowerUrl.includes('jpg') || lowerUrl.includes('png') || lowerUrl.includes('gif') || lowerUrl.includes('webp')) {
      return 'image';
    }

    // If it has a file URL but isn't an image, treat as document
    return 'document';
  };

  // Transform posts to messages for ChatArea
  const allMessages: Message[] = posts.map(post => ({
    id: post.id,
    user: post.profiles?.full_name || 'Unknown User',
    user_id: post.author_id,
    user_avatar: post.profiles?.avatar_url,
    content: post.content,
    timestamp: new Date(post.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
    channelId: "general",
    kind: getFileKind(post.file_url, post.file_name),
    fileUrl: post.file_url || undefined,
    fileName: post.file_name || undefined,
    comments: [],
    likes: 0,
    isLiked: false,
  }));

  // --- Tab state (fix: missing state) ---
  const [selectedTab, setSelectedTab] = useState<string>(TABS[0].id);

  // --- Invite modal state ---
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [selectedFriends, setSelectedFriends] = useState<string[]>([]);

  // --- Save to Notes modal state ---
  const [saveToNotesModalData, setSaveToNotesModalData] = useState<{
    isOpen: boolean;
    documentUrl: string;
    documentName: string;
    originalContent: string;
    authorName: string;
  }>({
    isOpen: false,
    documentUrl: '',
    documentName: '',
    originalContent: '',
    authorName: ''
  });

  // Handle profile preview
  const handleProfilePreview = (userId: string) => {
    setProfilePreviewUserId(userId);
  };

  // Handle starting conversation from profile preview
  const handleStartConversation = async (conversationId: string, friendName: string, friendAvatar: string) => {
    toast.success(`Conversation with ${friendName} started! Check your profile inbox.`);
  };

  // Filter messages based on active tab
  const messages = selectedTab === "notes"
    ? allMessages.filter(message => message.kind === 'document' || (message.kind === 'text' && posts.find(p => p.id === message.id)?.post_type === 'note'))
    : allMessages;

  // --- Invite functionality ---
  const handleInviteFriends = async () => {
    if (selectedFriends.length === 0) {
      toast.error("Please select friends to invite");
      return;
    }

    try {
      await sendInvitesMutation.mutateAsync({
        group_id: group.id,
        friend_ids: selectedFriends,
      });

      setSelectedFriends([]);
      setShowInviteModal(false);
      toast.success(`Invited ${selectedFriends.length} friends!`);
    } catch (error) {
      toast.error("Failed to send invitations");
    }
  };

  const toggleFriendSelection = (friendId: string) => {
    setSelectedFriends(prev =>
      prev.includes(friendId)
        ? prev.filter(id => id !== friendId)
        : [...prev, friendId]
    );
  };

  const generateInviteLink = () => {
    const baseUrl = window.location.origin;
    return `${baseUrl}/invite/group/${group.id}`;
  };

  const copyInviteLink = () => {
    const link = generateInviteLink();
    navigator.clipboard.writeText(link);
    toast.success("Invite link copied to clipboard!");
  };

  const shareViaWhatsApp = () => {
    const link = generateInviteLink();
    const text = `Join our study group "${group.name}" on StudyHub!`;
    const url = `https://wa.me/?text=${encodeURIComponent(text + ' ' + link)}`;
    window.open(url, '_blank');
  };

  const shareViaFacebook = () => {
    const link = generateInviteLink();
    const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(link)}`;
    window.open(url, '_blank');
  };

  const shareViaTwitter = () => {
    const link = generateInviteLink();
    const text = `Join our study group "${group.name}" on StudyHub!`;
    const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(link)}`;
    window.open(url, '_blank');
  };

  const shareViaEmail = () => {
    const link = generateInviteLink();
    const subject = `Join our study group "${group.name}"`;
    const body = `Hi! I'd like to invite you to join our study group "${group.name}" on StudyHub. Click here to join: ${link}`;
    const url = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.open(url);
  };

  // --- Save to Notes functionality ---
  const handleSaveToNotes = (documentUrl: string, documentName: string, content: string, authorName: string) => {
    setSaveToNotesModalData({
      isOpen: true,
      documentUrl,
      documentName,
      originalContent: content,
      authorName
    });
  };

  const closeSaveToNotesModal = () => {
    setSaveToNotesModalData({
      isOpen: false,
      documentUrl: '',
      documentName: '',
      originalContent: '',
      authorName: ''
    });
  };

  // --- Search State & Logic ---
  const [searchQuery, setSearchQuery] = useState("");

  // Combine possible searchable entities: members and posts
  const memberResults =
    searchQuery.trim() === ""
      ? []
      : (group.members || []).filter((m: any) =>
          m.name?.toLowerCase().includes(searchQuery.toLowerCase())
        );
  const noteResults =
    searchQuery.trim() === ""
      ? []
      : posts.filter((post) =>
          (post.title && post.title.toLowerCase().includes(searchQuery.toLowerCase())) ||
          post.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (post.profiles?.full_name && post.profiles.full_name.toLowerCase().includes(searchQuery.toLowerCase()))
        );

  // Actions for the More button
  const handleReport = () => {
    toast.info("Report functionality coming soon!");
  };

  const handleLeaveGroup = async () => {
    try {
      await leaveGroupMutation.mutateAsync(group.id);
      toast.success("Successfully left the group!");
      navigate("/study-groups");
    } catch (error) {
      toast.error("Failed to leave group");
    }
  };

  // --- Tab Content ---
  let tabContent: React.ReactNode = null;
  if (selectedTab === "discussion") {
    tabContent = (
      <>
        <div className="py-1 mb-6">
          {postsLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="bg-white/10 backdrop-blur-2xl rounded-3xl p-6 shadow-2xl border border-white/20">
                <Loader2 className="w-6 h-6 animate-spin text-cyan-300 mx-auto" />
                <span className="ml-2 text-white/80 block mt-2 text-center">Loading discussions...</span>
              </div>
            </div>
          ) : (
            <>
              <ChatArea
                messages={messages}
                groupId={group.id}
                groupName={group.name}
                isMember={isMember}
                onProfilePreview={handleProfilePreview}
                highlightPostId={highlightPostId}
              />
              {!isMember && (
                <div className="mt-4 p-4 bg-gradient-to-r from-violet-500/20 to-purple-500/20 backdrop-blur-sm border border-white/20 rounded-2xl text-center shadow-xl">
                  <p className="text-white font-medium mb-2">
                    Join this group to participate in discussions
                  </p>
                  <p className="text-white/70 text-sm">
                    You can view posts but need to be a member to like, comment, or create posts.
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      </>
    );
  } else if (selectedTab === "notes") {
    const notePosts = posts.filter(post => post.post_type === 'note' || post.file_url);

    tabContent = (
      <div className="py-4 px-1">
        <h3 className="text-lg font-semibold bg-gradient-to-r from-cyan-300 to-blue-300 bg-clip-text text-transparent mb-2">Shared Notes & Files</h3>
        {postsLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="bg-white/10 backdrop-blur-2xl rounded-3xl p-6 shadow-2xl border border-white/20">
              <Loader2 className="w-6 h-6 animate-spin text-cyan-300 mx-auto" />
              <span className="ml-2 text-white/80 block mt-2 text-center">Loading notes...</span>
            </div>
          </div>
        ) : notePosts.length > 0 ? (
          <ul className="space-y-3">
            {notePosts.map(post => (
              <li key={post.id} className="bg-white/10 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 flex items-center px-4 py-3 hover:bg-white/20 transition-all duration-300 hover:scale-105">
                <span className="bg-gradient-to-r from-violet-500/20 to-purple-500/20 text-cyan-300 rounded-xl p-2 mr-3 backdrop-blur-sm border border-white/20">
                  📄
                </span>
                <div className="flex-1">
                  <div className="font-medium text-white">{post.title || post.file_name || 'Untitled'}</div>
                  <div className="text-xs text-white/60">
                    by <span
                      className="cursor-pointer hover:text-cyan-300 transition-colors"
                      onClick={() => post.author_id && handleProfilePreview(post.author_id)}
                    >
                      {post.profiles?.full_name || 'Unknown User'}
                    </span> · {new Date(post.created_at).toLocaleDateString()}
                  </div>
                  {post.content && (
                    <div className="text-sm text-white/80 mt-1">{post.content}</div>
                  )}
                </div>
                <div className="flex gap-2 ml-3">
                  {post.file_url && (
                    <>
                      <a
                        href={post.file_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-cyan-300 hover:text-cyan-200 text-xs font-bold bg-white/10 px-3 py-1 rounded-xl border border-white/20 hover:bg-white/20 transition-all duration-300"
                      >
                        View
                      </a>
                      <button
                        onClick={() => handleSaveToNotes(
                          post.file_url!,
                          post.file_name || post.title || 'Document',
                          post.content || '',
                          post.profiles?.full_name || 'Unknown User'
                        )}
                        className="text-violet-300 hover:text-violet-200 text-xs font-bold bg-white/10 px-3 py-1 rounded-xl border border-white/20 hover:bg-white/20 transition-all duration-300 flex items-center gap-1"
                      >
                        <Save size={12} />
                        Save
                      </button>
                    </>
                  )}
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <div className="text-white/60 text-sm italic text-center py-8 bg-white/5 rounded-2xl border border-white/10">No notes shared yet.</div>
        )}
      </div>
    );
  } else if (selectedTab === "members") {
    tabContent = (
      <div className="py-3 px-1">
        <h3 className="text-lg font-semibold bg-gradient-to-r from-cyan-300 to-blue-300 bg-clip-text text-transparent mb-3">Group Members</h3>
        <div className="grid gap-3">
          {(group.members || []).map((m: any) =>  (
            <div key={m.name} className="flex items-center gap-3 bg-white/10 backdrop-blur-sm py-3 px-4 rounded-2xl shadow-xl border border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-105 cursor-pointer"
                 onClick={() => m.id && handleProfilePreview(m.id)}>
              <img src={m.avatar} alt={m.name} className="w-10 h-10 rounded-full border-2 border-white/30 shadow-lg" />
              <span className="font-medium text-white">{m.name}</span>
            </div>
          ))}
        </div>
      </div>
    );
  } else if (selectedTab === "search") {
    tabContent = (
      <div className="py-6 px-1 max-w-xl mx-auto">
        <div className="mb-4">
          <input
            className="w-full border-2 border-white/20 bg-white/10 backdrop-blur-sm rounded-2xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-cyan-400 text-base text-white placeholder:text-white/50 shadow-xl"
            placeholder="Search notes or members..."
            value={searchQuery}
            onChange={e => {
              setSearchQuery(e.target.value);
            }}
            autoFocus
          />
        </div>
        {searchQuery.trim() === "" ? (
          <div className="text-white/60 text-sm text-center italic bg-white/5 py-4 rounded-2xl border border-white/10">Type to search for members or notes.</div>
        ) : (
          <div>
            {(memberResults.length > 0 || noteResults.length > 0) ? (
              <>
                {memberResults.length > 0 && (
                  <div className="mb-6">
                    <div className="text-xs font-bold text-cyan-300 mb-3">Members</div>
                    <ul className="space-y-2">
                      {memberResults.map((m: any) => (
                        <li key={m.name} className="flex items-center gap-3 bg-white/10 backdrop-blur-sm py-3 px-4 rounded-2xl shadow-xl border border-white/20 hover:bg-white/20 transition-all duration-300 cursor-pointer"
                            onClick={() => m.id && handleProfilePreview(m.id)}>
                          <img src={m.avatar} alt={m.name} className="w-8 h-8 rounded-full border-2 border-white/30 shadow-lg" />
                          <span className="font-medium text-white">{m.name}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                {noteResults.length > 0 && (
                  <div>
                    <div className="text-xs font-bold text-cyan-300 mb-3">Posts & Notes</div>
                    <ul className="space-y-2">
                      {noteResults.map((post) => (
                        <li key={post.id} className="bg-white/10 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 flex items-center px-4 py-3 hover:bg-white/20 transition-all duration-300">
                          <span className="bg-gradient-to-r from-violet-500/20 to-purple-500/20 text-cyan-300 rounded-xl p-2 mr-3 backdrop-blur-sm border border-white/20">📄</span>
                          <div className="flex-1">
                            <div className="font-medium text-white">{post.title || post.content.substring(0, 50) + '...'}</div>
                            <div className="text-xs text-white/60">
                              by <span
                                className="cursor-pointer hover:text-cyan-300 transition-colors"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  post.author_id && handleProfilePreview(post.author_id);
                                }}
                              >
                                {post.profiles?.full_name || 'Unknown User'}
                              </span> · {new Date(post.created_at).toLocaleDateString()}
                            </div>
                          </div>
                          {post.file_url && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleSaveToNotes(
                                  post.file_url!,
                                  post.file_name || post.title || 'Document',
                                  post.content || '',
                                  post.profiles?.full_name || 'Unknown User'
                                );
                              }}
                              className="ml-3 text-violet-300 hover:text-violet-200 text-xs font-bold bg-white/10 px-3 py-1 rounded-xl border border-white/20 hover:bg-white/20 transition-all duration-300 flex items-center gap-1"
                            >
                              <Save size={12} />
                              Save
                            </button>
                          )}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </>
            ) : (
              <div className="text-white/60 text-sm italic text-center py-4 bg-white/5 rounded-2xl border border-white/10">No results found.</div>
            )}
          </div>
        )}
      </div>
    );
  }

  // --- Main Render ---
  return (
    <div
      className="
        bg-white/10 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/20
        max-w-4xl w-full mx-auto
        mt-4 mb-8 pb-8
        px-0 xs:px-1 sm:px-2 lg:px-4
        relative overflow-hidden
        animate-fade-in hover:shadow-3xl transition-all duration-500
      "
    >
      {/* Banner */}
      <div className="relative w-full h-32 xs:h-36 sm:h-44 overflow-hidden rounded-t-3xl">
        <img
          src={group.cover_image_url || group.image}
          alt={`${group.name} banner`}
          className="absolute w-full h-full object-cover transition-transform duration-700 hover:scale-110"
          style={{ objectPosition: "center" }}
        />
        <div className="absolute inset-0 bg-gradient-to-r from-pink-500/30 to-violet-500/20" />
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />

        {/* Floating elements on banner */}
        <div className="absolute top-4 right-4 w-3 h-3 bg-white/40 rounded-full animate-pulse"></div>
        <div className="absolute bottom-6 left-6 w-2 h-2 bg-cyan-300/60 rounded-full animate-pulse delay-500"></div>
        <div className="absolute top-1/2 right-1/4 w-1.5 h-1.5 bg-pink-300/50 rounded-full animate-pulse delay-1000"></div>
      </div>
      
      {/* Header */}
      <div className="px-4 pt-4 pb-1 sm:px-6">
        <div className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-cyan-300 via-blue-300 to-indigo-300 bg-clip-text text-transparent mb-0 animate-fade-in" style={{ letterSpacing: "-0.5px" }}>{group.name}</div>
        <div className="flex items-center gap-2 text-white/70 text-sm mt-1 mb-2">
          {group.privacy === 'public' ? (
            <>
              <Globe className="w-4 h-4 mr-1" />
              Public group
            </>
          ) : (
            <>
              <Users className="w-4 h-4 mr-1" />
              Private group
            </>
          )}
          <span className="ml-2 text-white/50">· {formatMemberCount(group.member_count)} members</span>
          {/* More (ellipsis) button beside member count on all screen sizes */}
          <div className="ml-1">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  size="icon"
                  variant="outline"
                  className="border-white/20 bg-white/10 backdrop-blur-sm text-white hover:bg-white/20 w-8 h-8 p-0 transition-all duration-300 hover:scale-110 shadow-xl"
                >
                  <MoreHorizontal className="w-5 h-5" />
                </Button>
              </PopoverTrigger>
              <PopoverContent align="start" className="w-40 p-1 bg-white/10 backdrop-blur-2xl border border-white/20 shadow-2xl rounded-2xl" sideOffset={6}>
                <button
                  className="block w-full px-4 py-2 text-left text-sm text-red-300 hover:bg-red-500/20 rounded-xl transition-all duration-300"
                  onClick={handleLeaveGroup}
                >
                  Leave Group
                </button>
                <button
                  className="block w-full px-4 py-2 text-left text-sm text-white/80 hover:bg-white/10 rounded-xl transition-all duration-300"
                  onClick={handleReport}
                >
                  Report
                </button>
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </div>

      {/* Admin Panel */}
      <div className="px-4 sm:px-6">
        <GroupAdminPanel group={group} isAdmin={isAdmin} />
      </div>

      {/* Actions Row */}
      <div className="flex flex-col sm:flex-row gap-2 justify-center sm:justify-between px-4 sm:px-6 mt-2 items-center">
        <div className="flex gap-2 justify-center w-full sm:w-auto">
          {isMember && (
            <Button
              className={`bg-gradient-to-r from-pink-500 to-violet-500 hover:from-pink-600 hover:to-violet-600 text-white px-5 font-semibold shadow-2xl hover:shadow-pink-500/25 transition-all duration-300 hover:scale-105 border border-white/20`}
              size="sm"
              onClick={() => setShowInviteModal(true)}
            >
              + Invite
            </Button>
          )}
          <Button
            variant="outline"
            className={clsx(
              "border-white/20 bg-white/10 backdrop-blur-sm text-white hover:bg-white/20 px-4 transition-all duration-300 hover:scale-105 shadow-xl",
              selectedTab === "members" && "ring-2 ring-cyan-400 bg-cyan-500/20"
            )}
            size="sm"
            onClick={() => setSelectedTab("members")}
          >
            <Users className="w-4 h-4 mr-2" />
            Members
          </Button>
          <Button
            variant="outline"
            className={clsx(
              "border-white/20 bg-white/10 backdrop-blur-sm text-white hover:bg-white/20 px-4 transition-all duration-300 hover:scale-105 shadow-xl",
              selectedTab === "search" && "ring-2 ring-cyan-400 bg-cyan-500/20"
            )}
            size="sm"
            onClick={() => setSelectedTab("search")}
          >
            <Search className="w-4 h-4 mr-2" />
            Search
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <div className="my-3 border-b border-white/20 px-4 sm:px-6">
        <nav className="flex gap-2 text-sm font-semibold overflow-x-auto">
          {TABS.map((tab) => (
            <button
              key={tab.id}
              className={clsx(
                "py-2 px-3 sm:px-4 border-b-2 transition-all min-w-[94px] text-sm rounded-t-xl hover:bg-white/10",
                selectedTab === tab.id
                  ? "border-cyan-400 text-cyan-300 bg-white/10 shadow-lg"
                  : "border-transparent text-white/60 hover:text-cyan-300 hover:border-cyan-400/50"
              )}
              onClick={() => setSelectedTab(tab.id)}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="px-2 xs:px-3 sm:px-6">{tabContent}</div>

      {/* Invite Friends Modal */}
      {showInviteModal && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white/10 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/20 max-w-2xl w-full max-h-[80vh] overflow-hidden animate-fade-in">
            <div className="flex items-center justify-between p-6 border-b border-white/20">
              <h3 className="text-lg font-semibold bg-gradient-to-r from-cyan-300 to-blue-300 bg-clip-text text-transparent">Invite Friends to {group.name}</h3>
              <Button
                size="icon"
                variant="ghost"
                className="text-white hover:bg-white/20 transition-all duration-300"
                onClick={() => setShowInviteModal(false)}
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            <div className="p-6 space-y-6 overflow-y-auto max-h-96">
              {/* Platform Friends */}
              <div>
                <h4 className="font-medium text-white mb-3">Invite Platform Friends</h4>
                {friends.length === 0 ? (
                  <p className="text-white/60 text-sm bg-white/5 p-3 rounded-xl border border-white/10">No friends found. Add friends to invite them!</p>
                ) : (
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {friends.map((friend: any) => (
                      <div key={friend.id} className="flex items-center gap-3 p-3 hover:bg-white/10 rounded-2xl transition-all duration-300 border border-white/10">
                        <input
                          type="checkbox"
                          checked={selectedFriends.includes(friend.id)}
                          onChange={() => toggleFriendSelection(friend.id)}
                          className="w-4 h-4 text-cyan-400 rounded focus:ring-cyan-400 bg-white/10 border-white/20"
                        />
                        <div className="w-8 h-8 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-full flex items-center justify-center text-sm font-semibold text-cyan-300 border border-white/20">
                          {friend.full_name[0].toUpperCase()}
                        </div>
                        <span className="text-sm font-medium text-white">{friend.full_name}</span>
                      </div>
                    ))}
                  </div>
                )}

                {selectedFriends.length > 0 && (
                  <Button
                    onClick={handleInviteFriends}
                    disabled={sendInvitesMutation.isPending}
                    className="mt-3 bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white shadow-xl border border-white/20"
                  >
                    {sendInvitesMutation.isPending ? "Sending..." : `Invite ${selectedFriends.length} Friends`}
                  </Button>
                )}
              </div>

              {/* Share Invite Link */}
              <div>
                <h4 className="font-medium text-white mb-3">Share Group Invite Link</h4>
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={generateInviteLink()}
                    readOnly
                    className="flex-1 px-3 py-2 border border-white/20 rounded-2xl bg-white/10 backdrop-blur-sm text-white text-sm placeholder:text-white/50"
                  />
                  <Button onClick={copyInviteLink} variant="outline" size="sm" className="border-white/20 bg-white/10 text-white hover:bg-white/20">
                    Copy
                  </Button>
                </div>
              </div>

              {/* Social Media Sharing */}
              <div>
                <h4 className="font-medium text-white mb-3">Share via Social Media</h4>
                <div className="flex gap-3 flex-wrap">
                  <Button onClick={shareViaWhatsApp} variant="outline" size="sm" className="flex items-center gap-2 border-white/20 bg-white/10 text-white hover:bg-white/20">
                    <span className="text-green-400">📱</span>
                    WhatsApp
                  </Button>
                  <Button onClick={shareViaFacebook} variant="outline" size="sm" className="flex items-center gap-2 border-white/20 bg-white/10 text-white hover:bg-white/20">
                    <span className="text-blue-400">📘</span>
                    Facebook
                  </Button>
                  <Button onClick={shareViaTwitter} variant="outline" size="sm" className="flex items-center gap-2 border-white/20 bg-white/10 text-white hover:bg-white/20">
                    <span className="text-cyan-400">🐦</span>
                    Twitter
                  </Button>
                  <Button onClick={shareViaEmail} variant="outline" size="sm" className="flex items-center gap-2 border-white/20 bg-white/10 text-white hover:bg-white/20">
                    <span className="text-white/80">✉️</span>
                    Email
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Profile Preview Dialog */}
      <ProfilePreviewDialog
        userId={profilePreviewUserId}
        open={!!profilePreviewUserId}
        onOpenChange={(open) => {
          if (!open) {
            setProfilePreviewUserId(null);
          }
        }}
      />

      {/* Save to Notes Modal */}
      <SaveToNotesModal
        open={saveToNotesModalData.isOpen}
        onOpenChange={closeSaveToNotesModal}
        documentUrl={saveToNotesModalData.documentUrl}
        documentName={saveToNotesModalData.documentName}
        originalContent={saveToNotesModalData.originalContent}
        groupName={group.name}
        authorName={saveToNotesModalData.authorName}
      />
    </div>
  );
}
