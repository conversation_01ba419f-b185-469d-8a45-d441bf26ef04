
import React from "react";
import { Hash, Mic, Video, MessageSquare, Users } from "lucide-react";

type Channel = {
  id: string;
  name: string;
};

type ChannelListProps = {
  channels: Channel[];
  selectedChannelId: string;
  onSelectChannel: (channelId: string) => void;
};

// Use lucide icon based on name
function channelIcon(id: string) {
  if (id === "general" || id === "homework" || id === "literature" || id === "lab") return <MessageSquare className="w-4 h-4 mr-2" />;
  if (id === "voice") return <Mic className="w-4 h-4 mr-2" />;
  if (id === "video") return <Video className="w-4 h-4 mr-2" />;
  return <Hash className="w-4 h-4 mr-2" />;
}

export default function ChannelList({ channels, selectedChannelId, onSelectChannel }: ChannelListProps) {
  return (
    <aside className="w-56 bg-[#f5f7fa] h-full border-r flex flex-col pt-4">
      <header className="px-4 pb-2 mb-2 border-b text-xs font-semibold text-violet-700">
        Channels
      </header>
      <ul>
        {channels.map(channel => (
          <li key={channel.id}>
            <button
              className={
                "w-full flex items-center px-4 py-2 rounded-lg mb-1 text-left " +
                (selectedChannelId === channel.id
                  ? "bg-violet-100 text-violet-700 font-medium"
                  : "text-gray-700 hover:bg-violet-50")
              }
              onClick={() => onSelectChannel(channel.id)}
            >
              {channelIcon(channel.id)}
              <span>#{channel.name}</span>
            </button>
          </li>
        ))}
      </ul>
    </aside>
  );
}
