
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useCreateStudyGroupPost } from "@/hooks/useStudyGroups";
import { Send, Paperclip, Image } from "lucide-react";
import { toast } from "sonner";

interface MessageInputProps {
  groupId: string;
  groupName: string;
}

export function MessageInput({ groupId, groupName }: MessageInputProps) {
  const [content, setContent] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const createPostMutation = useCreateStudyGroupPost();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!content.trim() && !file) {
      toast.error("Please enter a message or select a file");
      return;
    }

    try {
      await createPostMutation.mutateAsync({
        group_id: groupId,
        content: content.trim(),
        post_type: 'discussion',
        file: file || undefined,
      });
      
      setContent("");
      setFile(null);
      toast.success("Message sent!");
    } catch (error) {
      toast.error("Failed to send message");
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
    }
  };

  return (
    <div className="bg-white/10 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/20 p-4">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="flex gap-2">
          <input
            type="text"
            placeholder={`Share something with ${groupName}...`}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            className="flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-2xl text-white placeholder:text-white/50 focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent"
          />
          
          <input
            type="file"
            id="file-upload"
            className="hidden"
            onChange={handleFileSelect}
            accept="image/*,.pdf,.doc,.docx,.txt"
          />
          
          <label
            htmlFor="file-upload"
            className="p-3 bg-white/10 hover:bg-white/20 rounded-2xl cursor-pointer transition-colors border border-white/20"
          >
            <Paperclip className="w-5 h-5 text-white" />
          </label>
          
          <Button
            type="submit"
            disabled={createPostMutation.isPending}
            className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 rounded-2xl px-6"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>

        {file && (
          <div className="flex items-center gap-2 text-white/80 text-sm">
            <Paperclip className="w-4 h-4" />
            <span>{file.name}</span>
            <button
              type="button"
              onClick={() => setFile(null)}
              className="text-red-400 hover:text-red-300 ml-2"
            >
              Remove
            </button>
          </div>
        )}
      </form>
    </div>
  );
}

export default MessageInput;
