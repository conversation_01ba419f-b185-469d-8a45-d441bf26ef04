
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useCreateComment, useLikePost, useLikeComment, useCreateCommentReply } from "@/hooks/useStudyGroups";
import { Heart, MessageCircle, Share2, Send, Reply } from "lucide-react";
import { toast } from "sonner";
import { MessageInput } from "./MessageInput";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";

export type Message = {
  id: string;
  user: string;
  user_id: string;
  user_avatar?: string;
  content: string;
  timestamp: string;
  channelId: string;
  kind?: "text" | "image" | "document";
  fileUrl?: string;
  fileName?: string;
  comments?: any[];
  likes?: number;
  isLiked?: boolean;
};

type ChatAreaProps = {
  messages: Message[];
  groupId: string;
  groupName: string;
  isMember: boolean;
  onProfilePreview?: (userId: string) => void;
  highlightPostId?: string | null;
};

export default function ChatArea({ 
  messages, 
  groupId, 
  groupName, 
  isMember,
  onProfilePreview,
  highlightPostId 
}: ChatAreaProps) {
  const [expandedComments, setExpandedComments] = useState<Set<string>>(new Set());
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [commentInputs, setCommentInputs] = useState<{ [key: string]: string }>({});
  const [replyInputs, setReplyInputs] = useState<{ [key: string]: string }>({});

  const createCommentMutation = useCreateComment();
  const likePostMutation = useLikePost();
  const likeCommentMutation = useLikeComment();
  const createCommentReplyMutation = useCreateCommentReply();

  const toggleComments = (messageId: string) => {
    const newExpanded = new Set(expandedComments);
    if (newExpanded.has(messageId)) {
      newExpanded.delete(messageId);
    } else {
      newExpanded.add(messageId);
    }
    setExpandedComments(newExpanded);
  };

  const handleLikePost = async (postId: string) => {
    if (!isMember) {
      toast.error("You must be a member to like posts");
      return;
    }
    
    try {
      await likePostMutation.mutateAsync(postId);
    } catch (error) {
      toast.error("Failed to like post");
    }
  };

  const handleAddComment = async (postId: string) => {
    if (!isMember) {
      toast.error("You must be a member to comment");
      return;
    }

    const content = commentInputs[postId]?.trim();
    if (!content) return;

    try {
      await createCommentMutation.mutateAsync({
        postId: postId,
        content: content,
      });
      setCommentInputs({ ...commentInputs, [postId]: '' });
      toast.success("Comment added!");
    } catch (error) {
      toast.error("Failed to add comment");
    }
  };

  const handleLikeComment = async (commentId: string) => {
    if (!isMember) {
      toast.error("You must be a member to like comments");
      return;
    }
    
    try {
      await likeCommentMutation.mutateAsync(commentId);
    } catch (error) {
      toast.error("Failed to like comment");
    }
  };

  const handleAddReply = async (commentId: string) => {
    if (!isMember) {
      toast.error("You must be a member to reply");
      return;
    }

    const content = replyInputs[commentId]?.trim();
    if (!content) return;

    try {
      await createCommentReplyMutation.mutateAsync({
        commentId: commentId,
        content: content,
      });
      setReplyInputs({ ...replyInputs, [commentId]: '' });
      setReplyingTo(null);
      toast.success("Reply added!");
    } catch (error) {
      toast.error("Failed to add reply");
    }
  };

  const handleProfileClick = (userId: string) => {
    if (onProfilePreview) {
      onProfilePreview(userId);
    }
  };

  if (messages.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="bg-white/10 backdrop-blur-2xl rounded-3xl p-8 shadow-2xl border border-white/20">
          <MessageCircle className="w-12 h-12 text-cyan-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-white mb-2">No posts yet</h3>
          <p className="text-white/70">Be the first to start a discussion in {groupName}!</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {messages.map((message) => (
        <div
          key={message.id}
          className={`bg-white/10 backdrop-blur-2xl rounded-3xl shadow-2xl border border-white/20 p-6 transition-all duration-300 hover:shadow-3xl hover:scale-[1.02] ${
            highlightPostId === message.id ? 'ring-2 ring-cyan-400 bg-cyan-500/20' : ''
          }`}
        >
          {/* Post Header */}
          <div className="flex items-center gap-3 mb-4">
            <Avatar 
              className="w-12 h-12 border-2 border-white/30 shadow-lg cursor-pointer hover:scale-110 transition-transform duration-200"
              onClick={() => handleProfileClick(message.user_id)}
            >
              <AvatarImage src={message.user_avatar} alt={message.user} />
              <AvatarFallback className="bg-gradient-to-br from-pink-400 to-violet-400 text-white font-bold">
                {message.user.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <div 
                className="font-semibold text-white cursor-pointer hover:text-cyan-300 transition-colors"
                onClick={() => handleProfileClick(message.user_id)}
              >
                {message.user}
              </div>
              <div className="text-sm text-white/60">{message.timestamp}</div>
            </div>
          </div>

          {/* Post Content */}
          <div className="mb-4">
            <div className="text-white whitespace-pre-wrap">{message.content}</div>
            
            {/* File/Image Display */}
            {message.kind === 'image' && message.fileUrl && (
              <div className="mt-4">
                <img 
                  src={message.fileUrl} 
                  alt={message.fileName || "Shared image"} 
                  className="max-w-full rounded-2xl shadow-xl border border-white/20"
                />
              </div>
            )}
            
            {message.kind === 'document' && message.fileUrl && (
              <div className="mt-4 p-4 bg-white/5 rounded-2xl border border-white/20">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-full flex items-center justify-center">
                    <span className="text-cyan-300">📄</span>
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-white">{message.fileName}</div>
                    <a 
                      href={message.fileUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-cyan-300 hover:text-cyan-200 text-sm"
                    >
                      Download
                    </a>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Post Actions */}
          <div className="flex items-center gap-4 text-white/70">
            <button
              onClick={() => handleLikePost(message.id)}
              disabled={!isMember || likePostMutation.isPending}
              className={`flex items-center gap-2 hover:text-red-400 transition-colors ${
                message.isLiked ? 'text-red-400' : ''
              }`}
            >
              <Heart className={`w-4 h-4 ${message.isLiked ? 'fill-current' : ''}`} />
              <span>{message.likes || 0}</span>
            </button>
            
            <button
              onClick={() => toggleComments(message.id)}
              className="flex items-center gap-2 hover:text-cyan-400 transition-colors"
            >
              <MessageCircle className="w-4 h-4" />
              <span>{message.comments?.length || 0}</span>
            </button>
          </div>

          {/* Comments Section */}
          {expandedComments.has(message.id) && (
            <div className="mt-6 space-y-4">
              {/* Existing Comments */}
              {message.comments?.map((comment: any) => (
                <div key={comment.id} className="bg-white/5 rounded-2xl p-4">
                  <div className="flex items-start gap-3">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={comment.author_avatar} alt={comment.author_name} />
                      <AvatarFallback className="bg-gradient-to-br from-pink-400 to-violet-400 text-white text-sm">
                        {comment.author_name?.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="font-medium text-white text-sm">{comment.author_name}</div>
                      <div className="text-white/80 text-sm mt-1">{comment.content}</div>
                      <div className="flex items-center gap-3 mt-2">
                        <button
                          onClick={() => handleLikeComment(comment.id)}
                          disabled={!isMember}
                          className="flex items-center gap-1 text-xs text-white/60 hover:text-red-400"
                        >
                          <Heart className="w-3 h-3" />
                          <span>{comment.likes || 0}</span>
                        </button>
                        <button
                          onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                          className="text-xs text-white/60 hover:text-cyan-400"
                        >
                          Reply
                        </button>
                      </div>
                      
                      {/* Reply Input */}
                      {replyingTo === comment.id && isMember && (
                        <div className="mt-3">
                          <div className="flex gap-2">
                            <input
                              type="text"
                              placeholder="Write a reply..."
                              value={replyInputs[comment.id] || ''}
                              onChange={(e) => setReplyInputs({ ...replyInputs, [comment.id]: e.target.value })}
                              className="flex-1 px-3 py-2 bg-white/10 border border-white/20 rounded-xl text-white placeholder:text-white/50 text-sm"
                              onKeyPress={(e) => {
                                if (e.key === 'Enter') {
                                  handleAddReply(comment.id);
                                }
                              }}
                            />
                            <Button
                              size="sm"
                              onClick={() => handleAddReply(comment.id)}
                              disabled={createCommentReplyMutation.isPending}
                              className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600"
                            >
                              <Send className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {/* Add Comment Input */}
              {isMember && (
                <div className="flex gap-2 mt-4">
                  <input
                    type="text"
                    placeholder="Write a comment..."
                    value={commentInputs[message.id] || ''}
                    onChange={(e) => setCommentInputs({ ...commentInputs, [message.id]: e.target.value })}
                    className="flex-1 px-4 py-2 bg-white/10 border border-white/20 rounded-xl text-white placeholder:text-white/50"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        handleAddComment(message.id);
                      }
                    }}
                  />
                  <Button
                    onClick={() => handleAddComment(message.id)}
                    disabled={createCommentMutation.isPending}
                    className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600"
                  >
                    <Send className="w-4 h-4" />
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      ))}

      {/* Message Input at Bottom */}
      {isMember && (
        <div className="mt-8">
          <MessageInput groupId={groupId} groupName={groupName} />
        </div>
      )}
    </div>
  );
}
