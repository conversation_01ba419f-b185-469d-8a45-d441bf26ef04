
import React from "react";

type StudyGroupsTabsProps = {
  activeTab: "my" | "discover";
  onTabChange: (newTab: "my" | "discover") => void;
  myGroupsCount: number;
  discoverGroupsCount: number;
};

const StudyGroupsTabs: React.FC<StudyGroupsTabsProps> = ({
  activeTab,
  onTabChange,
  myGroupsCount,
  discoverGroupsCount,
}) => (
  <div className="relative">
    <div className="flex">
      <button
        onClick={() => onTabChange('my')}
        className={`
          flex-1 py-3 px-4 text-sm font-medium text-center relative
          ${activeTab === 'my' 
            ? 'text-white' 
            : 'text-white/60 hover:text-white/80'}
        `}
        aria-current={activeTab === 'my' ? 'page' : undefined}
      >
        <div className="flex flex-col items-center">
          <span>My Groups</span>
          <span 
            className={`mt-1 text-xs px-2 py-0.5 rounded-full ${
              activeTab === 'my' 
                ? 'bg-white/10 text-white' 
                : 'text-white/60'
            }`}
          >
            {myGroupsCount}
          </span>
        </div>
        {activeTab === 'my' && (
          <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-violet-400 to-purple-500"></div>
        )}
      </button>
      
      <button
        onClick={() => onTabChange('discover')}
        className={`
          flex-1 py-3 px-4 text-sm font-medium text-center relative
          ${activeTab === 'discover' 
            ? 'text-white' 
            : 'text-white/60 hover:text-white/80'}
        `}
        aria-current={activeTab === 'discover' ? 'page' : undefined}
      >
        <div className="flex flex-col items-center">
          <span>Discover</span>
          <span 
            className={`mt-1 text-xs px-2 py-0.5 rounded-full ${
              activeTab === 'discover' 
                ? 'bg-white/10 text-white' 
                : 'text-white/60'
            }`}
          >
            {discoverGroupsCount}
          </span>
        </div>
        {activeTab === 'discover' && (
          <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-violet-400 to-purple-500"></div>
        )}
      </button>
    </div>
    <div className="h-px bg-white/10 w-full"></div>
  </div>
);

export default StudyGroupsTabs;
