
import React from "react";

// Sidebar - Discord style "servers" (groups) icons
type Group = {
  id: string;
  name: string;
  icon: string;
};

type SidebarProps = {
  groups: Group[];
  selectedGroupId: string;
  onSelectGroup: (groupId: string) => void;
};

export default function Sidebar({ groups, selectedGroupId, onSelectGroup }: SidebarProps) {
  return (
    <nav className="h-full w-16 bg-gradient-to-b from-indigo-600 to-violet-600 flex flex-col items-center py-4 gap-2 border-r shadow-lg">
      {groups.map(group => (
        <button
          key={group.id}
          title={group.name}
          className={`mb-2 w-12 h-12 rounded-2xl flex items-center justify-center text-2xl font-bold transition-all duration-150
            ${selectedGroupId === group.id
              ? "bg-white text-indigo-700 shadow-lg scale-105"
              : "text-white hover:bg-indigo-500/70"
            }`}
          onClick={() => onSelectGroup(group.id)}
        >
          <span>{group.icon}</span>
        </button>
      ))}
    </nav>
  );
}
