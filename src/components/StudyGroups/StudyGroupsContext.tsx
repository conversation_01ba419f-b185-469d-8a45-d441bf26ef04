import React, { createContext, useContext } from "react";
import { useMyStudyGroups, useDiscoverStudyGroups, StudyGroup } from "@/hooks/useStudyGroups";

// Legacy types for compatibility
export type Member = { id?: string; name: string; avatar: string };
export type Group = {
  id: string;
  name: string;
  description?: string;
  image: string;
  isPublic: boolean;
  isJoined: boolean;
  memberCount?: number;
  coverPhotoFile?: File | null;
  members: Member[];
};

type StudyGroupsContextType = {
  myGroups: StudyGroup[];
  discoverGroups: StudyGroup[];
  isLoadingMyGroups: boolean;
  isLoadingDiscoverGroups: boolean;
  errorMyGroups: any;
  errorDiscoverGroups: any;
};

const StudyGroupsContext = createContext<StudyGroupsContextType | undefined>(undefined);

export function StudyGroupsProvider({ children }: {
  children: React.ReactNode;
}) {
  const { data: myGroups = [], isLoading: isLoadingMyGroups, error: errorMyGroups } = useMyStudyGroups();
  const { data: discoverGroups = [], isLoading: isLoadingDiscoverGroups, error: errorDiscoverGroups } = useDiscoverStudyGroups();

  return (
    <StudyGroupsContext.Provider
      value={{
        myGroups,
        discoverGroups,
        isLoadingMyGroups,
        isLoadingDiscoverGroups,
        errorMyGroups,
        errorDiscoverGroups,
      }}
    >
      {children}
    </StudyGroupsContext.Provider>
  );
}

export function useStudyGroups() {
  const ctx = useContext(StudyGroupsContext);
  if (!ctx) throw new Error("useStudyGroups must be used within StudyGroupsProvider");
  return ctx;
}
