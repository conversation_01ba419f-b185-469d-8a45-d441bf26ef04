import React from 'react';
import { X, Copy, MessageCircle, Facebook, Twitter, Mail, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  postId: string;
  content: string;
  authorName: string;
  groupId: string;
}

const ShareModal: React.FC<ShareModalProps> = ({
  isOpen,
  onClose,
  postId,
  content,
  authorName,
  groupId
}) => {
  if (!isOpen) return null;

  const baseUrl = window.location.origin;
  const postUrl = `${baseUrl}/study-groups/${groupId}?post=${postId}`;
  const shareText = `Check out this post by ${authorName}: "${content.substring(0, 100)}${content.length > 100 ? '...' : ''}"`;

  const handleCopyLink = () => {
    navigator.clipboard.writeText(postUrl);
    toast.success('Post link copied to clipboard!');
    onClose();
  };

  const handleWhatsAppShare = () => {
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(shareText + ' ' + postUrl)}`;
    window.open(whatsappUrl, '_blank');
    onClose();
  };

  const handleFacebookShare = () => {
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(postUrl)}`;
    window.open(facebookUrl, '_blank');
    onClose();
  };

  const handleTwitterShare = () => {
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(postUrl)}`;
    window.open(twitterUrl, '_blank');
    onClose();
  };

  const handleEmailShare = () => {
    const subject = `Check out this post from StudyFam`;
    const body = `${shareText}\n\nView the full post here: ${postUrl}`;
    const emailUrl = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.open(emailUrl);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-xl max-w-md w-full overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Share Post</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content Preview */}
        <div className="p-6 border-b border-gray-100">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm font-medium text-gray-800 mb-1">
              Post by {authorName}
            </div>
            <div className="text-sm text-gray-600 line-clamp-3">
              {content}
            </div>
          </div>
        </div>

        {/* Share Options */}
        <div className="p-6">
          <div className="grid grid-cols-2 gap-3">
            {/* Copy Link */}
            <Button
              variant="outline"
              className="flex items-center gap-2 justify-start h-12"
              onClick={handleCopyLink}
            >
              <Copy size={18} className="text-gray-600" />
              <span className="text-sm">Copy Link</span>
            </Button>

            {/* WhatsApp */}
            <Button
              variant="outline"
              className="flex items-center gap-2 justify-start h-12"
              onClick={handleWhatsAppShare}
            >
              <MessageCircle size={18} className="text-green-600" />
              <span className="text-sm">WhatsApp</span>
            </Button>

            {/* Facebook */}
            <Button
              variant="outline"
              className="flex items-center gap-2 justify-start h-12"
              onClick={handleFacebookShare}
            >
              <Facebook size={18} className="text-blue-600" />
              <span className="text-sm">Facebook</span>
            </Button>

            {/* Twitter */}
            <Button
              variant="outline"
              className="flex items-center gap-2 justify-start h-12"
              onClick={handleTwitterShare}
            >
              <Twitter size={18} className="text-blue-400" />
              <span className="text-sm">Twitter</span>
            </Button>

            {/* Email */}
            <Button
              variant="outline"
              className="flex items-center gap-2 justify-start h-12 col-span-2"
              onClick={handleEmailShare}
            >
              <Mail size={18} className="text-gray-600" />
              <span className="text-sm">Email</span>
            </Button>
          </div>

          {/* Direct Link */}
          <div className="mt-4 pt-4 border-t border-gray-100">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Direct Link
            </label>
            <div className="flex items-center gap-2">
              <input
                type="text"
                value={postUrl}
                readOnly
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm bg-gray-50 text-gray-600"
              />
              <Button
                size="sm"
                variant="outline"
                onClick={handleCopyLink}
                className="flex items-center gap-1"
              >
                <Copy size={14} />
                Copy
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShareModal;
