# Database Setup Guide

## 🎉 Database Successfully Configured!

Your StudyFam application database has been successfully set up with all necessary tables, functions, and sample data.

## 📊 Database Schema

### Core Tables Created:
- ✅ **profiles** - User profile information
- ✅ **friendships** - Friend relationships
- ✅ **conversations** - Chat conversations
- ✅ **conversation_participants** - Conversation members
- ✅ **messages** - Chat messages
- ✅ **countries** - Country dropdown data
- ✅ **courses** - Course dropdown data  
- ✅ **institutes** - Institute dropdown data

### Functions Created:
- ✅ **get_countries()** - Get all countries for dropdown
- ✅ **get_courses()** - Get all courses for dropdown
- ✅ **get_institutes()** - Get all institutes for dropdown
- ✅ **get_user_friends(user_id)** - Get user's friends list
- ✅ **get_user_conversations(user_id)** - Get user's conversations
- ✅ **get_or_create_conversation(user1_id, user2_id)** - Create/find direct conversation
- ✅ **handle_new_user()** - Auto-create profile on signup
- ✅ **handle_updated_at()** - Auto-update timestamps

### Security:
- ✅ **Row Level Security (RLS)** enabled on all tables
- ✅ **Policies** created for secure data access
- ✅ **Triggers** set up for automatic operations

### Storage Buckets:
- ✅ **avatars** - Profile pictures (public)
- ✅ **uploads** - General file uploads (private)
- ✅ **notes-files** - Study notes files (private)
- ✅ **past-papers** - Past exam papers (public)
- ✅ **group-covers** - Study group cover images (public)

## 🚀 How to Use

### 1. Start the Application
```bash
npm run dev
```

### 2. Create an Account
- Go to the signup page
- Enter your details
- A profile will be automatically created in the database

### 3. Update Your Profile
- Navigate to the Profile page
- Click the edit button
- Update your information using the dropdown data

### 4. Test Features
- **Profile Management**: Update name, country, course, institute
- **Friends System**: Add and manage friends (when implemented)
- **Messaging**: Send messages to friends (when implemented)

## 🔧 Backend Integration

The frontend is now connected to the backend with:

### Custom Hooks Created:
- `useProfile()` - Get/update user profile
- `useFriends()` - Manage friendships
- `useMessages()` - Handle messaging
- `useAuth()` - Authentication management

### API Features:
- ✅ Real-time profile updates
- ✅ Dropdown data from database
- ✅ File upload for avatars
- ✅ Secure data access with RLS
- ✅ Automatic profile creation on signup

## 📝 Sample Data

The database includes sample data for:
- **20 Countries** (US, UK, Canada, Australia, etc.)
- **42 Courses** across different categories
- **40 Universities** from around the world

## 🔍 Testing the Database

You can test the database functions:

```sql
-- Test countries
SELECT * FROM public.get_countries() LIMIT 5;

-- Test courses  
SELECT * FROM public.get_courses() LIMIT 5;

-- Test institutes
SELECT * FROM public.get_institutes() LIMIT 5;
```

## 🎯 Next Steps

1. **Create a user account** in your app
2. **Update your profile** with real information
3. **Test the profile editing** functionality
4. **Implement additional features** like friends and messaging

## 🛠 Troubleshooting

If you encounter issues:

1. **Check Supabase connection**: Verify your environment variables
2. **Check browser console**: Look for any JavaScript errors
3. **Check Supabase dashboard**: Verify tables and data exist
4. **Restart development server**: `npm run dev`

## 📚 Database Commands

```bash
# View database status
npm run supabase:start

# Reset database (if needed)
npm run db:reset

# Stop Supabase
npm run supabase:stop
```

---

**🎉 Your StudyFam database is ready to use!**
