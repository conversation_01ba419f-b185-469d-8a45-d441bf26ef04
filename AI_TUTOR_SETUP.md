
# 🤖 AI Tutor Setup Guide

This guide will help you set up the AI Tutor feature with OpenAI API integration.

## 🚨 Current Status

**The AI Tutor is now powered by OpenAI GPT-4o-mini!**

## 📋 Prerequisites

1. **Supabase CLI installed**
   ```bash
   npm install -g supabase
   ```

2. **Logged in to Supabase**
   ```bash
   supabase login
   ```

3. **OpenAI API Key** ✅
   - Already configured: `********************************************************************************************************************************************************************`

## ✅ Current Status

**Database**: ✅ Ready (ai_tutor_logs table created)
**Frontend**: ✅ Complete and functional
**Function Code**: ✅ Ready and using OpenAI
**API**: ✅ OpenAI GPT-4o-mini integrated
**Deployment**: 🔄 Needs setup (choose option below)

## 🎯 Quick Setup Options

### Option 1: Automated CLI Setup

Run the automated setup script:

```bash
./scripts/deploy-ai-tutor.sh
```

This script will:
- ✅ Check if Supabase CLI is installed
- ✅ Verify you're logged in
- ✅ Link to your project
- ✅ Deploy the Edge Function
- ✅ Set the OpenAI API key
- ✅ Test the function

### Option 2: Manual Dashboard Deployment

If you prefer using the Supabase Dashboard:

1. **Go to Supabase Dashboard**: https://supabase.com/dashboard/project/umavpljptvamtmaygztq
2. **Navigate to Edge Functions**: Click "Edge Functions" in the sidebar
3. **Create New Function**: Click "Create a new function"
4. **Function Details**:
   - Name: `ai-tutor`
   - Copy code from `supabase/functions/ai-tutor/index.ts`
5. **Set Environment Variable**:
   - Go to Settings → Environment Variables
   - Add: `OPENAI_API_KEY` = `********************************************************************************************************************************************************************`
6. **Deploy**: Click "Deploy function"

## 🔧 Manual Setup Steps

If you prefer to do it manually:

### 1. Install & Login to Supabase

```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Link to your project
supabase link --project-ref umavpljptvamtmaygztq
```

### 2. Deploy the Database Migration

Run the migration to create the AI tutor logs table:

```bash
supabase db push
```

This will create the `ai_tutor_logs` table for tracking usage analytics.

### 3. Deploy the Edge Function

Deploy the AI tutor Edge Function:

```bash
supabase functions deploy ai-tutor
```

### 4. Set Environment Variables

Set the OpenAI API key as a Supabase secret:

```bash
supabase secrets set OPENAI_API_KEY=********************************************************************************************************************************************************************
```

### 5. Test the Function

Test the deployed function:

```bash
supabase functions invoke ai-tutor --data '{
  "messages": [
    {
      "role": "user", 
      "content": "Explain photosynthesis in simple terms"
    }
  ],
  "subject": "science"
}'
```

## Features Implemented

### Frontend Features
- ✅ **Chat Interface**: Real-time conversation with AI tutor
- ✅ **Subject Selection**: Choose from 8 different subjects
- ✅ **Conversation Management**: Save and manage multiple conversations
- ✅ **Quick Prompts**: Pre-built prompts for common learning tasks
- ✅ **Message Rating**: Like/dislike system for feedback
- ✅ **Copy to Clipboard**: Easy sharing of AI responses
- ✅ **Responsive Design**: Works on mobile and desktop
- ✅ **Local Storage**: Conversations persist between sessions
- ✅ **Authentication**: Secure access with Supabase Auth

### Backend Features
- ✅ **Supabase Edge Function**: Secure API proxy for OpenAI
- ✅ **OpenAI Integration**: GPT-4o-mini model for intelligent responses
- ✅ **Subject-Specific Prompts**: Tailored system prompts for each subject
- ✅ **Authentication**: User verification before API calls
- ✅ **Error Handling**: Comprehensive error management
- ✅ **Usage Logging**: Track interactions for analytics
- ✅ **CORS Support**: Proper cross-origin request handling
- ✅ **Rate Limiting**: Built-in Supabase rate limiting

### Security Features
- ✅ **API Key Protection**: OpenAI API key stored securely as Supabase secret
- ✅ **User Authentication**: Only authenticated users can access
- ✅ **RLS Policies**: Row-level security on logs table
- ✅ **Input Validation**: Proper request validation
- ✅ **Error Sanitization**: No sensitive data in error responses

## API Endpoint

The AI tutor is accessible via:
```
POST https://your-project.supabase.co/functions/v1/ai-tutor
```

### Request Format
```json
{
  "messages": [
    {
      "role": "user",
      "content": "Your question here"
    }
  ],
  "subject": "mathematics",
  "temperature": 0.7
}
```

### Response Format
```json
{
  "message": "AI tutor response here",
  "usage": {
    "prompt_tokens": 100,
    "completion_tokens": 150,
    "total_tokens": 250
  }
}
```

## Subjects Available

1. **General** - General knowledge and study help
2. **Mathematics** - Algebra, geometry, calculus, statistics
3. **Science** - Physics, chemistry, biology
4. **English** - Literature, writing, grammar
5. **History** - World history, social studies
6. **Art** - Visual arts, design, creativity
7. **Music** - Music theory, instruments, composition
8. **Computer Science** - Programming, algorithms, software

## Usage Analytics

The system tracks:
- User interactions
- Subject preferences
- Message counts
- Response lengths
- Usage patterns

Access analytics via the `ai_tutor_logs` table.

## AI Model Details

**Model**: GPT-4o-mini
**Provider**: OpenAI
**Capabilities**:
- Fast and efficient responses
- Subject-specific expertise
- Step-by-step explanations
- Educational focus
- Context awareness

## Troubleshooting

### Function Not Working
1. Check if function is deployed: `supabase functions list`
2. Check logs: `supabase functions logs ai-tutor`
3. Verify API key: `supabase secrets list`

### Authentication Errors
1. Ensure user is logged in
2. Check RLS policies
3. Verify JWT token

### API Errors
1. Check OpenAI API key validity
2. Monitor rate limits
3. Check network connectivity

## Cost Considerations

- **OpenAI API**: Pay per token usage (GPT-4o-mini is cost-effective)
- **Supabase**: Edge Function invocations and database storage
- **Estimated cost**: ~$0.001-0.01 per conversation (much lower with GPT-4o-mini)

## Next Steps

1. Monitor usage and costs
2. Implement conversation limits if needed
3. Add more subjects or specializations
4. Enhance analytics and reporting
5. Consider caching for common questions
6. Optimize prompts for better responses

## Support

For issues or questions:
1. Check Supabase function logs
2. Review error messages in browser console
3. Test API endpoint directly
4. Check authentication status
5. Verify OpenAI API key is valid

## Migration from X.AI

The AI Tutor has been successfully migrated from X.AI (Grok) to OpenAI:
- ✅ More reliable API
- ✅ Better educational responses
- ✅ Lower costs
- ✅ Faster response times
- ✅ Better error handling
