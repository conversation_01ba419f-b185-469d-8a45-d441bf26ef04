<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>StudyFam - Your Study Companion</title>
    <meta name="description" content="StudyFam - The ultimate study companion app for students. Take notes, join study groups, access past papers, and get AI tutoring help." />
    <meta name="author" content="StudyFam" />

    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="apple-touch-icon" sizes="192x192" href="/logo192.png" />

    <!-- P<PERSON> Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#8b5cf6" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="StudyFam" />
    <meta name="mobile-web-app-capable" content="yes" />

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="StudyFam - Your Study Companion" />
    <meta property="og:description" content="The ultimate study companion app for students. Take notes, join study groups, access past papers, and get AI tutoring help." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/logo512.png" />
    <meta property="og:url" content="https://studyfam.vercel.app" />

    <!-- Twitter Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="StudyFam - Your Study Companion" />
    <meta name="twitter:description" content="The ultimate study companion app for students. Take notes, join study groups, access past papers, and get AI tutoring help." />
    <meta name="twitter:image" content="/logo512.png" />
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
