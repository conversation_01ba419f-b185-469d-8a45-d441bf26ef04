#!/usr/bin/env node

// Simple Node.js script to test profile loading
// Run with: node debug-profile.js

const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = "https://umavpljptvamtmaygztq.supabase.co";
const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVtYXZwbGpwdHZhbXRtYXlnenRxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyMzg1NjEsImV4cCI6MjA2NTgxNDU2MX0.f16xzL7S0VLtiYyovLl4mOxWFW4ys-KVJQgF-VkgLU4";

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function testConnection() {
    console.log('🔍 Testing Supabase connection...');
    try {
        const { data, error } = await supabase.from('profiles').select('count').limit(1);
        if (error) throw error;
        console.log('✅ Supabase connection successful');
        return true;
    } catch (error) {
        console.log('❌ Supabase connection failed:', error.message);
        return false;
    }
}

async function checkProfiles() {
    console.log('\n🔍 Checking profiles table...');
    try {
        const { data, error } = await supabase
            .from('profiles')
            .select('id, email, full_name, created_at')
            .limit(5);
        
        if (error) throw error;
        
        console.log(`✅ Found ${data.length} profiles in the database`);
        if (data.length > 0) {
            console.log('Sample profiles:');
            data.forEach((profile, index) => {
                console.log(`  ${index + 1}. ${profile.full_name || 'No name'} (${profile.email})`);
            });
        }
        return data;
    } catch (error) {
        console.log('❌ Failed to query profiles:', error.message);
        return null;
    }
}

async function checkAuthUsers() {
    console.log('\n🔍 Checking auth.users (requires service key)...');
    // This would require service key, so we'll skip for now
    console.log('⚠️  Skipping auth.users check (requires service key)');
}

async function simulateProfileLoad() {
    console.log('\n🔍 Simulating profile load process...');
    
    // This simulates what happens in the useProfile hook
    try {
        // Step 1: Try to get current user (this would normally work with a valid session)
        console.log('1. Getting current user...');
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        
        if (userError) {
            console.log('❌ Auth error:', userError.message);
            return;
        }
        
        if (!user) {
            console.log('⚠️  No authenticated user found');
            return;
        }
        
        console.log('✅ User found:', user.email);
        
        // Step 2: Get profile for this user
        console.log('2. Getting profile for user...');
        const { data: profile, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();
        
        if (profileError) {
            console.log('❌ Profile error:', profileError.message);
            return;
        }
        
        if (!profile) {
            console.log('⚠️  No profile found for user');
            return;
        }
        
        console.log('✅ Profile loaded successfully:');
        console.log('   Name:', profile.full_name || 'Not set');
        console.log('   Email:', profile.email);
        console.log('   Country:', profile.country || 'Not set');
        console.log('   Course:', profile.course || 'Not set');
        console.log('   Institute:', profile.institute || 'Not set');
        
    } catch (error) {
        console.log('❌ Simulation failed:', error.message);
    }
}

async function main() {
    console.log('🚀 StudyFam Profile Debug Tool\n');
    
    const connected = await testConnection();
    if (!connected) {
        console.log('\n❌ Cannot proceed without Supabase connection');
        return;
    }
    
    await checkProfiles();
    await checkAuthUsers();
    await simulateProfileLoad();
    
    console.log('\n📋 Summary:');
    console.log('- If you see "No authenticated user found", you need to sign in first');
    console.log('- If you see "No profile found for user", the profile might not exist');
    console.log('- Check the browser console for more detailed error messages');
    console.log('- Use the debug-profile.html file in your browser for interactive testing');
    
    console.log('\n🔧 Next steps:');
    console.log('1. Open your app in the browser');
    console.log('2. Go to /login and sign in');
    console.log('3. Try accessing /profile again');
    console.log('4. Check browser console for errors');
    console.log('5. Use /debug-profile.html for detailed diagnostics');
}

main().catch(console.error);
