-- Revision Planner Database Setup
-- Run this in Supabase SQL Editor

-- Create exams table
CREATE TABLE IF NOT EXISTS public.exams (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,
    subject TEXT NOT NULL,
    exam_date DATE NOT NULL,
    difficulty INTEGER CHECK (difficulty >= 1 AND difficulty <= 3) DEFAULT 2,
    type TEXT CHECK (type IN ('exam', 'assignment')) DEFAULT 'exam',
    owner_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create study sessions table
CREATE TABLE IF NOT EXISTS public.study_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    exam_id UUID REFERENCES public.exams(id) ON DELETE CASCADE NOT NULL,
    date DATE NOT NULL,
    subject TEXT NOT NULL,
    topic TEXT NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    is_completed BOOLEA<PERSON> DEFAULT false,
    owner_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_exams_owner_id ON public.exams(owner_id);
CREATE INDEX IF NOT EXISTS idx_exams_exam_date ON public.exams(exam_date);
CREATE INDEX IF NOT EXISTS idx_study_sessions_owner_id ON public.study_sessions(owner_id);
CREATE INDEX IF NOT EXISTS idx_study_sessions_exam_id ON public.study_sessions(exam_id);
CREATE INDEX IF NOT EXISTS idx_study_sessions_date ON public.study_sessions(date);

-- Enable RLS
ALTER TABLE public.exams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.study_sessions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for exams
CREATE POLICY "Users can view their own exams" ON public.exams
    FOR SELECT USING (auth.uid() = owner_id);

CREATE POLICY "Users can insert their own exams" ON public.exams
    FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Users can update their own exams" ON public.exams
    FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "Users can delete their own exams" ON public.exams
    FOR DELETE USING (auth.uid() = owner_id);

-- Create RLS policies for study sessions
CREATE POLICY "Users can view their own study sessions" ON public.study_sessions
    FOR SELECT USING (auth.uid() = owner_id);

CREATE POLICY "Users can insert their own study sessions" ON public.study_sessions
    FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Users can update their own study sessions" ON public.study_sessions
    FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "Users can delete their own study sessions" ON public.study_sessions
    FOR DELETE USING (auth.uid() = owner_id);

-- Update RLS policies for existing revision tables if they exist
DO $$
BEGIN
    -- Check if revision_plans table exists and add RLS policies
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'revision_plans') THEN
        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "Users can view their own revision plans" ON public.revision_plans;
        DROP POLICY IF EXISTS "Users can insert their own revision plans" ON public.revision_plans;
        DROP POLICY IF EXISTS "Users can update their own revision plans" ON public.revision_plans;
        DROP POLICY IF EXISTS "Users can delete their own revision plans" ON public.revision_plans;
        
        -- Create new policies
        CREATE POLICY "Users can view their own revision plans" ON public.revision_plans
            FOR SELECT USING (auth.uid() = owner_id);

        CREATE POLICY "Users can insert their own revision plans" ON public.revision_plans
            FOR INSERT WITH CHECK (auth.uid() = owner_id);

        CREATE POLICY "Users can update their own revision plans" ON public.revision_plans
            FOR UPDATE USING (auth.uid() = owner_id);

        CREATE POLICY "Users can delete their own revision plans" ON public.revision_plans
            FOR DELETE USING (auth.uid() = owner_id);
    END IF;

    -- Check if revision_tasks table exists and add RLS policies
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'revision_tasks') THEN
        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "Users can view revision tasks for their plans" ON public.revision_tasks;
        DROP POLICY IF EXISTS "Users can insert revision tasks for their plans" ON public.revision_tasks;
        DROP POLICY IF EXISTS "Users can update revision tasks for their plans" ON public.revision_tasks;
        DROP POLICY IF EXISTS "Users can delete revision tasks for their plans" ON public.revision_tasks;
        
        -- Create new policies (tasks are linked to plans, so we check plan ownership)
        CREATE POLICY "Users can view revision tasks for their plans" ON public.revision_tasks
            FOR SELECT USING (
                EXISTS (
                    SELECT 1 FROM public.revision_plans 
                    WHERE id = revision_tasks.plan_id AND owner_id = auth.uid()
                )
            );

        CREATE POLICY "Users can insert revision tasks for their plans" ON public.revision_tasks
            FOR INSERT WITH CHECK (
                EXISTS (
                    SELECT 1 FROM public.revision_plans 
                    WHERE id = revision_tasks.plan_id AND owner_id = auth.uid()
                )
            );

        CREATE POLICY "Users can update revision tasks for their plans" ON public.revision_tasks
            FOR UPDATE USING (
                EXISTS (
                    SELECT 1 FROM public.revision_plans 
                    WHERE id = revision_tasks.plan_id AND owner_id = auth.uid()
                )
            );

        CREATE POLICY "Users can delete revision tasks for their plans" ON public.revision_tasks
            FOR DELETE USING (
                EXISTS (
                    SELECT 1 FROM public.revision_plans 
                    WHERE id = revision_tasks.plan_id AND owner_id = auth.uid()
                )
            );
    END IF;
END $$;

-- Add triggers for updated_at timestamps
CREATE TRIGGER handle_updated_at_exams
    BEFORE UPDATE ON public.exams
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at_study_sessions
    BEFORE UPDATE ON public.study_sessions
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Create a function to auto-generate study sessions for an exam
CREATE OR REPLACE FUNCTION public.generate_study_sessions(
    exam_id_param UUID,
    study_hours_per_day INTEGER DEFAULT 2
) RETURNS SETOF public.study_sessions AS $$
DECLARE
    exam_record public.exams;
    days_until_exam INTEGER;
    sessions_needed INTEGER;
    session_date DATE;
    i INTEGER;
    new_session public.study_sessions;
BEGIN
    -- Get the exam details
    SELECT * INTO exam_record FROM public.exams WHERE id = exam_id_param;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Exam not found';
    END IF;
    
    -- Calculate days until exam
    days_until_exam := exam_record.exam_date - CURRENT_DATE;
    
    -- Calculate number of sessions needed based on difficulty
    sessions_needed := CASE exam_record.difficulty
        WHEN 1 THEN LEAST(days_until_exam, 3)  -- Easy: 3 sessions max
        WHEN 2 THEN LEAST(days_until_exam, 5)  -- Medium: 5 sessions max
        WHEN 3 THEN LEAST(days_until_exam, 7)  -- Hard: 7 sessions max
        ELSE 3
    END;
    
    -- Generate study sessions
    FOR i IN 1..sessions_needed LOOP
        session_date := CURRENT_DATE + (i - 1);
        
        -- Don't schedule sessions after the exam date
        IF session_date >= exam_record.exam_date THEN
            EXIT;
        END IF;
        
        INSERT INTO public.study_sessions (
            exam_id,
            date,
            subject,
            topic,
            duration_minutes,
            owner_id
        ) VALUES (
            exam_id_param,
            session_date,
            exam_record.subject,
            exam_record.title,
            study_hours_per_day * 60,
            exam_record.owner_id
        ) RETURNING * INTO new_session;
        
        RETURN NEXT new_session;
    END LOOP;
    
    RETURN;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get upcoming deadlines
CREATE OR REPLACE FUNCTION public.get_upcoming_deadlines(user_id_param UUID)
RETURNS TABLE (
    id UUID,
    title TEXT,
    type TEXT,
    subject TEXT,
    due_date DATE,
    days_left INTEGER,
    priority TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        e.id,
        e.title,
        e.type,
        e.subject,
        e.exam_date as due_date,
        (e.exam_date - CURRENT_DATE) as days_left,
        CASE 
            WHEN e.exam_date - CURRENT_DATE <= 3 THEN 'high'
            WHEN e.exam_date - CURRENT_DATE <= 7 THEN 'medium'
            ELSE 'low'
        END as priority
    FROM public.exams e
    WHERE e.owner_id = user_id_param
    AND e.exam_date >= CURRENT_DATE
    ORDER BY e.exam_date ASC
    LIMIT 10;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
