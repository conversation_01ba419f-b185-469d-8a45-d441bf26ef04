-- Check current table structure
-- Run this first to see what exists

-- Check if tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('profiles', 'notes', 'units', 'topics')
ORDER BY table_name;

-- Check notes table structure
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'notes' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check if functions exist
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('get_user_units', 'get_unit_topics', 'get_topic_notes')
ORDER BY routine_name;
