-- Manual setup script for SortNotes system
-- Run this in Supabase SQL Editor

-- Create units table (main folders/subjects)
CREATE TABLE IF NOT EXISTS public.units (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    color TEXT DEFAULT '#6366f1',
    owner_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(name, owner_id)
);

-- Create topics table (subfolders within units)
CREATE TABLE IF NOT EXISTS public.topics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    unit_id UUID REFERENCES public.units(id) ON DELETE CASCADE NOT NULL,
    owner_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(name, unit_id)
);

-- Update notes table to reference units and topics
ALTER TABLE public.notes 
ADD COLUMN IF NOT EXISTS unit_id UUID REFERENCES public.units(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS topic_id UUID REFERENCES public.topics(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS file_name TEXT,
ADD COLUMN IF NOT EXISTS file_size BIGINT;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_units_owner_id ON public.units(owner_id);
CREATE INDEX IF NOT EXISTS idx_topics_unit_id ON public.topics(unit_id);
CREATE INDEX IF NOT EXISTS idx_topics_owner_id ON public.topics(owner_id);
CREATE INDEX IF NOT EXISTS idx_notes_unit_id ON public.notes(unit_id);
CREATE INDEX IF NOT EXISTS idx_notes_topic_id ON public.notes(topic_id);
CREATE INDEX IF NOT EXISTS idx_notes_owner_id ON public.notes(owner_id);

-- Enable RLS
ALTER TABLE public.units ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.topics ENABLE ROW LEVEL SECURITY;

-- RLS Policies for units
DROP POLICY IF EXISTS "Users can view their own units" ON public.units;
DROP POLICY IF EXISTS "Users can create their own units" ON public.units;
DROP POLICY IF EXISTS "Users can update their own units" ON public.units;
DROP POLICY IF EXISTS "Users can delete their own units" ON public.units;

CREATE POLICY "Users can view their own units" ON public.units
    FOR SELECT USING (auth.uid() = owner_id);

CREATE POLICY "Users can create their own units" ON public.units
    FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Users can update their own units" ON public.units
    FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "Users can delete their own units" ON public.units
    FOR DELETE USING (auth.uid() = owner_id);

-- RLS Policies for topics
DROP POLICY IF EXISTS "Users can view their own topics" ON public.topics;
DROP POLICY IF EXISTS "Users can create their own topics" ON public.topics;
DROP POLICY IF EXISTS "Users can update their own topics" ON public.topics;
DROP POLICY IF EXISTS "Users can delete their own topics" ON public.topics;

CREATE POLICY "Users can view their own topics" ON public.topics
    FOR SELECT USING (auth.uid() = owner_id);

CREATE POLICY "Users can create their own topics" ON public.topics
    FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Users can update their own topics" ON public.topics
    FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "Users can delete their own topics" ON public.topics
    FOR DELETE USING (auth.uid() = owner_id);

-- Function to get user's units with topic counts
CREATE OR REPLACE FUNCTION get_user_units(user_id UUID)
RETURNS TABLE (
    id UUID,
    name TEXT,
    description TEXT,
    color TEXT,
    topic_count BIGINT,
    note_count BIGINT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id,
        u.name,
        u.description,
        u.color,
        COUNT(DISTINCT t.id) as topic_count,
        COUNT(DISTINCT n.id) as note_count,
        u.created_at,
        u.updated_at
    FROM public.units u
    LEFT JOIN public.topics t ON u.id = t.unit_id
    LEFT JOIN public.notes n ON u.id = n.unit_id
    WHERE u.owner_id = user_id
    GROUP BY u.id, u.name, u.description, u.color, u.created_at, u.updated_at
    ORDER BY u.created_at DESC;
END;
$$;

-- Function to get topics for a specific unit
CREATE OR REPLACE FUNCTION get_unit_topics(unit_id UUID, user_id UUID)
RETURNS TABLE (
    id UUID,
    name TEXT,
    description TEXT,
    note_count BIGINT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.id,
        t.name,
        t.description,
        COUNT(n.id) as note_count,
        t.created_at,
        t.updated_at
    FROM public.topics t
    LEFT JOIN public.notes n ON t.id = n.topic_id
    WHERE t.unit_id = get_unit_topics.unit_id 
    AND t.owner_id = user_id
    GROUP BY t.id, t.name, t.description, t.created_at, t.updated_at
    ORDER BY t.created_at DESC;
END;
$$;

-- Function to get notes for a specific topic
CREATE OR REPLACE FUNCTION get_topic_notes(topic_id UUID, user_id UUID)
RETURNS TABLE (
    id UUID,
    title TEXT,
    content TEXT,
    file_url TEXT,
    file_name TEXT,
    file_type TEXT,
    file_size BIGINT,
    tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        n.id,
        n.title,
        n.content,
        n.file_url,
        n.file_name,
        n.file_type,
        n.file_size,
        n.tags,
        n.created_at,
        n.updated_at
    FROM public.notes n
    WHERE n.topic_id = get_topic_notes.topic_id 
    AND n.owner_id = user_id
    ORDER BY n.created_at DESC;
END;
$$;
