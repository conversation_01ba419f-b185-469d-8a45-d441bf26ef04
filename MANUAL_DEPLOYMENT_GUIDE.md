# 🚀 Manual Deployment Guide - Quiz Generator Edge Function

## 📋 **Prerequisites**
- Supabase account and project
- OpenRouter API key
- Access to Supabase Dashboard

## 🔧 **Method 1: Supabase Dashboard (Recommended)**

### **Step 1: Access Edge Functions**
1. Go to your Supabase Dashboard: https://supabase.com/dashboard
2. Select your project
3. Navigate to **Edge Functions** in the left sidebar
4. Click **"Create a new function"**

### **Step 2: Create the Function**
1. **Function Name**: `generate-quiz`
2. **Copy the code** from `supabase/functions/generate-quiz/index.ts`
3. **Paste it** into the function editor
4. Click **"Deploy function"**

### **Step 3: Set Environment Variables**
1. In the same Edge Functions page
2. Click on **"Settings"** or **"Environment Variables"**
3. Add: `OPENROUTER_API_KEY` = `your_openrouter_api_key_here`
4. Save the environment variable

### **Step 4: Test the Function**
1. Use the **"Invoke"** button in the dashboard
2. Test payload:
```json
{
  "content": "Photosynthesis is the process by which plants convert sunlight into energy using chlorophyll.",
  "numQuestions": 3,
  "difficulty": "medium",
  "subject": "Biology"
}
```

## 🔧 **Method 2: Supabase CLI**

### **Step 1: Install Supabase CLI**
```bash
# Using npm
npm install -g supabase

# Using homebrew (Mac)
brew install supabase/tap/supabase

# Using scoop (Windows)
scoop bucket add supabase https://github.com/supabase/scoop-bucket.git
scoop install supabase
```

### **Step 2: Login and Link Project**
```bash
# Login to Supabase
supabase login

# Navigate to your project
cd /path/to/study-fam

# Link to your project (get project ref from dashboard)
supabase link --project-ref YOUR_PROJECT_REF
```

### **Step 3: Deploy Function**
```bash
# Deploy the specific function
supabase functions deploy generate-quiz

# Set environment variable
supabase secrets set OPENROUTER_API_KEY=your_openrouter_api_key_here
```

### **Step 4: Test Deployment**
```bash
# Test the function
curl -X POST 'https://YOUR_PROJECT_REF.supabase.co/functions/v1/generate-quiz' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "content": "Test content for quiz generation",
    "numQuestions": 2,
    "difficulty": "easy",
    "subject": "Test"
  }'
```

## 🔧 **Method 3: Manual File Upload**

If CLI doesn't work, you can manually create the function:

### **Step 1: Get the Function Code**
Copy this entire code block:

```typescript
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface QuizRequest {
  content: string;
  numQuestions?: number;
  difficulty?: 'easy' | 'medium' | 'hard';
  questionTypes?: ('multiple-choice' | 'true-false' | 'short-answer')[];
  subject?: string;
  includeAnswers?: boolean;
}

interface QuizQuestion {
  id: string;
  type: 'multiple-choice' | 'true-false' | 'short-answer';
  question: string;
  options?: string[];
  correct_answer: string;
  explanation?: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

interface QuizResponse {
  success: boolean;
  quiz?: {
    title: string;
    questions: QuizQuestion[];
    metadata: {
      totalQuestions: number;
      difficulty: string;
      subject: string;
      generatedAt: string;
      estimatedTime: number;
    };
  };
  error?: string;
}

function getOpenRouterModel(model: string): string {
  const modelMap: { [key: string]: string } = {
    'gpt-4': 'openai/gpt-4',
    'gpt-4-turbo': 'openai/gpt-4-turbo',
    'gpt-3.5-turbo': 'openai/gpt-3.5-turbo',
    'claude-3-sonnet': 'anthropic/claude-3-sonnet',
    'claude-3-haiku': 'anthropic/claude-3-haiku'
  };
  return modelMap[model] || 'openai/gpt-3.5-turbo';
}

function generateQuizPrompt(request: QuizRequest): string {
  const { content, numQuestions = 10, difficulty = 'medium', questionTypes = ['multiple-choice'], subject = 'General' } = request;
  
  const questionTypeInstructions = questionTypes.map(type => {
    switch (type) {
      case 'multiple-choice':
        return 'Multiple choice questions with 4 options (A, B, C, D)';
      case 'true-false':
        return 'True/False questions';
      case 'short-answer':
        return 'Short answer questions requiring 1-2 sentence responses';
      default:
        return 'Multiple choice questions';
    }
  }).join(', ');

  return `You are an expert quiz generator. Create a comprehensive quiz based on the following content.

CONTENT TO ANALYZE:
${content}

QUIZ REQUIREMENTS:
- Generate exactly ${numQuestions} questions
- Difficulty level: ${difficulty}
- Question types: ${questionTypeInstructions}
- Subject area: ${subject}
- Include clear explanations for correct answers
- Ensure questions test understanding, not just memorization

RESPONSE FORMAT (JSON):
{
  "title": "Quiz Title Based on Content",
  "questions": [
    {
      "id": "q1",
      "type": "multiple-choice",
      "question": "Question text here?",
      "options": ["A) Option 1", "B) Option 2", "C) Option 3", "D) Option 4"],
      "correct_answer": "A",
      "explanation": "Explanation of why this is correct",
      "difficulty": "medium"
    }
  ]
}

IMPORTANT:
- For multiple-choice: options array with A), B), C), D) format, correct_answer as letter only
- For true-false: options as ["True", "False"], correct_answer as "True" or "False"
- For short-answer: no options array, correct_answer as the expected answer
- Make questions challenging but fair
- Vary difficulty within the specified level
- Ensure all questions are directly related to the provided content

Generate the quiz now:`;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { content, numQuestions, difficulty, questionTypes, subject, includeAnswers = true }: QuizRequest = await req.json()

    if (!content || content.trim().length < 50) {
      return Response.json(
        { success: false, error: 'Content must be at least 50 characters long' },
        { status: 400, headers: corsHeaders }
      )
    }

    const openRouterApiKey = Deno.env.get('OPENROUTER_API_KEY')
    if (!openRouterApiKey) {
      console.error('OpenRouter API key not found')
      return Response.json(
        { success: false, error: 'OpenRouter API key not configured' },
        { status: 500, headers: corsHeaders }
      )
    }

    const authHeader = req.headers.get('Authorization')
    let preferences = null
    
    if (authHeader) {
      try {
        const supabaseClient = createClient(
          Deno.env.get('SUPABASE_URL') ?? '',
          Deno.env.get('SUPABASE_ANON_KEY') ?? '',
          { global: { headers: { Authorization: authHeader } } }
        )
        
        const { data: { user } } = await supabaseClient.auth.getUser()
        if (user) {
          const { data } = await supabaseClient
            .from('ai_preferences')
            .select('*')
            .eq('user_id', user.id)
            .single()
          preferences = data
        }
      } catch (error) {
        console.log('Could not fetch user preferences:', error)
      }
    }

    const prompt = generateQuizPrompt({
      content,
      numQuestions,
      difficulty,
      questionTypes,
      subject
    })

    console.log('Generating quiz with OpenRouter...')

    const openRouterResponse = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openRouterApiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://study-fam.com',
        'X-Title': 'Study Fam Quiz Generator',
      },
      body: JSON.stringify({
        model: getOpenRouterModel(preferences?.default_ai_model || 'gpt-3.5-turbo'),
        messages: [
          {
            role: 'system',
            content: 'You are an expert educational quiz generator. Create high-quality, engaging quiz questions that test comprehension and critical thinking. Always respond with valid JSON format.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: Math.min(4000, numQuestions * 200),
        temperature: 0.7,
      }),
    })

    if (!openRouterResponse.ok) {
      const errorText = await openRouterResponse.text()
      console.error('OpenRouter API error:', errorText)
      return Response.json(
        { success: false, error: 'Failed to generate quiz. Please try again.' },
        { status: 500, headers: corsHeaders }
      )
    }

    const openRouterData = await openRouterResponse.json()
    
    if (!openRouterData.choices || !openRouterData.choices[0]) {
      console.error('Invalid OpenRouter response:', openRouterData)
      return Response.json(
        { success: false, error: 'Invalid response from AI service' },
        { status: 500, headers: corsHeaders }
      )
    }

    const aiResponse = openRouterData.choices[0].message.content

    let quizData
    try {
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error('No JSON found in response')
      }
      
      quizData = JSON.parse(jsonMatch[0])
    } catch (error) {
      console.error('Failed to parse AI response:', error, 'Response:', aiResponse)
      return Response.json(
        { success: false, error: 'Failed to parse quiz data. Please try again.' },
        { status: 500, headers: corsHeaders }
      )
    }

    if (!quizData.questions || !Array.isArray(quizData.questions)) {
      return Response.json(
        { success: false, error: 'Invalid quiz format generated' },
        { status: 500, headers: corsHeaders }
      )
    }

    const estimatedTime = quizData.questions.length * 2

    const response: QuizResponse = {
      success: true,
      quiz: {
        title: quizData.title || `${subject} Quiz`,
        questions: quizData.questions,
        metadata: {
          totalQuestions: quizData.questions.length,
          difficulty: difficulty || 'medium',
          subject: subject || 'General',
          generatedAt: new Date().toISOString(),
          estimatedTime
        }
      }
    }

    return Response.json(response, { headers: corsHeaders })

  } catch (error) {
    console.error('Quiz generation error:', error)
    return Response.json(
      { success: false, error: 'An unexpected error occurred while generating the quiz' },
      { status: 500, headers: corsHeaders }
    )
  }
})
```

### **Step 2: Create Function in Dashboard**
1. Go to Supabase Dashboard → Edge Functions
2. Click "Create a new function"
3. Name: `generate-quiz`
4. Paste the code above
5. Deploy

### **Step 3: Add Environment Variable**
1. In Edge Functions settings
2. Add: `OPENROUTER_API_KEY` = `your_api_key`
3. Save

## 🔑 **Getting OpenRouter API Key**

1. Go to https://openrouter.ai/
2. Sign up/Login
3. Go to "Keys" section
4. Create a new API key
5. Copy the key (starts with `sk-or-...`)

## ✅ **Verification**

After deployment, the quiz generator should:
1. ✅ Stop showing "Demo Mode" in quiz titles
2. ✅ Generate higher quality, AI-powered questions
3. ✅ Show no CORS errors in browser console
4. ✅ Create contextual questions based on content

## 🚨 **If Deployment Fails**

Don't worry! The fallback system ensures the quiz generator works perfectly even without the Edge Function. Users can still:
- ✅ Generate quizzes from any content
- ✅ Export as PDF
- ✅ Save to notes
- ✅ Copy and share quizzes

The only difference is questions will be in "Demo Mode" (still functional and useful).

---

**Choose the method that works best for your setup!** 🚀
