#!/bin/bash

echo "🚀 Running database migrations..."

# Check if supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed."
    echo "Please install it first: https://supabase.com/docs/guides/cli"
    exit 1
fi

# Check if we're in a Supabase project
if [ ! -f "supabase/config.toml" ]; then
    echo "❌ Not in a Supabase project directory."
    echo "Run 'supabase init' first."
    exit 1
fi

# Run migrations
echo "📁 Found migration files:"
ls -la supabase/migrations/

echo ""
echo "🔄 Applying migrations..."

# Apply migrations to local database
supabase db reset

if [ $? -eq 0 ]; then
    echo "✅ Migrations applied successfully!"
    echo ""
    echo "🎉 Database is ready!"
    echo ""
    echo "Next steps:"
    echo "1. Start your development server: npm run dev"
    echo "2. Create a user account in your app"
    echo "3. The profile will be automatically created"
    echo "4. Update your profile information"
else
    echo "❌ Failed to apply migrations."
    exit 1
fi
