#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables:');
  console.error('- VITE_SUPABASE_URL');
  console.error('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration(filePath) {
  console.log(`Running migration: ${filePath}`);
  
  try {
    const sql = fs.readFileSync(filePath, 'utf8');
    const { error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      console.error(`Error in ${filePath}:`, error);
      return false;
    }
    
    console.log(`✅ Successfully ran ${filePath}`);
    return true;
  } catch (err) {
    console.error(`Error reading ${filePath}:`, err);
    return false;
  }
}

async function setupDatabase() {
  console.log('🚀 Setting up database...\n');
  
  const migrationsDir = path.join(__dirname, '..', 'supabase', 'migrations');
  
  if (!fs.existsSync(migrationsDir)) {
    console.error(`Migrations directory not found: ${migrationsDir}`);
    process.exit(1);
  }
  
  const migrationFiles = fs.readdirSync(migrationsDir)
    .filter(file => file.endsWith('.sql'))
    .sort();
  
  if (migrationFiles.length === 0) {
    console.log('No migration files found.');
    return;
  }
  
  console.log(`Found ${migrationFiles.length} migration files:\n`);
  
  for (const file of migrationFiles) {
    const filePath = path.join(migrationsDir, file);
    const success = await runMigration(filePath);
    
    if (!success) {
      console.error(`\n❌ Migration failed: ${file}`);
      process.exit(1);
    }
  }
  
  console.log('\n🎉 Database setup completed successfully!');
  console.log('\nNext steps:');
  console.log('1. Create a user account in your app');
  console.log('2. The profile will be automatically created');
  console.log('3. Update your profile information');
  console.log('4. Start using the app!');
}

// Alternative method using direct SQL execution
async function runSqlDirect(sql) {
  try {
    // Split SQL into individual statements
    const statements = sql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);
    
    for (const statement of statements) {
      const { error } = await supabase.rpc('exec_sql', { sql: statement });
      if (error) {
        console.error('SQL Error:', error);
        throw error;
      }
    }
    
    return true;
  } catch (error) {
    console.error('Failed to execute SQL:', error);
    return false;
  }
}

async function setupDatabaseDirect() {
  console.log('🚀 Setting up database with direct SQL execution...\n');
  
  const migrationsDir = path.join(__dirname, '..', 'supabase', 'migrations');
  const migrationFiles = fs.readdirSync(migrationsDir)
    .filter(file => file.endsWith('.sql'))
    .sort();
  
  for (const file of migrationFiles) {
    console.log(`Running migration: ${file}`);
    const filePath = path.join(migrationsDir, file);
    const sql = fs.readFileSync(filePath, 'utf8');
    
    const success = await runSqlDirect(sql);
    if (!success) {
      console.error(`❌ Failed to run migration: ${file}`);
      process.exit(1);
    }
    
    console.log(`✅ Successfully ran ${file}`);
  }
  
  console.log('\n🎉 Database setup completed!');
}

// Run the setup
if (require.main === module) {
  setupDatabase().catch(console.error);
}
