
#!/bin/bash

# Deploy AI Tutor Edge Function to Supabase with OpenAI
echo "🚀 Setting up AI Tutor with OpenAI API integration..."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project details
PROJECT_REF="umavpljptvamtmaygztq"
OPENAI_API_KEY="********************************************************************************************************************************************************************"

echo -e "${BLUE}📋 Project Information:${NC}"
echo "   Project Ref: $PROJECT_REF"
echo "   Function: ai-tutor"
echo "   API: OpenAI GPT-4o-mini"
echo ""

# Step 1: Check if Supabase CLI is installed
echo -e "${YELLOW}Step 1: Checking Supabase CLI...${NC}"
if ! command -v supabase &> /dev/null; then
    echo -e "${RED}❌ Supabase CLI is not installed.${NC}"
    echo ""
    echo -e "${BLUE}Please install it first:${NC}"
    echo "   npm install -g supabase"
    echo ""
    echo -e "${BLUE}Or using other package managers:${NC}"
    echo "   yarn global add supabase"
    echo "   pnpm add -g supabase"
    echo ""
    exit 1
else
    echo -e "${GREEN}✅ Supabase CLI is installed${NC}"
fi

# Step 2: Check if logged in
echo -e "${YELLOW}Step 2: Checking Supabase authentication...${NC}"
if ! supabase projects list &> /dev/null; then
    echo -e "${RED}❌ Not logged in to Supabase.${NC}"
    echo ""
    echo -e "${BLUE}Please login first:${NC}"
    echo "   supabase login"
    echo ""
    exit 1
else
    echo -e "${GREEN}✅ Logged in to Supabase${NC}"
fi

# Step 3: Link to project
echo -e "${YELLOW}Step 3: Linking to project...${NC}"
if ! supabase status &> /dev/null; then
    echo "🔗 Linking to project $PROJECT_REF..."
    supabase link --project-ref $PROJECT_REF
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Failed to link to project${NC}"
        exit 1
    fi
fi
echo -e "${GREEN}✅ Project linked${NC}"

# Step 4: Deploy the function
echo -e "${YELLOW}Step 4: Deploying ai-tutor function...${NC}"
supabase functions deploy ai-tutor

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ AI Tutor function deployed successfully!${NC}"
else
    echo -e "${RED}❌ Failed to deploy AI Tutor function${NC}"
    exit 1
fi

# Step 5: Set the API key
echo -e "${YELLOW}Step 5: Setting OpenAI API key...${NC}"
supabase secrets set OPENAI_API_KEY=$OPENAI_API_KEY

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ OpenAI API key set successfully!${NC}"
else
    echo -e "${RED}❌ Failed to set API key${NC}"
    exit 1
fi

# Step 6: Run database migration
echo -e "${YELLOW}Step 6: Running database migration...${NC}"
supabase db push

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Database migration completed!${NC}"
else
    echo -e "${RED}❌ Failed to run migration${NC}"
    exit 1
fi

# Step 7: Test the function
echo -e "${YELLOW}Step 7: Testing the function...${NC}"
TEST_RESPONSE=$(supabase functions invoke ai-tutor --data '{"messages":[{"role":"user","content":"Hello, can you help me with math?"}],"subject":"mathematics"}' 2>/dev/null)

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Function test successful!${NC}"
    echo ""
    echo -e "${BLUE}🎉 AI Tutor is now ready to use!${NC}"
    echo ""
    echo -e "${GREEN}✨ What's been set up:${NC}"
    echo "   • Supabase Edge Function deployed"
    echo "   • OpenAI API key configured securely"
    echo "   • Database tables created"
    echo "   • Authentication enabled"
    echo "   • CORS properly configured"
    echo ""
    echo -e "${BLUE}🚀 You can now:${NC}"
    echo "   • Ask questions in any subject"
    echo "   • Get step-by-step explanations"
    echo "   • Create study plans"
    echo "   • Generate practice questions"
    echo ""
    echo -e "${YELLOW}📊 Monitor usage:${NC}"
    echo "   • Check function logs: supabase functions logs ai-tutor"
    echo "   • View analytics in ai_tutor_logs table"
    echo ""
else
    echo -e "${RED}❌ Function test failed${NC}"
    echo ""
    echo -e "${YELLOW}🔍 Troubleshooting:${NC}"
    echo "   • Check function logs: supabase functions logs ai-tutor"
    echo "   • Verify API key: supabase secrets list"
    echo "   • Test manually in Supabase dashboard"
fi
