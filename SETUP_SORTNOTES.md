# SortNotes Setup Instructions

## Overview
The SortNotes system has been implemented with a complete backend and frontend solution. However, due to authentication and Docker issues, you need to manually set up the database components.

## Step 1: Set Up Database Tables and Functions

1. **Go to your Supabase Dashboard**
   - Navigate to: https://supabase.com/dashboard/project/umavpljptvamtmaygztq
   - Go to the "SQL Editor" tab

2. **Run the Database Setup Script**
   - Copy the entire content from `manual_setup.sql`
   - Paste it into the SQL Editor
   - Click "Run" to execute the script

   This will create:
   - `units` table (main folders/subjects)
   - `topics` table (subfolders within units)
   - Enhanced `notes` table with unit/topic references
   - RLS policies for security
   - Database functions for optimized queries

## Step 2: Set Up Storage Bucket

1. **Still in Supabase Dashboard**
   - Copy the content from `create_storage_bucket.sql`
   - Paste it into the SQL Editor
   - Click "Run" to execute

   This will create:
   - `notes-files` storage bucket
   - Storage policies for file access control

## Step 3: Verify Setup

1. **Check Tables**
   - Go to "Table Editor" in Supabase Dashboard
   - You should see: `units`, `topics`, and updated `notes` tables

2. **Check Storage**
   - Go to "Storage" in Supabase Dashboard
   - You should see the `notes-files` bucket

3. **Check Functions**
   - Go to "Database" → "Functions" in Supabase Dashboard
   - You should see: `get_user_units`, `get_unit_topics`, `get_topic_notes`

## Step 4: Test the Application

1. **Refresh the Application**
   - The app should now work without 404 errors
   - Navigate to the SortNotes page

2. **Test the Upload Flow**
   - Click "Upload New File"
   - Follow the 5-step process:
     1. Select a file (PDF, DOCX, etc.)
     2. Choose or create a unit (subject)
     3. Choose or create a topic
     4. Add title and description
     5. Confirm upload

## Features Implemented

### Backend
- ✅ **Hierarchical Organization**: Units → Topics → Notes
- ✅ **File Storage**: Secure file uploads with user-specific folders
- ✅ **Database Functions**: Optimized queries with counts
- ✅ **RLS Security**: Row-level security for all data
- ✅ **Metadata Tracking**: File names, sizes, types

### Frontend
- ✅ **5-Step Upload Process**: Exactly as specified
- ✅ **Real-time Data**: React Query integration
- ✅ **Search Functionality**: Across all levels
- ✅ **Responsive Design**: Works on all devices
- ✅ **Error Handling**: Graceful fallbacks
- ✅ **Loading States**: User feedback during operations

### User Flow
1. **Upload Trigger**: Click "Upload Notes" button
2. **File Selection**: Choose PDF/document with preview
3. **Unit Selection**: Choose existing or create new unit
4. **Topic Selection**: Choose existing or create new topic
5. **Confirmation**: Review and upload with metadata

## Troubleshooting

### If you get 404 errors:
- Make sure you ran the `manual_setup.sql` script completely
- Check that all functions were created in the Supabase dashboard

### If file uploads fail:
- Make sure you ran the `create_storage_bucket.sql` script
- Check that the `notes-files` bucket exists in Storage

### If authentication issues persist:
- Try logging out and logging back in
- Clear browser cache and cookies
- The hooks have fallback queries that work even if RPC functions fail

## File Structure

```
src/
├── hooks/
│   └── useNotes.ts              # All data fetching hooks
├── components/
│   └── sort-notes/
│       └── UploadNotesModal.tsx # 5-step upload modal
└── pages/
    └── SortNotes.tsx            # Main page with navigation
```

## Database Schema

```
units (main folders)
├── id, name, description, color, owner_id
├── created_at, updated_at
└── topics (subfolders)
    ├── id, name, description, unit_id, owner_id
    ├── created_at, updated_at
    └── notes (files)
        ├── id, title, content, unit_id, topic_id
        ├── file_url, file_name, file_type, file_size
        ├── tags, owner_id, created_at, updated_at
```

The system is now ready to use once the database setup is complete!
