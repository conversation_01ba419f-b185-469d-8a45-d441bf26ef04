# WooCommerce Payment Integration

This document describes the implementation of WooCommerce payment integration for StudyFam subscriptions, replacing the previous Paystack integration.

## Overview

The integration allows users to subscribe to StudyFam Premium through WooCommerce checkout, with payments processed via Pesapal (configured in WooCommerce), all within the app without external redirects.

## Components

### 1. WooCommerce Service (`src/services/woocommerceService.ts`)
- Handles WooCommerce REST API interactions
- Consumer Key: `ck_c5c40420db86d92e18e2628dff01d2b5b9c39356`
- Consumer Secret: `cs_187ac0a88c1f66032647235d060acb654195aa2e`
- Product URL: `https://studyfam.co.ke/product/studyfam-subscripton`

### 2. Checkout Modal (`src/components/subscription/CheckoutModal.tsx`)
- Displays WooCommerce checkout in an iframe/modal
- Detects payment success through URL monitoring and postMessage API
- Handles subscription activation upon successful payment
- Prevents external redirects by keeping everything in-app

### 3. Payment Wall (`src/components/PaymentWall.tsx`)
- Updated to use WooCommerce instead of Paystack
- Opens checkout modal when "Subscribe Now" is clicked
- Handles payment success callbacks

### 4. Subscription Management (`src/hooks/useSubscription.ts`)
- Removed Paystack dependencies
- Direct database integration for subscription status
- Handles trial and paid subscription states

### 5. Webhook Handler (`supabase/functions/woocommerce-webhook/index.ts`)
- Processes WooCommerce webhooks for order completion
- Automatically activates subscriptions when payments are confirmed
- Handles refunds and cancellations

## Flow

1. User clicks "Subscribe Now" in PaymentWall
2. CheckoutModal opens with WooCommerce product page in iframe
3. User completes checkout using Pesapal (configured in WooCommerce)
4. Success detection via URL monitoring and postMessage
5. Subscription activated in database
6. User stays within the app throughout the process

## Configuration

### WooCommerce Settings
- Ensure Pesapal is configured as payment gateway
- Set up webhooks to point to Supabase function
- Configure success/failure redirect URLs

### Environment Variables
No additional environment variables needed - credentials are hardcoded as requested.

### Database Tables
- `user_subscriptions`: Stores subscription data
- `subscription_plans`: Available plans
- `payment_transactions`: Transaction records
- `notifications`: Welcome messages

## Success Detection

The integration uses multiple methods to detect successful payments:

1. **URL Monitoring**: Checks for `/order-received/` in iframe URL
2. **DOM Elements**: Looks for WooCommerce success elements
3. **PostMessage API**: Receives messages from injected script
4. **Webhooks**: Server-side confirmation via WooCommerce webhooks

## Testing

To test the integration:

1. Click "Subscribe Now" in the payment wall
2. Complete checkout in the modal
3. Verify subscription activation
4. Check that user stays within the app

## Security

- CORS headers configured for cross-origin iframe communication
- Webhook signature verification (optional)
- Input validation on all API endpoints
- Secure credential handling

## Troubleshooting

### Common Issues

1. **Cross-origin restrictions**: Some features may be limited due to browser security
2. **Webhook delays**: Allow time for webhook processing
3. **Payment gateway issues**: Check Pesapal configuration in WooCommerce

### Debug Steps

1. Check browser console for postMessage events
2. Verify webhook endpoint is receiving data
3. Check database for subscription records
4. Monitor network requests in developer tools

## Future Enhancements

- Add payment method selection
- Implement subscription management (pause/cancel)
- Add invoice generation
- Enhanced error handling and retry logic
