-- Complete storage bucket setup for StudyFam application
-- Run this in Supabase SQL Editor to create all necessary storage buckets

-- Create notes-files bucket (for document uploads in notes)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'notes-files', 
  'notes-files', 
  true, 
  26214400, -- 25MB limit
  ARRAY[
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/bmp'
  ]
)
ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Create group-files bucket (for study group file uploads)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'group-files', 
  'group-files', 
  true, 
  52428800, -- 50MB limit
  ARRAY[
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'video/mp4',
    'audio/mpeg'
  ]
)
ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Create message-files bucket (for chat attachments)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'message-files', 
  'message-files', 
  true, 
  10485760, -- 10MB limit
  ARRAY[
    'application/pdf',
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'video/mp4',
    'audio/mpeg',
    'text/plain'
  ]
)
ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Drop existing storage policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Users can upload their own files" ON storage.objects;
DROP POLICY IF EXISTS "Users can view their own files" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own files" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own files" ON storage.objects;
DROP POLICY IF EXISTS "Public file access" ON storage.objects;

-- Create comprehensive storage policies

-- Policy for uploading files (users can upload to their own folder)
CREATE POLICY "Users can upload their own files" ON storage.objects
FOR INSERT WITH CHECK (
  (bucket_id = 'notes-files' AND auth.uid()::text = (storage.foldername(name))[1]) OR
  (bucket_id = 'group-files' AND auth.uid() IS NOT NULL) OR
  (bucket_id = 'message-files' AND auth.uid() IS NOT NULL)
);

-- Policy for viewing files (users can view their own files + public files)
CREATE POLICY "Users can view files" ON storage.objects
FOR SELECT USING (
  (bucket_id = 'notes-files' AND auth.uid()::text = (storage.foldername(name))[1]) OR
  (bucket_id = 'group-files' AND auth.uid() IS NOT NULL) OR
  (bucket_id = 'message-files' AND auth.uid() IS NOT NULL) OR
  bucket_id IN ('avatars', 'group-covers', 'past-papers')
);

-- Policy for updating files (users can update their own files)
CREATE POLICY "Users can update their own files" ON storage.objects
FOR UPDATE USING (
  (bucket_id = 'notes-files' AND auth.uid()::text = (storage.foldername(name))[1]) OR
  (bucket_id = 'group-files' AND auth.uid() IS NOT NULL) OR
  (bucket_id = 'message-files' AND auth.uid() IS NOT NULL)
);

-- Policy for deleting files (users can delete their own files)
CREATE POLICY "Users can delete their own files" ON storage.objects
FOR DELETE USING (
  (bucket_id = 'notes-files' AND auth.uid()::text = (storage.foldername(name))[1]) OR
  (bucket_id = 'group-files' AND auth.uid() IS NOT NULL) OR
  (bucket_id = 'message-files' AND auth.uid() IS NOT NULL)
);

-- Verify buckets were created
SELECT 
  id,
  name,
  public,
  file_size_limit,
  allowed_mime_types,
  created_at
FROM storage.buckets 
WHERE id IN ('notes-files', 'group-files', 'message-files')
ORDER BY created_at;
