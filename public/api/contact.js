// Simple contact form handler using Netlify Functions or similar
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { name, email, message, to } = req.body;

    // Validate input
    if (!name || !email || !message) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Here you would integrate with your preferred email service
    // For now, we'll simulate success and log the message
    console.log('Contact form submission:', {
      name,
      email,
      message,
      to: to || '<EMAIL>',
      timestamp: new Date().toISOString()
    });

    // In a real implementation, you would use services like:
    // - SendGrid
    // - Mailgun
    // - AWS SES
    // - Nodemailer with SMTP
    
    return res.status(200).json({ 
      success: true, 
      message: 'Message sent successfully' 
    });
  } catch (error) {
    console.error('Contact form error:', error);
    return res.status(500).json({ 
      error: 'Internal server error' 
    });
  }
}
