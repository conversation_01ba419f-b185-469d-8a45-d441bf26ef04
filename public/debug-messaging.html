<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Messaging Debug Tool</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success { background: rgba(34, 197, 94, 0.2); border-color: #22c55e; }
        .error { background: rgba(239, 68, 68, 0.2); border-color: #ef4444; }
        .warning { background: rgba(245, 158, 11, 0.2); border-color: #f59e0b; }
        .info { background: rgba(59, 130, 246, 0.2); border-color: #3b82f6; }
        button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: rgba(255, 255, 255, 0.3); }
        button:disabled { opacity: 0.5; cursor: not-allowed; }
        pre {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        input, select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            margin: 5px;
        }
        input::placeholder { color: rgba(255, 255, 255, 0.6); }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .section { background: rgba(255, 255, 255, 0.05); padding: 20px; border-radius: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>StudyFam Messaging Debug Tool</h1>
        <p>This tool helps diagnose messaging issues step by step.</p>
        
        <div id="status"></div>
        
        <div class="grid">
            <div class="section">
                <h3>1. Basic Checks</h3>
                <button onclick="checkAuth()">Check Authentication</button>
                <button onclick="checkTables()">Check Database Tables</button>
                <button onclick="checkFriends()">Check Friends</button>
            </div>
            
            <div class="section">
                <h3>2. Conversation Tests</h3>
                <input type="text" id="friendId" placeholder="Friend User ID" />
                <button onclick="testCreateConversation()">Create Conversation</button>
                <button onclick="listConversations()">List My Conversations</button>
            </div>
        </div>
        
        <div class="section">
            <h3>3. Message Tests</h3>
            <input type="text" id="conversationId" placeholder="Conversation ID" />
            <input type="text" id="messageContent" placeholder="Test message content" />
            <button onclick="testSendMessage()">Send Test Message</button>
            <button onclick="checkParticipants()">Check Participants</button>
        </div>
        
        <div class="section">
            <h3>4. RLS Policy Tests</h3>
            <button onclick="testRLSPolicies()">Test RLS Policies</button>
            <button onclick="checkPermissions()">Check Permissions</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const SUPABASE_URL = "https://umavpljptvamtmaygztq.supabase.co";
        const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVtYXZwbGpwdHZhbXRtYXlnenRxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyMzg1NjEsImV4cCI6MjA2NTgxNDU2MX0.f16xzL7S0VLtiYyovLl4mOxWFW4ys-KVJQgF-VkgLU4";
        
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_KEY);
        let currentUser = null;
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function addResult(title, data, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultHtml = `
                <div class="status ${type}">
                    <h3>${title}</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
            resultsDiv.innerHTML += resultHtml;
        }
        
        async function checkAuth() {
            updateStatus('Checking authentication...', 'info');
            try {
                const { data: { user }, error } = await supabase.auth.getUser();
                if (error) throw error;
                
                if (user) {
                    currentUser = user;
                    updateStatus('✅ User is authenticated', 'success');
                    addResult('Authentication Status', {
                        authenticated: true,
                        userId: user.id,
                        email: user.email
                    }, 'success');
                } else {
                    updateStatus('❌ User is not authenticated', 'warning');
                    addResult('Authentication Status', {
                        authenticated: false,
                        message: 'Please sign in first'
                    }, 'warning');
                }
            } catch (error) {
                updateStatus('❌ Authentication check failed', 'error');
                addResult('Authentication Error', { error: error.message }, 'error');
            }
        }
        
        async function checkTables() {
            updateStatus('Checking database tables...', 'info');
            try {
                const tables = ['conversations', 'conversation_participants', 'messages'];
                const results = {};
                
                for (const table of tables) {
                    const { data, error } = await supabase.from(table).select('count').limit(1);
                    results[table] = error ? { error: error.message } : { accessible: true };
                }
                
                updateStatus('✅ Database tables checked', 'success');
                addResult('Database Tables', results, 'success');
            } catch (error) {
                updateStatus('❌ Database check failed', 'error');
                addResult('Database Error', { error: error.message }, 'error');
            }
        }
        
        async function checkFriends() {
            if (!currentUser) {
                updateStatus('❌ Please authenticate first', 'warning');
                return;
            }
            
            updateStatus('Checking friends...', 'info');
            try {
                const { data, error } = await supabase.rpc('get_user_friends', { 
                    p_user_id: currentUser.id 
                });
                
                if (error) throw error;
                
                updateStatus(`✅ Found ${data?.length || 0} friends`, 'success');
                addResult('Friends List', data || [], 'success');
            } catch (error) {
                updateStatus('❌ Friends check failed', 'error');
                addResult('Friends Error', { error: error.message }, 'error');
            }
        }
        
        async function testCreateConversation() {
            const friendId = document.getElementById('friendId').value;
            if (!friendId) {
                updateStatus('❌ Please enter a friend ID', 'warning');
                return;
            }
            
            if (!currentUser) {
                updateStatus('❌ Please authenticate first', 'warning');
                return;
            }
            
            updateStatus('Creating conversation...', 'info');
            try {
                const { data, error } = await supabase.rpc('get_or_create_conversation', {
                    p_user1_id: currentUser.id,
                    p_user2_id: friendId
                });
                
                if (error) throw error;
                
                document.getElementById('conversationId').value = data;
                updateStatus('✅ Conversation created/found', 'success');
                addResult('Conversation Creation', { conversationId: data }, 'success');
            } catch (error) {
                updateStatus('❌ Conversation creation failed', 'error');
                addResult('Conversation Error', { error: error.message }, 'error');
            }
        }
        
        async function listConversations() {
            if (!currentUser) {
                updateStatus('❌ Please authenticate first', 'warning');
                return;
            }
            
            updateStatus('Listing conversations...', 'info');
            try {
                const { data, error } = await supabase.rpc('get_user_conversations', {
                    p_user_id: currentUser.id
                });
                
                if (error) throw error;
                
                updateStatus(`✅ Found ${data?.length || 0} conversations`, 'success');
                addResult('Conversations List', data || [], 'success');
            } catch (error) {
                updateStatus('❌ Conversations list failed', 'error');
                addResult('Conversations Error', { error: error.message }, 'error');
            }
        }
        
        async function testSendMessage() {
            const conversationId = document.getElementById('conversationId').value;
            const messageContent = document.getElementById('messageContent').value;
            
            if (!conversationId || !messageContent) {
                updateStatus('❌ Please enter conversation ID and message content', 'warning');
                return;
            }
            
            if (!currentUser) {
                updateStatus('❌ Please authenticate first', 'warning');
                return;
            }
            
            updateStatus('Sending test message...', 'info');
            try {
                const messageData = {
                    conversation_id: conversationId,
                    sender_id: currentUser.id,
                    content: messageContent,
                    message_type: 'text'
                };
                
                const { data, error } = await supabase
                    .from('messages')
                    .insert(messageData)
                    .select();
                
                if (error) throw error;
                
                updateStatus('✅ Message sent successfully', 'success');
                addResult('Message Send', data, 'success');
            } catch (error) {
                updateStatus('❌ Message send failed', 'error');
                addResult('Message Error', { error: error.message }, 'error');
            }
        }
        
        async function checkParticipants() {
            const conversationId = document.getElementById('conversationId').value;
            
            if (!conversationId) {
                updateStatus('❌ Please enter conversation ID', 'warning');
                return;
            }
            
            updateStatus('Checking participants...', 'info');
            try {
                const { data, error } = await supabase
                    .from('conversation_participants')
                    .select('*, profiles(full_name, email)')
                    .eq('conversation_id', conversationId);
                
                if (error) throw error;
                
                updateStatus(`✅ Found ${data?.length || 0} participants`, 'success');
                addResult('Participants', data || [], 'success');
            } catch (error) {
                updateStatus('❌ Participants check failed', 'error');
                addResult('Participants Error', { error: error.message }, 'error');
            }
        }
        
        async function testRLSPolicies() {
            if (!currentUser) {
                updateStatus('❌ Please authenticate first', 'warning');
                return;
            }
            
            updateStatus('Testing RLS policies...', 'info');
            try {
                const tests = [];
                
                // Test conversations access
                const { data: convData, error: convError } = await supabase
                    .from('conversations')
                    .select('id')
                    .limit(1);
                tests.push({ table: 'conversations', success: !convError, error: convError?.message });
                
                // Test messages access
                const { data: msgData, error: msgError } = await supabase
                    .from('messages')
                    .select('id')
                    .limit(1);
                tests.push({ table: 'messages', success: !msgError, error: msgError?.message });
                
                // Test participants access
                const { data: partData, error: partError } = await supabase
                    .from('conversation_participants')
                    .select('id')
                    .limit(1);
                tests.push({ table: 'conversation_participants', success: !partError, error: partError?.message });
                
                updateStatus('✅ RLS policies tested', 'success');
                addResult('RLS Policy Tests', tests, 'success');
            } catch (error) {
                updateStatus('❌ RLS policy test failed', 'error');
                addResult('RLS Error', { error: error.message }, 'error');
            }
        }
        
        async function checkPermissions() {
            if (!currentUser) {
                updateStatus('❌ Please authenticate first', 'warning');
                return;
            }
            
            updateStatus('Checking permissions...', 'info');
            try {
                // Check if user can insert into messages table
                const testMessage = {
                    conversation_id: '00000000-0000-0000-0000-000000000000', // Dummy ID
                    sender_id: currentUser.id,
                    content: 'Test message',
                    message_type: 'text'
                };
                
                const { error } = await supabase
                    .from('messages')
                    .insert(testMessage)
                    .select();
                
                const result = {
                    canInsertMessages: !error || !error.message.includes('permission'),
                    error: error?.message
                };
                
                updateStatus('✅ Permissions checked', 'success');
                addResult('Permissions Check', result, 'success');
            } catch (error) {
                updateStatus('❌ Permissions check failed', 'error');
                addResult('Permissions Error', { error: error.message }, 'error');
            }
        }
        
        // Auto-run basic checks on load
        window.onload = async function() {
            updateStatus('Initializing messaging debug tool...', 'info');
            await checkAuth();
        };
    </script>
</body>
</html>
