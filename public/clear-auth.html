<!DOCTYPE html>
<html>
<head>
    <title>Clear Authentication Data</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #45a049;
        }
        .danger {
            background: #f44336;
        }
        .danger:hover {
            background: #da190b;
        }
        .info {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 <PERSON><PERSON><PERSON> Auth Troubleshooter</h1>
        <p>If you're experiencing authentication issues, use the tools below to clear invalid session data.</p>
        
        <div class="info">
            <h3>Current Status</h3>
            <p id="status">Checking...</p>
        </div>
        
        <button onclick="clearAuthData()">Clear Authentication Data</button>
        <button onclick="clearAllData()" class="danger">Clear All Local Data</button>
        <button onclick="checkStatus()">Check Status</button>
        <button onclick="goToApp()">Go to App</button>
        
        <div class="info">
            <h3>What this does:</h3>
            <ul style="text-align: left;">
                <li><strong>Clear Auth Data:</strong> Removes only authentication-related localStorage items</li>
                <li><strong>Clear All Data:</strong> Removes all localStorage data (use if problems persist)</li>
                <li><strong>Check Status:</strong> Shows current authentication status</li>
            </ul>
        </div>
    </div>

    <script>
        function clearAuthData() {
            const authKeys = Object.keys(localStorage).filter(key => 
                key.startsWith('supabase.auth') || 
                key.includes('auth') ||
                key.includes('session') ||
                key.includes('token')
            );
            
            authKeys.forEach(key => {
                localStorage.removeItem(key);
                console.log('Removed:', key);
            });
            
            alert(`Cleared ${authKeys.length} authentication items. Please refresh the app.`);
            checkStatus();
        }
        
        function clearAllData() {
            if (confirm('This will clear ALL local data. Are you sure?')) {
                localStorage.clear();
                sessionStorage.clear();
                alert('All local data cleared. Please refresh the app.');
                checkStatus();
            }
        }
        
        function checkStatus() {
            const authKeys = Object.keys(localStorage).filter(key => 
                key.startsWith('supabase') || 
                key.includes('auth')
            );
            
            const statusEl = document.getElementById('status');
            if (authKeys.length === 0) {
                statusEl.innerHTML = '✅ No authentication data found';
                statusEl.style.color = '#4CAF50';
            } else {
                statusEl.innerHTML = `⚠️ Found ${authKeys.length} auth-related items:<br>${authKeys.join('<br>')}`;
                statusEl.style.color = '#ff9800';
            }
        }
        
        function goToApp() {
            window.location.href = '/';
        }
        
        // Check status on load
        checkStatus();
    </script>
</body>
</html>
