<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Debug Tool</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .success {
            background: rgba(34, 197, 94, 0.2);
            border-color: #22c55e;
        }
        .error {
            background: rgba(239, 68, 68, 0.2);
            border-color: #ef4444;
        }
        .warning {
            background: rgba(245, 158, 11, 0.2);
            border-color: #f59e0b;
        }
        .info {
            background: rgba(59, 130, 246, 0.2);
            border-color: #3b82f6;
        }
        button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        pre {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>StudyFam Profile Debug Tool</h1>
        <p>This tool helps diagnose profile loading issues.</p>
        
        <div id="status"></div>
        
        <div style="margin: 20px 0;">
            <button onclick="checkAuth()">Check Authentication</button>
            <button onclick="checkProfile()">Check Profile</button>
            <button onclick="clearAuth()">Clear Auth Data</button>
            <button onclick="testConnection()">Test Supabase Connection</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const SUPABASE_URL = "https://umavpljptvamtmaygztq.supabase.co";
        const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVtYXZwbGpwdHZhbXRtYXlnenRxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyMzg1NjEsImV4cCI6MjA2NTgxNDU2MX0.f16xzL7S0VLtiYyovLl4mOxWFW4ys-KVJQgF-VkgLU4";
        
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_KEY);
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function addResult(title, data, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultHtml = `
                <div class="status ${type}">
                    <h3>${title}</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
            resultsDiv.innerHTML += resultHtml;
        }
        
        async function testConnection() {
            updateStatus('Testing Supabase connection...', 'info');
            try {
                const { data, error } = await supabase.from('profiles').select('count').limit(1);
                if (error) throw error;
                updateStatus('✅ Supabase connection successful', 'success');
                addResult('Connection Test', { success: true, message: 'Connected to Supabase' }, 'success');
            } catch (error) {
                updateStatus('❌ Supabase connection failed', 'error');
                addResult('Connection Test', { success: false, error: error.message }, 'error');
            }
        }
        
        async function checkAuth() {
            updateStatus('Checking authentication...', 'info');
            try {
                const { data: { session }, error } = await supabase.auth.getSession();
                if (error) throw error;
                
                if (session) {
                    updateStatus('✅ User is authenticated', 'success');
                    addResult('Authentication Status', {
                        authenticated: true,
                        userId: session.user.id,
                        email: session.user.email,
                        expiresAt: new Date(session.expires_at * 1000).toLocaleString()
                    }, 'success');
                } else {
                    updateStatus('❌ User is not authenticated', 'warning');
                    addResult('Authentication Status', {
                        authenticated: false,
                        message: 'No active session found'
                    }, 'warning');
                }
            } catch (error) {
                updateStatus('❌ Authentication check failed', 'error');
                addResult('Authentication Error', { error: error.message }, 'error');
            }
        }
        
        async function checkProfile() {
            updateStatus('Checking profile data...', 'info');
            try {
                // First check auth
                const { data: { user }, error: authError } = await supabase.auth.getUser();
                if (authError) throw authError;
                
                if (!user) {
                    updateStatus('❌ No authenticated user found', 'warning');
                    addResult('Profile Check', { error: 'User not authenticated' }, 'warning');
                    return;
                }
                
                // Then check profile
                const { data: profile, error: profileError } = await supabase
                    .from('profiles')
                    .select('*')
                    .eq('id', user.id)
                    .single();
                
                if (profileError) {
                    updateStatus('❌ Profile query failed', 'error');
                    addResult('Profile Error', {
                        userId: user.id,
                        error: profileError.message,
                        code: profileError.code,
                        details: profileError.details
                    }, 'error');
                    return;
                }
                
                if (profile) {
                    updateStatus('✅ Profile loaded successfully', 'success');
                    addResult('Profile Data', {
                        id: profile.id,
                        email: profile.email,
                        full_name: profile.full_name,
                        country: profile.country,
                        course: profile.course,
                        institute: profile.institute,
                        created_at: profile.created_at
                    }, 'success');
                } else {
                    updateStatus('❌ No profile found', 'warning');
                    addResult('Profile Check', {
                        userId: user.id,
                        message: 'Profile exists in auth but not in profiles table'
                    }, 'warning');
                }
                
            } catch (error) {
                updateStatus('❌ Profile check failed', 'error');
                addResult('Profile Check Error', { error: error.message }, 'error');
            }
        }
        
        function clearAuth() {
            updateStatus('Clearing authentication data...', 'info');
            localStorage.clear();
            sessionStorage.clear();
            supabase.auth.signOut();
            updateStatus('✅ Authentication data cleared', 'success');
            addResult('Clear Auth', { message: 'Local storage and session cleared' }, 'info');
        }
        
        // Auto-run basic checks on load
        window.onload = async function() {
            updateStatus('Initializing debug tool...', 'info');
            await testConnection();
            await checkAuth();
        };
    </script>
</body>
</html>
