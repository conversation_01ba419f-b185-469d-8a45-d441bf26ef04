# 🗂️ Storage Bucket Setup Guide

## ❌ **Current Issue**
You're getting a "Bucket not found" error because the Supabase storage buckets haven't been created yet.

## ✅ **Quick Fix (5 minutes)**

### **Step 1: Open Supabase Dashboard**
1. Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Select your project: `supabase-indigo-window`
3. Click on **"SQL Editor"** in the left sidebar

### **Step 2: Run Storage Setup Script**
1. Copy the entire content from `setup_storage_buckets.sql`
2. Paste it into the SQL Editor
3. Click **"Run"** button
4. You should see a success message and the verification query results

### **Step 3: Verify Storage Buckets**
1. Go to **"Storage"** in the left sidebar
2. You should now see these buckets:
   - ✅ `notes-files` (for document uploads)
   - ✅ `group-files` (for study group files)
   - ✅ `message-files` (for chat attachments)

### **Step 4: Test the Application**
1. Refresh your application
2. Try uploading a document when creating a note
3. The upload should now work without errors!

## 📋 **What This Setup Creates**

### **Storage Buckets:**
- **`notes-files`**: For PDF, Word, PowerPoint, Excel, images, and text files (25MB limit)
- **`group-files`**: For study group file sharing (50MB limit)
- **`message-files`**: For chat attachments (10MB limit)

### **Security Policies:**
- ✅ Users can only upload to their own folders
- ✅ Users can view their own files and public files
- ✅ Proper file type restrictions
- ✅ File size limits enforced

### **Supported File Types:**
- **Documents**: PDF, Word (.doc/.docx), PowerPoint (.ppt/.pptx), Excel (.xls/.xlsx)
- **Images**: JPEG, PNG, GIF, WebP, BMP
- **Text**: Plain text (.txt)
- **Media**: MP4 videos, MP3 audio (for group files)

## 🔧 **Troubleshooting**

### **If you still get "Bucket not found":**
1. Make sure you ran the SQL script in the correct project
2. Check that the buckets appear in Storage section
3. Try refreshing your application
4. Clear browser cache if needed

### **If upload fails with "Policy violation":**
1. Make sure you're logged in to the application
2. Check that the file type is supported
3. Verify file size is within limits

### **If files don't display:**
1. Check that the file was uploaded successfully
2. Verify the file URL is accessible
3. Make sure the document viewer is working

## 🎯 **Next Steps**

After running this setup:
1. ✅ Document uploads will work
2. ✅ Document viewer will display files
3. ✅ File sharing in study groups will work
4. ✅ Chat attachments will work

## 📞 **Need Help?**

If you encounter any issues:
1. Check the browser console for error messages
2. Verify the SQL script ran successfully
3. Make sure you're in the correct Supabase project
4. Try the troubleshooting steps above

The storage setup is essential for the document viewer to work properly!
